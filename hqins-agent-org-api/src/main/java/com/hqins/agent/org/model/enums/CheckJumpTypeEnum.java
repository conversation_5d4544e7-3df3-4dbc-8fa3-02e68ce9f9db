package com.hqins.agent.org.model.enums;

public enum CheckJumpTypeEnum {
    STAFF_POP_UP("1","下辖人员清单弹窗"),
    NAME_POP_UP("2","姓名弹窗");

    private String code;

    private String label;

    CheckJumpTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static CheckJumpTypeEnum get(String name) {
        for (CheckJumpTypeEnum value : CheckJumpTypeEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static CheckJumpTypeEnum getLabelByCode(String code) {
        for (CheckJumpTypeEnum value : CheckJumpTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
