package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.AppType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@ApiModel("机构配置对象")
@Getter
@SuperBuilder
public class PersonalizationrQueryRequest {

    @ApiModelProperty("顶层机构编码，依赖orgType")
    private String code;

    @ApiModelProperty("菜单类型 H5:H5、MINI_PROGRAM:小程序")
    private AppType appType;


}
