package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.AppEmployeeInfoVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025/3/4 09:19
 */
@FeignClient(name = "hqins-agent-org")
public interface AppEmployeeRecruitmentApi {

    @ApiOperation("查询招募人员详情")
    @GetMapping("/agent-org/app/employee/recruitmentEmployeeInfo")
    AppEmployeeInfoVO queryAppRecruitmentEmployeeInfo(@ApiParam("被招募人员编码") @RequestParam(value = "employeeCode", required = false) String employeeCode,
                                                      @ApiParam("被招募人员访客Id") @RequestParam(value = "visitorId", required = false) String visitorId);
}
