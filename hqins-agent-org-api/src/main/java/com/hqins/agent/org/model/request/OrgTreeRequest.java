package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.NodeLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/5/19
 * @Description
 */
@ApiModel("机构树查询请求")
@Getter
@SuperBuilder
public class OrgTreeRequest {

    @ApiModelProperty("合伙人或者渠道商代码")
    private String topCode;

    @ApiModelProperty("销售机构代码")
    private String orgCode;

    @ApiModelProperty("要查到哪一级：TOP-合伙人；ORG-机构；TEAM-团队, 默认为ORG")
    private NodeLevel nodeLevel;

}
