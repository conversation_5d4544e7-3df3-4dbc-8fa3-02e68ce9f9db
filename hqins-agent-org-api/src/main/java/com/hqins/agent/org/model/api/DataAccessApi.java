package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.MyDataAccessVO;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
@FeignClient(name = "hqins-agent-org")
public interface DataAccessApi {

    /**
     * 获取后台某个用户拥有数据权限的组织机构code列表
     *
     * @param adminAppId
     * @param staffId
     * @return
     */
    @GetMapping("/agent-org/data/accesses/current-staff/data-access")
    MyDataAccessVO getCurrentStaffDataAccess(@ApiParam("应用id") @RequestParam("adminAppId") Long adminAppId,
                                             @ApiParam("工号") @RequestParam("staffId") Long staffId);

}
