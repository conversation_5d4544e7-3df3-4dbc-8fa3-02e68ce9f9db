package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentTeamDateVO {

    @ApiModelProperty("表头信息")
    private List<AssessmentTableTitleVO> tableTitles;

    @ApiModelProperty("表格值")
    private List<AssessmentTableValueVO> tableValues;

    @ApiModelProperty("团队编码")
    private String saleTeamCode;

    @ApiModelProperty("团队名称")
    private String saleTeamName;

    @ApiModelProperty("区部组的类型")
    private String saleTeamType;

    @ApiModelProperty("用与获取合格率与在职人力")
    private HashSet<String> saleTeamCodes;

    @ApiModelProperty("所有的标题")
    private HashSet<String> tableTitleSet;

    @ApiModelProperty("现有在职人力数")
    private Integer deptCurrentNum;

    @ApiModelProperty("主管编码")
    private String empInCode;

    @ApiModelProperty("下级机构")
    private List<AssessmentTeamDateVO> childrenList;
}
