package com.hqins.agent.org.model.enums;

public enum HonorClassEnum {

    QXJYH("QXJYH", "琴星精英会"),
    QYBSX("QYBSX", "琴韵博识轩"),
    QHRYT("QHRYT", "琴辉荣耀堂"),
    MDRT("MDRT", "MDRT百万圆桌年会"),
    QHJNX("QHJNX", "琴海吉尼斯");

    private final String value;
    private final String label;

    HonorClassEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static HonorClassEnum getHonorClassEnumByValue(String queryValue) {
        for (HonorClassEnum honorClass : HonorClassEnum.values()) {
            if (honorClass.getValue().equals(queryValue)) {
                return honorClass;
            }
        }
        return null;
    }
}
