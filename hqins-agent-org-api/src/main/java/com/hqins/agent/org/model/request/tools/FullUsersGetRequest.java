package com.hqins.agent.org.model.request.tools;

import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.enums.Gender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021/6/10
 */
@ApiModel("获取完整用户信息的接口")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FullUsersGetRequest {

    @ApiModelProperty("营业组编码")
    @NotBlank(message = "营业组编码不能为空")
    private String teamCode;

    @ApiModelProperty("销售员代码")
    @NotBlank(message = "销售员代码不能为空")
    private String employeeCode;

    @ApiModelProperty("性别")
    @EnumValue(enumClass = Gender.class, enumMethod = "isValid", message = "性别不正确")
    private String gender;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名")
    private String name;

    @ApiModelProperty("身份证号")
    @NotBlank(message = "身份证号不能为空")
    private String idCode;

    @ApiModelProperty("管理后台-用户名")
    @NotBlank(message = "管理后台-用户名不能为空")
    private String username;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号")
    private String mobile;

}
