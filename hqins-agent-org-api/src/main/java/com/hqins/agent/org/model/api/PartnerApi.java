package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.SimpleNodeVO;
import com.hqins.agent.org.model.vo.TeamVO;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

@FeignClient(name = "hqins-agent-org")
public interface PartnerApi {

    /**
     * 获取有管理权限的合伙人，简化版下拉框里使用
     */
    @GetMapping("/agent-org/partners/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    @CollectionElement(targetClass = SimpleNodeVO.class)
    PageInfo<SimpleNodeVO> listMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "10") long size);

}
