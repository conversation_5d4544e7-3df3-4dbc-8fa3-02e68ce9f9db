package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lijian
 * @Date: 2023/9/11 3:26 下午
 */
@ApiModel("渠道商代理人结果对象")
@Data
public class CheckEmployeeResultVO {

    @ApiModelProperty("校验结果：1 全部验证成功；2 部分验证成功； 0 验证全部失败")
    private Integer checkResult;

    @ApiModelProperty("校验结果：1 全部验证成功；2 部分验证成功； 0 验证全部失败")
    private String checkResultMessage;

    @ApiModelProperty("校验结果明细")
    private List<CheckResultDetail> checkResultDetailList;
}
