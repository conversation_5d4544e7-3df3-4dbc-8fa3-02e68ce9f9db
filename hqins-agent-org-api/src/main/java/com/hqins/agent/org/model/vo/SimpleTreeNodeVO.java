package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.Treeable;
import com.hqins.agent.org.model.enums.DataType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("组织机构或团队结点")
@Data
public class SimpleTreeNodeVO implements Treeable<SimpleTreeNodeVO> {

    @ApiModelProperty("代码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("父结点代码")
    private String parentCode;

    @ApiModelProperty("数据类型")
    private DataType dataType;

    @ApiModelProperty("编码与dataType")
    private String codeDateType;

    @ApiModelProperty("所有孩子结点")
    private List<SimpleTreeNodeVO> children;

}
