package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("分页查询所有员工请求")
public class AllEmpByPageRequest implements Serializable {
    @ApiModelProperty("开始页面 必须大于0")
    private Integer page;

    @ApiModelProperty("每页条数 必须大于0")
    private Integer size;

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商 SUPERVISOR-督导账号")
    private String orgType;
}
