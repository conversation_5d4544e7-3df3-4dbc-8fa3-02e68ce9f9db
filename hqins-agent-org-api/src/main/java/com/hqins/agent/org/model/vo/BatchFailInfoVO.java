package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.common.base.enums.Gender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2023/4/25 14:58
 */
@ApiModel("错误信息详情对象")
@Data
public class BatchFailInfoVO {

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("是否有万能险销售资格 true-有 false-没有")
    private Boolean universalQualification = false;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    private Gender gender;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;


    @ApiModelProperty("销售员手机号")
    private String mobile;

    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ApiModelProperty("执业证号启期")
    private LocalDate licenseStartDate;

    @ApiModelProperty("执业证号止期")
    private LocalDate licenseEndDate;

    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("入职时间")
    private LocalDate entryTime;

    private Long staffId;

    @ApiModelProperty("人员类型  TESTER体验人员 FORMAL正式人员 ")
    private RoleType roleType;

    @ApiModelProperty("失败原因")
    private String errorMessage;

    @ApiModelProperty("所属渠道商编码")
    private String channelCode;

    @ApiModelProperty("所属渠道商名称")
    private String channelName;

    @ApiModelProperty("所属机构编码")
    private String orgCode;

    @ApiModelProperty("所属渠道商名称")
    private String orgName;

    @ApiModelProperty("归属销售团队编码")
    private String teamCode;

    @ApiModelProperty("归属销售团队名称")
    private String teamName;


}
