package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @Date 2023/4/3 17:55
 */
@ApiModel("角色类型")
public enum SupervisorType {
    <PERSON><PERSON><PERSON><PERSON>("总部"),
    <PERSON><PERSON><PERSON>("机构"),
    <PERSON><PERSON><PERSON><PERSON>("团队"),
    <PERSON><PERSON><PERSON><PERSON>("普通");

    private String label;

    SupervisorType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static TeamLevel get(String name) {
        for (TeamLevel value : TeamLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
