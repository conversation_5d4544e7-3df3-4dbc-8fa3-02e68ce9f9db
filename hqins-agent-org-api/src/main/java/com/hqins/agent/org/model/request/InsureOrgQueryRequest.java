package com.hqins.agent.org.model.request;

import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19
 * @Description
 */
@ApiModel("渠道商分页查询请求")
@Data
@SuperBuilder
public class InsureOrgQueryRequest extends PageQueryRequest {

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商\"")
    private AgentOrgType orgType;

    @ApiModelProperty("销售机构代码")
    private String code;

    @ApiModelProperty("销售机构名称")
    private String name;

    @ApiModelProperty("合伙人、渠道商代码")
    private String topCode;

    @ApiModelProperty("合伙人、渠道商名称")
    private String topName;

    @ApiModelProperty("销售机构层级：00-总行/总公司 01-省分/分公司 02-市分 03-支行 04-网点")
    private List<String> orgLevels;

}
