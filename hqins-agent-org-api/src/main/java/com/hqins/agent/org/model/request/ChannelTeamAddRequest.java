package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.TeamLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("渠道商团队新增请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelTeamAddRequest {

    @ApiModelProperty("团队名称")
    @NotBlank(message = "团队名称不能为空")
    private String name;

    @ApiModelProperty("团队级别")
    private TeamLevel level;

    @ApiModelProperty("归属销售机构编码")
    @NotBlank(message = "归属销售机构编码不能为空")
    private String orgCode;

    @ApiModelProperty("上级销售团队编码")
    private String parentCode;


}
