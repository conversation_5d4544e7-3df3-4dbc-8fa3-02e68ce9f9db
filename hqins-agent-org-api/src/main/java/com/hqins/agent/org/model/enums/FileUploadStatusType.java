package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构类型
 */
@ApiModel("文件上传状态")
public enum FileUploadStatusType {
    /**
     * 组织机构类型
     */
    HAVE_NOT_STARTED("待执行"),
    UNFINISHED("执行中"),
    SUCCEED("执行成功");

    private String value;

    FileUploadStatusType(String value) {
        this.value = value;
    }
    public String getValue() { return this.value; }


    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static FileUploadStatusType get(String name) {
        for (FileUploadStatusType value : FileUploadStatusType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
