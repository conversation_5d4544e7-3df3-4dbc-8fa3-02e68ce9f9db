package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2023/9/17 9:02 下午
 */
public enum ZYBXIdTypeEnum {

    /**
     * 111 居民身份证
     * 114 军官证
     * 123 中国人民解放军军官证 包含军官证、士兵证
     * 414 普通护照
     * 420 香港特别行政区护照
     * 421 澳门特别行政区护照
     * 511 台湾居民来往大陆通行证
     * 513 往来港澳通行证
     * 516 港澳居民来往内地通行证
     */

    ID("111", "居民身份证"),
    OFFICER_CERTIFICATE("114", "军官证"),
    POLICE_OFFICER("123", "警官证"),
    PASSPORT("414", "普通护照"),
    ID_HK("420", "香港特别行政区护照"),
    ID_MACAO("421", "澳门特别行政区护照"),
    TAIWAN_CN_PASS("511", "台湾居民来往大陆通行证"),
    HK_MACAO_PASS("513","往来港澳通行证"),
    CH_HK_MACAO_PASS("516","港澳居民来往内地通行证"),
    ID_FOREIGNER("553","外国人永久居留身份证"),
    OTHER("990","其他");

    private String code;
    private String label;

    ZYBXIdTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
