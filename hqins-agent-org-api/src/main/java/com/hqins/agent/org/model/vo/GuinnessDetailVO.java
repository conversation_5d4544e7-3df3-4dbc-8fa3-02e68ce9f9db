package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/17
 */
@ApiModel("琴海吉尼斯详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuinnessDetailVO {

    @ApiModelProperty("千万期缴大师")
    private MasterAwardVO premiumMaster;

    @ApiModelProperty("千万标保大师")
    private MasterAwardVO standardMaster;

    @ApiModelProperty(value = "获奖年度")
    private String year;

    @ApiModel("琴海吉尼斯奖项详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MasterAwardVO {
        @ApiModelProperty(value = "奖项状态 0 未达成 1 已达成", example = "1")
        private String status;
        @ApiModelProperty(value = "已达成的保费")
        private String requiredValue;
        @ApiModelProperty(value = "贺报ID")
        private String honorId;
        @ApiModelProperty(value = "获奖时间")
        private String awardTime;
        @ApiModelProperty(value = "达成标准")
        private String standard;
        @ApiModelProperty(value = "达成标准单位")
        private String standardUnit;
    }

}
