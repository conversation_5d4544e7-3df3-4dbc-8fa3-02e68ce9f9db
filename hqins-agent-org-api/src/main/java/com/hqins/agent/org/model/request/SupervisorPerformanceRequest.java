package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@ApiModel("督导业绩查询条件")
@Data
@SuperBuilder
public class SupervisorPerformanceRequest {

    @ApiModelProperty("合伙人机构集合")
    private List<String> instCodeList;

    @ApiModelProperty("团队code")
    private String teamCode;

    @ApiModelProperty("查询类型:当月:month;当年:year;时间范围:time")
    private String type;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("统计口径:按承保:sign,按生效:eff")
    private String accType;

    @ApiModelProperty("汇总口径:机构:inst,团队:team,人员:agent")
    private String groupType;




}
