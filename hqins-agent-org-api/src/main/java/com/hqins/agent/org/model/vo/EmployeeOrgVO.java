package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lijian
 * @Date: 2022/12/6 6:22 上午
 */
@Data
public class EmployeeOrgVO implements Serializable {

    @ApiModelProperty("员工代码")
    private String employeeCode;

    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("当前职级类型")
    private String serialCode;

    @ApiModelProperty("当前职级名称")
    private String serialName;

    @ApiModelProperty("当前职级")
    private String rankCode;

    @ApiModelProperty("当前职级名称")
    private String rankName;

    @ApiModelProperty("归属团队代码")
    private String teamCode;

    @ApiModelProperty("归属团队名称")
    private String teamName;

    @ApiModelProperty("团队Leader代码")
    private String teamLeaderCode;

    @ApiModelProperty("归属Leader名称")
    private String teamLeaderName;

    @ApiModelProperty("所属营业组编码")
    private String businessTeamCode;

    @ApiModelProperty("所属营业组名称")
    private String businessTeamName;

    @ApiModelProperty("所属营业组Leader编码")
    private String businessTeamLeaderCode;

    @ApiModelProperty("所属营业组Leader名称")
    private String businessTeamLeaderName;

    @ApiModelProperty("所属营业部编码")
    private String businessDeptCode;

    @ApiModelProperty("所属营业部名称")
    private String businessDeptName;

    @ApiModelProperty("所属营业部Leader编码")
    private String businessDeptLeaderCode;

    @ApiModelProperty("所属营业部Leader名称")
    private String businessDeptLeaderName;

    @ApiModelProperty("所属营业区编码")
    private String businessAreaCode;

    @ApiModelProperty("所属营业区名称")
    private String businessAreaName;

    @ApiModelProperty("所属营业区Leader编码")
    private String businessAreaLeaderCode;

    @ApiModelProperty("所属营业区Leader名称")
    private String businessAreaLeaderName;

    @ApiModelProperty("销售员状态")
    private String status;

    @ApiModelProperty("内外勤标识")
    private String isInsideFlag;

    @ApiModelProperty("是否为虚拟代理人")
    private String isVirtualEmp;

    @ApiModelProperty("所属机构编码")
    private String orgCode;

    @ApiModelProperty("所属机构名称")
    private String orgName;
}
