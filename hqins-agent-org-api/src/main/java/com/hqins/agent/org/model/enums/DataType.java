package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/5/21
 * @Description 组织机构类型
 */
@ApiModel("组织机构类型")
public enum DataType {
    /**
     * 授权资源的类型 PARTNER:合伙人、CHANNEL:渠道商、PARTNER_ORG:合伙人销售机构、CHANNEL_ORG:渠道商销售机构"
     *
     * Insurance
     */
    PARTNER("合伙人"),
    CHANNEL("渠道商"),
    PARTNER_ORG("合伙人销售机构"),
    CHANNEL_ORG("渠道商销售机构");

    private String label;

    DataType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static DataType get(String name) {
        for (DataType value : DataType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
