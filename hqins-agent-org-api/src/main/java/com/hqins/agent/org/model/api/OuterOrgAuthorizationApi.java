package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.request.OuterOrgRequest;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.common.base.annotations.CollectionElement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = "hqins-agent-org")
public interface OuterOrgAuthorizationApi {

    /**
     * 根据orgCode获取销售员信息
     *
     * @param request 申请
     * @return 销售员授权信息
     */
    @PostMapping("/agent-org/authorization/org")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> getEmployeesByOrgCode(@RequestBody OuterOrgRequest request);

}
