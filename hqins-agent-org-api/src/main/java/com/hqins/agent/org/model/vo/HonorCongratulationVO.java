package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/2
 */
@ApiModel("荣誉贺报表")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HonorCongratulationVO {

    @ApiModelProperty(value = "贺报ID")
    private String honorId;

    @ApiModelProperty(value = "荣誉类型")
    private String honorType;

    @ApiModelProperty(value = "荣誉奖项")
    private String honorCode;

    @ApiModelProperty(value = "荣誉奖项名称")
    private String honorName;

    @ApiModelProperty(value = "代理人工号")
    private String agentCode;

    @ApiModelProperty(value = "代理人名称")
    private String agentName;

    @ApiModelProperty(value = "荣誉表彰")
    private String honorsAwards;

    @ApiModelProperty(value = "荣誉表彰达成时间")
    private String honorsAwardsTime;

}
