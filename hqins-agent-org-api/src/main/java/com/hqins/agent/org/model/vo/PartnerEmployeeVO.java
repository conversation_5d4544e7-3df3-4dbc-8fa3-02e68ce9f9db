package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.enums.PartnerEmployeeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> <PERSON>
 * @date 2021/5/8
 * @Description
 */
@ApiModel("合伙人销售员对象")
@Data
public class PartnerEmployeeVO {

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ApiModelProperty("归属销售机构代码")
    private String orgCode;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("归属营销团队代码")
    private String teamCode;

    @ApiModelProperty("归属营销团队名称")
    private String teamName;

    @ApiModelProperty("推荐人名称")
    private String references;

    @ApiModelProperty("入职日期")
    private LocalDateTime entryTime;

    @ApiModelProperty("离职日期")
    private LocalDateTime quitTime;

    @ApiModelProperty("销售员状态")
    private PartnerEmployeeStatus status;

    @ApiModelProperty("人员状态标题")
    private String statusTitle;

}
