package com.hqins.agent.org.model.enums;

import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

@ApiModel("证件类型枚举")
@Getter
public enum IdTypeMapping {
    //格式   枚举名("枚举名","和核心对应的编码")
    ID(IdType.ID, "0", "0", "0"),
    ID_TEMPORARY(IdType.ID_TEMPORARY, "C", "0", "7"),
    HOUSEHOLD_REGISTER(IdType.HOUSEHOLD_REGISTER, "4", "4", "1"),
    BIRTH_CERTIFICATE(IdType.BIRTH_CERTIFICATE, "7", "7", "11"),
    ID_TAIWAN(IdType.ID_TAIWAN, "H", "B", "99"),
    ID_HK_MACAO(IdType.ID_HK_MACAO, "G", "G", "10"),
    HK_MACAO_CN_PASS(IdType.HK_MACAO_CN_PASS, "B", "B", "5"),
    CH_HK_MACAO_TAIWAN_PASS(IdType.CH_HK_MACAO_TAIWAN_PASS, "F", "F", "6"),
    TAIWAN_CN_PASS(IdType.TAIWAN_CN_PASS, "E", "H", "99"),
    POLICE_CERTIFICATE(IdType.POLICE_CERTIFICATE, "null", "8", "9"),
    SOLDIER_CERTIFICATE(IdType.SOLDIER_CERTIFICATE, "A", "A", "4"),
    OFFICER_CERTIFICATE(IdType.OFFICER_CERTIFICATE, "2", "2", "3"),
    ID_FOREIGNER(IdType.ID_FOREIGNER, "I", "I", "8"),
    FOREIGNER_PASSPORT(IdType.FOREIGNER_PASSPORT, "1", "1", "2"),
    DRIVER_LICENSE(IdType.DRIVER_LICENSE, "3", "3", "99"),
    EMPLOYEE_CARD(IdType.EMPLOYEE_CARD, "6", "6", "99"),
    STUDENT_CARD(IdType.STUDENT_CARD, "5", "5", "99"),
    CHINA_PASSPORT(IdType.CHINA_PASSPORT, "J", "J", "99"),
    ;


    private IdType idType;
    private String hqCore;//核心
    private String slIdType;//双录
    private String fundCore;//资金

    IdTypeMapping(IdType idType, String hqCore, String slIdType, String fundCore) {
        this.idType = idType;
        this.hqCore = hqCore;
        this.slIdType = slIdType;
        this.fundCore = fundCore;
    }


    public static String getHqCore(String idType) {
        for (IdTypeMapping value : IdTypeMapping.values()) {
            if (value.idType.name().equals(idType)) {
                return value.getHqCore();
            }
        }
        return "";
    }

    public static String getHqCore(IdType idType) {
        for (IdTypeMapping value : IdTypeMapping.values()) {
            if (value.idType.equals(idType)) {
                return value.getHqCore();
            }
        }
        return null;
    }

    public static String getSlIdType(String idType) {
        for (IdTypeMapping value : IdTypeMapping.values()) {
            if (value.idType.name().equals(idType)) {
                return value.getSlIdType();
            }
        }
        return null;
    }

    public static String getFundCore(String fundCore) {
        for (IdTypeMapping value : IdTypeMapping.values()) {
            if (value.idType.name().equals(fundCore)) {
                return value.getFundCore();
            }
        }
        return null;
    }

    //根据核心编码查
    public static IdTypeMapping getIdType(String coreId) {
        for (IdTypeMapping value : IdTypeMapping.values()) {
            if (value.hqCore.equals(coreId)) {
                return value;
            }
        }
        return null;
    }
}
