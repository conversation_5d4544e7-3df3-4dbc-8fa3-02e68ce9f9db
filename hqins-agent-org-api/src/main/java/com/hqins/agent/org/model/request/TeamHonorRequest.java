package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;


@ApiModel("团队荣誉查询条件")
@Data
@SuperBuilder
public class TeamHonorRequest {

    @ApiModelProperty("团队code")
    private String saleTeamCode;

    @ApiModelProperty("人员代码")
    private String empCode;

    @ApiModelProperty("所属机构编码")
    private Integer honorYear;


}
