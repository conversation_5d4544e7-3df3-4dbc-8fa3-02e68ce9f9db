package com.hqins.agent.org.model.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 机构VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InstResultVo {

    /**
     * 机构ID
     */
    private String instId;

    /**
     * 机构名称
     */
    private String instName;

    /**
     * 机构代码
     */
    private String instCode;

    /**
     * 机构所属省
     */
    private String instProvince;

    /**
     * 机构所属省名称
     */
    private String instProvincename;

    /**
     * 机构所属市
     */
    private String instCity;

    /**
     * 机构所属市名称
     */
    private String instCityname;

    /**
     * 渠道ID（ORGTYPE为03时有效数值同INST_ID）
     */
    private String channelorgcode;

    /**
     * 渠道名称
     */
    private String channelorgname;

    /**
     * 类别：00:保险公司组织机构，01：中介组织机构，03：分销商户（渠道）
     */
    private String orgtype;

    /**
     * 机构简称
     */
    private String instSmpName;

    /**
     * 机构英文简称
     */
    private String instSmpEnName;

    /**
     * 上级机构ID
     */
    private String parentInstId;

    /**
     * 上级机构代码
     */
    private String parentInstCode;

    /**
     * 上级机构名称
     */
    private String parentInstName;

    /**
     * 机构所属公司ID
     */
    private String companyid;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮编
     */
    private String zip;

    /**
     * 电话
     */
    private String tel;

    /**
     * 传真
     */
    private String fax;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者ID
     */
    private String createuser;

    /**
     * 修改时间
     */
    private LocalDateTime modifytime;

    /**
     * 修改者ID
     */
    private String modifyuser;

    /**
     * 创建人名字
     */
    private String createname;

    /**
     * 修改人名字
     */
    private String modifyname;

    /**
     * 机构是否锁定（false不锁定，true锁定）
     */
    private String islocked;

    /**
     * 机构启用日期
     */
    private LocalDate startDate;

    /**
     * 机构冻结日期
     */
    private LocalDate endDate;

    /**
     * 机构停用/停用原因
     */
    private String description;

    /**
     * 机构所属区域
     */
    private String instRegion;

    /**
     * 机构所属区域名称
     */
    private String instRegionname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮箱域名
     */
    private String mailboxdns;

    /**
     * 渠道所属平台管理机构CODE
     */
    private String manageorgcode;

    /**
     * 渠道所属平台管理机构名称
     */
    private String manageorgname;

    /**
     * 机构树路径
     */
    private String instPath;

    /**
     * 机构级别
     */
    private String instLevel;

    /**
     * 是否根机构
     */
    private String isHead;

    /**
     * 租户代码
     */
    private String partnercode;

    /**
     * 机构中文名称
     */
    private String instEnName;

    /**
     * 负责人
     */
    private String leaderName;

    /**
     * 负责人电话
     */
    private String leaderPhone;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 纳税人识别号
     */
    private String taxpayeridno;

    /**
     * 企业注册地址
     */
    private String enterpriseAddress;

    /**
     * 企业电话
     */
    private String enterpriseTel;

    /**
     * 账户信息-银行开户行
     */
    private String bankname;

    /**
     * 账户信息-银行帐号
     */
    private String bankaccountNo;

    /**
     * 账户信息-银行户名
     */
    private String bankaccountName;

    /**
     * 发票信息-开票开户行
     */
    private String invoicebankname;

    /**
     * 发票类型 01 增值税专票, 02 增值税普票
     */
    private String invoiceType;

    /**
     * 增值税率
     */
    private BigDecimal taxRate;

    /**
     * 成本中心（横琴）
     */
    private String costCenter;

    /**
     * 机构级别（A、B、C、D）
     */
    private String instClass;

    /**
     * 是否虚拟
     */
    private String isvirtual;

    /**
     * 组织机构评级：a类 b类 c类 d类
     */
    private String instRank;

    /**
     * 银行网点代码
     */
    private String bankoutletscode;

    /**
     * 机构标识 1-银行 2-中介 3-科技公司
     */
    private String instTypeFlag;

    /**
     * 机构代理级别 1-专业代理 2-兼业代理 3-经纪公司 4-相互代理
     */
    private String instAgencyLevel;

    /**
     * 机构代理类型 00-全国代理 01-省级代理 02-市级代理 3-县级代理 04-个人
     */
    private String instAgencyClass;

    /**
     * 中介许可证号  机构代理级别为1-专业代理、3-经纪公司时，限长15位；机构代理级别为2-兼业代理是，限长18位
     */
    private String instLicenseCode;

    /**
     * 获得中介许可证日期
     */
    private LocalDate licenceStartDate;

    /**
     * 中介许可证到期日
     */
    private LocalDate licenceEndDate;

    /**
     * 停业标志 Y-是，N-否
     */
    private String closureFlag;

    /**
     * 是否核算机构 Y-是，N-否
     */
    private String accountingStatus;

    /**
     * 机构成立日期
     */
    private LocalDate instEstablishDate;

    /**
     * 业绩归属
     */
    private String branchtype;

    /**
     * 统一社会信用代码
     */
    private String uniformSocialCreditCode;

    /**
     * 是否可以销售，Y-是，N-否
     */
    private String canSold;

    /**
     * 监管系统机构名称
     */
    private String instLicenseName;

    /**
     * 合作起始日期
     */
    private LocalDate cooperationStarttime;

    /**
     * 合作终止日期
     */
    private LocalDate cooperationEndtime;

    /**
     * 工作流标志
     */
    private String isworkflow;

    /**
     * 机构状态(0:未生效 1:生效 2:失效)
     */
    private String instStatus;

    /**
     * 分销渠道标识 0-否 1-是
     */
    private String distributionchannelflag;

    /**
     * 是否是物理网点：Y-是，N-否
     */
    private String physicalStation;

    /**
     * 中介机构业务许可证中的业务范围。
     */
    private String businessScope;

    /**
     * 中介机构业务许可证中的地域范围。
     */
    private String businessArea;

    /**
     * 中介机构最近2年违法违规情况说明，包括中介机构受到人民银行/银保监会/银保监局/政府执法部门的行政处罚。
     */
    private String illegatRecord;

    /**
     * 填写经营保险代理、兼业代理、经纪业务许可证左上角的红色流水号。对于银行分支机构作为兼业中介机构报送总行对其的授权号。
     */
    private String instBusinesLicenseCode;

    /**
     * 中介机构经营业务许可证名称。
     */
    private String instBusinesLicenseName;

    /**
     * BU编码
     */
    private String bucode;

    /**
     * BU名称
     */
    private String buname;

    /**
     * 考核机构代码
     */
    private String assessCode;

    /**
     * 影像截图
     */
    private String instLicenseJson;
    /**
     * 网点区域
     */
    private String bankscopeofinst;
    /**
     * 网点区域代码
     */
    private String bankscopecode;
    /**
     * 业绩归属标记 1为自营,0为非自营
     */
    private String performance;
    /**
     * 考核机构名称
     */
    private String assessName;
    /**
     * 终端渠道名称
     */
    private String terminalchannelsname;


}
