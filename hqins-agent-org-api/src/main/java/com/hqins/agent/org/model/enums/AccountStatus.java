package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @date 2021/5/25
 * @Description
 */
public enum AccountStatus {

    /**
     * 账号有效、销售员状态正常
     */
    ENABLED("有效"),
    /**
     * 账号失效、销售员状态离职
     */
    DISABLED("失效"),
    /**
     * 账号失效、销售员状态正常
     */
    BLOCKED("冻结"),

    /**
     * 状态为正常、但是未绑定账号的。
     */
    UNBIND("未授权");

    private String label;

    AccountStatus(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static TeamLevel get(String name) {
        for (TeamLevel value : TeamLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
