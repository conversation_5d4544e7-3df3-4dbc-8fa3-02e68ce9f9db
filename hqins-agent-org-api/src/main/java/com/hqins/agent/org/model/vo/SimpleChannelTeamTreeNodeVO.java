package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
@ApiModel("渠道商团队对象")
@Data
public class SimpleChannelTeamTreeNodeVO {

    @ApiModelProperty("所有下级团队")
    private List<SimpleChannelTeamTreeNodeVO> children;

    @ApiModelProperty("营销团队id")
    private Long id;

    @ApiModelProperty("营销团队代码")
    private String saleteamcode;

    @ApiModelProperty("营销团队名称")
    private String saleteamname;

    @ApiModelProperty("团队级别:01,02,03")
    private String teamlevel;


}
