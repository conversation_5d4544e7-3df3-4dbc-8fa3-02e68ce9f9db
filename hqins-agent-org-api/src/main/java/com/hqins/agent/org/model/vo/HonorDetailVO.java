package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/17
 */
@ApiModel("荣誉详情信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HonorDetailVO {

    @ApiModelProperty("琴星精英会")
    private QinxingEliteDetailVO qinxingEliteDetail;

    @ApiModelProperty("琴韵博识轩")
    private QinyunScholarDetailVO qinyunScholardDetail;

    @ApiModelProperty("琴辉荣耀堂")
    private GloryHallDetailVO gloryHallderDetail;

    @ApiModelProperty("百万圆桌")
    private MDRTDetailVO millionTabledDetail;

    @ApiModelProperty("琴海吉尼斯")
    private GuinnessDetailVO guinnessDetail;

}
