package com.hqins.agent.org.model.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.fastjson.annotation.JSONField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
* <p>
* 承保业绩清单返回结果
* </p>
*
* <AUTHOR>
* @since 2022-11-30
*/
@ApiModel(value="全渠道业绩清单返回结果", description="全渠道业绩清单返回结果")
@Data
public class HoloDaDetailUwVo  implements Serializable {


    @ApiModelProperty(value = "销售BU",example = "销售BU")
    public String buName;

    @ApiModelProperty(value = "合伙人名称",example = "合伙人名称")
    public String companyName;

    @ApiModelProperty(value = "合伙人机构代码",example = "合伙人机构代码")
    public String performanceGroup;

    @ApiModelProperty(value = "合伙人机构名称",example = "合伙人机构名称")
    public String performanceGroupName;

    @ApiModelProperty(value = "二级管理机构编码",example = "二级管理机构编码")
    public String manageCom2v;

    @ApiModelProperty(value = "二级管理机构名称",example = "二级管理机构名称")
    public String manageComName2v;

    @ApiModelProperty(value = "三级管理机构编码",example = "三级管理机构编码")
    public String manageCom3v;

    @ApiModelProperty(value = "三级管理机构名称",example = "三级管理机构名称")
    public String manageComName3v;

    @ApiModelProperty(value = "四级管理机构编码",example = "四级管理机构编码")
    public String manageCom4v;

    @ApiModelProperty(value = "四级管理机构名称",example = "四级管理机构名称")
    public String manageComName4v;

    @ApiModelProperty(value = "五级管理机构编码",example = "五级管理机构编码")
    public String manageCom5v;

    @ApiModelProperty(value = "五级管理机构名称",example = "五级管理机构名称")
    public String manageComName5v;

    @ApiModelProperty(value = "中介机构类别",example = "中介机构类别")
    public String agentComType;

    @ApiModelProperty(value = "代理机构编码",example = "代理机构编码")
    public String agentCom;

    @ApiModelProperty(value = "代理机构名称",example = "代理机构名称")
    public String agentComName;

    @ApiModelProperty(value = "一级代理机构编码",example = "一级代理机构编码")
    public String agentCom1v;

    @ApiModelProperty(value = "一级经代机构名称",example = "一级经代机构名称")
    public String agentComName1v;

    @ApiModelProperty(value = "二级代理机构编码",example = "二级代理机构编码")
    public String agentCom2v;

    @ApiModelProperty(value = "二级经代机构名称",example = "二级经代机构名称")
    public String agentComName2v;

    @ApiModelProperty(value = "三级代理机构编码",example = "三级代理机构编码")
    public String agentCom3v;

    @ApiModelProperty(value = "三级经代机构名称",example = "三级经代机构名称")
    public String agentComName3v;

    @ApiModelProperty(value = "四级代理机构编码",example = "四级代理机构编码")
    public String agentCom4v;

    @ApiModelProperty(value = "四级代理机构名称",example = "四级代理机构名称")
    public String agentComName4v;

    @ApiModelProperty(value = "渠道商编码",example = "渠道商编码")
    public String orgComCode;

    @ApiModelProperty(value = "渠道商名称",example = "渠道商名称")
    public String orgComName;

    @ApiModelProperty(value = "出单经代机构编码",example = "出单经代机构编码")
    public String orgCode;

    @ApiModelProperty(value = "出单经代机构名称",example = "出单经代机构名称")
    public String orgName;

    @ApiModelProperty(value = "一级经代机构编码",example = "一级经代机构编码")
    public String orgcode1v;

    @ApiModelProperty(value = "一级经代机构名称",example = "一级经代机构名称")
    public String orgname1v;

    @ApiModelProperty(value = "二级经代机构编码",example = "二级经代机构编码")
    public String orgcode2v;

    @ApiModelProperty(value = "二级经代机构名称",example = "二级经代机构名称")
    public String orgname2v;

    @ApiModelProperty(value = "三级经代机构编码",example = "三级经代机构编码")
    public String orgcode3v;

    @ApiModelProperty(value = "三级经代机构名称",example = "三级经代机构名称")
    public String orgname3v;

    @ApiModelProperty(value = "四级经代机构编码",example = "四级经代机构编码")
    public String orgcode4v;

    @ApiModelProperty(value = "四级经代机构名称",example = "四级经代机构名称")
    public String orgname4v;

    @ApiModelProperty(value = "险种代码",example = "险种代码")
    public String riskCode;

    @ApiModelProperty(value = "险种名称",example = "险种名称")
    public String riskName;

    @ApiModelProperty(value = "组合产品代码",example = "组合产品代码")
    public String productComCode;

    @ApiModelProperty(value = "组合产品名称",example = "组合产品名称")
    public String productComName;

    @ApiModelProperty(value = "长短险",example = "长短险")
    public String riskPeriodName;

    @ApiModelProperty(value = "组合产品代码",example = "组合产品代码")
    public String riskKind1Name;

    @ApiModelProperty(value = "组合产品名称",example = "组合产品名称")
    public String riskKind2Name;

    @ApiModelProperty(value = "产品设计类型",example = "产品设计类型")
    public String riskType;

    @ApiModelProperty(value = "主险附加险",example = "主险附加险")
    public String subRiskFlag;

    @ApiModelProperty(value = "主险险种代码",example = "主险险种代码")
    public String prtInsCode;

    @ApiModelProperty(value = "投保单号",example = "投保单号")
    public String prtNo;

    @ApiModelProperty(value = "团体保单号",example = "团体保单号")
    public String grpContNo;

    @ApiModelProperty(value = "保单号",example = "保单号")
    public String contNo;

    @ApiModelProperty(value = "投保人名称",example = "投保人名称")
    public String appntName;

    @ApiModelProperty(value = "投保人客户号",example = "投保人客户号")
    public String appntNo;

    @ApiModelProperty(value = "投保人年龄",example = "投保人年龄")
    public String appntApplyAge;

    @ApiModelProperty(value = "投保人性别",example = "投保人性别")
    public String appntSex;

    @ApiModelProperty(value = "被保险人姓名",example = "被保险人姓名")
    public String insuredName;

    @ApiModelProperty(value = "被保险人客户号",example = "被保险人客户号")
    public String insuredNo;

    @ApiModelProperty(value = "被保险人年龄",example = "被保险人年龄")
    public String insuredApplyAge;

    @ApiModelProperty(value = "被保险人年龄",example = "被保险人年龄")
    public String insuredSex;

    @ApiModelProperty(value = "保险期限",example = "保险期限")
    public String insPeriod;

    @ApiModelProperty(value = "缴费期",example = "缴费期")
    public String payPeriod;

    @ApiModelProperty(value = "缴费频率",example = "缴费频率")
    public String payIntv;

    @ApiModelProperty(value = "交费期数",example = "交费期数")
    public Integer payInStage;

    @ApiModelProperty(value = "保单年度",example = "保单年度")
    public Integer contYear;

    @ApiModelProperty(value = "保费年度",example = "保费年度")
    public Integer premYear;

    @ApiModelProperty(value = "保险金额",example = "保险金额")
    public BigDecimal amnt;

    @ApiModelProperty(value = "承保时规模保费",example = "承保时规模保费")
    public BigDecimal signPrem;

    @ApiModelProperty(value = "规模保费",example = "规模保费")
    public BigDecimal prem;

    @ApiModelProperty(value = "提奖",example = "提奖")
    public BigDecimal fyc;

    @ApiModelProperty(value = "基本法标准保费",example = "基本法标准保费")
    public BigDecimal stdPrem_base;

    @ApiModelProperty(value = "标准保费",example = "标准保费")
    public BigDecimal stdPrem;

//    @ApiModelProperty(value = "签单业务员编码",example = "签单业务员编码")
//    public String signAgent;
//
//    @ApiModelProperty(value = "签单业务员姓名",example = "签单业务员姓名")
//    public String signAgentName;
//
//    @ApiModelProperty(value = "柜员编码",example = "柜员编码")
//    public String bankAgent;
//
//    @ApiModelProperty(value = "柜员姓名",example = "柜员姓名")
//    public String bankAgentName;

    @ApiModelProperty(value = "渠道业务员编码",example = "渠道业务员编码")
    public String signAgent;

    @ApiModelProperty(value = "渠道业务员姓名",example = "渠道业务员姓名")
    public String signAgentName;

    @ApiModelProperty(value = "业务员编码",example = "业务员编码")
    public String agentCode;

    @ApiModelProperty(value = "业务员姓名",example = "业务员姓名")
    public String agentName;

    @ApiModelProperty(value = "业务员执业证编码",example = "业务员执业证编码")
    public String agentLicense;

    @ApiModelProperty(value = "业务员职级代码",example = "业务员职级代码")
    public String agentGrade;

    @ApiModelProperty(value = "业务员职级名称",example = "业务员职级名称")
    public String agentGradeName;

    @ApiModelProperty(value = "内外勤标识",example = "内外勤标识")
    public String agtInsideFlag;

    @ApiModelProperty(value = "营业区编码",example = "营业区编码")
    public String upBranchAttr;

    @ApiModelProperty(value = "营业区名称",example = "营业区名称")
    public String upBranchAttrName;

    @ApiModelProperty(value = "营业区主管编码",example = "营业区主管编码")
    public String upBranchManager;

    @ApiModelProperty(value = "营业区主管姓名",example = "营业区主管姓名")
    public String upBranchManagerName;

    @ApiModelProperty(value = "营业部编码",example = "营业部编码")
    public String branchAttr;

    @ApiModelProperty(value = "营业部名称",example = "营业部名称")
    public String branchAttrName;

    @ApiModelProperty(value = "营业部主管编码",example = "营业部主管编码")
    public String branchManager;

    @ApiModelProperty(value = "营业部主管姓名",example = "营业部主管姓名")
    public String branchManagerName;

    @ApiModelProperty(value = "营业组编码",example = "营业组编码")
    public String agentGroup;

    @ApiModelProperty(value = "营业组名称",example = "营业组名称")
    public String agentGroupName;

    @ApiModelProperty(value = "营业组主管编码",example = "营业组主管编码")
    public String agtGrpManager;

    @ApiModelProperty(value = "营业组主管姓名",example = "营业组主管姓名")
    public String agtGrpManagerName;

    @ApiModelProperty(value = "追加业务员编码",example = "追加业务员编码")
    public String agentCodeZp;

    @ApiModelProperty(value = "追加业务员姓名",example = "追加业务员姓名")
    public String agentNameZp;

    @ApiModelProperty(value = "追加营业区编码",example = "追加营业区编码")
    public String upBranchAttrZp;

    @ApiModelProperty(value = "追加营业区名称",example = "追加营业区名称")
    public String upBranchAttrNameZp;

    @ApiModelProperty(value = "追加营业部编码",example = "追加营业部编码")
    public String branchAttrZp;

    @ApiModelProperty(value = "追加营业部名称",example = "追加营业部名称")
    public String branchAttrNameZp;

    @ApiModelProperty(value = "追加营业组编码",example = "追加营业组编码")
    public String agentGroupZp;

    @ApiModelProperty(value = "追加营业组名称",example = "追加营业组名称")
    public String agentGroupNameZp;

    @ApiModelProperty(value = "销售主渠道",example = "销售主渠道")
    public String saleChnlName;

    @ApiModelProperty(value = "销售子渠道",example = "销售子渠道")
    public String sellTypeName;

    @ApiModelProperty(value = "三级渠道",example = "三级渠道")
    public String agentTypeName;

    @ApiModelProperty(value = "交易类型",example = "交易类型")
    public String tradeType;

    @ApiModelProperty(value = "险种状态",example = "险种状态")
    public String contState;

    @ApiModelProperty(value = "入机日期",example = "入机日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date makeDate;

    @ApiModelProperty(value = "投保日期",example = "投保日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date applyDate;

    @ApiModelProperty(value = "投保时间",example = "投保时间")
    public String applyTime;

    @ApiModelProperty(value = "承保日期",example = "承保日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date signDate;

    @ApiModelProperty(value = "承保时间",example = "承保时间")
    public String signTime;

    @ApiModelProperty(value = "生效日期",example = "生效日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date valiDate;

    @ApiModelProperty(value = "保单签收日期",example = "保单签收日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date getPolDate;

    @ApiModelProperty(value = "保单回执回销日期",example = "保单回执回销日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date customGetPolDate;

    @ApiModelProperty(value = "交易日期",example = "交易日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date tradeDate;

    @ApiModelProperty(value = "受理日期",example = "受理日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date inputDate;

    @ApiModelProperty(value = "退保日期",example = "退保日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date ctDate;

    @ApiModelProperty(value = "回访成功日期",example = "回访成功日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date visitDate;

    @ApiModelProperty(value = "犹豫期截止日期",example = "犹豫期截止日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date hesitateEnd;

    @ApiModelProperty(value = "是否过犹豫期",example = "是否过犹豫期")
    public String isHesitate;

    @ApiModelProperty(value = "保险止期",example = "保险止期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date insEndDate;

    @ApiModelProperty(value = "退保金额",example = "退保金额")
    public BigDecimal ctMoney;

    @ApiModelProperty(value = "退保方式",example = "退保方式")
    public String ctmode;

    @ApiModelProperty(value = "责任编码",example = "责任编码")
    public String dutyCode;

    @ApiModelProperty(value = "责任名称",example = "责任名称")
    public String dutyName;

    @ApiModelProperty(value = "业务来源",example = "业务来源")
    public String butype;

    @ApiModelProperty(value = "是否自营",example = "是否自营")
    public String zyFlag;

    @ApiModelProperty(value = "是否深合区",example = "是否深合区")
    public String shqFlag;

    @ApiModelProperty(value = "是否境内非居民",example = "是否境内非居民")
    public String jnfjmFlag;

    @ApiModelProperty(value = "考核机构",example = "考核机构")
    public String chkComName;

    @ApiModelProperty(value = "是否mga业务",example = "是否mga业务")
    public String mgaFlag;

    @ApiModelProperty(value = "是否股东业务",example = "是否股东业务")
    public String isStkType;

    @ApiModelProperty(value = "交费收据号",example = "交费收据号")
    public String payno;

    @ApiModelProperty(value = "交费方式",example = "交费方式")
    public String paymodename;

    @ApiModelProperty(value = "险种是否自动续保",example = "险种是否自动续保")
    public String isRnewflag;

    @ApiModelProperty(value = "续期分配日期",example = "续期分配日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date xqfpDate;

    @ApiModelProperty(value = "续期分配时间",example = "续期分配时间")
    public String xqfpTime;

    @ApiModelProperty(value = "保单服务状态",example = "保单服务状态")
    public String contSeverState;

    @ApiModelProperty(value = "缴费止期",example = "缴费止期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date payenddate;

    @ApiModelProperty(value = "应收日期",example = "应收日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date lastpaytodate;

    @ApiModelProperty(value = "交费日期",example = "交费日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date paydate;

    @ApiModelProperty(value = "核销日期",example = "核销日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date confdate;

    @ApiModelProperty(value = "宽限期截止日",example = "宽限期截止日")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date kxqEnddate;

    @ApiModelProperty(value = "追加保费生效日期",example = "追加保费生效日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date paymodename1;

    @ApiModelProperty(value = "追加保费保单年度",example = "追加保费保单年度")
    public Long paymodename2;

    @ApiModelProperty(value = "追加规模保费",example = "追加规模保费")
    public BigDecimal paymodename3;

    @ApiModelProperty(value = "追加标准保费",example = "追加标准保费")
    public BigDecimal paymodename4;

    @ApiModelProperty(value = "费用类型",example = "费用类型")
    public String expenseTypeName;

    @ApiModelProperty(value = "费用项",example = "费用项")
    public String expenseItem;

    @ApiModelProperty(value = "手续费费率",example = "手续费费率")
    public BigDecimal costRate;

    @ApiModelProperty(value = "手续费金额",example = "手续费金额")
    public BigDecimal costamount;

    @ApiModelProperty(value = "是否可结算",example = "是否可结算")
    public String settleableflag;

    @ApiModelProperty(value = "应付月份",example = "应付月份")
    public String payableMonth;

    @ApiModelProperty(value = "协议编码",example = "协议编码")
    public String agreementCode;

    @ApiModelProperty(value = "协议编码",example = "协议编码")
    public String agreementName;

    @ApiModelProperty(value = "协议合作开始日期",example = "协议合作开始日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date startDate;

    @ApiModelProperty(value = "协议合作截止日期",example = "协议合作截止日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date endDate;

    @ApiModelProperty(value = "险种合作开始日期",example = "险种合作开始日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date riskStartDate;

    @ApiModelProperty(value = "险种合作截止日期",example = "险种合作截止日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date riskEndDate;

    @ApiModelProperty(value = "实收核销日期",example = "实收核销日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date payconfdate;

    @ApiModelProperty(value = "当前代理人(业务员代码)",example = "当前代理人(业务员代码)")
    public String agentCodeNew;

    @ApiModelProperty(value = "当前代理人(业务员姓名)",example = "当前代理人(业务员姓名)")
    public String agentNameNew;

    @ApiModelProperty(value = "险种终止日期",example = "险种终止日期")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    public Date terminateDate;

    @ApiModelProperty(value = "保单险种号",example = "保单险种号")
    public String polNo;

    @ApiModelProperty(value = "险种状态",example = "险种状态")
    public String contStatus;

    @ApiModelProperty(value = "新增被保险人定期结算批次号",example = "新增被保险人定期结算批次号")
    public String fixedPayNoNi;

    @ApiModelProperty(value = "减少被保险人定期结算批次号",example = "减少被保险人定期结算批次号")
    public String fixedPayNoZt;

    @ApiModelProperty(value = "续保当期是否豁免",example = "续保当期是否豁免")
    public String rnewhmflag;

    @ApiModelProperty(value = "是否共保业务",example = "是否共保业务")
    public String coinsFlag;

    @ApiModelProperty(value = "是否家庭单",example = "是否家庭单")
    public String famContFlag;

    @ApiModelProperty(value = "团单增减人保全受理号",example = "团单增减人保全受理号")
    public String eDorAcceptNo;

    @ApiModelProperty(value = "渠道业务员执业证编码",example = "渠道业务员执业证编码")
    private String signAgentLic;
}
