package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupervisorMarkVO implements Serializable {

    @ApiModelProperty("自互保件数")
    private Integer  selfPolicyCount;

    @ApiModelProperty("13个月继续率未达标人数")
    private Integer  disCompleteCr13Count;

    @ApiModelProperty("犹豫期退保件数")
    private Integer  ctPolicyCount;

    @ApiModelProperty("自互保件保单列表")
    private List<MarkDetailVO> selfPolicyList;

    @ApiModelProperty("13个月继续率未达标列表")
    private List<MarkDetailVO> cr13List;

    @ApiModelProperty("犹豫期退保保单列表")
    private List<MarkDetailVO> ctPolicyList;




}
