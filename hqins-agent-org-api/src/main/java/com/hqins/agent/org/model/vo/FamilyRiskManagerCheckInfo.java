package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/7 10:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FamilyRiskManagerCheckInfo {

    @ApiModelProperty("基本法Id")
    private String basicLawId;

    @ApiModelProperty("基本法类型")
    private String basicLawType;

    @ApiModelProperty("基本法名称")
    private String basicLawName;

    @ApiModelProperty("机构代码")
    private String  instCode;

    @ApiModelProperty("机构名称")
    private String  instName;

    @ApiModelProperty("团队代码")
    private String  teamCode;

    @ApiModelProperty("团队名称")
    private String  teamName;

    @ApiModelProperty("代理人代码")
    private String  agentCode;

    @ApiModelProperty("代理人名称")
    private String  agentName;

    @ApiModelProperty(value = "职级代码")
    private String rankCode;

    @ApiModelProperty(value = "职级名称")
    private String rankName;

    @ApiModelProperty(value = "是否连续9个月挂0")
    private String  consecutiveZeroNineMonths;

    @ApiModelProperty(value = "FYC维持考核标准")
    private String maintenanceMonthlyFyc;

    @ApiModelProperty(value = "FYC实际达成")
    private String keepActualStp;

    @ApiModelProperty(value = "距离达标差距")
    private String distanceGap;

    @ApiModelProperty(value = "考核初算结果")
    private String checkSettleResult;

    @ApiModelProperty(value = "考核期")
    private String checkMonth;
}
