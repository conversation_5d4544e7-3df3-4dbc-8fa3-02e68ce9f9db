package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.Treeable;
import com.hqins.agent.org.model.enums.TeamLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13
 * @Description
 */
@ApiModel("团队树节点")
@Data
public class SimpleTeamTreeNodeVO implements Treeable<SimpleTeamTreeNodeVO> {

    @ApiModelProperty("所有下级团队")
    private List<SimpleTeamTreeNodeVO> children;

    @ApiModelProperty("营销团队代码")
    private String code;

    @ApiModelProperty("上级营销团队代码")
    private String parentCode;

    @ApiModelProperty("营销团队名称")
    private String name;

    @ApiModelProperty("团队级别")
    private TeamLevel level;
}
