package com.hqins.agent.org.model.request;

import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel("机构配置对象")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonalizationSaveByFeatureRequest implements Serializable {



    @ApiModelProperty("选中的菜单id")
    @NotNull(message = "选中的菜单id不能为空")
    private Long customMenuId;

    @ApiModelProperty("机构列表")
    @NotNull(message = "机构列表")
    private List<OrgDataByPersonalizationSaveRequest> orgByReq;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrgDataByPersonalizationSaveRequest implements Serializable {
        @ApiModelProperty("顶层机构编码，依赖orgType")
        @NotBlank(message = "顶层机构编码不能为空")
        private String code;

        @ApiModelProperty("组织机构类型 PARTNER:合伙人、CHANNEL:渠道商")
        @NotNull(message = "组织机构类型不能为空")
        private AgentOrgType orgType;

    }

}

