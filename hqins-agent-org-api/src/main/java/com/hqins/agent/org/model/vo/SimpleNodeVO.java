package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("组织机构简化对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleNodeVO implements Serializable {
    @ApiModelProperty("代码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("")
    private String type;

}
