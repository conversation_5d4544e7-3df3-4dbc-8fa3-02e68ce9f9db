package com.hqins.agent.org.model.enums;

import lombok.Getter;

/**
 * app人员状态
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 16:28
 */
@Getter
public enum AppEmployeeStatusEnum {

    UNDER_REVIEW("00","审核中"),
    STAFF_VALID("01","人员有效"),
    STAFF_INVALID("02","人员失效"),
    STAFF_TEMPORARILY_STORED("03","人员暂存"),
    STAFF_RESIGNED("04","人员离职"),
    PENDING_REPORT("05","待报备"),
    REVIEW_FAILED("06","审核失败"),
    PENDING_INTERVIEW("11","待面谈"),
    FIRST_INTERVIEW_PENDING("12","待一面"),
    FIRST_INTERVIEW_FAILED("13","一面未通过"),
    PENDING_SECOND_INTERVIEW("14","待二面"),
    SECOND_INTERVIEW_FAILED("15","二面未通过"),
    OFFER_PENDING_CONFIRMATION("16","offer待确认"),
    PENDING_PRELIMINARY_REVIEW("17","待初审"),
    PRELIMINARY_REVIEW_TERMINATED("18","初审终止"),
    REVIEW_TERMINATED("19","审核终止"),
    ;

    private final String code;

    private final String label;

    AppEmployeeStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

}
