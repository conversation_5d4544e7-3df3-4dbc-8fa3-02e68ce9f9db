package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.vo.DataAccessVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("数据权限保存请求")
@Data
public class DataAccessSaveRequest {

    @ApiModelProperty("角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    @ApiModelProperty("已经选中的合伙人权限列表")
    private List<DataAccessVO> partners;

    @ApiModelProperty("已经选中的渠道商权限列表")
    private List<DataAccessVO> channels;

}
