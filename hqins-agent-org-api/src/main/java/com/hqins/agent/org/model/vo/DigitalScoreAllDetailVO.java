package com.hqins.agent.org.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 09:25
 */
@Data
@ApiModel(value = "IFP积分明细返回对象",description = "IFP积分明细返回对象")
public class DigitalScoreAllDetailVO {

    @ApiModelProperty(value = "代理人工号")
    private String agentCode;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "团队名称")
    private String teamName;

    @ApiModelProperty(value = "团队编码")
    private String teamCode;

    @ApiModelProperty(value = "积分")
    private Integer score;

    @ApiModelProperty(value = "明细数据集合")
    private List<DetailDataVO> detailDataList;

    @Data
    @ApiModel(value = "明细数据",description = "明细数据")
    public static class DetailDataVO{

        @ApiModelProperty(value = "类型")
        private String actionType;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty(value = "行为完成时间")
        private LocalDateTime actionTime;

        @ApiModelProperty(value = "是否可记分")
        private String recordPointsFlag;
    }
}
