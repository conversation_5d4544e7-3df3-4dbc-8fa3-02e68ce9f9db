package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@ApiModel("团队业绩查询条件")
@Data
@SuperBuilder
public class TeamPerformanceRequest {

    @ApiModelProperty("团队code")
    private String saleTeamCode;

    @ApiModelProperty("查询类型:team 按团队;personal:按个人;product:按产品;rankSeqCode:按职级")
    private String type;

    @ApiModelProperty("人员code")
    private String agentCode;

    @ApiModelProperty("产品code")
    private String riskCode;

    @ApiModelProperty("职级序列Code")
    private String rankSeqCode;

    @ApiModelProperty("业绩月(按团队查询必传)")
    private String performanceMonth;

    @ApiModelProperty("缴费期间")
    private String paymentYears;

    @ApiModelProperty("开始日期")
    private Date startDate;

    @ApiModelProperty("结束日期")
    private Date endDate;

    @ApiModelProperty("保单id数据集合")
    private List<String> policyIdList;

    @ApiModelProperty("当日保单号数据集合")
    private List<String> currentDayPolicyNoList;


}
