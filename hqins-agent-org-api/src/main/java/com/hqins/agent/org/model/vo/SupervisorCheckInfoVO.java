package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupervisorCheckInfoVO implements Serializable {

    @ApiModelProperty("基本法考核信息")
    List<BasicLawInfoVO> basicLawInfoVOList;

}
