package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.common.base.enums.Gender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/3 17:59
 */
@ApiModel("督导账号对象")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SupervisorEmployeeVO implements Serializable {

    @ApiModelProperty("人员ID")
    private Long id;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("人员代码")
    private String employeeCode;

    @ApiModelProperty("人员类型 总部,机构,团队,普通 ")
    private SupervisorType roleType;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    private Gender gender;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty("销售员手机号")
    private String mobile;

    @ApiModelProperty("合伙人组织编码")
    private String topCode;

    @ApiModelProperty("合伙人组织名称")
    private String topCodeName;

    @ApiModelProperty("所属机构名称")
    private String orgName;

    @ApiModelProperty("所属机构编码")
    private String orgCode;

    @ApiModelProperty("人员工作状态:SERVING-在职；LEAVING-已离职")
    private String status;

    @ApiModelProperty("配置能切换的顶级机构")
    private List<String> topList;

    @ApiModelProperty("配置的二级机构")
    private List<String> orgList;

    @ApiModelProperty("团队编码")
    private String teamCode;

    @ApiModelProperty("团队名称")
    private String teamName;

    @ApiModelProperty("团队等级")
    private String teamLevel;

    @ApiModelProperty("职位")
    private String position;

}
