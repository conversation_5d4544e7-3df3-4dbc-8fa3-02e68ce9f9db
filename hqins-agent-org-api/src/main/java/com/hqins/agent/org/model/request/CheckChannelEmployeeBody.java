package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: lijian
 * @Date: 2023/9/18 10:36 下午
 */
@ApiModel("渠道商校验实体")
@Data
public class CheckChannelEmployeeBody {

    @ApiModelProperty("代理人姓名")
    private String employeeName;

    @ApiModelProperty("代理人所属机构")
    private String orgCode;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号")
    private String idNo;

    @ApiModelProperty("代理人执业证书编号")
    private String licenseNo;

    //执业证号状态
    @ApiModelProperty("执业证号状态")
    private String licenseStatus;

    @ApiModelProperty("有效起始日期")
    private String registerTime;

    @ApiModelProperty("入职机构code")
    private String superOrgan;

    @ApiModelProperty("入职机构名称")
    private String superOrganName;

    @ApiModelProperty("白名单标记")
    private boolean whiteFlag;
}
