package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/5/19
 * @Description
 */
@ApiModel("销售机构分页查询请求")
@Getter
@Setter
@SuperBuilder
public class OrgQueryRequest extends PageQueryRequest {

    @ApiModelProperty("销售机构代码")
    private String code;

    @ApiModelProperty("销售机构名称")
    private String name;

    @ApiModelProperty("父销售机构代码")
    private String parentCode;

    @ApiModelProperty("顶层机构代码")
    private String topCode;

    @ApiModelProperty("顶层机构名称")
    private String topName;

    @ApiModelProperty("销售机构状态 ENABLED-有效 ALL-所有")
    private String status;

}
