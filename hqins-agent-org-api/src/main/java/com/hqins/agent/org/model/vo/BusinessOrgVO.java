package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.BusinessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: lijian
 * @Date: 2022/12/6 6:35 上午
 */
@Data
public class BusinessOrgVO implements Serializable {

    @ApiModelProperty("所属营业机构编码")
    private String businessCode;

    @ApiModelProperty("所属营业机构名称")
    private String businessName;

    @ApiModelProperty("所属营业机构类型")
    private BusinessTypeEnum businessType;

    @ApiModelProperty("是否ifp")
    private Boolean ifpFlag;

    @ApiModelProperty("所属营业下的员工集合")
    private List<EmployeeOrgVO>  employeeOrgVOList;

    @ApiModelProperty("所属营业下的组织集合")
    private List<BusinessOrgVO>  businessOrgVOList;
}
