package com.hqins.agent.org.model.enums;

import lombok.Getter;

/**
 * 基本法子类型枚举.
 *
 * <AUTHOR> MXH
 * @create 2025/3/17 17:44
 */
@Getter
public enum SubLawType {
    FAMILY_RISK_MANAGER("家庭风险管理师"),
    PARTNER("合伙人"),
    ;

    private final String desc;

    SubLawType(String desc) {
        this.desc = desc;
    }

    public String getDesc() { return this.desc; }

    private static SubLawType get(String name) {
        for (SubLawType value : SubLawType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
