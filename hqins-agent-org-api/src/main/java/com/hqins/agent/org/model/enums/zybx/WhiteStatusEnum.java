package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2023/9/11 3:34 下午
 */
public enum WhiteStatusEnum {

    VALID(1, "有效"),
    INVALID(0, "无效");

    private Integer value;
    private String label;

    WhiteStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(Integer value) {
        this.value = value;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
