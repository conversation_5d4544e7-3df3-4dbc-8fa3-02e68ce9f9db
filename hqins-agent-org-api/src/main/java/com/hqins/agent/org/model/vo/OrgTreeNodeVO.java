package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13
 * @Description
 */
@ApiModel("组织机构对象")
@Data
public class OrgTreeNodeVO {

    @ApiModelProperty("所有下级组织机构")
    private List<OrgTreeNodeVO> children;

    @ApiModelProperty("组织机构id")
    private String id;

    @ApiModelProperty("组织机构代码")
    private String code;

    @ApiModelProperty("组织机构名称")
    private String name;

    @ApiModelProperty("组织机构类型:1合伙人渠道商，2销售机构")
    private String type;


}
