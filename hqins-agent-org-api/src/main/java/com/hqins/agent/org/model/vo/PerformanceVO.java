package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PerformanceVO implements Serializable {

    @ApiModelProperty("首年佣金")
    private BigDecimal  fycAmount;

    @ApiModelProperty("月均FYC")
    private BigDecimal  avgMonthFyc;

    @ApiModelProperty("期交保费")
    private BigDecimal  periodPremium;

    @ApiModelProperty("首年保费")
    private BigDecimal  fycPremium;

    @ApiModelProperty("签单客户数量")
    private Integer  appntnoNum;

    @ApiModelProperty("期交标保")
    private BigDecimal  periodDCP;

    @ApiModelProperty("13个月继续率")
    private BigDecimal  cr13;

    @ApiModelProperty("期交保单件数")
    private Integer  periodPolicyNum;

    @ApiModelProperty("首年保单件数")
    private Integer  fycPolicyNum;

    @ApiModelProperty("续期保件实收")
    private Integer  rycActualNum;

    @ApiModelProperty("续期保费实收")
    private BigDecimal  rycActualPremium;

    @ApiModelProperty("续期佣金")
    private BigDecimal  rycAmount;

    @ApiModelProperty("续期保件应收")
    private Integer  rycNum;

    @ApiModelProperty("续期保费应收")
    private BigDecimal  rycPremium;

    @ApiModelProperty("续年追加保件")
    private Integer  ZPRycNum;

    @ApiModelProperty("续年追加保费")
    private BigDecimal  ZPRycPremium;

    @ApiModelProperty("续年追加佣金")
    private BigDecimal  ZPRycAmount;


    @ApiModelProperty("13个月继续率_续期保费实收")
    private BigDecimal  cr13ActualPremium;

    @ApiModelProperty("13个月继续率_续期保费应收")
    private BigDecimal  cr13Premium;

    @ApiModelProperty("首年标保")
    private BigDecimal  dcp;

    @ApiModelProperty("增员数量")
    private Integer  increaseNum;

    @ApiModelProperty("新单销售佣金")
    private BigDecimal  fscAmount;

    @ApiModelProperty("团单销售奖金")
    private BigDecimal  gscAmount;

    @ApiModelProperty("续期服务奖金")
    private BigDecimal  rscAmount;

    @ApiModelProperty("基本法类型 金莲花:'SELF_LOTUS'")
    private String  versionType;


}
