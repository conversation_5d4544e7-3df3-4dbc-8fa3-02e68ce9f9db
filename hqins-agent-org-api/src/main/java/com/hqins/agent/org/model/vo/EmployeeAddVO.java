package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.request.InitEmployeeAddRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("筛选结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeAddVO {

    @ApiModelProperty("验证结果")
    private List<InitResultVo> results;

    @ApiModelProperty("筛选后的请求")
    private List<InitEmployeeAddRequest> requests;
    
}
