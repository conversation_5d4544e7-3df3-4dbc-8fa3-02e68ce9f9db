package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description
 */
@ApiModel("合伙人分页查询请求")
@Getter
@SuperBuilder
public class PartnerTeamQueryRequest extends PageQueryRequest {

    @ApiModelProperty("归属合伙人代码")
    private String partnerCode;

    @ApiModelProperty("归属合伙人名称")
    private String partnerName;

    @ApiModelProperty("归属销售机构代码")
    private String orgCode;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("团队代码")
    private String code;

    @ApiModelProperty("团队名称")
    private String name;

    @ApiModelProperty("团队级别")
    private TeamLevel level;

    @ApiModelProperty("团队状态 ENABLED-有效 ALL-所有")
    private String status;

    @ApiModelProperty("团队级别,支持多个团队类别")
    private String teamLevel;


}
