package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 招募进度列表VO类
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 13:56
 */
@ApiModel("招募进度列表VO类")
@Data
public class AppEmployeeRecruitmentProgressVO {

    @ApiModelProperty("全部")
    private List<AppEmployeeRecruitmentInfoListVO> allEmployeeInfoList;

    @ApiModelProperty("面谈")
    private List<AppEmployeeRecruitmentInfoListVO> interviewEmployeeInfoList;

    @ApiModelProperty("一面")
    private List<AppEmployeeRecruitmentInfoListVO> firstRoundInterviewEmployeeInfoList;

    @ApiModelProperty("二面")
    private List<AppEmployeeRecruitmentInfoListVO> secondRoundInterviewEmployeeInfoList;

    @ApiModelProperty("offer")
    private List<AppEmployeeRecruitmentInfoListVO> offerEmployeeInfoList;

    @ApiModelProperty("审核")
    private List<AppEmployeeRecruitmentInfoListVO> auditEmployeeInfoList;

    @ApiModelProperty("不合适")
    private List<AppEmployeeRecruitmentInfoListVO> inappropriateEmployeeInfoList;

    @ApiModelProperty("入职")
    private List<AppEmployeeRecruitmentInfoListVO> onboardingEmployeeInfoList;
}
