package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentRankDefVO {

    @ApiModelProperty("职级名称")
    private String rankName;

    @ApiModelProperty("职级等级")
    private Integer rankLevel;

    @ApiModelProperty("是否为当前目标")
    private Boolean isCurrentTarget;

    @ApiModelProperty("是否达成当前目标")
    private Boolean isCompleteCurrentTarget;

    @ApiModelProperty("当前职级目标考核标准")
    private BigDecimal rankMoney;

    @ApiModelProperty("当前考核目标名称")
    private String currentTargetName;
}
