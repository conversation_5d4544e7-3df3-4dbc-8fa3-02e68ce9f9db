package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupervisorPerformanceDetailVO implements Serializable {

    @ApiModelProperty("机构代码")
    private String  instCode;

    @ApiModelProperty("机构名称")
    private String  instName;

    @ApiModelProperty("团队代码")
    private String  teamCode;

    @ApiModelProperty("团队名称")
    private String  teamName;

    @ApiModelProperty("代理人代码")
    private String  agentCode;

    @ApiModelProperty("代理人名称")
    private String  agentName;

    @ApiModelProperty("首年保费")
    private String  fycPremium;

    @ApiModelProperty("首年标保")
    private String  dcp;

    @ApiModelProperty("首年保单件数")
    private String  fycPolicyNum;

    @ApiModelProperty("期交保费")
    private String  periodPremium;

    @ApiModelProperty("期交标保")
    private String  periodDCP;

    @ApiModelProperty("期交保单件数")
    private String  periodPolicyNum;

    @ApiModelProperty("新单短险保费")
    private String  fycMRiskPremium;

    @ApiModelProperty("新单短险件数")
    private String  fycMRiskPolicyNum;

    @ApiModelProperty("新单客户数")
    private String  fycAppntNoNum;

    @ApiModelProperty("期交客户数")
    private String  periodAppntNoNum;

    @ApiModelProperty("增员数量")
    private String  increaseNum;

}
