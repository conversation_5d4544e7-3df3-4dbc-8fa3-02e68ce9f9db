package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员工作日志查询结果VO类
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:24
 */
@Data
@ApiModel("人员工作日志查询结果VO类")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmployeeLogResultVO {

    @ApiModelProperty("代理人工号")
    private String employeeCode;

    @ApiModelProperty("代理人角色")
    private String employeeRole;

    @ApiModelProperty("层级列表")
    private List<EmployeeLogHierarchyLevelVO> hierarchyList;
}
