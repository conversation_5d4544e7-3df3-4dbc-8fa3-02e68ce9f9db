package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/18
 */
@ApiModel("历史荣誉详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoryHonorDetailVO {

    @ApiModelProperty("历史荣誉列表")
    private List<YearHonorGroup> historyHonors;

    @ApiModel("年度荣誉")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YearHonorGroup {
        @ApiModelProperty("荣誉年度")
        private String year;
        @ApiModelProperty("荣誉项列表")
        private List<HonorItem> honors;
    }

    @ApiModel("荣誉项列表")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HonorItem {
        @ApiModelProperty("奖项名称")
        private String awardName;
        @ApiModelProperty("贺报ID")
        private String honorId;
    }

}
