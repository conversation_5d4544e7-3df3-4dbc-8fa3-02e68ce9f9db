package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @date 2021/5/24
 * @Description
 */
public enum OrgStatus {
    /**
     * 数据状态
     */
    ENABLED("有效"),
    DISABLED("失效");

    private String label;

    OrgStatus(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static TeamLevel get(String name) {
        for (TeamLevel value : TeamLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
