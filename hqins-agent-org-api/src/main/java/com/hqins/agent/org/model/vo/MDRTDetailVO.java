package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/17
 */
@ApiModel("百万年桌详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MDRTDetailVO {

    @ApiModelProperty("达成佣金")
    private String achievedCommission;

    @ApiModelProperty("达成保费")
    private String achievedPremium;

    @ApiModelProperty("获奖年度")
    private String year;

    @ApiModelProperty("MDRT")
    private MDRTAwardVO MDRT;
    @ApiModelProperty("COT")
    private MDRTAwardVO COT;
    @ApiModelProperty("TOT")
    private MDRTAwardVO TOT;


    @ApiModel("百万年桌奖项详情")
    @Data
    @Builder
    public static class MDRTAwardVO {
        @ApiModelProperty(value = "奖项状态 0 未达成 1 已达成", example = "1")
        private String status;
        @ApiModelProperty(value = "贺报ID")
        private String honorId;
        @ApiModelProperty(value = "佣金达标标准")
        private String commissionStandard;
        @ApiModelProperty(value = "佣金达标标准")
        private String commissionStandardUnit;
        @ApiModelProperty(value = "保费达标标准")
        private String premiumStandard;
        @ApiModelProperty(value = "保费达标标准")
        private String premiumStandardUnit;
        @ApiModelProperty(value = "获奖日期")
        private String awardDate;
    }

}
