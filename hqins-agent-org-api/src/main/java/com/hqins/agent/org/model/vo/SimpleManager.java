package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/6/30
 * 经理信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SimpleManager {

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("电话")
    private String mobile;

}
