package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/19
 * @Description
 */
@ApiModel("销售机构分页查询请求")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class OuterOrgRequest {

    @ApiModelProperty("销售机构代码")
    @NotEmpty(message = "销售机构代码不可为空")
    private String orgCode;

    @ApiModelProperty("执业证号列表")
    private List<String> nos;


}
