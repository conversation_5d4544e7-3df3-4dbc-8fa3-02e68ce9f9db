package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class AuthEmployeeVO extends EmployeeVO implements Serializable {
    private static final long serialVersionUID = 3095991537608684504L;
    @ApiModelProperty("销售员授权码")
    private String authId;
    @ApiModelProperty("物理ID")
    private Long id;
}
