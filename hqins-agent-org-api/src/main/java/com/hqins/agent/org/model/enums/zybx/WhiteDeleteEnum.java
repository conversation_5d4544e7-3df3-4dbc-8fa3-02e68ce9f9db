package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2023/9/11 3:34 下午
 */
public enum WhiteDeleteEnum {

    DELETED(1, "已删除"),
    UN_DELETED(0, "未删除");

    private Integer value;
    private String label;

    WhiteDeleteEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(Integer value) {
        this.value = value;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
