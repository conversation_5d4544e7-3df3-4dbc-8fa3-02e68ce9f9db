package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName TbepartnerVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/17 10:23
 **/
@ApiModel("公司信息")
@Data
public class TbepartnerVO implements Serializable {
    /**
     * 公司ID
     */
    private String companyid;

    /**
     * 所属平台代码默认4001
     */
    private String partnercode;

    /**
     * 公司代码
     */
    private String companycode;

    /**
     * 公司名称
     */
    private String companyname;

    /**
     * 销售渠道代码
     */
    private String companychannelcode;

    /**
     * 公司简称
     */
    private String companyshortname;

    /**
     * 公司英文名称
     */
    private String companyenname;

    /**
     * 公司英文简称
     */
    private String companyenshortname;

    /**
     * 公司类型（01:保险公司（平台），02:中介公司，03:经销商渠道）
     */
    private String companytype;

    /**
     * 地址
     */
    private String mainaddress;

    /**
     * 邮编
     */
    private String mainzipcode;

    /**
     * 网站
     */
    private String websitejson;

    /**
     * 邮箱
     */
    private String emailjson;

    /**
     * 传真
     */
    private String faxjson;

    /**
     * 电话
     */
    private String telephonejson;

    /**
     * 县区
     */
    private String countrycode;

    /**
     * 市
     */
    private String citycode;

    /**
     * 省
     */
    private String provincecode;

    /**
     * 所属区域
     */
    private String areacode;

    /**
     * 所属集团
     */
    private String crop;

    /**
     * logo图标
     */
    private String logo;

    /**
     * 代理品牌；多个逗号分隔
     */
    private String proxyband;

    /**
     * 激活状态（废弃） 0 未激活 1 已激活  2 激活申请中 3激活申请驳回
     */
    private Integer activestatus;

    /**
     * 公司状态（0:未生效，1，已生效，2，失效）
     */
    private Integer comanystatus;

    /**
     * 账户信息-银行帐号
     */
    private String bankaccountno;

    /**
     * 账户信息-银行户名
     */
    private String bankaccountname;

    /**
     * 账户信息-银行开户行
     */
    private String bankname;

    /**
     * 账户信息-联行号
     */
    private String cnapscode;

    /**
     * 发票信息-纳税人识别号
     */
    private String taxpayeridno;

    /**
     * 发票信息-开票名称
     */
    private String invoicetitle;

    /**
     * 发票信息-开票地址
     */
    private String invoiceaddress;

    /**
     * 发票信息-开票电话
     */
    private String invoicephone;

    /**
     * 发票信息-开票开户行
     */
    private String invoicebankname;

    /**
     * 发票信息-开票账号
     */
    private String invoicebankaccountno;

    /**
     * 上传材料信息JSON
     */
    private String imagelistjons;

    /**
     * 申请审核状态（0:审核中，1:审核成功，2:审核失败）
     */
    private Integer applystate;

    /**
     * 审核意见
     */
    private String vertifymessage;

    /**
     * 创建时间
     */
    private LocalDateTime createtime;

    /**
     * 创建者ID
     */
    private String createuser;

    /**
     * 修改时间
     */
    private LocalDateTime modifytime;

    /**
     * 修改者ID
     */
    private String modifyuser;

    /**
     * 创建人名字
     */
    private String createname;

    /**
     * 修改人名字
     */
    private String modifyname;

    /**
     * 市名称
     */
    private String cityname;

    /**
     * 省名称
     */
    private String provincename;

    /**
     * 所属区域名称
     */
    private String areaname;

    /**
     * 所属集团名称
     */
    private String cropname;

    /**
     * 法人（合伙人名称，渠道商法人名称）
     */
    private String legalPerson;

    /**
     * 法人联系电话
     */
    private String legalPersonPhone;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 发票类型 01 增值税专票, 02 增值税普票
     */
    private String invoiceType;

    /**
     * 增值税率
     */
    private BigDecimal taxRate;

    /**
     * 新渠道商关系: 01 直接结算 02 推荐
     */
    private String channelRela;

    /**
     * 渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理
     */
    private String channelType;

    /**
     * 上级公司 ID
     */
    private String parentCompanyId;

    /**
     * 上级公司名称
     */
    private String parentCompanyName;

    /**
     * 失效、生效原因
     */
    private String stateChangeReason;

    /**
     * 失效日期
     */
    private LocalDate disabledDate;

    /**
     * 复效日期
     */
    private LocalDate reenabledDate;

    /**
     * 公司全路径
     */
    private String companypath;

    /**
     * 管理机构ID
     */
    private String managerinst;

    /**
     * 管理机构代码
     */
    private String managerinstcode;

    /**
     * 合作级别：PARTY-合伙人，CHANNEL-渠道商
     */
    private String cptype;

    /**
     * 渠道商评级：a类 b类 c类 d类
     */
    private String channelRank;

    /**
     * 渠道商户推荐人，或合伙人推荐人
     */
    private String referrerName;

    /**
     * 育成合伙人代码
     */
    private String raiseCompanycode;

    /**
     * 育成合伙人公司名称
     */
    private String raiseCompanyname;

    /**
     * 合伙人级别
     */
    private String partyGrade;

    /**
     * 合同生效日期
     */
    private LocalDate cooperationStarttime;

    /**
     * 合同失效日期
     */
    private LocalDate cooperationEndtime;

    /**
     * 初始资本金额
     */
    private BigDecimal initialCapital;

    /**
     * 中介许可证编号
     */
    private String intermediaryLicenceNo;

    /**
     * 中介许可证获得日期
     */
    private LocalDate intermediaryLicenceStartdate;

    /**
     * 是否可以销售，Y-是，N-否
     */
    private String canSold;

    /**
     * 中介许可证到期日期
     */
    private LocalDate intermediaryLicenceEnddate;

    /**
     * 二阶段基准线 单位：元
     */
    private BigDecimal twostageBaseline;

    /**
     * 是否具有网络销售资格，Y-是，N-否
     */
    private String canSoldIsinternet;

    /**
     * 渠道商归业务类型（1-商户业务，2-自营业务）
     */
    private String businesstype;

    /**
     * BU编码
     */
    private String bucode;

    /**
     * BU名称
     */
    private String buname;
}
