package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/27
 */
public enum CheckMetricsItemEnum {

    ANNUAL_PREMIUM_STANDARD("ANNUAL_PREMIUM_STANDARD", "个人期交标准保费/个人新单期缴标准保费"),
    ANNUAL_PREMIUM_STANDARD_TOTAL("ANNUAL_PREMIUM_STANDARD_TOTAL", "个人累计期交标准保费/个人累计新单期缴标准保费"),

    ANNUAL_PREMIUM_TOTAL("ANNUAL_PREMIUM_TOTAL", "个人期交保费/个人新单期缴保费"),
    TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL("TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL", "团队期交标准保费"),
    TEAM_ANNUAL_POLICY_COUNT_TOTAL("TEAM_ANNUAL_POLICY_COUNT_TOTAL", "团队新单期缴件数"),
    CONTINUATION_RATE_R13("CONTINUATION_RATE_R13", "个人13个月保费继续率"),
    UNDERWRITTEN_POLICY_COUNT("UNDERWRITTEN_POLICY_COUNT_TOTAL", "月承保保单件数"),
    UNDERWRITTEN_POLICY_COUNT_TOTAL("UNDERWRITTEN_POLICY_COUNT_TOTAL", "累计承保保单件数"),
    ANNUAL_POLICY_COUNT_TOTAL("ANNUAL_POLICY_COUNT_TOTAL", "个人累计期缴件数"),
    DIVIDEND_INSURANCE_POLICY_COUNT_TOTAL("DIVIDEND_INSURANCE_POLICY_COUNT_TOTAL", "分红险产品累计销售件数"),
    DIVIDEND_PRODUCT_PREMIUM_STANDARD_TOTAL("DIVIDEND_PRODUCT_PREMIUM_STANDARD_TOTAL", "分红型产品累计新单期缴标保"),
    NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL("NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL", "新单期缴件数"),
    NEW_BUSINESS_RISK_COVERAGE_AMOUNT_TOTAL("NEW_BUSINESS_RISK_COVERAGE_AMOUNT_TOTAL", "新单风险保额"),
    NEW_BUSINESS_CUSTOMER_COUNT("NEW_BUSINESS_CUSTOMER_COUNT", "年度个人累计新单客户数"),
    MDRT_PREMIUM_TOTAL("MDRT_PREMIUM_TOTAL", "MDRT保费"),
    MDRT_COMMISSION_TOTAL("MDRT_COMMISSION_TOTAL", "MDRT佣金"),
    MDRT_COMMISSION_RISK_PROTECTION_TOTAL("MDRT_COMMISSION_RISK_PROTECTION_TOTAL", "MDRT佣金（风险保障类产品的佣金）"),
    MDRT_PREMIUM_RISK_PROTECTION_TOTAL("MDRT_PREMIUM_RISK_PROTECTION_TOTAL", "MDRT保费-风险保障产品类标准"),
    EFFECTIVE_NEW_RECRUIT_COUNT("EFFECTIVE_NEW_RECRUIT_COUNT", "有效增员人数"),
    EFFECTIVE_NEW_RECRUIT_PREMIUM_STANDARD("EFFECTIVE_NEW_RECRUIT_PREMIUM_STANDARD", "有效增员期缴标准保费"),

    INITIAL_SEQUENCEK_CODE("INITIAL_SEQUENCEK_CODE", "考核周期初始职级序列"),
    INITIAL_SEQUENCEK_NAME("INITIAL_SEQUENCEK_NAME", "考核周期初始职级序列名称"),
    INITIAL_RANK_CODE("INITIAL_RANK_CODE", "考核周期初始职级"),
    INITIAL_RANK_NAME("INITIAL_RANK_NAME", "考核周期初始职级名称"),

    SEQUENCEK_CODE("SEQUENCEK_CODE", "考核周期结束职级序列"),
    SEQUENCEK_NAME("SEQUENCEK_NAME", "考核周期结束职级序列名称"),
    RANK_CODE("RANK_CODE", "考核周期结束职级"),
    RANK_NAME("RANK_NAME", "考核周期结束职级名称")

    ;

    private final String value;
    private final String label;

    CheckMetricsItemEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static CheckMetricsItemEnum getMetricsItemEnumByValue(String queryValue) {
        for (CheckMetricsItemEnum metricsItem : CheckMetricsItemEnum.values()) {
            if (metricsItem.getValue().equals(queryValue)) {
                return metricsItem;
            }
        }
        return null;
    }
}
