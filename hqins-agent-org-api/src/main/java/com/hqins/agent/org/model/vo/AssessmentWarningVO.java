package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 上期/当期考核预警VO类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentWarningVO implements Serializable {

    @ApiModelProperty("当前目标")
    private String currentTarget;

    @ApiModelProperty("上一阶段目标")
    private String previousStageGoal;

    @ApiModelProperty("当前目标对应职级")
    private String correspondingRank;

    @ApiModelProperty("考核期间时间(S-E)")
    private String assessmentDuration;

    @ApiModelProperty("规则及数据说明")
    private String rulesAndDataDescription;

    @ApiModelProperty("业绩达成")
    private BigDecimal performanceAchieved;

    @ApiModelProperty("距离目标")
    private BigDecimal distanceFromTarget;

    @ApiModelProperty("百分比")
    private BigDecimal percentage;

    @ApiModelProperty("考核项目")
    private List<AssessmentItemsVO> assessmentItems;

    @ApiModelProperty("考核目标集合")
    private List<AssessmentRankDefVO> assessmentRankDefVOS;

    @ApiModelProperty("是否完成所有目标")
    private Boolean isCompleteAllTarget;
}
