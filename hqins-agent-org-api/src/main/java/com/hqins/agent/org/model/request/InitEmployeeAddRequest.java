package com.hqins.agent.org.model.request;

import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.annotations.Mobile;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/5/27
 * @Description
 */
@ApiModel("初始化销售员新增")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class InitEmployeeAddRequest {

    @ApiModelProperty("归属销售机构编码")
    @NotBlank(message = "归属销售机构编码不能为空")
    private String orgCode;

    @ApiModelProperty("销售员代码")
//    @NotBlank(message = "销售员代码不能为空")
    private String code;

    @ApiModelProperty("销售员名称")
    @NotBlank(message = "销售员名称不能为空")
    private String name;

    @ApiModelProperty("是否有万能险销售资格 true-有 false-没有")
//    @NotBlank(message = "万能险销售资格不能为空")
    private Boolean universalQualification;

    @ApiModelProperty("证件类型")
    @EnumValue(enumClass = IdType.class, enumMethod = "isValid", message = "证件类型不正确")
    private String idType;

    @ApiModelProperty("证件编码")
    @NotBlank(message = "证件编码不能为空")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
//    @NotBlank(message = "性别不能为空")
    @EnumValue(enumClass = Gender.class, enumMethod = "isValid", message = "性别格式不正确")
    private String gender;

    @ApiModelProperty("出生日期")
//    @NotBlank(message = "出生日期不能为空")
    private LocalDate birthday;

    @ApiModelProperty("销售员手机号")
    @Mobile(message = "手机号格式错误", required = false)
    private String mobile;

    @ApiModelProperty("执业证号")
    @NotBlank(message = "执业证号不能为空")
    private String licenseNo;

    @ApiModelProperty("执业证号启期")
    // @NotBlank(message = "执业证号启期不能为空")
    private LocalDate licenseStartDate;

    @ApiModelProperty("执业证号止期")
    //  @NotBlank(message = "执业证号止期不能为空")
    private LocalDate licenseEndDate;

    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ApiModelProperty("入职时间")
    private LocalDate entryTime;

    @ApiModelProperty("系统来源")
    @NotBlank(message = "系统来源不能为空")
    private String sourceSystem;

}
