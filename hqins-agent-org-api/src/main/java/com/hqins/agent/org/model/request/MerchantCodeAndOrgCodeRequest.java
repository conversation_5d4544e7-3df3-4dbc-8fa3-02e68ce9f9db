package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

@ApiModel("根据“商户代码（银行）”和“商户组织机构代码（网点）”查询请求")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantCodeAndOrgCodeRequest {

    @ApiModelProperty("商户代码（银行）")
    @NotNull(message = "商户代码不能为空")
    private String[] merchantCodes;

    @ApiModelProperty("商户组织机构代码（网点）")
    private String[] orgCodes;


}
