package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@FeignClient(name = "hqins-agent-org")
public interface OrgApi {
    /**
     * 根据机构代码查其上的所属公司（Tbepartner）信息
     *
     * @param orgCode 机构代码
     * @return Tbepartner
     */
    @GetMapping("/agent-org/orgs/getTbepartnerByOrgCode")
    @CollectionElement(targetClass = TbepartnerVO.class)
     TbepartnerVO getTbepartnerByOrgCode(@ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据合伙人或渠道商代码查其下的所有儿子（不查孙子）机构
     *
     * @param orgType 机构类型
     * @param topCode 合伙人或渠道商代码
     * @return 其下所有的顶级机构列表
     */
    @GetMapping("/agent-org/orgs/top")
    @CollectionElement(targetClass = OrgVO.class)
    List<OrgVO> getOrgsByTop(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                             @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", defaultValue = "false") boolean levels,
                             @ApiParam("合伙人或渠道商代码") @RequestParam(value = "topCode") String topCode);

    /**
     * 根据机构代码查其下的所有机构（有BUG）
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有的顶级机构列表
     */
    @GetMapping("/agent-org/orgs")
    @CollectionElement(targetClass = OrgVO.class)
    List<OrgVO> getOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                        @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", defaultValue = "false") boolean levels,
                        @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据机构代码查其上的所有父级机构
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有的顶级机构列表
     */
    @GetMapping("/agent-org/orgs/parents")
    @CollectionElement(targetClass = OrgVO.class)
    List<OrgVO> getParentsOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                        @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", defaultValue = "false") boolean levels,
                        @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据机构代码查其下的所有团队
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有团队
     */
    @GetMapping("/agent-org/orgs/teams/org")
    @CollectionElement(targetClass = TeamVO.class)
    List<TeamVO> getTeamsByOrg(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                               @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", defaultValue = "false") boolean levels,
                               @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据团队代码查其下的所有下级团队
     *
     * @param orgType  机构类型
     * @param teamCode 团队代码
     * @return 其下所有下级团队
     */
    @GetMapping("/agent-org/orgs/teams")
    @CollectionElement(targetClass = TeamVO.class)
    List<TeamVO> getTeams(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                          @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", defaultValue = "false") boolean levels,
                          @ApiParam("上级团队代码") @RequestParam(value = "teamCode") String teamCode);


    /**
     * 根据机构代码查询机构的经销商
     *
     * @param orgCode 机构代码
     */
    @GetMapping("/agent-org/orgs/dealer")
    DealerVO getDealer(@ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 查询所有顶级机构
     */
    @GetMapping("/agent-org/orgs/topOrg")
    @CollectionElement(targetClass = OrgVO.class)
    List<OrgVO> topOrg();

    /**
     * 根据机构代码查公司信息
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 公司信息
     */
    @GetMapping("/agent-org/orgs/company")
    CompanyVO getCompany(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                           @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) ;

    /**
     * 根据机构代码列表，批量查公司信息
     *
     * @param orgType 机构类型
     * @param orgCodes 机构代码列表
     * @return 公司信息列表
     */
    @CollectionElement(targetClass = CompanyVO.class)
    @GetMapping("/agent-org/orgs/companys")
    List<CompanyVO> getCompanys(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                         @ApiParam("机构代码列表") @RequestParam(value = "orgCodes") String[] orgCodes) ;

    /**
     * 查机构信息
     */
    @CollectionElement(targetClass = CompanyVO.class)
    @GetMapping("/agent-org/orgs/inst")
    InstResultVo queryInst(@ApiParam("机构编码orgCode") @RequestParam(value = "orgCode") String orgCode) ;

    /**
     * 根据合伙人二级机构编码，查询所有网点信息
     */
    @ApiOperation("根据合伙人二级机构编码，查询所有网点信息")
    @CollectionElement(targetClass = PartassignmanagerVO.class)
    @GetMapping("/agent-org/orgs/companyInst")
    List<PartassignmanagerVO> queryCompanyInst(@ApiParam("合伙人二级机构编码") @RequestParam(value = "companyInstCode") String companyInstCode) ;

    /**
     * 查所有网点的地址信息、机构信息
     */
    @ApiOperation("查所有网点的地址信息、机构信息")
    @CollectionElement(targetClass = QueryAllVO.class)
    @GetMapping("/agent-org/orgs/queryAll")
    List<QueryAllVO> queryAll() ;

}
