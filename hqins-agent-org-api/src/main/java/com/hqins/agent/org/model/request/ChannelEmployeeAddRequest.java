package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.annotations.Mobile;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("渠道商销售员新增请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelEmployeeAddRequest {

    @ApiModelProperty("销售员名称")
    @NotBlank(message = "销售员名称不能为空")
    private String name;

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("是否有万能险销售资格 true-有 false-没有")
    private Boolean universalQualification = false;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    @NotNull(message = "性别不能为空")
    private Gender gender;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty("归属销售团队编码")
    @NotNull(message = "归属销售团队编码不能为空")
    private String teamCode;

    @ApiModelProperty("销售员手机号")
    @NotNull(message = "手机号不能为空")
    @Mobile(message = "手机号格式错误")
    private String mobile;

    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ApiModelProperty("执业证号启期")
    private LocalDate licenseStartDate;

    @ApiModelProperty("执业证号止期")
    private LocalDate licenseEndDate;

    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("入职时间")
    private LocalDate entryTime;

    private Long staffId;

    @ApiModelProperty("人员类型  TESTER体验人员 FORMAL正式人员 ")
    private RoleType roleType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChannelEmployeeAddRequest that = (ChannelEmployeeAddRequest) o;
        return Objects.equals(name, that.name) && Objects.equals(code, that.code) && Objects.equals(universalQualification, that.universalQualification) && Objects.equals(idType, that.idType) && Objects.equals(idCode, that.idCode) && gender == that.gender && Objects.equals(birthday, that.birthday) && Objects.equals(teamCode, that.teamCode) && Objects.equals(mobile, that.mobile) && Objects.equals(licenseNo, that.licenseNo) && Objects.equals(licenseStartDate, that.licenseStartDate) && Objects.equals(licenseEndDate, that.licenseEndDate) && Objects.equals(jobNumber, that.jobNumber) && Objects.equals(sourceSystem, that.sourceSystem) && Objects.equals(entryTime, that.entryTime) && Objects.equals(staffId, that.staffId) && roleType == that.roleType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, code, universalQualification, idType, idCode, gender, birthday, teamCode, mobile, licenseNo, licenseStartDate, licenseEndDate, jobNumber, sourceSystem, entryTime, staffId, roleType);
    }
}
