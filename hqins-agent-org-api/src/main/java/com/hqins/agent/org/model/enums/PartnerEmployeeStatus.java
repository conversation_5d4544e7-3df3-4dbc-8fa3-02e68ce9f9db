package com.hqins.agent.org.model.enums;


/**
 * <AUTHOR>
 * @date 2022-09-05
 * @Description
 */
public enum PartnerEmployeeStatus {

    /**
     * 销售员状态  00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备
     * 产品确定
     * 有效 01
     * 离职 02、04
     */
    ADDING("00", "新增中"),
    SERVING("01", "有效"),
    INVALID("02", "失效"),
    STORAGE("03", "暂存"),
    LEAVING("04", "离职"),
    REPORTING("05", "待报备");

    private String code;

    private String label;

    PartnerEmployeeStatus(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static PartnerEmployeeStatus get(String name) {
        for (PartnerEmployeeStatus value : PartnerEmployeeStatus.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static PartnerEmployeeStatus getLabelByCode(String code) {
        for (PartnerEmployeeStatus value : PartnerEmployeeStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
