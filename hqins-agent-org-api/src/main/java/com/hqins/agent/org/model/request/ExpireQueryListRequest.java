package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("满期列表查询请求对象")
public class ExpireQueryListRequest implements Serializable {


    @ApiModelProperty("开始时间")
    private LocalDate expireDateStart;

    @ApiModelProperty("结束时间")
    private LocalDate expireDateEnd;

    @ApiModelProperty("代理人姓名")
    private String name;

    @ApiModelProperty("代理人工号")
    private String agentCode;

    @ApiModelProperty("已领取-0,未领取-1,未产生-9")
    private String flag;

    @ApiModelProperty("current")
    private long current;

    @ApiModelProperty("size")
    private long size;

    @ApiModelProperty("contno")
    private String contNo;
}
