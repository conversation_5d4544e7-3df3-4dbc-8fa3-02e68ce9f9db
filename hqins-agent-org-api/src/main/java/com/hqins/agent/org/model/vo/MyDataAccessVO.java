package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
@ApiModel("数据访问权限对象")
@Data
@SuperBuilder
public class MyDataAccessVO implements Serializable {
    private Boolean containsSuperAdmin;

    public MyDataAccessVO() {
        containsSuperAdmin = false;
        partnerCodes = new HashSet<>();
        selectPartnerCodes = new HashSet<>();
        partnerOrgCodes = new HashSet<>();
        channelCodes = new HashSet<>();
        selectChannelCodes = new HashSet<>();
        channelOrgCodes = new HashSet<>();
    }

    /**
     * 所有的有权限的合伙人编码数组（下拉框专用，下一级有权限也会添加进来）
     */
    private Set<String> selectPartnerCodes;

    /**
     * 所有的有权限的合伙人编码数组
     */
    private Set<String> partnerCodes;
    /**
     * 所有的有权限的合伙人销售机构编码数组
     */
    private Set<String> partnerOrgCodes;

    /**
     * 所有的有权限的渠道商编码数组
     */
    private Set<String> channelCodes;
    /**
     * 所有的有权限的渠道商销售机构编码数组
     */
    private Set<String> channelOrgCodes;

    /**
     * 所有的有权限的渠道商编码数组（下拉框专用，下一级有权限也会添加进来）
     */
    private Set<String> selectChannelCodes;
}
