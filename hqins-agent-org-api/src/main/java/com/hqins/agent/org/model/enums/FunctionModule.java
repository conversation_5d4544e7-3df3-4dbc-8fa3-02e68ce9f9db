package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构类型
 */
@ApiModel("功能所属模块")
public enum FunctionModule {
    /**
     * 售前
     */
    PRE_SALE(1, "售前"),
    /**
     * 售中
     */
    ON_SALE(2, "售中"),
    /**
     * 售后
     */
    POST_SALE(3, "售后");

    private Integer sort;
    private String label;

    FunctionModule(Integer sort, String label) {
        this.sort = sort;
        this.label = label;
    }

    public Integer getSort() {
        return this.sort;
    }

    public String getLabel() {
        return this.label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static FunctionModule get(String name) {
        for (FunctionModule value : FunctionModule.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
