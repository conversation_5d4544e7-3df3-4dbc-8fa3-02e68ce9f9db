package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/25
 */
public class HonorSystemConstants {

    public static final String QXJYH = "QXJYH";

    public static final String QYBSX = "QYBSX";

    public static final String QHRYT = "QHRYT";

    public static final String MDRT = "MDRT";

    public static final String QHJNX = "QHJNX";


    // 已入围 / 已获将
    public static final String SHORTLISTED = "1";

    // 琴辉荣耀堂 达标标准
    public static final String QHRYT_SDXRMX_STANDARD = "12";
    public static final String QHRYT_SDJSMX_STANDARD = "12";
    public static final String QHRYT_SDZYMX_STANDARD = "20";
    public static final String QHRYT_NDRYTD_STANDARD = "50";
    public static final String QHRYT_NDFHXXSZX_STANDARD = "12";

    // 琴韵博识轩 达标标准
    public static final String QYBSX_BF_STANDARD = "18";
    public static final String QYBSX_MONTH_STANDARD = "20000";

    // 琴海吉尼斯 达标标准
    public static final String QHJNX_STANDARD = "1000";


    // MDRT 标准定义
    public static final String MDRT_MDRT = "MDRT_MDRT";
    public static final String MDRT_COT = "MDRT_COT";
    public static final String MDRT_TOT = "MDRT_TOT";
    public static final String MDRT_MDRT_YJ_STANDARD = "28.26";
    public static final String MDRT_MDRT_BF_STANDARD = "84.78";
    public static final String MDRT_COT_BF_STANDARD = "254.34";
    public static final String MDRT_COT_YJ_STANDARD = "84.78";
    public static final String MDRT_TOT_YJ_STANDARD = "169.56";
    public static final String MDRT_TOT_BF_STANDARD = "508.68";


    // 公共单位常量
    public static final String UNIT_WAN = "万元";
    public static final String UNIT_YUAN = "元";
    public static final String UNIT_JIAN = "件";
    public static final String UNIT_PERSON = "人";
    public static final String UNIT_CUSTOMER = "客户";


}
