package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/14 15:24
 */
@ApiModel("入职资料VO类")
@Data
public class AppEmployeeEntryDocumentsVO {

    @ApiModelProperty("证件照")
    private List<String> idPhotoList;

    @ApiModelProperty("银行卡正反面")
    private List<String> bankList;

    @ApiModelProperty("学历证明")
    private List<String> educationalCredentialList;

    @ApiModelProperty("一寸免冠照")
    private List<String> bareheadedPhotoList;

    @ApiModelProperty("入司健康声明")
    private List<String> healthStatementList;

    @ApiModelProperty("承诺书")
    private List<String> promiseLetterList;

    @ApiModelProperty("诚信从业承诺书")
    private List<String> honestPracticeList;

    @ApiModelProperty("超龄协议书")
    private List<String> overageAgreementList;
}
