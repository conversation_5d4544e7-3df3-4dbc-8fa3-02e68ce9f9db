package com.hqins.agent.org.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarkDetailVO implements Serializable {

    @ApiModelProperty("销售机构代码")
    private String instCode;

    @ApiModelProperty("销售机构名称")
    private String instName;

    @ApiModelProperty("出单业务员代码")
    private String agentCode;

    @ApiModelProperty("出单业务员名称")
    private String agentName;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("投保人号")
    private String appntNo;

    @ApiModelProperty("投保人名称")
    private String appntName;

    @ApiModelProperty("被保人号")
    private String insuredNo;

    @ApiModelProperty("被保人名称")
    private String insuredName;

    @ApiModelProperty("类型代码")
    private String typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;



    @ApiModelProperty("13个月继续率")
    private BigDecimal  cr13;

    @ApiModelProperty("13个月继续率_续期保费实收")
    private BigDecimal  cr13ActualPremium;

    @ApiModelProperty("13个月继续率_续期保费应收")
    private BigDecimal  cr13Premium;


    @ApiModelProperty("险种号")
    private String riskCode;

    @ApiModelProperty("险种名称")
    private String riskName;


    @ApiModelProperty("交费方式")
    private String paymentWay;

    @ApiModelProperty("交费年期")
    private String paymentYear;

    @ApiModelProperty("保障期间")
    private String insureYears;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("保额")
    private BigDecimal amnt;

    @ApiModelProperty("退保日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate ctDate;

    @ApiModelProperty("退保日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ctD;

    @ApiModelProperty("备注")
    private String remark;

}
