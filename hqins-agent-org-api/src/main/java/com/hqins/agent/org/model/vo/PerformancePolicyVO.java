package com.hqins.agent.org.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PerformancePolicyVO implements Serializable {

    @ApiModelProperty("业绩保单id")
    private String id;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("代理人code")
    private String agentCode;

    @ApiModelProperty("代理人名称")
    private String agentName;

    @ApiModelProperty("保单类型:1团单")
    private Integer contType;

    @ApiModelProperty("保全受理号")
    private String edoraccepto;

    @ApiModelProperty("险种号")
    private String riskCode;

    @ApiModelProperty("险种名称")
    private String riskName;

    @ApiModelProperty("保单状态")
    private String policyState;

    @ApiModelProperty("交易类型")
    private String tradeType;

    @ApiModelProperty("投保人号")
    private String appntNo;

    @ApiModelProperty("投保人名称")
    private String appntName;

    @ApiModelProperty("被保人名称")
    private String insuredName;

    @ApiModelProperty("佣金项code")
    private String commissionItem;

    @ApiModelProperty("保障期间")
    private String insureYears;

    @ApiModelProperty("保险金额")
    private BigDecimal insuranceAmount;

    @ApiModelProperty("缴费期间")
    private String paymentYears;

    @ApiModelProperty("长短险(M:短险 L:长险)")
    private String riskPeriod;

    @ApiModelProperty("长短险名称")
    private String riskPeriodName;

    @ApiModelProperty("主险附加险标志(S:附加险 M:主险)")
    private String subRiskFlag;

    @ApiModelProperty("续期状态")
    private String qqdFlag;

    @ApiModelProperty("业绩日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate performanceDate;

    @ApiModelProperty("续期日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate qqdDate;

    @ApiModelProperty("承保日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signDate;

    @ApiModelProperty("保单结算状态")
    private String settleFlag;

    @ApiModelProperty("保单结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate settleDate;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("佣金金额")
    private BigDecimal amount;

    @ApiModelProperty("首年佣金")
    private BigDecimal fyc;

    @ApiModelProperty("续期佣金")
    private BigDecimal ryc;

    @ApiModelProperty("标准保费")
    private BigDecimal dcp;

    @ApiModelProperty("新单服务奖金")
    private BigDecimal fsc;

    @ApiModelProperty("续期服务奖金")
    private BigDecimal rsc;

    @ApiModelProperty("团单销售佣金")
    private BigDecimal gsc;

    @ApiModelProperty("数据来源")
    private String source;

    @ApiModelProperty("保单号集合")
    private List<String> policyNoList;

    @ApiModelProperty("保单数据集合")
    private List<PerformancePolicyVO> policyVOList;

    @ApiModelProperty("保单数量")
    private Integer policyNum;

}
