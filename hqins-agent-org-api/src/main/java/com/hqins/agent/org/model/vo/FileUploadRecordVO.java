package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/4/25 13:40
 */
@ApiModel("上传文件列表返回对象")
@Data
public class FileUploadRecordVO implements Serializable {


    /**
     * id
     */
    @ApiModelProperty("序号")
    private Long id;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     * 导入人员数量
     */
    @ApiModelProperty("导入人员数量")
    private Integer personNumber;

    /**
     * 成功数量
     */
    @ApiModelProperty("成功数量")
    private Integer successNumber;

    /**
     * 失败数量
     */
    @ApiModelProperty("失败数量")
    private Integer failNumber;

    /**
     * 创建状态
     */
    @ApiModelProperty("创建状态")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
