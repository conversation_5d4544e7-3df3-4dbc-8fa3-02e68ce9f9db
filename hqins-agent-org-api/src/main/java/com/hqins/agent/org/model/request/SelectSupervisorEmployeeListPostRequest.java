package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.SupervisorType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("账号分页查询请求")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SelectSupervisorEmployeeListPostRequest implements Serializable {
    @ApiModelProperty("账号类型")
    private SupervisorType roleType;
    @ApiModelProperty("机构编码")
    private String orgCode;
    @ApiModelProperty("人员代码")
    private String employeeCode;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("团队CODE")
    private String teamCode;
}
