package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupervisorPerformanceVO implements Serializable {

    @ApiModelProperty("标题列数据")
//    private LinkedHashMap<String,String> headList;
    private List<HeadVO> headList;

    @ApiModelProperty("汇总信息")
    private List<SupervisorPerformanceDetailVO>  accVoList;

    @ApiModelProperty("明细数据")
    private List<SupervisorPerformanceDetailVO> detailVOList;
}
