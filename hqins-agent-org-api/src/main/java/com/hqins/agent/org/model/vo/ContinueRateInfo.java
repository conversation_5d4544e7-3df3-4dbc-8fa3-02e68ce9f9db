package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


@ApiModel(value = "ContinueRateInfo对象", description = "继续率信息")
public class ContinueRateInfo  implements Serializable {

	/**
	 *继续率代码
	 */
	@ApiModelProperty(value = "继续率代码", example = "继续率代码")
	public String CONTINUE_RATE_CODE;

	/**
	 *继续率
	 */
	@ApiModelProperty(value = "继续率", example = "继续率")
	public String CONTINUE_RATE;

	/**
	 *分子
	 */
	@ApiModelProperty(value = "分子", example = "分子")
	public String NUMERATOR;

	/**
	 *分母
	 */
	@ApiModelProperty(value = "分母", example = "分母")
	public String DENOMINATOR;

	/**
	 *分母
	 */
	@ApiModelProperty(value = "查询条件", example = "查询条件")
	public String QUERY_CONDITION;
	
}
