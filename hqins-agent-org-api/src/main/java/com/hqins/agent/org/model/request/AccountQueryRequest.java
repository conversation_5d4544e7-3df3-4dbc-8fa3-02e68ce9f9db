package com.hqins.agent.org.model.request;

import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/5/25
 * @Description
 */
@ApiModel("账号分页查询请求")
@Getter
@SuperBuilder
public class AccountQueryRequest extends PageQueryRequest {

    @ApiModelProperty("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人")
    private AgentOrgType orgType;

    @ApiModelProperty("归属渠道商、合伙人名称")
    private String topName;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("归属销售团队名称")
    private String teamName;

    @ApiModelProperty("销售人员代码")
    private String code;

    @ApiModelProperty("销售人员名称")
    private String name;

    @ApiModelProperty("销售人员手机号")
    private String mobile;

    @ApiModelProperty("是否是督导账号")
    private Boolean isSupervisor;

}
