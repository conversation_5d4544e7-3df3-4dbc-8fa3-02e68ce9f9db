package com.hqins.agent.org.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * app招募详情响应参数.
 *
 * <AUTHOR> MXH
 * @create 2025/2/20 15:35
 */
@Data
public class AppEmployeeInfoVO {

    /**
     * 人员工号
     */
    public String empCode;

    /**
     * 人员姓名
     */
    public String empName;

    /**
     * 人员证件类型
     */
    public String idType;

    /**
     * 证件类型名称
     */
    public String idTypeName;

    /**
     * 证件编号
     */
    public String idCode;

    /**
     * 性别编码
     */
    public String sexCode;

    /**
     * 性别名称
     */
    public String sexName;

    /**
     * 出生年月
     */
    public LocalDate birthday;

    /**
     * 民族
     */
    public String nationCode;

    /**
     * 政治面貌
     */
    public String polityVisage;

    /**
     * 婚姻状况
     */
    public String marriage;

    /**
     * 兴趣特长
     */
    public String liking;

    /**
     * 手机号码
     */
    public String mainTelephone;

    /**
     * 电子邮箱
     */
    public String mainEmail;

    /**
     * 邮编
     */
    public String mainZipcode;

    /**
     * 籍贯
     */
    public String birthplace;

    /**
     * 户口所在地
     */
    public String rgtAddress;

    /**
     * 常住省编码
     */
    public String provinceCode;

    /**
     * 常住省名称
     */
    public String provinceName;

    /**
     * 常住市编码
     */
    public String cityCode;

    /**
     * 常住市名称
     */
    public String cityName;

    /**
     * 常住县编码
     */
    public String areCode;

    /**
     * 常住县名称
     */
    public String areName;

    /**
     * 详细地址
     */
    public String mainAddress;

    /**
     * 最高学历
     */
    public String highestEducation;

    /**
     * 毕业院校
     */
    public String graduateSchool;

    /**
     * 专业
     */
    public String major;

    /**
     * 工作年限
     */
    public BigDecimal workExperienceYears;

    /**
     * 是否有从业经历
     */
    public String isInsSale;

    /**
     * 从业年限
     */
    public String workAge;

    /**
     * 调换工作原因
     */
    public String reasonForJobChange;

    /**
     * 银行名称
     */
    public String cardBankName;

    /**
     * 银行编码
     */
    public String cardBank;

    /**
     * 开户行名称
     */
    public String cardBankBranch;

    /**
     * 开户行编码
     */
    public String cardBankBranchCode;

    /**
     * 银行卡号
     */
    public String cardNo;

    /**
     * 个人经历
     */
    public List<AppEmployeePersonalExperienceVO> employeePersonalExperiences;

    /**
     * 自我评价
     */
    public String selfEvaluation;

    /**
     * 如何看待寿险营销
     */
    public String viewOnLifeInsuranceMarketing;

    /**
     * 自己最自豪的一件事
     */
    public String proudestAchievement;

    /**
     * 对未来的期许
     */
    public String expectationForFuture;

    /**
     * 事业目标
     */
    public String careerGoal;

    /**
     * 面谈评价
     */
    public String interviewEvaluation;

    /**
     * 自信度
     */
    public String confidence;

    /**
     * 乐观性
     */
    public String optimism;

    /**
     * 团队精神
     */
    public String teamSpirit;

    /**
     * 表达能力
     */
    public String expressionAbility;

    /**
     * 创新能力
     */
    public String innovationAbility;

    /**
     * 录用职级序列
     */
    public String rankSequence;


    /**
     * 录用职级
     */
    public String hiringLevel;

    /**
     * 入职资料
     */
    public String entryDocuments;

    /**
     * 推荐人工号
     */
    public String fatherEmpCode;

    /**
     * 推荐人姓名
     */
    public String fatherEmpName;

    /**
     * 人员状态
     */
    public String empStatus;

    /**
     * 审核督导账号
     */
    public String supervisorEmployeeCode;

    /**
     * 审核督导姓名
     */
    public String supervisorEmployeeName;

    /**
     * 人员内码
     */
    public String empInCode;

    /**
     * 录用职级序列名称
     */
    public String rankSequenceName;

    /**
     * 录用职级名称
     */
    public String hiringLevelName;

    /**
     * 创建时间
     */
    public Date createTime;
}
