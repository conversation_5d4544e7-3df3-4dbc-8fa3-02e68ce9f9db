package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 09:25
 */
@Data
@ApiModel(value = "IFP积分明细传输对象",description = "IFP积分明细传输对象")
public class DigitalScoreDetailVO {

    @ApiModelProperty(value = "代理人工号")
    private String agentCode;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "类型")
    private String actionType;

    @ApiModelProperty(value = "行为完成时间")
    private LocalDateTime actionTime;

    @ApiModelProperty(value = "得分月份")
    private String actionMonth;

    @ApiModelProperty(value = "团队名称")
    private String teamName;

    @ApiModelProperty(value = "团队编码")
    private String teamCode;

    @ApiModelProperty(value = "积分")
    private Integer score;

    @ApiModelProperty(value = "是否可记分")
    private String recordPointsFlag;
}
