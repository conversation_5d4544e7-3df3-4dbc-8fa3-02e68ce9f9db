package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.BusinessOrgVO;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * @Author: lijian
 * @Date: 2022/12/7 4:14 下午
 */
@FeignClient(name = "hqins-agent-org")
public interface EmployeeOrgApi {

    @ApiOperation("获取销售员及所属org信息")
    @GetMapping("/agent-org/employee-org/employee-info")
    @ResponseStatus(HttpStatus.OK)
    EmployeeOrgVO queryEmployeeOrgInfo(@ApiParam("销售员编码") @RequestParam(value = "employeeCode", required = true) String employeeCode);


    @ApiOperation("获取销售员及所属org信息")
    @GetMapping("/agent-org/employee-org/org-employee-info")
    @ResponseStatus(HttpStatus.OK)
    BusinessOrgVO querySaleTeamEmployeeInfo(@ApiParam("团队编码") @RequestParam(value = "saleTeamCode", required = true) String saleTeamCode);


    @ApiOperation("获取集合中所有销售员及所属org信息")
    @GetMapping("/agent-org/employee-org/employee-info/list")
    @ResponseStatus(HttpStatus.OK)
    @CollectionElement(targetClass = EmployeeOrgVO.class)
    List<EmployeeOrgVO> queryEmployeeOrgInfo(@ApiParam("销售员编码") @RequestParam(value = "employeeCodeList", required = true) List<String> employeeCodeList);

}
