package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TeamManageVO {

    @ApiModelProperty("团队内码")
    private String saleTeamInCode;

    @ApiModelProperty("人员代码")
    private String empCode;

    @ApiModelProperty("人员名称")
    private String empName;

    @ApiModelProperty("团队代码")
    private String teamCode;

    @ApiModelProperty("团队名称")
    private String teamName;

    @ApiModelProperty("在职人力")
    private Integer personNum;

    @ApiModelProperty("当月活动人力")
    private Integer activePersonNum;

    @ApiModelProperty("当月标准人力")
    private Integer standardPersonNum;

    @ApiModelProperty("当月继续率")
    private BigDecimal currentMonthCr;

    @ApiModelProperty("当月FYP")
    private BigDecimal currentMonthFyp;

    @ApiModelProperty("当月Fyc")
    private BigDecimal currentMonthFyc;

    @ApiModelProperty("当月保单件数")
    private Integer currentMonthPolicyNum;

    @ApiModelProperty("人员列表")
    private List<PersonalVO> personalVOList;

    @ApiModelProperty("当月FYP人员排名数据集合")
    private List<PersonalVO> FYPPersonalVOList;

    @ApiModelProperty("当月保单件数排名数据集合")
    private List<PersonalVO> PolicyNumPersonalVOList;

    @ApiModelProperty("当月FYC人员排名数据集合")
    private List<PersonalVO> FYCPersonalVOList;

    @ApiModelProperty("当月标保")
    private BigDecimal currentMonthDCP;

    @ApiModelProperty("增员数量")
    private Integer  increaseNum;

    @ApiModelProperty("当月标保人员排名数据集合")
    private List<PersonalVO> DCPPersonalVOList;


}
