package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PersonalIncomeVO implements Serializable {

    @ApiModelProperty("基本法类型")
    private String versionType;

    @ApiModelProperty("基本法Id")
    private String versionId;

    @ApiModelProperty("年月份")
    private String month;

    @ApiModelProperty("税前收入")
    private BigDecimal amountPreTax;

    @ApiModelProperty("实发金额/实际收入")
    private BigDecimal totalPaidAmount;

    @ApiModelProperty("实际收入(去年)")
    private BigDecimal lastYearTotalPaidAmount;

    @ApiModelProperty("已结算批次id")
    private String settleBatchId;

    @ApiModelProperty("佣金项数据")
    private List<CommissionItemVO> commissionItemList;

    @ApiModelProperty("佣金类目数据")
    private List<CommissionItemVO> commissionTypeList;



}
