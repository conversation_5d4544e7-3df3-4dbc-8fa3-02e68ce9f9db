package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentPerformanceDataVO {

    @ApiModelProperty("在职人力")
    private Double activeWorkforce;

    @ApiModelProperty("在职人力考核标准")
    private Double activeWorkforceStandard;

    @ApiModelProperty("合格率")
    private Double 	qualificationRate;

    @ApiModelProperty("合格率考核标准")
    private Double 	qualificationRateStandard;

    @ApiModelProperty("待达标人数（不合格人数）")
    private Integer unqualifiedPersonnel;

    @ApiModelProperty("饼图数据")
    private List<AssessmentPieChartDataVO> pieChartData;
}
