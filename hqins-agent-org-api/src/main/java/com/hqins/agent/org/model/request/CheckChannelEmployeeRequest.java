package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.zybx.EntryTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: liji<PERSON>
 * @Date: 2023/9/11 2:36 下午
 */
@ApiModel("渠道商校验请求类")
@Data
public class CheckChannelEmployeeRequest {

    @ApiModelProperty("代理人列表")
    List<CheckChannelEmployeeBody> employeeList;

    @ApiModelProperty("入口类型")
    EntryTypeEnum entryTypeEnum;
}
