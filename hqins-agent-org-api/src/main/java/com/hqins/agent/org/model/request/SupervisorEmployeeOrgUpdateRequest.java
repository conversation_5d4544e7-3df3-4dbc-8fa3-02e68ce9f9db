package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2023/4/7 11:23
 */
@ApiModel("督导账号机构切换更新请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupervisorEmployeeOrgUpdateRequest {


    @ApiModelProperty("人员工号")
    @NotEmpty(message = "人员工号不能为空")
    private String employeeCode;

    /**
     * 合伙人组织编码
     */
    @ApiModelProperty("合伙人组织编码")
    @NotEmpty(message = "合伙人组织编码不能为空")
    private String topCode;

    /**
     * 合伙人组织名称
     */
    @ApiModelProperty("合伙人组织名称")
    @NotEmpty(message = "合伙人组织名称不能为空")
    private String topCodeName;

    /**
     * 所属机构名称
     */
    @ApiModelProperty("所属机构名称")
    @NotEmpty(message = "所属机构名称不能为空")
    private String orgName;

    /**
     * 所属机构编码
     */
    @ApiModelProperty("所属机构编码")
    @NotEmpty(message = "所属机构编码不能为空")
    private String orgCode;

    @ApiModelProperty("团队编码")
    @NotEmpty(message = "团队编码不能为空")
    private String teamCode;

    @ApiModelProperty("团队名称")
    @NotEmpty(message = "团队名称不能为空")
    private String teamName;


    @ApiModelProperty("团队等级")
    @NotEmpty(message = "团队等级不能为空")
    private String teamLevel;
}
