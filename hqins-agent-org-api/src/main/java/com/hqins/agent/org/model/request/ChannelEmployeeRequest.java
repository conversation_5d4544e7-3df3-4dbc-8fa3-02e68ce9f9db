package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/5/21
 * @Description
 */
@ApiModel("渠道商销售员分页查询请求")
@Getter
@SuperBuilder
public class ChannelEmployeeRequest extends PageQueryRequest {

    @ApiModelProperty("归属渠道商名称")
    private String channelCode;

    @ApiModelProperty("归属渠道商名称")
    private String channelName;

    @ApiModelProperty("归属销售机构名称")
    private String orgCode;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("归属销售团队名称")
    private String teamCode;

    @ApiModelProperty("归属销售团队名称")
    private String teamName;

    @ApiModelProperty("人员代码")
    private String code;

    @ApiModelProperty("证件号码")
    private String idCode;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("入职日期-开始")
    private LocalDate entryTimeStart;

    @ApiModelProperty("入职日期-结束")
    private LocalDate entryTimeEnd;

    @ApiModelProperty("离职日期-开始")
    private LocalDate quitTimeStart;

    @ApiModelProperty("离职日期-结束")
    private LocalDate quitTimeEnd;

}
