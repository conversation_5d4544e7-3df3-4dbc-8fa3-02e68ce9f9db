package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public enum TeamStatus {
    /**
     * 营销团队状态(00--有效 01--失效 02 --暂存 03--审核中  04--撤销)
     * 产品确定
     *      有效 00
     *      失效 01、02、03、04
     */
    ENABLED("有效"),
    DISABLED("失效");

    private String label;

    TeamStatus(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static TeamLevel get(String name) {
        for (TeamLevel value : TeamLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
