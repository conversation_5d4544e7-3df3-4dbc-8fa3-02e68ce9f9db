package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构结点级别
 */
@ApiModel("组织机构结点级别")
public enum NodeLevel {
    /**
     * 组织机构结点级别
     */
    TOP("合伙人或渠道商"),
    ORG("机构"),
    TEAM("团队");

    private String label;

    NodeLevel(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static NodeLevel get(String name) {
        for (NodeLevel value : NodeLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
