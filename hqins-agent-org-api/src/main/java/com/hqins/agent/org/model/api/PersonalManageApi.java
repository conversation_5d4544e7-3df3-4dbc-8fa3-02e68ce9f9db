package com.hqins.agent.org.model.api;


import com.hqins.agent.org.model.request.TeamPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


@FeignClient(name = "hqins-agent-org")
public interface PersonalManageApi {




    @GetMapping("/agent-org/personal/manage/getCurrentMonthIncomeInfo")
    @CollectionElement(targetClass = PersonalVO.class)
    PersonalVO getCurrentMonthIncomeInfo(@ApiParam("组员代码(组长查看组员时传入)") @RequestParam(value = "employeeCode",required = false) String employeeCode);



    @GetMapping("/agent-org/personal/manage/getPreSettleIncomeInfo")
    @CollectionElement(targetClass = PersonalIncomeVO.class)
    List<PersonalIncomeVO> getPreSettleIncomeInfo();



    @GetMapping("/agent-org/personal/manage/getSettleIncomeTrendInfo")
    @CollectionElement(targetClass = PersonalIncomeVO.class)
    List<PersonalIncomeVO> getSettleIncomeTrendInfo();


    @GetMapping("/agent-org/personal/manage/getSettleIncomeInfo")
    @CollectionElement(targetClass = PersonalIncomeVO.class)
    PersonalIncomeVO getSettleIncomeInfo(@ApiParam("已结算月份") @RequestParam(value = "settleMonth",required = false) String settleMonth) ;


    @GetMapping("/agent-org/personal/manage/getSettlePolicyInfo")
    @CollectionElement(targetClass = PerformancePolicyVO.class)
    List<PerformancePolicyVO> getSettlePolicyInfo(@ApiParam("查询佣金code") @RequestParam(value = "commissionItem") String commissionItem,
                                                  @ApiParam("结算月份") @RequestParam(value = "settleMonth") String settleMonth,
                                                  @ApiParam("查询类型:续年实收:QQD,续年追加:ZP") @RequestParam(value = "queryType",required = false) String queryType);



    @GetMapping("/agent-org/personal/manage/getCurrentMonthPerformanceInfo")
    @CollectionElement(targetClass = PersonalVO.class)
    PersonalVO getCurrentMonthPerformanceInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode);


    @GetMapping("/agent-org/personal/manage/getPerformanceFycMonthInfo")
    @CollectionElement(targetClass = PerformanceVO.class)
    PerformanceVO getPerformanceFycMonthInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                             @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth);



    @GetMapping("/agent-org/personal/manage/getPerformanceRycMonthInfo")
    @CollectionElement(targetClass = PerformanceVO.class)
    PerformanceVO getPerformanceRycMonthInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                             @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth);



    @GetMapping("/agent-org/personal/manage/getPerformanceCr13Info")
    @CollectionElement(targetClass = PerformanceVO.class)
    PerformanceVO getPerformanceCr13Info(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                         @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth);



    @GetMapping("/agent-org/personal/manage/getPerformancePolicyInfo")
    @CollectionElement(targetClass = PerformanceVO.class)
    List<PerformancePolicyVO> getPerformancePolicyInfo(@ApiParam("查询类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType") String queryType,
                                                       @ApiParam("业绩月份(实收月,续缴月)") @RequestParam(value = "performanceMonth") String performanceMonth);

    @GetMapping("/agent-org/personal/manage/getPerformancePolicyNoList")
    @CollectionElement(targetClass = String.class)
    List<String> getPerformancePolicyNoList(@ApiParam("组员代码") @RequestParam(value = "employeeCode") String employeeCode,
                                            @ApiParam("查询类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType") String queryType,
                                            @ApiParam("业绩月份(实收月,续缴月)") @RequestParam(value = "performanceMonth") String performanceMonth);


    @GetMapping("/agent-org/personal/manage/getPerformancePolicyList")
    @CollectionElement(targetClass = PerformancePolicyVO.class)
    List<PerformancePolicyVO> getPerformancePolicyList(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                                         @ApiParam("查询条件") @RequestParam(value = "qureyString") String qureyString);

    @GetMapping("/agent-org/personal/manage/getPolicyCommissionList")
    @CollectionElement(targetClass = PolicyCommissionVO.class)
    PolicyCommissionVO getPolicyCommissionList(@ApiParam("保单号") @RequestParam(value = "policyNo") String policyNo);

    @GetMapping("/agent-org/personal/manage/getPersonalPolicyInfo")
    @CollectionElement(targetClass = PerformancePolicyVO.class)
    List<PerformancePolicyVO> getPersonalPolicyInfo(@ApiParam("类型:收入:income,业绩:performance") @RequestParam(value = "type") String type,
                                                                      @ApiParam("查询佣金code") @RequestParam(value = "commissionItem",required = false) String commissionItem,
                                                                      @ApiParam("查询月份") @RequestParam(value = "queryMonth") String queryMonth,
                                                                      @ApiParam("佣金类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType",required = false) String queryType);

    @GetMapping("/agent-org/personal/manage/getPersonalTargetPerformance")
    @CollectionElement(targetClass = PerformanceVO.class)
    PerformanceVO getPersonalTargetPerformance(
            @ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
            @ApiParam("登录人代码") @RequestParam(value = "loginEmpCode",required = false) String loginEmpCode,
            @ApiParam("按年查询业绩:2024") @RequestParam(value = "queryYear",required = false) String queryYear,
            @ApiParam("按月查询业绩:2024-01") @RequestParam(value = "queryMonth",required = false) String queryMonth);

    @GetMapping("/agent-org/team/manage/getCurrentPersonList")
    @CollectionElement(targetClass = TeamManageVO.class)
    TeamManageVO getTeamCurrentPersonList(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode);

    @GetMapping("/agent-org/team/manage/getCurrentTeamPerformance")
    @CollectionElement(targetClass = TeamManageVO.class)
    TeamManageVO getCurrentTeamPerformance(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode);

    @GetMapping("/agent-org/team/manage/getTeamTrendPerformanceTrend")
    @CollectionElement(targetClass = PerformanceTrendVO.class)
    List<PerformanceTrendVO> getTeamTrendPerformanceTrend(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
                                                          @ApiParam("查询类型:month:月业绩,half_year:半年业绩,active:活动人力") @RequestParam(value = "type") String type);


    @GetMapping("/agent-org/team/manage/getTeamRangePerformance")
    @CollectionElement(targetClass = TeamVersionVO.class)
    TeamVersionVO getTeamRangePerformance(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
                                                    @ApiParam("查询类型:team 按团队;personal:按个人;product:按产品;rankSeqCode:按职级") @RequestParam(value = "type") String type,
                                                    @ApiParam("业绩月(按团队查询必传)") @RequestParam(value = "performanceMonth",required = false) String performanceMonth,
                                                    @ApiParam("缴费期间") @RequestParam(value = "paymentYears",required = false) String paymentYears,
                                                    @ApiParam("开始日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "startDate",required = false) Date startDate,
                                                    @ApiParam("结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "endDate",required = false) Date endDate);


    @PostMapping("/agent-org/team/manage/getTeamRangePerformancePolicyList")
    @CollectionElement(targetClass = PerformancePolicyVO.class)
    PerformancePolicyVO getTeamRangePerformancePolicyList(@RequestBody TeamPerformanceRequest request);

    @PostMapping("/agent-org/team/manage/queryTeamRangePerformancePolicyList")
    @CollectionElement(targetClass = PerformancePolicyVO.class)
    PerformancePolicyVO queryTeamRangePerformancePolicyList(@RequestBody TeamPerformanceRequest request);



    @GetMapping("/agent-org/team/manage/getTeamTargetPerformance")
    @CollectionElement(targetClass = PerformanceVO.class)
    PerformanceVO getTeamTargetPerformance(
            @ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
            @ApiParam("登录人代码") @RequestParam(value = "loginEmpCode",required = false) String loginEmpCode,
            @ApiParam("按年查询业绩:2024") @RequestParam(value = "queryYear",required = false) String queryYear,
            @ApiParam("按月查询业绩:2024-01") @RequestParam(value = "queryMonth",required = false) String queryMonth);

}
