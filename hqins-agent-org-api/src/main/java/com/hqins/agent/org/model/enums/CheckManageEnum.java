package com.hqins.agent.org.model.enums;

import com.hqins.common.utils.StringUtil;
import org.springframework.util.ObjectUtils;

public enum CheckManageEnum {

    REDUCE_LEVEL2("REDUCE_LEVEL2", "降两级",-2),
    REDUCE_LEVEL1("REDUCE_LEVEL1", "降一级",-1),
    KEEP_LEVEL("KEEP_LEVEL", "维持",0),
    PROMOTED_LEVEL1("PROMOTED_LEVEL1", "晋升一级",1),
    PROMOTED_LEVEL2("PROMOTED_LEVEL2", "晋升两级",2),
    QUIT("QUIT", "清退/降职级序列",-99),
    NOT_CHECK("NOT_CHECK", "不考核",-100),
    ;

    private String value;
    private String label;
    private int level;
    CheckManageEnum (String value,String label,int level){
        this.value = value;
        this.label = label;
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static CheckManageEnum getEnumByLevel(Integer level) {
        if (ObjectUtils.isEmpty(level)) {
            return null;
        }
        for (CheckManageEnum manageEnum : CheckManageEnum.values()) {
            if (manageEnum.getLevel() == level) {
                return manageEnum;
            }
        }
        return null;
    }

    public static CheckManageEnum getEnumByLabel(String label) {
        if (StringUtil.isEmpty(label)){
            return null;
        }
        if (StringUtil.isEmpty(label)) {
            return null;
        }
        for (CheckManageEnum manageEnum : CheckManageEnum.values()) {
            if (manageEnum.getLabel().equals(label)) {
                return manageEnum;
            }
        }
        return null;
    }

    public static CheckManageEnum getEnumByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (CheckManageEnum manageEnum : CheckManageEnum.values()) {
            if (manageEnum.getValue().equals(value)) {
                return manageEnum;
            }
        }
        return null;
    }

    public enum BatchStatus{
        CHECKING("0","进行中"),
        COMPLETE("1","已完成"),
        ;
        
        
        private String value;
        private String label;

        
        BatchStatus(String value,String label){
            this.value = value;
            this.label = label;
        }
        
        
        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }

        public static BatchStatus getEnumByValue(String value) {
            if (StringUtil.isEmpty(value)) {
                return null;
            }
            for (BatchStatus batchStatus : BatchStatus.values()) {
                if (batchStatus.getValue().equals(value)) {
                    return batchStatus;
                }
            }
            return null;
        }
    }

    public enum AgentTypeEnum{
        ACCOUNT_MANAGER("ACCOUNT_MANAGER","客户经理","YB1"),
        FAMILY_RISK_MANAGER("FAMILY_RISK_MANAGER","家庭风险管理师","FX1"),
        SALES_MANAGER("SALES_MANAGER","营业部经理","YB2"),
        BUSINESS_DISTRICT_DIRECTOR("BUSINESS_DISTRICT_DIRECTOR","营业区总监","YB3"),
        ;


        private String value;
        private String label;
        private String rankSeq;


        AgentTypeEnum(String value,String label,String rankSeq){
            this.value = value;
            this.label = label;
            this.rankSeq = rankSeq;
        }

        public static AgentTypeEnum getEnumByValue(String value) {
            if (StringUtil.isEmpty(value)) {
                return null;
            }
            for (AgentTypeEnum agentTypeEnum : AgentTypeEnum.values()) {
                if (agentTypeEnum.getValue().equals(value)) {
                    return agentTypeEnum;
                }
            }
            return null;
        }


        public static AgentTypeEnum getEnumByRankSeq(String rankSeq) {
            if (StringUtil.isEmpty(rankSeq)) {
                return null;
            }
            for (AgentTypeEnum agentTypeEnum : AgentTypeEnum.values()) {
                if (agentTypeEnum.getRankSeq().equals(rankSeq)) {
                    return agentTypeEnum;
                }
            }
            return null;
        }


        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }

        public String getRankSeq() {
            return rankSeq;
        }
    }
}
