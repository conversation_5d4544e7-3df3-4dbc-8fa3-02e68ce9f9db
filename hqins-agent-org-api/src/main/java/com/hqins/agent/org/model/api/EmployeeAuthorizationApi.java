package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.AuthEmployeeVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@FeignClient(name = "hqins-agent-org")
public interface EmployeeAuthorizationApi {

    /**
     * 根据authId获取销售员信息
     *
     * @param authId 授权ID
     * @return 销售员授权信息
     */
    @ApiOperation("")
    @GetMapping("/agent-org/authorization/employee")
    @ResponseStatus(HttpStatus.OK)
    AuthEmployeeVO getEmployeeByAuthId(@RequestParam(value = "authId") String authId);

    /**
     * 根据执业证号获取销售员授权信息
     *
     * @param licenseNo 执业证号
     * @return 销售员授权信息
     */
    @ApiOperation("")
    @GetMapping("/agent-org/authorization/employee/license-no")
    @ResponseStatus(HttpStatus.OK)
    AuthEmployeeVO getEmployeeByLicenseNo(@RequestParam(value = "licenseNo") String licenseNo);

}
