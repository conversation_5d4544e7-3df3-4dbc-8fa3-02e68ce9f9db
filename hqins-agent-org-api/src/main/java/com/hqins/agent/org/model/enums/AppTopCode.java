package com.hqins.agent.org.model.enums;

/**
 * 个险团险校验
 * <AUTHOR>
 */

public enum AppTopCode {
    P00003("团险合伙人编码"),
    P00004("个险合伙人编码");
    private String label;

    AppTopCode(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }
    private static AppTopCode get(String name) {
        for (AppTopCode value : AppTopCode.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}