package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 考核项目VO类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentItemsVO implements Serializable {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("X(第一个数)")
    private Double x;

    @ApiModelProperty("y(第二个数)")
    private Double y;

    @ApiModelProperty("x单位")
    private String xUnit;

    @ApiModelProperty("y单位")
    private String yUnit;

    @ApiModelProperty("是否可以跳转")
    private Boolean canJump;

    @ApiModelProperty("是否合格")
    private Boolean isQualified;

    @ApiModelProperty("考核项code码")
    private String code;
}
