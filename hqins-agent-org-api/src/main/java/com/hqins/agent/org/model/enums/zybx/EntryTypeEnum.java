package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2022/12/6 6:39 上午
 * 入口类型
 */
public enum EntryTypeEnum {
    IHP("IHP", "ihomePro"),
    PRD("PRD", "产品中心"),
    YBT("YBT", "银保通"),
    CBT("CBT", "藏宝图");

    private String code;
    private String label;

    EntryTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
