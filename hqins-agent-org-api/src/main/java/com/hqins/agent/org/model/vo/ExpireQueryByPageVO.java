package com.hqins.agent.org.model.vo;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Api("满期保单查询-分页")
public class ExpireQueryByPageVO implements Serializable {
private static final long serialVersionUID = 1L;
    private String contNo;
    private String policyNo;
    private String insuredName;
    private String insuredIdNo;
    private String insuredMobile;
    private String agentName;
    private String agentCode;
    private String agentMobile;
    private String agentOrgName;
    private String agentOrgCode;
    private String agentOrgMobile;
    private String agentOrgType;
    private String agentOrgLevel;
    private String agentOrgProvince;
    private String agentOrgCity;
    private String agentOrgCounty;
    private String agentOrgTown;
    private String agentOrgVillage;
    private String agentOrgAddress;
    private String agentOrgPostcode;
    private String agentOrgEmail;
    private String agentOrgFax;
    private String agentOrgPhone;
    private String agentOrgLegalName;
    private String agentOrgLegalIdNo;
    private String agentOrgLegalMobile;
    private String agentOrgLegalEmail;
    private String agentOrgLegalFax;
    private String agentOrgLegalPhone;
    private String agentOrgLegalPostcode;
    private String agentOrgLegalAddress;
    private String agentOrgLegalProvince;
    private String agentOrgLegalCity;
    private String agentOrgLegalCounty;
    private String agentOrgLegalTown;
    private String agentOrgLegalVillage;
    private String agentOrgLegalIdType;
    private String agentOrgLegalSex;
    private String agentOrgLegalBirthday;
    private String agentOrgLegalNation;
    private String agentOrgLegalMarriage;
    private String agentOrgLegalEducation;
    private String agentOrgLegalPolitical;
    private String agentOrgLegalOccupation;
    private String agentOrgLegalWorkUnit;
    private String agentOrgLegalWorkUnitType;
    private String agentOrgLegalWorkUnitAddress;
    private String agentOrgLegalWorkUnitPhone;
    private String agentOrgLegalWorkUnitPostcode;
    private String agentOrgLegalWorkUnitFax;
    private String agentOrgLegalWorkUnitEmail;
    private String agentOrgLegalWorkUnitProvince;
    private String agentOrgLegalWorkUnitCity;
    private String agentOrgLegalWorkUnitCounty;
    private String agentOrgLegalWorkUnitTown;
    private String agentOrgLegalWorkUnitVillage;
    private String agentOrgLegalWorkUnitIndustry;
    private String agentOrgLegalWorkUnitPosition;
    private String agentOrgLegalWorkUnitJobTitle;
    private String agentOrgLegalWorkUnitJobTitleLevel;
}
