package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.OrgStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@ApiModel("合伙人销售机构对象")
@Data
public class PartnerOrgVO {

    @ApiModelProperty("销售机构id")
    private String id;

    @ApiModelProperty("销售机构代码")
    private String code;

    @ApiModelProperty("销售机构名称")
    private String name;

    @ApiModelProperty("合伙人代码")
    private String partnerCode;

    @ApiModelProperty("合伙人名称")
    private String partnerName;

    @ApiModelProperty("机构状态(ENABLED-有效 DISABLED-失效)")
    private OrgStatus status;

}
