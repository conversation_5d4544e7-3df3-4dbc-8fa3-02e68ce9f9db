package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/30
 */
@Data
public class HonorPeriodMetrics {

    @ApiModelProperty(value = "当前排名", example = "当前排名")
    private String currentPosition;

    @ApiModelProperty(value = "是否获奖", example = "是否获奖")
    private String isAchievedCurrent;

    @ApiModelProperty(value = "是否入围", example = "是否入围")
    private String isQualified;

    @ApiModelProperty(value = "总入围人数", example = "总入围人数")
    private String totalQualifiedCount;

    @ApiModelProperty(value = "荣誉表彰", example = "荣誉表彰")
    private String honorsAwards;

    @ApiModelProperty(value = "荣誉表彰达成时间", example = "荣誉表彰达成时间")
    private String honorsAwardsTime;

    @ApiModelProperty(value = "荣誉项", example = "荣誉项")
    private String honorCode;

    @ApiModelProperty(value = "贺报ID", example = "贺报ID")
    private String honorId;


    @ApiModelProperty(value = "代理人工号", example = "代理人工号")
    private String agentCode;

    @ApiModelProperty(value = "指标类型", example = "指标类型")
    private String metricsType;

    @ApiModelProperty(value = "指标类型名称", example = "指标类型名称")
    private String metricsTypeName;

    @ApiModelProperty(value = "指标值", example = "指标值")
    private String metricsValue;

    @ApiModelProperty(value = "指标达标标准", example = "指标达标标准")
    private String metricsStandard;

    @ApiModelProperty(value = "指标达标标准单位", example = "指标达标标准单位")
    private String metricsStandardUnit;

    @ApiModelProperty(value = "考核周期", example = "考核周期")
    private String checkPeriod;

    @ApiModelProperty(value = "指标月份", example = "指标月份")
    private String metricsMonth;
}
