package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 下辖人员信息VO类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentEmployeeInfoVO {

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("归属团队名称")
    private String orgName;

    @ApiModelProperty("归属团队Code")
    private String orgCode;

    @ApiModelProperty("归属团队类型（渠道商、合伙人）")
    private String orgType;

    @ApiModelProperty("最终考核结果")
    private String finalResult;

    @ApiModelProperty("职级序列")
    private String rankSeqCode;
}
