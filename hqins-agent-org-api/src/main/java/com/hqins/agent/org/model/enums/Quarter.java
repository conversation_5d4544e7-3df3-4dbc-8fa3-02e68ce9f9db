package com.hqins.agent.org.model.enums;

/**
 * 季度枚举
 */
public enum Quarter {

    FIRST_QUARTER(3, 1),
    SECOND_QUARTER(6, 4),
    THIRD_QUARTER(9, 7),
    FOURTH_QUARTER(12, 10);

    private final int endMonth;
    private final int startMonth;

    /**
     * 构造函数现在接受季度的结束月份和开始月份
     * @param endMonth      季度最终月份
     * @param startMonth    季度初始月份
     */
    Quarter(int endMonth, int startMonth) {
        this.endMonth = endMonth;
        this.startMonth = startMonth;
    }

    public int getEndMonth() {
        return endMonth;
    }

    public int getStartMonth() {
        return startMonth;
    }

    /**
     * 根据给定的月份返回对应的季度枚举。
     * @param month 月份（1-12）
     * @return 季度枚举
     */
    public static Quarter fromMonth(int month) {
        for (Quarter quarter : values()) {
            if (month >= quarter.startMonth && month <= quarter.endMonth) {
                return quarter;
            }
        }
        throw new IllegalArgumentException("Invalid month: " + month);
    }
}
