package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @Date 2024/7/2 09:25
 */
@ApiModel("登录类型")
public enum LoginType {
    /**
     * 组织机构类型
     */
    EMP_CODE("工号登录"),
    MOBILE("手机号登录");

    private String label;

    LoginType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static LoginType get(String name) {
        for (LoginType value : LoginType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
