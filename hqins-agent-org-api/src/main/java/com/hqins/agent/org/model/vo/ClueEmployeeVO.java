package com.hqins.agent.org.model.vo;

import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/5/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClueEmployeeVO implements Serializable {

    private static final long serialVersionUID = 1615532727484654096L;

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("销售员状态")
    private String status;

    @ApiModelProperty("归属渠道商或合伙人代码")
    private String topCode;

    @ApiModelProperty("归属渠道商或合伙人名称")
    private String topName;

    @ApiModelProperty("归属机构代码")
    private String orgCode;

    @ApiModelProperty("归属机构名称")
    private String orgName;

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商")
    private AgentOrgType orgType;

}
