package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentItemVO {
    @ApiModelProperty("13个月继续率")
    private Double m13Cr;

    @ApiModelProperty("25个月继续率")
    private Double m25Cr;

    @ApiModelProperty("数字化行为积分")
    private Double digitalBehavioralPoints;

    @ApiModelProperty("家庭账户扩展数")
    private Double householdAccountExtensions;

    @ApiModelProperty("营业部现有人力")
    private Double deptCurrentPersonNum;

    @ApiModelProperty("合格率")
    private Double agentPassRate;
}
