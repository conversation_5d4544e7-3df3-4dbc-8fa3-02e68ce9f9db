package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.request.ChannelEmployeeAddRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/6/5
 * @Description
 */
@ApiModel("渠道商销售员检测结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelEmployeeCheckVo extends ChannelEmployeeAddRequest {

    @ApiModelProperty("检测结果 true-成功，false-失败")
    private boolean success;

    @ApiModelProperty("结果描述")
    private String description;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
