package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.request.ExpireQueryListRequest;
import com.hqins.agent.org.model.vo.PolicyExpireVO;
import com.hqins.common.base.annotations.CollectionElement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2023/12/14 11:14
 */
@FeignClient(name = "hqins-agent-org")
public interface PolicyExpireApi {

    /**
     * 查询数仓满期保单详情
     * @return
     */
    @CollectionElement(targetClass = PolicyExpireVO.class)
    @GetMapping("/agent-org/policy/getPolicyExpire")
    PolicyExpireVO getPolicyExpire(@RequestBody ExpireQueryListRequest request);
}
