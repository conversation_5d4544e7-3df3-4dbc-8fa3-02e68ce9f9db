package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/5/20
 * @Description
 */
@ApiModel("合伙人销售员分页查询请求")
@Getter
@Setter
@SuperBuilder
public class PartnerEmployeeRequest extends PageQueryRequest {

    @ApiModelProperty("归属合伙人名称")
    private String partnerName;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("归属销售机构代码")
    private String orgCode;

    @ApiModelProperty("归属销售团队名称")
    private String teamName;

    @ApiModelProperty("人员代码")
    private String code;

    @ApiModelProperty("证件号")
    private String idCode;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("入职日期-开始")
    private LocalDate entryTimeStart;

    @ApiModelProperty("入职日期-结束")
    private LocalDate entryTimeEnd;

    @ApiModelProperty("离职日期-开始")
    private LocalDate quitTimeStart;

    @ApiModelProperty("离职日期-结束")
    private LocalDate quitTimeEnd;

    @ApiModelProperty("查询起始数")
    private Long start;

    @ApiModelProperty("代理人状态")
    private String status;



}
