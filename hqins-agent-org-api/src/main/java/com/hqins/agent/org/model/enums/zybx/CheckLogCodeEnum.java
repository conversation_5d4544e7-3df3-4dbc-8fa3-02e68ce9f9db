package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2023/9/18 10:51 上午
 */
public enum CheckLogCodeEnum {

    //WHITE_LIST 白名单用户;SUCCESS 通过, FAIL 不通过

    WHITE_LIST("WHITE_LIST", "白名单用户"),
    SUCCESS("SUCCESS", "通过"),
    FAIL("FAIL", "不通过");

    private String code;
    private String label;

    CheckLogCodeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
