package com.hqins.agent.org.model.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/12/31
 * 合伙人人员指派关系
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class PartassignmanagerVO implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "pkid")
    private String pkid;

    /**
     * 商户组织机构代码
     */
    private String merchantOrgCode;

    /**
     * 商户组织机构名称
     */
    private String merchantOrgName;

    /**
     * 客户经理代码
     */
    private String custManagerCode;

    /**
     * 客户经理名称
     */
    private String custManagerName;

    /**
     * 是否是主客户经理
     */
    private String mainManagerFlag;
}
