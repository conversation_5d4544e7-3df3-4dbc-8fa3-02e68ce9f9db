package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentTableValueVO {

    @ApiModelProperty("标题编码")
    private String titleCode;

    @ApiModelProperty("标题名称")
    private String correspondingValue;

    @ApiModelProperty("跳转类型")
    private String jumpType;

    @ApiModelProperty("跳转内容")
    private List<AssessmentEmployeeInfoVO> jumpData;
}
