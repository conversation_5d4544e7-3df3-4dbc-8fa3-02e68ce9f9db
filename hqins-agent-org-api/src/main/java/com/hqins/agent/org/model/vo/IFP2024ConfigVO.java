package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;

@ApiModel("自营2024版个人信息指标项响应对象")
@Data
public class IFP2024ConfigVO {

    @ApiModelProperty("考核指标项值名称")
    private String configName;

    @ApiModelProperty("考核指标项值")
    private String configValue;

    @ApiModelProperty("是否完成")
    private Boolean whetherCompleted;

    @ApiModelProperty("指标项明细数据")
    private LinkedHashMap<String,String> detailVale;
}
