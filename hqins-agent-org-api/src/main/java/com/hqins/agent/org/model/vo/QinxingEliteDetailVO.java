package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @value
 * @create 2025/3/17
 */
@ApiModel("琴星精英会详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QinxingEliteDetailVO {

    @ApiModelProperty(value = "当前年度", example = "当前年度")
    private String year;

    @ApiModelProperty(value = "当前达成情况")
    private CurrentAchievementVO currentAchievement;

    @ApiModelProperty(value = "年度达成情况列表")
    private List<AnnualAchievementVO> annualAchievements;

    @ApiModelProperty(value = "勋章墙列表")
    private List<MedalVO> medalWall;

    // ================== 当前达成情况 ==================
    @ApiModel(value = "当前达成情况详情")
    @Data
    @Builder
    public static class CurrentAchievementVO {
        @ApiModelProperty(value = "当前会员等级", example = "钻石会员")
        private String currentLevel;

        @ApiModelProperty(value = "当前年度", example = "当前年度")
        private String year;

        @ApiModelProperty(value = "连续达成星数", example = "8")
        private Integer continuousStars;

        @ApiModelProperty(value = "历史最高连星记录", example = "15")
        private Integer highestRecord;

        @ApiModelProperty(value = "护盾卡数量", example = "3")
        private Integer shieldCard;
    }

    // ================== 年度达成情况 ==================
    @ApiModel(value = "年度达成情况")
    @Data
    @Builder
    public static class AnnualAchievementVO {
        @ApiModelProperty(value = "年份 (格式: yyyy)", example = "2023")
        private String year;

        @ApiModelProperty(value = "月度状态列表")
        private List<MonthStatusVO> months = new ArrayList<>();
    }

    // ================== 月度状态 ==================
    @ApiModel(value = "月度达成状态")
    @Data
    @Builder
    public static class MonthStatusVO {
        @ApiModelProperty(value = "月份 (1-12)")
        private String month;

        @ApiModelProperty(value = "达成状态 (0-未达成 1-已达成)")
        private String status;

        @ApiModelProperty(value = "是否使用护盾卡 0-未使用 1-使用", example = "0")
        private String isShieldCard;
    }

    // ================== 勋章详情 ==================
    @ApiModel(value = "勋章详情")
    @Data
    @Builder
    public static class MedalVO {
        @ApiModelProperty(value = "奖项名称", example = "初级会员")
        private String medalName;

        @ApiModelProperty(value = "获得时间 (yyyy-MM-dd)", example = "2023-07-15")
        private String achievedDate;

        @ApiModelProperty(value = "奖项状态 (0-未获得 1-已获得)", example = "1")
        private String status;

        @ApiModelProperty(value = "贺报ID")
        private String honorId;
    }
}