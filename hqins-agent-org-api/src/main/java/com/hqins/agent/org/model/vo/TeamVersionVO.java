package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TeamVersionVO implements Serializable {


    @ApiModelProperty("基本法类型 金莲花:'SELF_LOTUS'")
    private String  versionType;

    @ApiModelProperty("团队业绩数据")
    List<TeamPerformanceVO> teamPerformanceVOList;


}
