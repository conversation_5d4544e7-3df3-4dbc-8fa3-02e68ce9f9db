package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public enum EmployeeStatus {
    /**
     * 销售员状态  00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备
     * 产品确定
     * 有效 01
     * 离职 02、04
     */
    SERVING("有效"),
    LEAVING("离职"),
    INVALID("失效");

    private String label;

    EmployeeStatus(String label) {
        this.label = label;
    }

    public String getLabel() {
        return this.label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static EmployeeStatus get(String name) {
        for (EmployeeStatus value : EmployeeStatus.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
