package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.enums.TeamStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @date 2021/5/13
 * @Description
 */
@ApiModel("渠道商团队对象")
@Data
public class ChannelTeamVO {

    @ApiModelProperty("营销团队id")
    private Long id;

    @ApiModelProperty("营销团队代码")
    private String code;

    @ApiModelProperty("上级营销团队代码")
    private String parentCode;

    @ApiModelProperty("上级营销团队名称")
    private String parentName;

    @ApiModelProperty("归属渠道商代码")
    private String channelCode;

    @ApiModelProperty("归属渠道商名称")
    private String channelName;

    @ApiModelProperty("营销团队名称")
    private String name;

    @ApiModelProperty("团队级别")
    private TeamLevel level;

    @ApiModelProperty("生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty("归属机构代码")
    private String orgCode;

    @ApiModelProperty("归属机构名称")
    private String orgName;

    @ApiModelProperty("团队主管名称")
    private String leader;

    @ApiModelProperty("主管工号")
    private String leaderCode;

    @ApiModelProperty("营销团队状态")
    private TeamStatus status;

}
