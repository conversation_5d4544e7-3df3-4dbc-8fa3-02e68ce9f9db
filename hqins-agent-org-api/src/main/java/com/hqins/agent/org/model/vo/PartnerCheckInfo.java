package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/7 10:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerCheckInfo {

    @ApiModelProperty("基本法Id")
    private String basicLawId;

    @ApiModelProperty("基本法类型")
    private String basicLawType;

    @ApiModelProperty("基本法名称")
    private String basicLawName;

    @ApiModelProperty("机构代码")
    private String  instCode;

    @ApiModelProperty("机构名称")
    private String  instName;

    @ApiModelProperty("团队代码")
    private String  teamCode;

    @ApiModelProperty("团队名称")
    private String  teamName;

    @ApiModelProperty("代理人代码")
    private String  agentCode;

    @ApiModelProperty("代理人名称")
    private String  agentName;

    @ApiModelProperty(value = "职级代码")
    private String rankCode;

    @ApiModelProperty(value = "职级名称")
    private String rankName;

    @ApiModelProperty(value = "个人FYC维持考核标准")
    private String partnerMaintainFycStandard;

    @ApiModelProperty(value = "个人FYC实际达成")
    private String partnerKeepActualStp;

    @ApiModelProperty(value = "个人FYC距离达标差距")
    private String partnerDistanceGap;

    @ApiModelProperty(value = "事务所FYC维持考核标准")
    private String agencyMaintainStandardFyc;

    @ApiModelProperty(value = "事务所FYC实际达成")
    private String agencyMaintainActualFyc;

    @ApiModelProperty(value = "事务所FYC距离达标差距")
    private String agencyDistanceGap;

    @ApiModelProperty(value = "中间表主键")
    private String tmpId;

    @ApiModelProperty(value = "考核初算结果")
    private String checkSettleResult;

    @ApiModelProperty(value = "考核期")
    private String checkMonth;
}
