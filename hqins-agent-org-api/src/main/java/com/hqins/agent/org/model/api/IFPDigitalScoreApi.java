package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.DigitalDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreAllDetailVO;
import com.hqins.agent.org.model.vo.DigitalScoreCodeVO;
import com.hqins.agent.org.model.vo.InsureOrgVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 17:26
 */
@FeignClient(name = "hqins-agent-org")
public interface IFPDigitalScoreApi {

    @ApiOperation(value = "代理人IFP全量月份和积分数据查询")
    @GetMapping("/agent-org/digital-score/all-month-score")
    @CollectionElement(targetClass = DigitalDataVO.class)
    List<DigitalDataVO> getAllMonthAndScoreData(
            @ApiParam("代理人工号") @RequestParam(value = "agentCode") String agentCode,
            @ApiParam("开始年月 2023-01") @RequestParam(value = "startDate") String startDate);


    @ApiOperation(value = "IFP积分查询")
    @GetMapping("/agent-org/digital-score/score")
    @CollectionElement(targetClass = DigitalScoreCodeVO.class)
    List<DigitalScoreCodeVO> getScore(
            @ApiParam("代理人工号集合") @RequestParam(value = "agentCodeList") List<String> agentCodeList,
            @ApiParam("查询年月 2023-01") @RequestParam(value = "queryDate") String queryDate);

    @ApiOperation(value = "IFP积分明细查询")
    @GetMapping("/agent-org/digital-score/score-detail")
    @CollectionElement(targetClass = DigitalScoreAllDetailVO.class)
    List<DigitalScoreAllDetailVO> getScoreDetail(
            @ApiParam("代理人工号集合") @RequestParam(value = "agentCodeList") List<String> agentCodeList,
            @ApiParam("查询年月 2023-01") @RequestParam(value = "queryDate") String queryDate,
            @ApiParam("是否可记分 Y:是 N:否 不传查全部") @RequestParam(value = "recordPointsFlag", required = false) String recordPointsFlag);
}
