package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.request.SelectSupervisorEmployeeListPostRequest;
import com.hqins.agent.org.model.vo.SupervisorEmployeeVO;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 督导账号API
 */
@FeignClient(name = "hqins-agent-org")
public interface SupervisorEmployeeApi {

    @ApiOperation(value = "多条件查询督导账号")
    @CollectionElement(targetClass = SupervisorEmployeeVO.class)
    @GetMapping("/agent-org/supervisor/supervisorEmps")
    List<SupervisorEmployeeVO> selectSupervisorEmployeeList(@ApiParam("账号类型") @RequestParam(value = "roleType", required = false) SupervisorType roleType,
                                          @ApiParam("机构编码") @RequestParam(value = "orgCode", required = false) String orgCode,
                                          @ApiParam("人员代码") @RequestParam(value = "employeeCode", required = false) String employeeCode,
                                          @ApiParam("手机号") @RequestParam(value = "mobile", required = false) String mobile);

    /**
     * 查询督导账号POST 与ORG的selectSupervisorEmployeeList能力相同 请求方式不同
     */
    @ApiOperation(value = "多条件查询督导账号")
    @CollectionElement(targetClass = SupervisorEmployeeVO.class)
    @PostMapping("/agent-org/supervisor/supervisorEmps-post")
    List<SupervisorEmployeeVO> selectSupervisorEmployeeListPost(@RequestBody SelectSupervisorEmployeeListPostRequest request);

}
