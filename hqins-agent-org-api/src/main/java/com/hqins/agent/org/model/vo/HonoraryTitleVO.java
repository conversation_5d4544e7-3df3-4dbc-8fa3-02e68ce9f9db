package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/12
 */
@ApiModel("团队成员荣誉信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HonoraryTitleVO implements Serializable {

    @ApiModelProperty("贺报列表")
    private Map<String, List<HonoraryTitleInfoVO>> honoraryTitleInfoMap;

}
