package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.ChannelCompanyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * @Author: lijian
 * @Date: 2023/8/23 9:24 上午
 */
@ApiModel("渠道商员工删除请求")
@Data
public class ChannelEmployeeDeleteRequest {

    @ApiModelProperty("销售员执业证书编号")
    private String licenseNo;

    @ApiModelProperty("销售员公司")
    private ChannelCompanyEnum channelCompany;
}
