package com.hqins.agent.org.model.enums;

import com.hqins.common.utils.StringUtil;


public enum CheckItemEnum {

    check_acc_premium("check_acc_premium", "业绩达成"),
    month_average_premium("month_average_premium", "月均标准保费"),

    reduce_level2_standard("reduce_level2_standard", "降低2级考核标准"),
    reduce_level1_standard("reduce_level1_standard", "降低1级考核标准"),
    keep_level_standard("keep_level_standard", "维持考核标准"),
    promoted_level1_standard("promoted_level1_standard", "晋升1级考核标准"),
    promoted_level2_standard("promoted_level2_standard", "晋升2级考核标准"),
    quit_standard("quit_standard", "清退标准"),

    dept_lowest_person_num("dept_lowest_person_num", "营业部最低人力要求"),
    dept_current_person_num("dept_current_person_num", "下辖在职人力"),
    dept_person_num_meet_flag("dept_person_num_meet_flag", "营业部人力是否达标"),
    attend_check_agent_num("attend_check_agent_num", "参与考核客户经理人数"),
    check_pass_agent_num("check_pass_agent_num", "考核合格客户经理人数"),
    agent_pass_rate("agent_pass_rate", "合格率"),
    agent_pass_rate_meet_flag("agent_pass_rate_meet_flag", "合格率是否达标(客户经理)"),
    area_lowest_person_num("area_lowest_person_num", "营业区最低人力要求"),
    area_current_person_num("area_current_person_num", "营业区现有人力"),
    area_person_num_meet_flag("area_person_num_meet_flag", "营业区人力是否达标"),
    area_lowest_dept_num("area_lowest_dept_num", "营业部要求数量"),
    area_current_dept_num("area_current_dept_num", "营业部现有数量"),
//    dept_num_meet_flag("dept_num_meet_flag", "最低营业部数量是否达标"),
    attend_dept_check_num("attend_dept_check_num", "参与考核营业部数量"),
    check_dept_pass_num("check_dept_pass_num", "考核合格营业部数量"),
    dept_pass_rate("dept_pass_rate", "达标营业部数占比"),
//    dept_pass_rate_meet_flag("dept_pass_rate_meet_flag", "达标营业部数占比是否达标"),
    m13_need_receive_premium("m13_need_receive_premium", "13个月应收保费"),
    m13_actual_receive_premium("m13_actual_receive_premium", "13个月实收保费"),
    m13_cr("m13_cr", "13个月继续率"),
    m13_cr_check_complete("m13_cr_check_complete", "13个月继续率考核是否达标"),
    m25_cr_check_complete("m25_cr_check_complete", "25个月继续率考核是否达标"),
//    m13_cr_check_result("m13_cr_check_result", "13个月继续率考核情况"),
    m25_need_receive_premium("m25_need_receive_premium", "25个月应收保费"),
    m25_actual_receive_premium("m25_actual_receive_premium", "25个月实收保费"),
    monthly_performance("monthly_performance", "每月业绩"),
    digital_behavioral_points("digital_behavioral_points", "数字化行为积分"),
    household_account_extensions("household_account_extensions", "家庭账户扩展数"),
    m25_cr("m25_cr", "25个月继续率")
//    m25_cr_check_result("m25_cr_check_result", "25个月继续率考核情况")
    ;


    private String value;
    private String label;

    public static String getLabelByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return "";
        }
        for (CheckItemEnum feeItem : CheckItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem.getLabel();
            }
        }
        return "";
    }

    CheckItemEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static CheckItemEnum getEnumByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (CheckItemEnum feeItem : CheckItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
