package com.hqins.agent.org.model.enums;

import lombok.Getter;

/**
 * 基本法主类型枚举.
 *
 * <AUTHOR> MXH
 * @create 2025/3/17 17:44
 */
@Getter
public enum BasicLawType {

    SELF_2024("自营2024版"),
    SELF_LOTUS("自营金莲花版");

    private final String desc;

    BasicLawType(String desc) {
        this.desc = desc;
    }

    public String getDesc() { return this.desc; }

    private static BasicLawType get(String name) {
        for (BasicLawType value : BasicLawType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
