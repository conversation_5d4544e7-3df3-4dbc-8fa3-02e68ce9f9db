package com.hqins.agent.org.model.enums;

/**
 * 自营2024-职级与职级序列枚举类
 */
public enum IFP2024RankEnum {
    FHWC ("FHWC", "家庭健康财富顾问"),
    SWS ("SWS", "家庭健康财富事务所合伙人"),
    FHWC01 ("FHWC01", "见习顾问"),
    FHWC02 ("FHWC02", "初级顾问"),
    FHWC03 ("FHWC03", "中级顾问"),
    FHWC04 ("FHWC04", "高级顾问"),
    SWS01 ("SWS01", "合伙人"),
    SWS02 ("SWS02", "高级合伙人");



    private String value;
    private String label;


    IFP2024RankEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getValueByLabel(String label) {
        for (IFP2024RankEnum levelEnum : IFP2024RankEnum.values()) {
            if (levelEnum.getLabel().equals(label)) {
                return levelEnum.getValue();
            }
        }
        return null;
    }

    public static boolean exists(String value) {
        for (IFP2024RankEnum level : IFP2024RankEnum.values()) {
            if (level.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String getLabelByValue(String value) {
        for (IFP2024RankEnum levelEnum : IFP2024RankEnum.values()) {
            if (levelEnum.getValue().equals(value)) {
                return levelEnum.getValue();
            }
        }
        return null;
    }
}
