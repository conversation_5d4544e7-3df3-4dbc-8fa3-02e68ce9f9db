package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentWarningResultVO  implements Serializable {

    @ApiModelProperty("当期/上期")
    private AssessmentWarningVO result;

    @ApiModelProperty("上期/当期/ALL")
    private String previousOrCurrentFlag;
}
