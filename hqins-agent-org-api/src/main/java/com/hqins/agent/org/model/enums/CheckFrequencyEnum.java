package com.hqins.agent.org.model.enums;


import com.hqins.common.utils.StringUtil;

/**
 * @Author: lijian
 * @Date: 2023/5/6 4:05 下午
 */
public enum CheckFrequencyEnum {

    QUATER_CHECK("QUATER_CHECK", "按季度"),
    HALF_YEAR_CHECK("HALF_YEAR_CHECK", "按半年");

    private String value;
    private String label;


    CheckFrequencyEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static CheckFrequencyEnum getCheckFrequencyEnumByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (CheckFrequencyEnum feeItem : CheckFrequencyEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem;
            }
        }
        return null;
    }
}
