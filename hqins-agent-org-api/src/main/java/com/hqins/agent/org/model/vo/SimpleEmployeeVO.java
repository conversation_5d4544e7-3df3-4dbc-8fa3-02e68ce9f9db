package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SimpleEmployeeVO {

    @ApiModelProperty("UM id")
    private Long agentId;

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商")
    private AgentOrgType orgType;

    @ApiModelProperty("归属团队名称")
    private String teamName;

    @ApiModelProperty("销售员状态")
    private EmployeeStatus status;

}
