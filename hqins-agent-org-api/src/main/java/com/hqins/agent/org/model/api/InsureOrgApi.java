package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.InsureOrgMgrVO;
import com.hqins.agent.org.model.vo.InsureOrgVO;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/27
 */
@FeignClient(name = "hqins-agent-org")
public interface InsureOrgApi {

    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCode
     * @return
     */
    @GetMapping("/agent-org/insure/org")
    InsureOrgVO getInsureOrg(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                    @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode);
    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCodes
     * @return
     */
    @PostMapping("/agent-org/insure/orgs")
    @CollectionElement(targetClass = InsureOrgVO.class)
    List<InsureOrgVO> getInsureOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                    @ApiParam("机构代码") @RequestBody List<String> orgCodes);

    /**
     *a端商品配置导出查询机构
     */
    @GetMapping("/agent-org/insure/all2")
    @CollectionElement(targetClass = InsureOrgMgrVO.class)
    List<InsureOrgMgrVO> getInsureOrgs2(
            @ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
            @ApiParam("合伙人、渠道商代码") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("合伙人、渠道商名称") @RequestParam(value = "topName", required = false) String topName,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构层级：00-总行/总公司 01-省分/分公司 02-市分 03-支行 04-网点") @RequestParam(value = "orgLevels", required = false) List<String> orgLevels,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size);

}
