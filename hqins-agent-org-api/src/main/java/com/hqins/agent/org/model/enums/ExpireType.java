package com.hqins.agent.org.model.enums;

/**
 * <AUTHOR>
 * @Date 2023/12/12 14:33
 */
public enum ExpireType {
    /**
     * 组织机构类型
     */
    UPCOMING("即将满期"),
    EXPIRE("已满期");

    private String label;

    ExpireType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static ExpireType get(String name) {
        for (ExpireType value : ExpireType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
