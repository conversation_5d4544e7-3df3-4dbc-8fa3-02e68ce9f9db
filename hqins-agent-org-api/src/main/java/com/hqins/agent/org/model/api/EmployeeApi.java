package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.request.AllEmpByPageRequest;
import com.hqins.agent.org.model.vo.ChannelEmployeeCheckVo;
import com.hqins.agent.org.model.vo.ClueEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.SimpleEmployeeVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@FeignClient(name = "hqins-agent-org")
public interface EmployeeApi {

    /**
     * 根据工号获取销售员详情，
     * 目前查询渠道商下代理人未区分代理人是否离职
     * 但查询合伙人下代理人区分代理人是否离职了
     *
     * @param orgType
     * @param employeeCode
     * @return
     */
    @GetMapping("/agent-org/employees/new")
    EmployeeVO getEmployeeInfo(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                               @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode);  /**
     * 根据工号获取销售员详情， 永达理在核心记录的是永达理自己的工号，这块需要额外处理
     *
     * @param orgType
     * @param employeeCode
     * @return
     */
    @GetMapping("/agent-org/employees/new-ydl")
    EmployeeVO getEmployeeInfoByYDL(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                               @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode);

    @ApiOperation("根据手机号获取销售员详情")
    @GetMapping("/agent-org/employees/queryByMobile")
    EmployeeVO queryByMobile(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                             @ApiParam("销售员手机号") @RequestParam(value = "mobile") String mobile);


    /**
     * 根据工号获取销售员详情,不区分代理人是否离职在职
     *
     * @param orgType
     * @param employeeCode
     * @return
     */
    @GetMapping("/agent-org/employees/info")
    EmployeeVO getEmployeeByEmployeeCode(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType", required = false) AgentOrgType orgType,
                                         @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode);

    /**
     * 更新渠道商销售员执业证编号
     *
     * @param employeeCode
     * @param licenseNo
     * @return
     */
    @PutMapping("/agent-org/channel/employees/license")
    ChannelEmployeeCheckVo updateLicenseNo(@ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode,
                                           @ApiParam("执业证编号") @RequestParam(value = "licenseNo") String licenseNo);

    /**
     * 获取所有销售员
     *
     * @return
     */
    @GetMapping("/agent-org/employees/allemployees")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> allEmployees();

    /**
     *分页查询所有代理人信息
     * by page & by orgType
     */
    @PostMapping("/agent-org/employees/all-employees-by-page")
    PageInfo<EmployeeVO> allEmployeesByPageAndType(@RequestBody AllEmpByPageRequest request);



        /**
         * 获取销售员信息
         *
         * @param partnerCodes
         * @param channelCodes
         * @param value
         * @param status
         * @return
         */
    @GetMapping("/agent-org/employees/simple-no-agent")
    @ApiOperation("获取销售员")
    @ResponseStatus(HttpStatus.OK)
    @CollectionElement(targetClass = SimpleEmployeeVO.class)
    List<SimpleEmployeeVO> listEmployeeSimpleCopy(@ApiParam("合伙人编码数组") @RequestParam(value = "partnerCodes", required = false) String[] partnerCodes,
                                                  @ApiParam("渠道商编码数组") @RequestParam(value = "channelCodes", required = false) String[] channelCodes,
                                                  @ApiParam("销售员名称或者代码") @RequestParam(value = "value") String value,
                                                  @ApiParam("状态：SERVING、ALL") @RequestParam(value = "status", required = false, defaultValue = "SERVING") String status);

    /**
     * 根据公司代码-获取所有有效的销售员
     *
     * @param companycode 公司代码
     * @return Map
     */
    @GetMapping("/agent-org/employees/company/employees")
    @CollectionElement(targetClass = EmployeeVO.class)
    Map<String, EmployeeVO> allEmployeesByCompanycode(@ApiParam("公司代码") @RequestParam(value = "companycode") String companycode);

    /**
     * 根据公司代码-获取所有的销售员
     *
     * @param companycode 公司代码
     * @param status      人员状态
     * @return List
     */
    @GetMapping("/agent-org/employees/company/allEmployees")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> allEmployeesByCompanycodeAndStatus(
            @ApiParam("公司代码") @RequestParam(value = "companycode", required = false) String companycode,
            @ApiParam("销售员状态 SERVING-有效 INVALID-无效 null查全部") @RequestParam(value = "status", required = false) String status);


    /**
     * 根据组织机构代理、人员状态查询渠道商人员信息
     *
     * @param channelCode 渠道商代码
     * @param status      人员状态
     * @return
     */
    @GetMapping("/agent-org/channel/employees/all")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> getChannelEmployeeByChannelCodeAndStatus(
            @ApiParam("渠道商代码") @RequestParam(value = "channelCode", required = false) String channelCode,
            @ApiParam("销售员状态 SERVING-有效 LEAVING-离职 null查全部") @RequestParam(value = "status", required = false) String status);


    /**
     * 查询销售员主管
     *
     * @param employeeCode 代理人工号
     * @return EmployeeVO
     */
    @GetMapping("/agent-org/employees/leader")
    @CollectionElement(targetClass = EmployeeVO.class)
    EmployeeVO queryEmployeeLeader(@ApiParam("销售员employeeCode") @RequestParam(value = "employeeCode") String employeeCode);


    /**
     * 根据代理人工号集合、组织机构类型查询代理人状态信息
     *
     * @param employeeCodeList 代理人工号集合
     * @param orgType          组织机构类型
     * @return EmployeeVO
     */
    @GetMapping("/agent-org/employees/clue")
    @CollectionElement(targetClass = ClueEmployeeVO.class)
    List<ClueEmployeeVO> getByEmployeeCodeList(@ApiParam("代理工号集合") @RequestParam(value = "employeeCodeList") List<String> employeeCodeList,
                                               @ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") String orgType);




    @GetMapping("/agent-org/employees/team/getBirthDayEmpList")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> getTeamBirthDayList(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode", required = false) String saleTeamCode,
                                                           @ApiParam("开始日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "startDate") Date startDate,
                                                           @ApiParam("结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "endDate") Date endDate);


    @GetMapping("/agent-org/employees/getEmpInfo")
    @CollectionElement(targetClass = EmployeeVO.class)
    EmployeeVO getEmpInfo(@ApiParam("业务员代码") @RequestParam(value = "employeeCode") String employeeCode);

    /**
     *
     * 根据生日查询代理人
     * @param birthdayList  生日集合
     * @return              代理人信息
     */
    @GetMapping("/agent-org/employees/queryByBirthday")
    @CollectionElement(targetClass = EmployeeVO.class)
    List<EmployeeVO> getEmployeeInfoByBirthday(@RequestParam(value = "birthdayList") List<String> birthdayList);
}
