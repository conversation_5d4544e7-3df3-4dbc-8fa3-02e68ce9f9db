package com.hqins.agent.org.model.enums;

/**
 * 考核预警参数类型
 */
public enum EmployeeAssessmentParamEnum {

    CURRENT_PERIOD("1","当期"),
    PREVIOUS_PERIOD("2","上期");

    private String code;

    private String label;

    EmployeeAssessmentParamEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static EmployeeAssessmentParamEnum get(String name) {
        for (EmployeeAssessmentParamEnum value : EmployeeAssessmentParamEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static EmployeeAssessmentParamEnum getLabelByCode(String code) {
        for (EmployeeAssessmentParamEnum value : EmployeeAssessmentParamEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
