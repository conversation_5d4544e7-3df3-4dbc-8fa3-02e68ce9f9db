package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @value
 * @create 2025/3/17
 */
@ApiModel("琴韵博识轩详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QinyunScholarDetailVO {

    @ApiModelProperty(value = "当前赛期数据")
    private SeasonDataVO currentSeason;

    @ApiModelProperty(value = "上个赛期数据")
    private SeasonDataVO lastSeason;

    // ================== 单赛期数据 ==================
    @ApiModel(value = "单赛期综合数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeasonDataVO {
        @ApiModelProperty(value = "累计期交标保数据")
        private AccumulatedPremiumVO accumulatedPremium;

        @ApiModelProperty(value = "月度连续达成数据")
        private MonthlyContinuityVO monthlyContinuity;

        @ApiModelProperty(value = "贺报ID")
        private String honorId;

        @ApiModelProperty(value = "奖项达成状态 (0-未达成 1-已达成)", example = "1")
        private String status;

        @ApiModelProperty(value = "获奖年度")
        private String year;
    }

    // ================== 累计期交标保 ==================
    @ApiModel(value = "累计保费达成详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccumulatedPremiumVO {

        @ApiModelProperty(value = "当前保费", example = "15000")
        private String currentPrem;

        @ApiModelProperty(value = "达标基准保费", example = "180000")
        private String benchmarkPremium;

        @ApiModelProperty(value = "单位", example = "单位")
        private String metricsStandard;
    }

    // ================== 月度连续达成 ==================
    @ApiModel(value = "月度连续性成就")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyContinuityVO {

        @ApiModelProperty(value = "当前连续月数", example = "3")
        private String currentMonths;

        @ApiModelProperty(value = "月度状态明细")
        private List<MonthStatusVO> monthlyStatus;
    }

    // ================== 月度状态 ==================
    @ApiModel(value = "月度达成状态")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthStatusVO {
        @ApiModelProperty(value = "月份", example = "01")
        private String month;

        @ApiModelProperty(value = "达成状态 (0-未达成 1-已达成)", example = "1")
        private String status;
    }
}