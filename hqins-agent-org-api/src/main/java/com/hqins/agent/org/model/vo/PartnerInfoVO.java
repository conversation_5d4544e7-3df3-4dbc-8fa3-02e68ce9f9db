package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/7 8:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerInfoVO implements Serializable {

    @ApiModelProperty("维持达标人数")
    private String  partnerKeepSuccessCount;

    @ApiModelProperty("维持未达标")
    private String  partnerKeepFailedCount;

    @ApiModelProperty("合伙人业绩考核预警")
    private List<PartnerCheckInfo> partnerCheckInfoList;
}
