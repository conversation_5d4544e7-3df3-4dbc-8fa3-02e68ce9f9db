package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: lijian
 * @Date: 2023/9/11 3:34 下午
 */
public enum CheckResultEnum {

    ALL_SUCCESS(1, "全部成功"),
    PART_SUCCESS(2, "部分成功"),
    ALL_FAIL(0, "全部失败");

    private Integer value;
    private String label;

    CheckResultEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(Integer value) {
        this.value = value;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
