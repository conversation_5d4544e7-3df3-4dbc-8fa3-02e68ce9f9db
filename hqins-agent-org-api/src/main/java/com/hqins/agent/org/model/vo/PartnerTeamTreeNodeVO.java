package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.enums.TeamLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@ApiModel("合伙人团队对象")
@Data
public class PartnerTeamTreeNodeVO {

    @ApiModelProperty("所有下级团队")
    private List<PartnerTeamTreeNodeVO> children;

    @ApiModelProperty("营销团队代码")
    private String code;

    @ApiModelProperty("上级营销团队代码")
    private String parentCode;

    @ApiModelProperty("营销团队名称")
    private String name;

    @ApiModelProperty("团队级别")
    private TeamLevel level;

    @ApiModelProperty("归属机构代码")
    private String orgCode;

    @ApiModelProperty("归属机构名称")
    private String orgName;

    @ApiModelProperty("团队主管名称")
    private String leader;

    @ApiModelProperty("主管工号")
    private String leaderCode;

    @ApiModelProperty("营销团队状态")
    private TeamStatus status;

    @ApiModelProperty("最顶层归属的合伙人")
    private String  topCode;
}
