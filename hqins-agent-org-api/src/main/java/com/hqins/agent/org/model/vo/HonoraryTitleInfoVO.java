package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @description
 * @create 2025/3/12
 */
@ApiModel("团队成员荣誉奖项信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HonoraryTitleInfoVO implements Serializable {

    @ApiModelProperty("代理人工号")
    private String empCode;

    @ApiModelProperty("代理人名称")
    private String empName;

    @ApiModelProperty("贺报生成时间")
    private String createDate;

    @ApiModelProperty("贺报ID")
    private String honorId;

    @ApiModelProperty("奖项名称")
    private String awardName;

    @ApiModelProperty("奖项类别")
    private String type;

}
