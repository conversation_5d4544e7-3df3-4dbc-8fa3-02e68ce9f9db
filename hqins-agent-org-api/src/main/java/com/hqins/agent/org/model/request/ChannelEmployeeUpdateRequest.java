package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.annotations.Mobile;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/5/15
 * @Description
 */
@ApiModel("渠道商销售员更新请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelEmployeeUpdateRequest {

    @ApiModelProperty("销售员id")
    @NotNull(message = "销售员id不能为空")
    private Long id;

    @ApiModelProperty("销售员名称")
    @NotBlank(message = "销售员名称不能为空")
    private String name;

    @ApiModelProperty("销售员手机号")
    @NotNull(message = "手机号不能为空")
    @Mobile(message = "手机号格式错误")
    private String mobile;

    @ApiModelProperty("是否有万能险销售资格 true-有 false-没有")
    @NotNull(message = "万能险销售资格不能为空")
    private Boolean universalQualification;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    @NotNull(message = "性别不能为空")
    private Gender gender;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ApiModelProperty("执业证号启期")
    private LocalDate licenseStartDate;

    @ApiModelProperty("执业证号止期")
    private LocalDate licenseEndDate;

    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ApiModelProperty("人员类型  TESTER体验人员 FORMAL正式人员 ")
    private RoleType roleType;

}
