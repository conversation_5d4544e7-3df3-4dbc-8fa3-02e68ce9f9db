package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.request.CheckChannelEmployeeRequest;
import com.hqins.agent.org.model.vo.ChannelOrgVO;
import com.hqins.agent.org.model.vo.CheckEmployeeResultVO;
import com.hqins.agent.org.model.vo.SimpleNodeVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "hqins-agent-org")
public interface ChannelApi {

    @ApiOperation(value = "根据代理人编号查询")
    @GetMapping("/agent-org/channels/config-logo")
    String getLogo(@ApiParam("销售员代码") @RequestParam("employeeCode") String employeeCode);

    @ApiOperation(value = "获取团队长下所有的员工以及所属的渠道")
    @GetMapping("/agent-org/channels/list-sub-emp-channel")
    @CollectionElement(targetClass = ChannelOrgVO.class)
    List<ChannelOrgVO> listSubEmpChannel(
            @ApiParam("团队长工号") @RequestParam("teamLeaderCode") String teamLeaderCode,
            @ApiParam("是否包含团队长") @RequestParam(value = "includeLeader", required = false) boolean includeLeader);

    @ApiOperation("渠道代理人验证")
    @PostMapping("/agent-org/channel/employees/check-for-channel")
    @ResponseStatus(HttpStatus.OK)
    ApiResult<CheckEmployeeResultVO> checkForChannel(@Valid @RequestBody CheckChannelEmployeeRequest checkChannelEmployeeRequest);

    @ApiOperation("渠道代理人验证-执业证号")
    @PostMapping("/agent-org/channel/employees/check-for-channel-license")
    @ResponseStatus(HttpStatus.OK)
    ApiResult<CheckEmployeeResultVO> checkForChannelByLicense(@Valid @RequestBody CheckChannelEmployeeRequest checkChannelEmployeeRequest);

    @ApiOperation("渠道代理人缓存删除-执业证号")
    @GetMapping("/delete-redis-cache-license")
    @ResponseStatus(HttpStatus.OK)
    ApiResult<Void> deleteRedisCacheByLicense(@RequestParam("licenseNo") String employeeName, @RequestParam("employeeName") String licenseNo);


    /**
     * 获取渠道商简化版，只展示名称编码
     * 有管理权限的且生效状态的渠道商
     * 下拉框中使用
     */
    @ApiOperation("获取有管理权限的渠道商，简化版下拉框里使用")
    @GetMapping("/agent-org/channels/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    @CollectionElement(targetClass = SimpleNodeVO.class)
    PageInfo<SimpleNodeVO> listMySimple(
            @ApiParam("渠道商代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("渠道商名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam("每页显示记录数 小于0则返回全部数据") @RequestParam(value = "size", required = false, defaultValue = "10") long size);

}
