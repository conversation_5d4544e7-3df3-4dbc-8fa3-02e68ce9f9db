package com.hqins.agent.org.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("保单佣金信息")
public class PolicyCommissionVO implements Serializable {

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("佣金类型Code")
    private String commissionItem;

    @ApiModelProperty("佣金类型")
    private String commissionType;

    @ApiModelProperty("佣金金额")
    private BigDecimal amount;

    @ApiModelProperty("保单结算状态")
    private String settleFlag;

    @ApiModelProperty("保单结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate settleDate;

    @ApiModelProperty("险种号")
    private String riskCode;

    @ApiModelProperty("险种名称")
    private String riskName;

    List<PolicyCommissionVO> commissionVOList;

}
