package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("自营2024版个人信息页签响应对象")
@Data
public class IFP2024TabVO {

    @ApiModelProperty("页签码值 KEEP:维持类型 PROMOTION:晋升类型 PARTNER:晋升合伙人类型")
    private String tabCode;

    @ApiModelProperty("页签名称")
    private String tabName;

    @ApiModelProperty("考核期")
    private String checkMonth;

    @ApiModelProperty("页签状态:已达标/未达标")
    private String tabStatus;

    @ApiModelProperty("进度条")
    private IFP2024ProgressBarVO progressBarVO;

    @ApiModelProperty("考核指标项集合")
    private List<IFP2024ConfigVO> checkConfigList;
}
