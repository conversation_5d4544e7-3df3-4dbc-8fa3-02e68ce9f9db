package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.enums.AppType;
import com.hqins.agent.org.model.vo.CustomMenuVO;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 个性化设置
 */
@FeignClient(name = "hqins-agent-org")
public interface PersonalizationApi {

    /**
     * 个性化设置
     *
     * @param appType 应用类型：H5-H5, MINI_PROGRAM-小程序
     * @return
     */
    @CollectionElement(targetClass = CustomMenuVO.class)
    @GetMapping("/agent-org/personalization/{appType}")
    List<CustomMenuVO> getPartnerCfg(@ApiParam("应用类型：H5-H5, MINI_PROGRAM-小程序") @PathVariable("appType") AppType appType);

}
