package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.Treeable;
import com.hqins.agent.org.model.enums.OrgStatus;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InsureOrgMgrVO implements Treeable<InsureOrgMgrVO> {

    @ApiModelProperty("所有孩子结点")
    private List<InsureOrgMgrVO> children;

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商")
    private AgentOrgType orgType;

    @ApiModelProperty("销售机构代码")
    private String code;

    @ApiModelProperty("销售机构名称")
    private String name;

    @ApiModelProperty("上级销售机构代码")
    private String parentCode;

    @ApiModelProperty("顶级机构代码")
    private String topCode;

    @ApiModelProperty("顶级机构名称")
    private String topName;

    @ApiModelProperty("经销商机构编码")
    private String dealerOrgCode;

    @ApiModelProperty("经销商机构名称")
    private String dealerOrgName;

    @ApiModelProperty("管理机构编码")
    private String manageOrgCode;
    @ApiModelProperty("管理机构名称")
    private String manageOrgName;
    @ApiModelProperty("项目经理编码")
    private String custManageCode;

    @ApiModelProperty("机构状态(ENABLED-有效 DISABLED-失效)")
    private OrgStatus status;

}
