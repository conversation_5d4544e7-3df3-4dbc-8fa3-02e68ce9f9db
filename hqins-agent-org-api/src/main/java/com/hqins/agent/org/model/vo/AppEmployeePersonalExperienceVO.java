package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/2/21 10:10
 */
@Data
public class AppEmployeePersonalExperienceVO {

    @ApiModelProperty(value = "人员编码", required = true, example = "人员编码")
    public String empCode;

    @ApiModelProperty(value = "开始时间", example = "开始时间")
    public LocalDate startDate;

    @ApiModelProperty(value = "结束时间", example = "结束时间")
    public LocalDate endDate;

    @ApiModelProperty(value = "学校/单位及部门", example = "学校/单位及部门")
    public String organization;

    @ApiModelProperty(value = "职务", example = "职务")
    public String position;
}
