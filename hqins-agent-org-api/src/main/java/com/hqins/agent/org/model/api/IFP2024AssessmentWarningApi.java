package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.IFP2024GroupVO;
import com.hqins.agent.org.model.vo.IFP2024TabVO;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "hqins-agent-org")
public interface IFP2024AssessmentWarningApi {

    @ApiOperation("当期/上期个人考核预警信息查询")
    @GetMapping("/agent-org/ifp-2024/assessment/queryEmployeeInfo")
    @CollectionElement(targetClass = IFP2024TabVO.class)
    List<IFP2024TabVO> queryEmployeeInfo(@ApiParam("1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                         @ApiParam("员工工号") @RequestParam(value = "employeeCode") String employeeCode);


    @ApiOperation("当期/上期团队考核预警信息查询")
    @GetMapping("/agent-org/ifp-2024/assessment/queryGroupInfo")
    @CollectionElement(targetClass = IFP2024GroupVO.class)
    IFP2024GroupVO queryGroupInfo(@ApiParam("1-当期；2-上期")  @RequestParam(value = "paramType") String paramType,
                                  @ApiParam("团队代码")        @RequestParam(value = "teamCode") String teamCode);
}
