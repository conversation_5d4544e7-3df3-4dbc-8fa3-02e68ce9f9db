package com.hqins.agent.org.model.enums;

public enum HonorItemEnum {

    QXJYH("QXJYH", "琴星精英会"),
    QYBSX("QYBSX", "琴韵博识轩"),
    MDRT("MDRT", "MDRT百万圆桌年会"),
    QHJNX("QHJNX", "琴海吉尼斯"),

    QHRYT_SDXRMX("QHRYT_SDXRMX", "琴辉荣耀堂-十大新人明星"),
    QHRYT_SDJSMX("QHRYT_SDJSMX", "琴辉荣耀堂-十大件数明星"),
    QHRYT_SDZYMX("QHRYT_SDZYMX", "琴辉荣耀堂-十大展业明星"),
    QHRYT_RYTTZ("QHRYT_RYTTZ", "琴辉荣耀堂堂主"),
    QHRYT_NDYCMX("QHRYT_NDYCMX", "琴辉荣耀堂-年度引才明星"),
    QHRYT_JSZX("QHRYT_JSZX", "琴辉荣耀堂-晋升之星"),
    QHRYT_NDRYTD("QHRYT_NDRYTD", "琴辉荣耀堂-年度荣耀团队"),
    QHRYT_NDCZTD("QHRYT_NDCZTD", "琴辉荣耀堂-年度成长团队"),
    QHRYT_NDFHXXSZX("QHRYT_NDFHXXSZX", "琴辉荣耀堂-年度分红险销售之星"),
    QHRYT_NDFXBZZX("QHRYT_NDFXBZZX", "琴辉荣耀堂-年度风险保障之星"),
    QHRYT_NDHKZX("QHRYT_NDHKZX", "琴辉荣耀堂-年度获客之星"),

    MDRT_MDRT("MDRT_MDRT", "MDRT百万圆桌年会"),
    MDRT_COT("MDRT_COT", "COT百万圆桌年会"),
    MDRT_TOT("MDRT_TOT", "TOT百万圆桌年会"),

    QHJNX_QWQJDS("QHJNX_QWQJDS", "琴海吉尼斯-千万期缴大师"),
    QHJNX_QWBBDS("QHJNX_QWBBDS", "琴海吉尼斯-千万标保大师");

    private final String value;
    private final String label;

    HonorItemEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static HonorItemEnum getHonorClassEnumByValue(String queryValue) {
        for (HonorItemEnum honorClass : HonorItemEnum.values()) {
            if (honorClass.getValue().equals(queryValue)) {
                return honorClass;
            }
        }
        return null;
    }
}
