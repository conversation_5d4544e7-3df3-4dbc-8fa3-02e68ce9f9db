package com.hqins.agent.org.model.enums;

import com.hqins.common.utils.StringUtil;


public enum IFP2024CheckItemEnum {

    consecutive_zero_9months("consecutive_zero_9months", "连续九个月业绩挂零"),
    monthly_actual_achievement("monthly_actual_achievement", "每月实际达成"),
    advisor_maintenance_monthly_fyc("advisor_maintenance_monthly_fyc", "顾问序列FYC维持标准"),
    personal_continuation_rate("personal_continuation_rate", "13个月继续率"),
    personal_continuation_rate_meet_standard("personal_continuation_rate_meet_standard", "13个月继续率维持标准"),
    personal_continuation_rate_standard("personal_continuation_rate_standard", "13个月继续率晋升标准"),
    personal_accumulated_count("personal_accumulated_count", "考核期内销售件数"),
    personal_accumulated_count_standard("personal_accumulated_count_standard", "考核期内销售件数标准"),
    monthly_average_manpower_achieved("monthly_average_manpower_achieved", "筹备所/事务所月均标准人力"),
    agency_maintain_monthly_manpower_standard("agency_maintain_monthly_manpower_standard", "筹备所/事务所月均标准人力维持标准"),
    preparation_office_continuation_rate("preparation_office_continuation_rate", "事务所13个月继续率"),
    preparation_office_continuation_rate_meet_standard("preparation_office_continuation_rate_meet_standard", "筹备所/事务所 13个月继续率维持标准"),
    directly_developed_agency("directly_developed_agency", "直接育成事务所"),
    agency_maintain_actual_fyc("agency_maintain_actual_fyc", "事务所考核期内累计FYC"),
    agency_maintain_fyc_standard("agency_maintain_fyc_standard", "事务所考核期内累计FYC维持标准"),
    yj_monthly_activity_manpower_achieved("yj_monthly_activity_manpower_achieved", "达成活动人力月数"),
    monthly_activity_manpower_achieved("monthly_activity_manpower_achieved", "每月是否达成活动人力"),
    advisor_promotion_fyc("advisor_promotion_fyc", "顾问序列FYC晋升标准"),
    zero_month_indicator("zero_month_indicator", "有无挂零月"),
    partner_tenure("partner_tenure", "任职合伙人时长"),
    partner_promotion_fyc_standard("partner_promotion_fyc_standard", "个人FYC晋升标准"),
    agency_promotion_monthly_manpower_standard("agency_promotion_monthly_manpower_standard", "筹备所/事务所月均标准人力晋升标准"),
    agency_promotion_fyc_standard("agency_promotion_fyc_standard", "事务所FYC考晋升标准"),
    agency_promotion_actual_fyc("agency_promotion_actual_fyc", "事务所考核期内累计FYC"),
    joining_duration("joining_duration", "入职时长"),
    partner_promotion_monthly_fyc("partner_promotion_monthly_fyc", "晋升到合伙人序列标准"),
    preparation_office_continuation_rate_standard("preparation_office_continuation_rate_standard", "筹备所/事务所 13个月继续率晋升标准"),
    agency_average_actual_fyc("agency_average_actual_fyc", "事务所月均实际FYC"),
    ;


    private String value;
    private String label;

    public static String getLabelByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return "";
        }
        for (IFP2024CheckItemEnum feeItem : IFP2024CheckItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem.getLabel();
            }
        }
        return "";
    }

    IFP2024CheckItemEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static IFP2024CheckItemEnum getEnumByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (IFP2024CheckItemEnum feeItem : IFP2024CheckItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
