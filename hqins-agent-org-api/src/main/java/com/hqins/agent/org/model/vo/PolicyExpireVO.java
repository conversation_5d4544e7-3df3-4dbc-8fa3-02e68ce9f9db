package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/12/12 14:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PolicyExpireVO implements Serializable {


    /**
     * 主键
     */
    private Integer id;

    /**
     * 机构类型，自营PARTNER/渠道CHANNEL
     */
    @ApiModelProperty("机构类型，自营PARTNER/渠道CHANNEL")
    private String orgType;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String riskname;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String riskcode;

    /**
     * 保单号
     */
    @ApiModelProperty("保单号")
    private String contno;

    /**
     * 投保人姓名
     */
    @ApiModelProperty("投保人姓名")
    private String appntname;

    /**
     * 被保人姓名
     */
    @ApiModelProperty("被保人姓名")
    private String insuredname;

    /**
     * 代理人姓名
     */
    @ApiModelProperty("代理人姓名")
    private String agentname;

    /**
     * 代理人工号
     */
    @ApiModelProperty("代理人工号")
    private String agentcode;

    /**
     * 保单生效日期
     */
    @ApiModelProperty("保单生效日期")
    private LocalDate cvalidate;

    /**
     * 满期日期
     */
    @ApiModelProperty("满期日期")
    private LocalDate getstartdate;

    /**
     * 满期日期
     */
    @ApiModelProperty("距离满期多久")
    private String distanceExpiration;

    /**
     * 已领取-0,未领取-1,未产生-9
     */
    @ApiModelProperty("已领取-0,未领取-1,未产生-9")
    private String balastate;

    /**
     * 网点编码
     */
    @ApiModelProperty("网点编码")
    private String orgcode;

    /**
     * 网点名称
     */
    @ApiModelProperty("网点名称")
    private String orgname;

    /**
     * 未领总额
     */
    @ApiModelProperty("未领总额")
    private String insuaccbala;


    /**
     * 满期未领红利金额
     */
    @ApiModelProperty("满期未领红利金额")
    private BigDecimal bonusbala;

    /**
     * 被保险人生存认证有效止期
     */
    @ApiModelProperty("被保险人生存认证有效止期")
    private LocalDateTime insdalivedate;

    /**
     * 被保险人生存认证有效期状态
     *
     * 已认证，未认证，认证已过期
     */
    @ApiModelProperty("被保险人生存认证有效期状态")
    private String insdalivestate;


}
