package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommissionItemVO implements Serializable {

    @ApiModelProperty("代理人工号")
    private String agentCode;

    @ApiModelProperty("代理人名称")
    private String agentName;

    @ApiModelProperty("职级序列")
    private String rankSeqCode;

    @ApiModelProperty("职级序列名称")
    private String rankSeqName;

    @ApiModelProperty("职级")
    private String rankCode;

    @ApiModelProperty("职级名称")
    private String rankName;

    @ApiModelProperty("结算月")
    private String settleMonth;

    @ApiModelProperty("佣金类型(跟人/跟单/类目/收入)")
    private String commissionType;

    @ApiModelProperty("佣金项code/类目code")
    private String commissionItem;

    @ApiModelProperty("佣金项名称/类目名称")
    private String commissionItemName;

    @ApiModelProperty("佣金项金额/类目金额")
    private BigDecimal amount;

    @ApiModelProperty("类目占比")
    private BigDecimal rate;

    @ApiModelProperty("税前金额")
    private BigDecimal amountPreTax;

    @ApiModelProperty("税后实发")
    private BigDecimal totalPaidAmount;

    @ApiModelProperty("基本法类型")
    private String versionType;

}
