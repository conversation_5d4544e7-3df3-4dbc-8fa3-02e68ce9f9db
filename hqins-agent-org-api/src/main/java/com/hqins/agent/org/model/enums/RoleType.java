package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构类型
 */
@ApiModel("应用类型")
public enum RoleType {
    /**
     * 组织机构类型
     */
    TESTER("是","测试人员"),
    FORMAL("否","正式人员");

    private String value;
    private String label;

    RoleType(String value,String label) {
        this.label = label;
        this.value = value;
    }
    public String getLabel() { return this.label; }
    public String getValue() { return this.value; }


    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static RoleType get(String name) {
        for (RoleType value : RoleType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
