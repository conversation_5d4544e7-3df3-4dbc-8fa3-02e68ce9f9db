package com.hqins.agent.org.model;

import com.google.common.collect.Lists;
import com.hqins.common.utils.StringUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR> <PERSON>
 * @date 2021/4/25
 * @Description
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TreeUtils {

    public static <T extends Treeable> List<T> buildTrees(List<T> list) {
        List<T> tree = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return tree;
        }
        Map<String, T> map = StreamEx.of(list).toMap(Treeable::getCode, Function.identity());
        list.forEach(t -> {
            if (!StringUtil.isBlank(t.getParentCode()) && !t.getCode().equals(t.getParentCode())) {
                T parent = map.get(t.getParentCode());
                if (parent != null) {
                    if (CollectionUtils.isEmpty(parent.getChildren())) {
                        parent.setChildren(Lists.newArrayList());
                    }
                    parent.getChildren().add(t);
                } else {
                    tree.add(t);
                }
            } else {
                tree.add(t);
            }
        });
        return tree;
    }
}
