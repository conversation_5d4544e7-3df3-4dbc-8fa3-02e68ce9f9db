package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.common.base.annotations.Mobile;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2023/4/3 17:40
 */
@ApiModel("督导账号查询请求")
@Data
@SuperBuilder
public class SupervisorEmployeeRequest extends PageQueryRequest {

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("人员代码")
    private String employeeCode;

    @ApiModelProperty("角色类型 ZongBu:总部 JiGou:机构 PuTong:普通 ")
    private SupervisorType roleType;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    private Gender gender;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;


    @ApiModelProperty("销售员手机号")
    @Mobile(message = "手机号格式错误")
    private String mobile;



    /**
     * 合伙人组织编码
     */
    @ApiModelProperty("合伙人组织编码")
    private String topCode;

    /**
     * 合伙人组织名称
     */
    @ApiModelProperty("合伙人组织名称")
    private String topCodeName;

    /**
     * 所属机构名称
     */
    @ApiModelProperty("所属机构名称")
    private String orgName;

    /**
     * 所属机构编码
     */
    @ApiModelProperty("所属机构编码")
    private String orgCode;


}
