package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MyEmployeeVO {

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("性别：UNKNOWN-保密；MALE-男性; FEMALE-女性")
    private Gender gender;

    @ApiModelProperty("销售员手机号")
    private String mobile;

    @ApiModelProperty("执业证号")
    private String licenseNo;

}
