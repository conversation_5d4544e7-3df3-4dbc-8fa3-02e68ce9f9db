package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PerformanceTrendVO implements Serializable {

    @ApiModelProperty("首年佣金")
    private BigDecimal  FYCAmount;

    @ApiModelProperty("首年佣金环比")
    private BigDecimal  FYCAmountRate;

    @ApiModelProperty("首年保费")
    private BigDecimal  FYPAmount;

    @ApiModelProperty("首年保费环比")
    private BigDecimal  FYPAmountRate;

    @ApiModelProperty("活动人力")
    private Integer  activePersonNum;

    @ApiModelProperty("活动人力环比")
    private BigDecimal  activePersonNumRate;

    @ApiModelProperty("趋势日")
    private String  trendDay;

    @ApiModelProperty("趋势时间戳")
    private String trendTimeString;

    @ApiModelProperty("趋势月")
    private String  trendMonth;

    @ApiModelProperty("首年标保")
    private BigDecimal  DCPAmount;

    @ApiModelProperty("首年标保环比")
    private BigDecimal  DCPAmountRate;


}
