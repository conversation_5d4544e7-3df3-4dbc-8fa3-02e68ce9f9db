package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/8/9 18:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "IFP全量月份和积分数据返回对象")
public class DigitalDataVO {

    @ApiModelProperty(value = "年月份 2023-01")
    private String monthDate;

    @ApiModelProperty(value = "分数")
    private Integer score;
}
