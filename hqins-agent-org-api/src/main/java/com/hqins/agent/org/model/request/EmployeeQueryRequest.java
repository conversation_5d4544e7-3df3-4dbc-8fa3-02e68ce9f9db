package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/5/21
 * @Description
 */
@ApiModel("销售员查询请求")
@Getter
@Setter
@SuperBuilder
public class EmployeeQueryRequest extends PageQueryRequest {

    @ApiModelProperty("合伙人编码数组")
    private String[] partnerCodes;

    @ApiModelProperty("渠道商编码数组")
    private String[] channelCodes;

    @ApiModelProperty("顶层机构编码")
    private String topCode;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("团队编码")
    private String teamCode;

    @ApiModelProperty("销售员名称或者代码")
    private String value;

    @ApiModelProperty("状态：SERVING、ALL")
    private String status;

    @ApiModelProperty("查询起始数")
    private Long start;

}
