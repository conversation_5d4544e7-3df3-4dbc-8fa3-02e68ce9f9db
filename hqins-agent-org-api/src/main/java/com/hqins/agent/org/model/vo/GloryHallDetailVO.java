package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @value
 * @create 2025/3/17
 */
@ApiModel("琴辉荣耀堂详情")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GloryHallDetailVO {

    @ApiModelProperty(value = "获奖年份", example = "2025年度")
    private String year;

    @ApiModelProperty(value = "最近一次获奖奖项", example = "最近一次获奖奖项")
    private String lastAward;

    @ApiModelProperty(value = "个人荣誉数据")
    private PersonalHonorsVO personalHonors;

    @ApiModelProperty(value = "团队荣誉数据")
    private TeamHonorsVO teamHonors;

    @ApiModelProperty(value = "专项荣誉数据")
    private SpecialHonorsVO specialHonors;

    // ================== 个人荣誉 ==================
    @ApiModel(value = "个人荣誉数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonalHonorsVO {
        @ApiModelProperty(value = "十大新人明星数据")
        private TopNewcomerVO topNewcomer;

        @ApiModelProperty(value = "十大件数明星数据")
        private TopNewcomerVO topPolicyCount;

        @ApiModelProperty(value = "十大展业明星数据")
        private TopNewcomerVO topPerformer;

        @ApiModelProperty(value = "荣誉堂堂主数据")
        private HallMasterVO hallMaster;

        @ApiModelProperty(value = "年度引才明星数据")
        private TalentRecruiterVO talentRecruiter;

        @ApiModelProperty(value = "晋升之星数据")
        private PromotionStarVO promotionStar;
    }

    // ================== 新人明星 ==================
    @ApiModel(value = "十大新人明星详情,十大件数明星详情,十大展业明星详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopNewcomerVO {
        @ApiModelProperty(value = "奖项达成状态 0-未获奖 1-已获奖", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "3")
        private String currentRank;

        @ApiModelProperty(value = "入围状态 0-未入围 1-已入围", example = "1")
        private String shortlisted;

        @ApiModelProperty(value = "入围标准")
        private String standard;

        @ApiModelProperty(value = "入围标准单位")
        private String standardUnit;

        @ApiModelProperty(value = "入围人数")
        private String shortlistedCount;

        @ApiModelProperty(value = "指标值", example = "15000000")
        private String metricsValue;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }


    // ================== 堂主荣誉 ==================
    @ApiModel(value = "荣誉堂堂主详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HallMasterVO {
        @ApiModelProperty(value = "奖项达成状态 0-未获奖 1-已获奖", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "2")
        private String currentRank;

        @ApiModelProperty(value = "个人期缴标保", example = "80000000")
        private String achievedPremium;

        @ApiModelProperty(value = "入围人数")
        private String shortlistedCount;

        @ApiModelProperty(value = "超过多少同行")
        private String standard;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }

    // ================== 引才明星 ==================
    @ApiModel(value = "年度引才明星详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TalentRecruiterVO {
        @ApiModelProperty(value = "奖项达成状态 0-未获奖 1-已获奖", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "2")
        private String currentRank;

        @ApiModelProperty(value = "入围人数")
        private String shortlistedCount;

        @ApiModelProperty(value = "有效增员人数", example = "5")
        private String achievedMembers;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }

    // ================== 晋升之星 ==================
    @ApiModel(value = "晋升之星详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromotionStarVO {
        @ApiModelProperty(value = "奖项达成状态 0-未获奖 1-已获奖", example = "1")
        private String status;

        @ApiModelProperty(value = "获奖时间", example = "2023.01.23")
        private String awardTime;
    }

    // ================== 团队荣誉 ==================
    @ApiModel(value = "团队荣誉数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TeamHonorsVO {
        @ApiModelProperty(value = "年度荣耀团队数据")
        private GloriousTeamVO gloriousTeam;

        @ApiModelProperty(value = "年度成长团队数据")
        private GrowthTeamVO growthTeam;
    }

    @ApiModel(value = "年度荣耀团队详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GloriousTeamVO {
        @ApiModelProperty(value = "奖项达成状态 0-未获奖 1-已获奖", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "3")
        private String currentRank;

        @ApiModelProperty(value = "入围状态 0-未入围 1-已入围", example = "1")
        private String shortlisted;

        @ApiModelProperty(value = "入围团队数量", example = "10")
        private String qualifiedTeams;

        @ApiModelProperty(value = "入围标准")
        private String standard;

        @ApiModelProperty(value = "入围标准单位")
        private String standardUnit;

        @ApiModelProperty(value = "团队新单期缴标保", example = "200000000")
        private String teamPremium;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }

    @ApiModel(value = "年度成长团队详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GrowthTeamVO {
        @ApiModelProperty(value = "奖项达成状态 0-未达成 1-已达成", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "5")
        private String currentRank;

        @ApiModelProperty(value = "入围团队数量", example = "10")
        private String qualifiedTeams;

        @ApiModelProperty(value = "有效增员人数", example = "8")
        private String achievedMembers;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }

    // ================== 专项荣誉 ==================
    @ApiModel(value = "专项荣誉数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpecialHonorsVO {
        @ApiModelProperty(value = "分红险销售之星数据")
        private DividendStarVO dividendStar;

        @ApiModelProperty(value = "风险保障之星数据")
        private DividendStarVO riskCoverageStar;

        @ApiModelProperty(value = "获客之星数据")
        private DividendStarVO customerAcquisitionStar;
    }

    @ApiModel(value = "分红险销售之星详情/风险保障之星详情/获客之星详情")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DividendStarVO {
        @ApiModelProperty(value = "奖项达成状态 0-未达成 1-已达成", example = "1")
        private String status;

        @ApiModelProperty(value = "当前排名", example = "15")
        private String currentRank;

        @ApiModelProperty(value = "入围状态 0-未入围 1-已入围", example = "1")
        private String shortlisted;

        @ApiModelProperty(value = "入围人数", example = "50")
        private String shortlistedCount;

        @ApiModelProperty(value = "入围标准")
        private String standard;

        @ApiModelProperty(value = "入围标准单位")
        private String standardUnit;

        @ApiModelProperty(value = "达成件数", example = "12")
        private String completedPolicyCount;

        @ApiModelProperty(value = "达成钱数/客户数", example = "60000000/12")
        private String totalPremiumAmount;

        @ApiModelProperty(value = "差距分析")
        private GapVO gapVO;
    }

//    @ApiModel(value = "风险保障之星详情")
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class RiskCoverageStarVO {
//        @ApiModelProperty(value = "奖项达成状态 0-未达成 1-已达成", example = "1")
//        private String status;
//
//        @ApiModelProperty(value = "入围状态 0-未入围 1-已入围", example = "0")
//        private String shortlisted;
//
//        @ApiModelProperty(value = "入围人数", example = "30")
//        private String shortlistedCount;
//
//        @ApiModelProperty(value = "入围标准")
//        private String standard;
//
//        @ApiModelProperty(value = "达成保单件数", example = "80")
//        private String achievedPolicies;
//
//        @ApiModelProperty(value = "当前排名", example = "0")
//        private String currentRank;
//
//        @ApiModelProperty(value = "风险保障金额", example = "1000000000")
//        private String coverageAmount;
//
//        @ApiModelProperty(value = "差距分析")
//        private GapVO gapVO;
//    }
//
//    @ApiModel(value = "获客之星详情")
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class CustomerAcquisitionStarVO {
//        @ApiModelProperty(value = "奖项达成状态 0-未达成 1-已达成", example = "1")
//        private String status;
//
//        @ApiModelProperty(value = "入围状态 0-未入围 1-已入围", example = "1")
//        private String shortlisted;
//
//        @ApiModelProperty(value = "入围人数", example = "20")
//        private String shortlistedCount;
//
//        @ApiModelProperty(value = "入围标准")
//        private String standard;
//
//        @ApiModelProperty(value = "新增客户数", example = "150")
//        private String newClients;
//
//        @ApiModelProperty(value = "当前排名", example = "5")
//        private String currentRank;
//
//        @ApiModelProperty(value = "已达成客户数", example = "200")
//        private String achievedPolicies;
//
//        @ApiModelProperty(value = "差距分析")
//        private GapVO gapVO;
//    }

    @ApiModel(value = "差距分析")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GapVO {
        @ApiModelProperty(value = "差距信息", example = "差距信息")
        private String message;

        @ApiModelProperty(value = "差距值", example = "差距值")
        private String value;

        @ApiModelProperty(value = "差距单位", example = "差距单位")
        private String unit;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    // 辅助类用于存储奖项信息
    public static class AwardRecord {
        String awardTime;
        String awardName;
    }

}