package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TeamPerformanceVO implements Serializable {

    @ApiModelProperty("团队代码")
    private String  teamCode;

    @ApiModelProperty("团队名称")
    private String  teamName;

    @ApiModelProperty("代理人代码")
    private String  agentCode;

    @ApiModelProperty("代理人名称")
    private String  agentName;

    @ApiModelProperty("产品代码")
    private String  riskCode;

    @ApiModelProperty("产品名称")
    private String  riskName;

    @ApiModelProperty("职级序列代码")
    private String  rankSeqCode;

    @ApiModelProperty("职级序列名称")
    private String  rankSeqName;

    @ApiModelProperty("保单件数")
    private Integer  policyNum;

    @ApiModelProperty("首年保费")
    private BigDecimal  FYP;

    @ApiModelProperty("首年佣金")
    private BigDecimal  FYC;

    @ApiModelProperty("活动人力")
    private Integer  activePersonNum;

    @ApiModelProperty("标准人力")
    private Integer  standardPersonNum;

    @ApiModelProperty("举绩人数")
    private Integer  premiumPersonNum;

    @ApiModelProperty("件均FYP")
    private BigDecimal  FYPPerPolicy;

    @ApiModelProperty("人均FYP")
    private BigDecimal  FYPPerPerson;

    @ApiModelProperty("件均FYC")
    private BigDecimal  FYCPerPolicy;

    @ApiModelProperty("人均FYC")
    private BigDecimal  FYCPerPerson;

    @ApiModelProperty("13个月继续率")
    private BigDecimal  cr13;

    @ApiModelProperty("13个月继续率_续期保费实收")
    private BigDecimal  cr13ActualPremium;

    @ApiModelProperty("13个月继续率_续期保费应收")
    private BigDecimal  cr13Premium;

    @ApiModelProperty("保单id数据")
    private String ids;

    @ApiModelProperty("保单id数据集合")
    private List<String> policyIdList;

    @ApiModelProperty("当日保单号数据集合")
    private List<String> currentDayPolicyNoList;

    @ApiModelProperty("基本法类型 金莲花:'SELF_LOTUS'")
    private String  versionType;

    @ApiModelProperty("首年标保")
    private BigDecimal DCP;

    @ApiModelProperty("件均首年标保")
    private BigDecimal DCPPerPolicy;

    @ApiModelProperty("人均首年标保")
    private BigDecimal DCPPerPerson;

    @ApiModelProperty("标保不为0人数")
    private Integer DCPPersonNum;


}
