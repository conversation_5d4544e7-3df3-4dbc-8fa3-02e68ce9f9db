package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.common.base.annotations.EnumValue;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@ApiModel("渠道商销售员对象")
@Data
public class ChannelEmployeeVO {

    @ApiModelProperty("销售员id")
    private Long id;

    @ApiModelProperty("销售员代码")
    private String code;

    @ApiModelProperty("销售员名称")
    private String name;

    @ApiModelProperty("证件类型")
    @EnumValue(enumClass = IdType.class, enumMethod = "isValid", message = "证件类型不正确")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;

    @ApiModelProperty("是否有万能险销售资格 true-有 false-没有")
    private Boolean universalQualification;

    @ApiModelProperty("性别：UNKNOWN-保密；MALE-男性; FEMALE-女性")
    private Gender gender;

    @ApiModelProperty("归属渠道商代码")
    private String channelCode;

    @ApiModelProperty("归属渠道商名称")
    private String channelName;

    @ApiModelProperty("归属销售机构代码")
    private String orgCode;

    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ApiModelProperty("归属营销团队代码")
    private String teamCode;

    @ApiModelProperty("归属营销团队名称")
    private String teamName;

    @ApiModelProperty("销售员手机号")
    private String mobile;

    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    @ApiModelProperty("入职日期")
    private LocalDateTime entryTime;

    @ApiModelProperty("离职日期")
    private LocalDateTime quitTime;

    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ApiModelProperty("执业证号启期")
    private LocalDate licenseStartDate;

    @ApiModelProperty("执业证号止期")
    private LocalDate licenseEndDate;

    @ApiModelProperty("销售员状态")
    private EmployeeStatus status;

    @ApiModelProperty("系统来源")
    private String sourceSystem;

    @ApiModelProperty("人员类型  TESTER体验人员 FORMAL正式人员 ")
    private RoleType roleType;
}
