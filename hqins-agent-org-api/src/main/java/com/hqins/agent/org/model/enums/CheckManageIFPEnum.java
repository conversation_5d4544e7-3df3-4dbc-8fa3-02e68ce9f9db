package com.hqins.agent.org.model.enums;

import com.hqins.common.utils.StringUtil;
import org.springframework.util.ObjectUtils;

public enum CheckManageIFPEnum {

    REDUCE_LEVEL1("REDUCE_LEVEL1", "降级",-1),
    KEEP_LEVEL("KEEP_LEVEL", "维持",0),
    PROMOTED_LEVEL1("PROMOTED_LEVEL1", "晋升",1),
    PROMOTED_LEVEL2("PROMOTED_LEVEL2", "晋升",2),
    QUIT("QUIT", "清退",-99),
    NOT_CHECK("NOT_CHECK", "不考核",-100),
    ;

    private String value;
    private String label;
    private int level;
    CheckManageIFPEnum(String value, String label, int level){
        this.value = value;
        this.label = label;
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static CheckManageIFPEnum getEnumByLevel(Integer level) {
        if (ObjectUtils.isEmpty(level)) {
            return null;
        }
        for (CheckManageIFPEnum manageEnum : CheckManageIFPEnum.values()) {
            if (manageEnum.getLevel() == level) {
                return manageEnum;
            }
        }
        return null;
    }

    public static CheckManageIFPEnum getEnumByLabel(String label) {
        if (StringUtil.isEmpty(label)){
            return null;
        }
        if (StringUtil.isEmpty(label)) {
            return null;
        }
        for (CheckManageIFPEnum manageEnum : CheckManageIFPEnum.values()) {
            if (manageEnum.getLabel().equals(label)) {
                return manageEnum;
            }
        }
        return null;
    }

    public static CheckManageIFPEnum getEnumByValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (CheckManageIFPEnum manageEnum : CheckManageIFPEnum.values()) {
            if (manageEnum.getValue().equals(value)) {
                return manageEnum;
            }
        }
        return null;
    }
}
