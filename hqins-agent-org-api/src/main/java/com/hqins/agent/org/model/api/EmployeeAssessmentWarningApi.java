package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.*;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "hqins-agent-org")
public interface EmployeeAssessmentWarningApi {

    @GetMapping("/agent-org/assessment/checkAssessmentInfoOrPerformanceData")
    AssessmentWarningResultVO checkAssessmentInfoOrPerformanceData(@ApiParam("参数类型：1-上期/当期；2-饼图") @RequestParam(value = "paramType") String paramType,
                                         @ApiParam("销售员代码") @RequestParam(value = "code") String code);

    @GetMapping("/agent-org/assessment/getAssessmentInfo")
    AssessmentWarningVO getAssessmentInfo (@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                           @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode);

    @GetMapping("/agent-org/assessment/getAssessmentSubordinatePersonnelInfo")
    AssessmentSubordinatePersonnelVO getAssessmentSubordinatePersonnelInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                                           @ApiParam("销售员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                                           @ApiParam("销售团队代码") @RequestParam(value = "teamCode",required = false) String teamCode,
                                                                           @ApiParam("考核项code") @RequestParam(value = "code") String code);

    @GetMapping("/agent-org/assessment/getAssessmentPerformanceDataInfo")
    AssessmentPerformanceDataVO getAssessmentPerformanceDataInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                                       @ApiParam("销售员代码") @RequestParam(value = "teamCode") String teamCode);

    @GetMapping("/agent-org/assessment/getAssessmentTableDataInfo")
    AssessmentTeamDateVO getAssessmentTableDataInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                          @ApiParam("销售员代码") @RequestParam(value = "teamCode") String teamCode);
}
