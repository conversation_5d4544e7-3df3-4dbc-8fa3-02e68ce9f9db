package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/5/9
 * @Description 团队级别
 */
@ApiModel("团队级别")
public enum TeamLevel {
    /**
     * 团队级别
     */
    AREA("营业区"),
    DEPT("营业部"),
    TEAM("营业组");

    private String label;

    TeamLevel(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static TeamLevel get(String name) {
        for (TeamLevel value : TeamLevel.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
