package com.hqins.agent.org.model.request;

import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@ApiModel("机构配置对象")
@Data
public class PersonalizationSaveRequest {

    @ApiModelProperty("顶层机构编码，依赖orgType")
    @NotBlank(message = "顶层机构编码不能为空")
    private String code;

    @ApiModelProperty("组织机构类型 PARTNER:合伙人、CHANNEL:渠道商")
    @NotNull(message = "组织机构类型不能为空")
    private AgentOrgType orgType;

    @ApiModelProperty("选中的菜单id列表")
    @NotNull(message = "选中的菜单id列表不能为空")
    private List<Long> customMenuIds;

}
