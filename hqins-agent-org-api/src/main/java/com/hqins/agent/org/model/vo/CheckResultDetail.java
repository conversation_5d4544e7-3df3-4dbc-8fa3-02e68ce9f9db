package com.hqins.agent.org.model.vo;

import com.hqins.agent.org.model.request.CheckChannelEmployeeBody;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lijian
 * @Date: 2023/9/11 3:30 下午
 */
@ApiModel("渠道商代理人校验结果明细")
@Data
public class CheckResultDetail {

    @ApiModelProperty("查验返回码")
    String returnCode;

    @ApiModelProperty("查验返回信息")
    String returnMessage;

    @ApiModelProperty("查验请求信息")
    CheckChannelEmployeeBody employeeRequest;


}
