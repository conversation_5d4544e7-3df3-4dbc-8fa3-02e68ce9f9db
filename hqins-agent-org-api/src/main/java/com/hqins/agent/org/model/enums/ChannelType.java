package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构类型
 */
@ApiModel("组织机构类型")
public enum ChannelType {
    /**
     * 渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理
     *
     * Insurance
     */
    INSURANCE_BROKER("保险经纪业务"),
    DIRECT_SALES("公司直销"),
    BANK_POSTAL("银行邮政业务"),
    OTHER("其他兼业代理"),
    PROFESSIONAL_AGENT("保险专业代理");

    private String label;

    ChannelType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static ChannelType get(String name) {
        for (ChannelType value : ChannelType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
