package com.hqins.agent.org.model.enums;

public enum EmployeeAssessmentInfoOrPerformanceDataEnum {
    ASSESSMENT_INFO("1","上期/当期考核"),
    PERFORMANCE_DATA_ENUM("2","饼图");

    private String code;

    private String label;

    EmployeeAssessmentInfoOrPerformanceDataEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    public static EmployeeAssessmentInfoOrPerformanceDataEnum get(String name) {
        for (EmployeeAssessmentInfoOrPerformanceDataEnum value : EmployeeAssessmentInfoOrPerformanceDataEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static EmployeeAssessmentInfoOrPerformanceDataEnum getLabelByCode(String code) {
        for (EmployeeAssessmentInfoOrPerformanceDataEnum value : EmployeeAssessmentInfoOrPerformanceDataEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
