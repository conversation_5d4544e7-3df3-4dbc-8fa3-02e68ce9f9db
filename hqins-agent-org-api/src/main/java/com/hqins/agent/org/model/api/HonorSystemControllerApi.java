package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.HistoryHonorDetailVO;
import com.hqins.agent.org.model.vo.HonoraryTitleInfoVO;
import com.hqins.agent.org.model.vo.HonoraryTitleVO;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/28
 */
@FeignClient(name = "hqins-agent-org")
public interface HonorSystemControllerApi {

    @ApiOperation("荣誉贺报列表查询")
    @GetMapping("/agent-org/honor-system/honor-announcements-teams")
    @ResponseStatus(HttpStatus.OK)
    HonoraryTitleVO getTeamHonorAnnouncements(@ApiParam("团队编码") @RequestParam(value = "saleTeamCode", required = true) String saleTeamCode,
                                              @ApiParam("荣誉类型") @RequestParam(value = "honorType", required = false) String honorType,
                                              @ApiParam("年度") @RequestParam(value = "year", required = true) String year);

    @ApiOperation("荣誉贺报详情")
    @GetMapping("/agent-org/honor-system/honor-announcements-agents")
    @ResponseStatus(HttpStatus.OK)
    HonoraryTitleInfoVO getAgentHonorAnnouncementDetail(@ApiParam("贺报ID") @RequestParam(value = "honorId", required = true) String empCode);


    @ApiOperation("荣誉历史")
    @GetMapping("/agent-org/honor-system/honor-history")
    @ResponseStatus(HttpStatus.OK)
    HistoryHonorDetailVO queryHonorHistory(@ApiParam("代理人编码") @RequestParam(value = "empCode", required = true) String empCode);

}
