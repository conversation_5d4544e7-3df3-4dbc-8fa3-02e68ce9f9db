package com.hqins.agent.org.model.enums;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2021/4/9
 * @Description 组织机构类型
 */
@ApiModel("应用类型")
public enum AppType {
    /**
     * 组织机构类型
     */
    H5("H5"),
    APP("APP"),
    MINI_PROGRAM("小程序");

    private String label;

    AppType(String label) {
        this.label = label;
    }
    public String getLabel() { return this.label; }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static AppType get(String name) {
        for (AppType value : AppType.values()) {
            if(value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
