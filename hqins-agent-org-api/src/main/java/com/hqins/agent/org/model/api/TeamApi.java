package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.SaleTeamVO;
import com.hqins.agent.org.model.vo.SimpleChannelOrgVO;
import com.hqins.agent.org.model.vo.TeamVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/22
 */
@FeignClient(name = "hqins-agent-org")
public interface TeamApi {

    /**
     * 获取销售员管理的团队
     *
     * @param employeeCode
     * @return
     */
    @GetMapping("/agent-org/partner/teams/managed")
    @CollectionElement(targetClass = TeamVO.class)
    List<TeamVO> listManagePartnerTeams(@ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode);

    @ApiOperation("查询合伙人机构下销售组信息")
    @GetMapping("/agent-org/partner/teams/orgCode/list-sale-team")
    @CollectionElement(targetClass = SaleTeamVO.class)
    List<SaleTeamVO> listSaleTeamByOrgCode(@ApiParam("合伙人机构") @RequestParam(value = "orgCode") String orgCode,
                                           @ApiParam("是否ifp 空-全量 true-IFP机构 false-非IFP机构") @RequestParam(value = "ifpFlag",required = false) Boolean ifpFlag
    );

    @ApiOperation("查询合伙人机构下顶级销售组信息")
    @GetMapping("/agent-org/partner/teams/list-top-sale-team")
    @CollectionElement(targetClass = SaleTeamVO.class)
    public List<SaleTeamVO> listTopSaleTeam(@ApiParam("机构代码") @RequestParam(value = "instCode", required = false) String instCode);

    @ApiOperation("根据团队code,获取团队下渠道列表(1级和5级)")
    @GetMapping("/agent-org/channels/list-1st-5th-orgs")
    @CollectionElement(targetClass = SimpleChannelOrgVO.class)
    List<SimpleChannelOrgVO> list1st5thOrgs(@ApiParam("团队代码") @RequestParam(value = "teamCode") String teamCode);

    @ApiOperation("判断团队是否为ifp")
    @GetMapping("/agent-org/channels/ifpTeam")
    Boolean ifpTeam(@ApiParam("团队编码") @RequestParam(value = "teamCode") String teamCode);
}
