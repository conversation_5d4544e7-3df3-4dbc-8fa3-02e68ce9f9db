package com.hqins.agent.org.model.api;

import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
@FeignClient(name = "hqins-agent-org")
public interface DataInitApi {

    /**
     * 加载销管机构数据到本地，并处理（初始化团队、更新名称、监测状态的变化）
     *
     * @param testFlag
     * @return
     */
    @PutMapping("/agent-org/data-init/xg/orgs")
    Void downloadProcessOrg(@ApiParam("测试标记：true-是 false-否") @RequestParam(value = "testFlag") String testFlag);

    /**
     * 加载销管用户数据到本地,并处理（创建um账号、停用、启用）
     *
     * @param testFlag
     * @return
     */
    @PutMapping("/agent-org/data-init/xg/employees")
    Void downloadProcessEmployee(@ApiParam("测试标记：true-是 false-否") @RequestParam(value = "testFlag") String testFlag);

    /**
     * 加载销管用户数据上传到神策
     *
     * @return
     */
    @PutMapping("/agent-org/data-init/xg/upload-sensors")
    Void uploadSensors();

}
