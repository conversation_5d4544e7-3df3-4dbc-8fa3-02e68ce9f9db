package com.hqins.agent.org.model.enums.zybx;

/**
 * @Author: liji<PERSON>
 * @Date: 2023/9/15 9:39 上午
 */
public enum CheckResultCodeEnum {

    CHECK_SUCCESS("100", "校验成功"),
    WRONG_USER_PASS("102", "用户名密码为空或错误"),
    WRONG_PARAMETER("103", "请求参数错误"),
    WRONG_PERMISSION("104", "权限错误"),
    CONNECTION_TIME_OUT("99", "交易超时，请重试或联系管理员"),
    SERVER_ERROR("500", "程序异常"),

//    DIS_MATCH_CHANNEL("110", "机构代码未匹配到渠道商"),
//    CHECK_FAILED("111", "用户校验失败"),
//    EMPLOYEE_NOT_EXIST("112", "人员不存在"),
//    NO_VALID_LICENSE_NO("113", "校验结果无有效的执业证，查验不通过"),
//    INVALID_LICENSE_NO("114", "入参中的执业证号无效"),
//    DIFF_CORPORATION("115", "所传代理人的法人机构与系统中不一致，查验不通过"),
//    DIFF_EMPLOYEE_NAME("116", "入参中姓名不一致"),
    DIS_MATCH_EMP("209","人员未在银保信查询平台查询到"),
    WRONG_ORG_CODE("210", "传入渠道商机构有误"),
    DIS_MATCH_EMP_INFO("211", "中介销售人员姓名、证件类型、证件号码与监管留存信息不匹配"),
    DIS_MATCH_ORG_INFO("212", "中介销售人员所在机构与监管留存信息不匹配"),
    DIS_MATCH_LICENCE_NO("213", "中介销售人员执业证号与监管留存信息不匹配"),
    INVALID_EMPLOYEE_INFO("214", "必录信息未传值"),
    ID_WRONG_LENGTH("215", "身份证号码长度错误"),
    LICENSE_NO_WRONG_LENGTH("216", "执业证号长度错误"),
    WRONG_ID_TYPE("217", "证件类型码值错误"),
    EXCLUDE_ID_TYPE("218", "银保信不包含该证件类型"),
    INVALID_LICENSE_NO("219", "中介销售人员职业证号已失效")
    ;

    private String code;
    private String label;

    CheckResultCodeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static CheckResultCodeEnum getEnumObjectByCode(String code) {
        for (CheckResultCodeEnum checkResultCodeEnum : CheckResultCodeEnum.values()) {
            if (code.equals(checkResultCodeEnum.getCode())) {
                return checkResultCodeEnum;
            }
        }
        return null;
    }

}
