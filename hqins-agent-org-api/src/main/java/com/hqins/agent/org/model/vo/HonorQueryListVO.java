package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/24
 */
@Data
public class HonorQueryListVO {

    @ApiModelProperty("代理人工号")
    private String agentCode;

    @ApiModelProperty("荣誉类")
    private String honorClass;

    @ApiModelProperty("荣誉项")
    private String honorCode;

    @ApiModelProperty("当前达星数")
    private String currentStar;

    @ApiModelProperty("当前奖项")
    private String honorsAwards;

    @ApiModelProperty("是否获奖")
    private String isAchievedCurrent;

    @ApiModelProperty("获奖年度")
    private String checkYear;

    @ApiModelProperty("考核周期")
    private String checkPeriod;

}
