package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2021/5/19
 * @Description
 */
@ApiModel("顶层机构分页查询请求")
@Getter
@SuperBuilder
public class TopQueryRequest extends PageQueryRequest {

    @ApiModelProperty("顶层机构代码")
    private String code;

    @ApiModelProperty("顶层机构名称")
    private String name;

}
