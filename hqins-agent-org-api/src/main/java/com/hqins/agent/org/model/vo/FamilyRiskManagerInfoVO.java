package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/7 8:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FamilyRiskManagerInfoVO implements Serializable {

    @ApiModelProperty("维持达标人数")
    private String  keepSuccessCount;

    @ApiModelProperty("维持未达标")
    private String  keepFailedCount;

    @ApiModelProperty("家庭风险管理师业绩考核预警")
    private List<FamilyRiskManagerCheckInfo> familyRiskManagerCheckInfos;
}
