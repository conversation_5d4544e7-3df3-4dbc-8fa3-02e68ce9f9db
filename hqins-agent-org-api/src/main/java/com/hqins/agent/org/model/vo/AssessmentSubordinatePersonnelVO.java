package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 下辖人员清单VO类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentSubordinatePersonnelVO {

    @ApiModelProperty("在职人力")
    private BigDecimal activeWorkforce;

    @ApiModelProperty("合格率")
    private BigDecimal qualificationRate;

    @ApiModelProperty("合格列表")
    private List<AssessmentEmployeeInfoVO> qualifiedPersonnelList;

    @ApiModelProperty("不合格列表")
    private List<AssessmentEmployeeInfoVO> unqualifiedPersonnelList;

    @ApiModelProperty("不参与考核列表")
    private List<AssessmentEmployeeInfoVO> notEvaluatePersonnelList;

    @ApiModelProperty("考核人列表")
    private List<AssessmentEmployeeInfoVO> allPersonnelList;
}
