package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.PartassignmanagerVO;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@FeignClient(name = "hqins-agent-org")
public interface PartnerChannelRelationApi {

    /**
     * 查询网点下的客户经理列表
     *
     * @param orgCode   商户组织机构(网点)代码
     * @return
     */
    @CollectionElement(targetClass = PartassignmanagerVO.class)
    @GetMapping("/agent-org/partnerChannelRelation/org/custManagers")
    List<PartassignmanagerVO> getOrgCustManagerList(@ApiParam("商户组织机构(网点)代码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据机构编码（orgCode）获取销售员（理财经理）详情
     *
     * @param orgCode
     * @return
     */
    @CollectionElement(targetClass = EmployeeVO.class)
    @GetMapping("/agent-org/partnerChannelRelation/org/employees")
    List<EmployeeVO> getOrgEmployeeList(@ApiParam("机构编码") @RequestParam(value = "orgCode") String orgCode);

    /**
     * 根据客户经理编码，查询网点列表
     *
     * @param custManagerCode   客户经理代码
     * @return
     */
    @CollectionElement(targetClass = PartassignmanagerVO.class)
    @GetMapping("/agent-org/partnerChannelRelation/custManager/orgs")
    List<PartassignmanagerVO> getMerchantOrgList(@ApiParam("客户经理编码") @RequestParam(value = "custManagerCode") String custManagerCode);

    /**
     * 根据客户经理编码，查询理财经理列表
     *
     * @param custManagerCode
     * @return
     */
    @CollectionElement(targetClass = EmployeeVO.class)
    @GetMapping("/agent-org/partnerChannelRelation/custManager/employees")
    List<EmployeeVO> getEmployeeListByCustManagerCode(@ApiParam("客户经理编码") @RequestParam(value = "custManagerCode") String custManagerCode);

}
