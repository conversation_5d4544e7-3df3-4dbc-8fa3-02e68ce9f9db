package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021/6/1
 */
@ApiModel("渠道商配置logo请求")
@Data
public class ChannelConfigLogoRequest {

    @ApiModelProperty("渠道商代码")
    @NotBlank(message = "渠道商代码不能为空")
    private String code;

    @ApiModelProperty("渠道商logo")
    @NotBlank(message = "渠道商logo不能为空")
    private String logo;
}
