package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 基本法基本信息VO类.
 *
 * <AUTHOR> MXH
 * @create 2025/3/7 8:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BasicLawInfoVO implements Serializable {

    @ApiModelProperty("基本法Id")
    private String BasicLawId;

    @ApiModelProperty("基本法类型")
    private String BasicLawType;

    @ApiModelProperty("基本法名称")
    private String BasicLawName;

    @ApiModelProperty("家庭风险管理师集合")
    private FamilyRiskManagerInfoVO familyRiskManagerInfoVO;

    @ApiModelProperty("合伙人集合")
    private PartnerInfoVO partnerInfoVO;
}
