package com.hqins.agent.org.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 招募人员信息VO类.
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 9:57
 */
@ApiModel("招募人员信息VO类")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppEmployeeRecruitmentInfoListVO {

    @ApiModelProperty("被招募人员工号")
    private String employeeCode;

    @ApiModelProperty("被招募人员姓名")
    private String employeeName;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("人员状态")
    private String employeeStatus;

    @ApiModelProperty("出生日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date birthday;

    @ApiModelProperty("最后一次修改日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
