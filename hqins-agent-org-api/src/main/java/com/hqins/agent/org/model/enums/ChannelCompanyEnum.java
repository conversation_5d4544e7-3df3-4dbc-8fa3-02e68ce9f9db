package com.hqins.agent.org.model.enums;

/**
 * 渠道商对应码值
 * 由于数据近似常量，所以不在u_base_inst中现查。
 * @Author: lijian
 * @Date: 2023/8/23 9:26 上午
 */
public enum ChannelCompanyEnum {

    ZTE("ZTE", "C00013", "中兴保险经纪有限公司");

    private String code;
    private String instCode;
    private String label;

    ChannelCompanyEnum(String code, String instCode, String label) {
        this.code = code;
        this.instCode = instCode;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
