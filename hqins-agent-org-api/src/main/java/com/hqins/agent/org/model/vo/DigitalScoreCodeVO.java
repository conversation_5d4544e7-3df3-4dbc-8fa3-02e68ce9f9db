package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/8/9 18:04
 */
@Data
@ApiModel(value = "IFP某月份代理人积分数据返回对象",description = "IFP某月份代理人积分数据返回对象")
public class DigitalScoreCodeVO {

    @ApiModelProperty(value = "代理人工号")
    private String agentCode;

    @ApiModelProperty(value = "分数")
    private Integer score;
}
