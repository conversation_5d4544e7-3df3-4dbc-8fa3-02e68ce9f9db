package com.hqins.agent.org.model.request;

import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.common.base.annotations.Mobile;
import com.hqins.common.base.enums.Gender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/7 09:56
 */
@ApiModel("督导账号更新请求")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupervisorEmployeeUpdateRequest {

    @ApiModelProperty("人员id")
    @NotNull(message = "人员id不能为空")
    private Long id;

    @ApiModelProperty("人员名称")
    @NotBlank(message = "人员名称不能为空")
    private String name;


    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件编码")
    private String idCode;


    /**
     * 角色类型 ZongBu:总部 JiGou:机构 PuTong:普通
     */
    @ApiModelProperty("角色类型 ZongBu:总部 JiGou:机构 PuTong:普通")
    private SupervisorType roleType;

    /**
     * 性别
     */
    @ApiModelProperty("性别：MALE-男性; FEMALE-女性")
    @NotNull(message = "性别不能为空")
    private Gender gender;

    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private LocalDate birthday;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不能为空")
    @Mobile(message = "手机号格式错误")
    private String mobile;

    /**
     * 合伙人组织编码
     */
    @ApiModelProperty("合伙人组织编码")
    @NotEmpty(message = "合伙人组织编码不能为空")
    private String topCode;

    /**
     * 合伙人组织名称
     */
    @ApiModelProperty("合伙人组织名称")
    @NotEmpty(message = "合伙人组织名称不能为空")
    private String topCodeName;

    /**
     * 所属机构名称
     */
    @ApiModelProperty("所属机构名称")
    @NotEmpty(message = "所属机构名称不能为空")
    private String orgName;

    /**
     * 所属机构编码
     */
    @ApiModelProperty("所属机构编码")
    @NotEmpty(message = "所属机构编码不能为空")
    private String orgCode;

    @NotEmpty(message = "职位")
    private String position;

    @ApiModelProperty("配置能切换的顶级机构")
    private List<String> topCodeList;

    @ApiModelProperty("配置的二级机构")
    private List<String> orgCodeList;

    @ApiModelProperty("团队编码")
    @NotEmpty(message = "团队编码不能为空")
    private String teamCode;

    @ApiModelProperty("团队名称")
    private String teamName;

    @ApiModelProperty("团队等级")
    private String teamLevel;

}
