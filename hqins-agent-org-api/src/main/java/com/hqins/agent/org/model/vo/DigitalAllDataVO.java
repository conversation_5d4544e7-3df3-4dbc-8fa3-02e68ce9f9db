package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/9 18:04
 */
@Data
@ApiModel(value = "IFP全量月份和积分数据返回对象",description = "IFP全量月份和积分数据返回对象")
public class DigitalAllDataVO {

    @ApiModelProperty(value = "代理人工号")
    private String agentCode;
    @ApiModelProperty(value = "年月份")
    private String monthDate;

    @ApiModelProperty(value = "分数")
    private Integer score;
}
