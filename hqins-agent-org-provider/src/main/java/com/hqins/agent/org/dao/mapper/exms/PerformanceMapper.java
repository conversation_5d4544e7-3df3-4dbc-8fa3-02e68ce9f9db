package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
@DS("exms")
@Repository
public interface PerformanceMapper {


    BigDecimal getCurrentMonthIncomeInfo(Map map);

    List<CommissionItemVO> getPreSettleCommissionVOList(Map map);

    List<CommissionItemVO> getSettleCommissionVOList(Map map);

    List<CommissionItemVO> getLastSettleCommissionVOList(Map map);

    List<PersonalIncomeVO> getMonthsSettleIncomeInfo(Map map);

    PersonalIncomeVO getSettleIncomeAmountInfo(Map map);

    List<PerformancePolicyVO> getPreSettlePolicyInfo(Map<String, Object> map);

    List<PerformancePolicyVO> getCommissionPolicyVOList(Map map);

    List<PerformancePolicyVO> getElePolicyVOList(Map map);

    PersonalVO getEmpCr(Map<String, Object> map);

    PersonalVO getCommissionCr(Map<String, Object> map);

    BigDecimal getEmpPerformanceFycAmount(Map<String, Object> map);

    List<PerformancePolicyVO> getPerformancePolicyInfo(Map<String, Object> map);

    List<PerformancePolicyVO> getPerformanceMonthPolicyInfo(Map<String, Object> map);

    List<PerformancePolicyVO> getPerformancePolicyList(Map<String, Object> map);

    List<PersonalVO> getTeamCurrentPersonList(Map<String, Object> map);

    List<PersonalVO> getTeamCurrentPersonQuitList(Map<String, Object> map);

    TeamManageVO getCommissionTeamInfo(Map<String, Object> map);

    List<PersonalVO> getPerformanceListByCodeList(Map<String, Object> map);

    List<PerformanceTrendVO> getTeamMonthTrendPerformanceList(Map<String, Object> map);

    List<PerformanceTrendVO> getTeamHalfYearTrendPerformanceList(Map<String, Object> map);

    List<PerformanceTrendVO> getTeamActiveTrendInfoList(Map<String, Object> map);

    List<PersonalVO> getPerformancemonthListByCodeList(Map<String, Object> map);

    List<TeamPerformanceVO> getPersonalRangePerformanceList(Map<String, Object> map);

    List<TeamPerformanceVO> getProductRangePerformanceList(Map<String, Object> map);

    List<TeamPerformanceVO> getRankSeqRangePerformanceList(Map<String, Object> map);

    List<TeamPerformanceVO> getAgentRankSeqInfoList(Map<String, Object> map);

    List<PerformancePolicyVO> getPerformancePolicyListById(Map<String, Object> map);

    List<PolicyCommissionVO> getPolicyCommissionList(Map<String, Object> map);

    List<TeamPerformanceVO> getTeamCrInfoByAgentCodeList(Map<String, Object> map);

    List<PersonalVO> getTeamMonthPersonQuitList(Map<String, Object> map);

    List<PerformancePolicyVO> getTeamPerformancePolicyList(Map<String, Object> map);

    List<PerformancePolicyVO> getTeamPersonalPerformancePolicyList(Map<String, Object> map);

    List<PerformancePolicyVO> getTeamProductPerformancePolicyList(Map<String, Object> map);

    List<PerformancePolicyVO> getTeamRankSeqPerformancePolicyList(Map<String, Object> map);

    PerformanceVO getPersonalPerformance(Map<String, Object> map);

    List<String> getPersonalAppntNoList(Map<String, Object> map);

    PerformanceVO getYearPersonalPerformance(Map<String, Object> map);

    PerformanceVO getMonthTeamPerformance(Map<String, Object> map);

    PerformanceVO getYearTeamPerformance(Map<String, Object> map);

    List<TeamPerformanceVO> getTeamCr13(Map<String, Object> map);

    List<String> getOrgListVersionTypeList(Map<String, Object> map);

    List<String> getVersionTypeListByEmpTeam(Map<String, Object> map);

    List<String> getVersionTypeListByEmpInst(Map<String, Object> map);

    String getAgentSaleTeamCode(Map map);

    String getTeamVersionType(Map map);

    Integer getAgentIncreaseNum(Map map);

    Integer getTeamIncreaseNum(Map map);

    List<PerformancePolicyVO> getCommissionRSCPolicyVOList(Map<String, Object> map);

    List<PerformancePolicyVO> getCommissionGSCPolicyVOList(Map<String, Object> map);

    List<PerformancePolicyVO> getPreSettleRSCPolicyInfo(Map<String, Object> map);

    List<PerformancePolicyVO> getEleRSCPolicyVOList(Map<String, Object> map);

    List<SimpleNodeVO> getSelfPolicyList();

    List<MarkDetailVO> getSupervisorCrList(Map map);

    List<SupervisorPerformanceDetailNumberVO> getSupervisorIncreaseVOList(Map map);
}
