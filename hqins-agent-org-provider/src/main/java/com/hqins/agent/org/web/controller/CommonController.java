package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.vo.DictsVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.SelectItem;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021-04-23
 */
@Api(tags = "公共接口")
@RestController
@RequestMapping("/common")
@Slf4j
public class CommonController {

    @ApiOperation("查询字典")
    @GetMapping("/dicts")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<DictsVO> getDicts() {
        DictsVO dictsVO = new DictsVO();
        dictsVO.setChannelTypes(StreamEx.of(ChannelType.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setDataStatus(StreamEx.of(DataStatus.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setEmployeeStatus(StreamEx.of(EmployeeStatus.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setGenders(StreamEx.of(Gender.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setNodeLevels(StreamEx.of(NodeLevel.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setOrgTypes(StreamEx.of(AgentOrgType.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setTeamLevels(StreamEx.of(TeamLevel.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setTeamStatus(StreamEx.of(TeamStatus.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());
        dictsVO.setAccountStatus(StreamEx.of(AccountStatus.values()).map(obj -> new SelectItem<>(obj.name(), obj.getLabel())).toList());

        dictsVO.setIdTypes(StreamEx.of(IdType.values()).sortedBy(IdType::getOrder).map(obj -> new SelectItem<>(obj.name(), obj.getLabel(), obj.getOrder())).toList());
        return ApiResult.ok(dictsVO);
    }

}
