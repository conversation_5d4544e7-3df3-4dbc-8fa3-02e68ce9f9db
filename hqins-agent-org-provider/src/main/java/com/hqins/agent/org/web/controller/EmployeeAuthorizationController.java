package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.vo.AuthEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.MyEmployeeVO;
import com.hqins.agent.org.model.vo.SimpleEmployeeVO;
import com.hqins.agent.org.service.ChannelEmployeeService;
import com.hqins.agent.org.service.EmployeeAuthService;
import com.hqins.agent.org.service.EmployeeService;
import com.hqins.agent.org.service.PartnerEmployeeService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "销售员授权管理")
@RestController
@RequestMapping("/authorization/employee")
@Slf4j
public class EmployeeAuthorizationController {


    @Autowired
    private EmployeeAuthService employeeAuthService;


    @ApiOperation("根据authId获取销售员信息")
    @GetMapping()
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AuthEmployeeVO> getEmployeeByAuthId(
            @ApiParam("授权ID") @RequestParam(value = "authId" ) String authId) {
        AssertUtil.notEmpty(authId,new ApiException("授权ID不可为空！"));
        return ApiResult.ok(employeeAuthService.getEmployeeByAuthId(authId));
    }
    @ApiOperation("根据执业证号获取销售员授权信息")
    @GetMapping("/license-no")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AuthEmployeeVO> getEmployeeByLicenseNo(
            @ApiParam("执业证号") @RequestParam(value = "licenseNo" ) String licenseNo) {
        AssertUtil.notEmpty(licenseNo,new ApiException("执业证号不可为空！"));
        return ApiResult.ok(employeeAuthService.getEmployeeByLicenseNo(licenseNo));
    }

}
