package com.hqins.agent.org.web.controller;


import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.PerformanceService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Api(tags = "个人管理")
@RestController
@RequestMapping("/personal/manage")
@Slf4j
public class PersonalManageController {

    @Autowired
    private PerformanceService performanceService;

    @ApiOperation("个人当月收入查询")
    @GetMapping("/getCurrentMonthIncomeInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PersonalVO> getCurrentMonthIncomeInfo(@ApiParam("组员代码(组长查看组员时传入)") @RequestParam(value = "employeeCode",required = false) String employeeCode) {
        try {
            return ApiResult.ok(performanceService.getCurrentMonthIncomeInfo(employeeCode));
        } catch (Exception e) {
            log.error("当月收入业绩查询出错", e);
            return ApiResult.fail("当月收入业绩查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人收入预结算信息")
    @GetMapping("/getPreSettleIncomeInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PersonalIncomeVO>> getPreSettleIncomeInfo() {
        try {
            return ApiResult.ok(performanceService.getPreSettleIncomeInfo());
        } catch (Exception e) {
            log.error("个人收入预结算信息查询出错", e);
            return ApiResult.fail("个人收入预结算信息查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人收入月度趋势信息")
    @GetMapping("/getSettleIncomeTrendInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PersonalIncomeVO>> getSettleIncomeTrendInfo() {
        try {
            return ApiResult.ok(performanceService.getSettleIncomeTrendInfo());
        } catch (Exception e) {
            log.error("个人收入月度趋势信息查询出错", e);
            return ApiResult.fail("个人收入月度趋势信息出错," + e.getMessage());
        }
    }


    @ApiOperation("个人收入已结算信息")
    @GetMapping("/getSettleIncomeInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PersonalIncomeVO> getSettleIncomeInfo(@ApiParam("已结算月份") @RequestParam(value = "settleMonth",required = false) String settleMonth) {
        try {
            return ApiResult.ok(performanceService.getSettleIncomeInfo(settleMonth));
        } catch (Exception e) {
            log.error("个人收入已结算信息出错", e);
            return ApiResult.fail("个人收入已结算信息出错," + e.getMessage());
        }
    }


    @ApiOperation("个人收入保单明细")
    @GetMapping("/getSettlePolicyInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PerformancePolicyVO>> getSettlePolicyInfo(@ApiParam("查询佣金code") @RequestParam(value = "commissionItem") String commissionItem,
                                                                    @ApiParam("结算月份") @RequestParam(value = "settleMonth") String settleMonth,
                                                                    @ApiParam("查询类型:续年实收:QQD,续年追加:ZP") @RequestParam(value = "queryType",required = false) String queryType) {
        try {
            return ApiResult.ok(performanceService.getSettlePolicyInfo(commissionItem,settleMonth,queryType));
        } catch (Exception e) {
            log.error("个人收入保单明细查询出错", e);
            return ApiResult.fail("个人收入保单明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人当月业绩查询")
    @GetMapping("/getCurrentMonthPerformanceInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PersonalVO> getCurrentMonthPerformanceInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode) {
        try {
            return ApiResult.ok(performanceService.getCurrentMonthPerformanceInfo(employeeCode));
        } catch (Exception e) {
            log.error("当月收入业绩查询出错", e);
            return ApiResult.fail("当月收入业绩查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人当月业绩查询-首年业绩明细")
    @GetMapping("/getPerformanceFycMonthInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getPerformanceFycMonthInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                               @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth) {
        try {
            return ApiResult.ok(performanceService.getPerformanceFycMonthInfo(employeeCode,performanceMonth));
        } catch (Exception e) {
            log.error("首年业绩明细查询出错", e);
            return ApiResult.fail("首年业绩明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人当月业绩查询-续年业绩明细")
    @GetMapping("/getPerformanceRycMonthInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getPerformanceRycMonthInfo(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                               @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth){
        try {
            return ApiResult.ok(performanceService.getPerformanceRycMonthInfo(employeeCode,performanceMonth));
        } catch (Exception e) {
            log.error("续年业绩明细查询出错", e);
            return ApiResult.fail("续年业绩明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人当月业绩查询-保费继续率")
    @GetMapping("/getPerformanceCr13Info")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getPerformanceCr13Info(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                           @ApiParam("业绩月") @RequestParam(value = "performanceMonth") String performanceMonth) {
        try {
            return ApiResult.ok(performanceService.getPerformanceCr13Info(employeeCode,performanceMonth));
        } catch (Exception e) {
            log.error("保费继续率查询出错", e);
            return ApiResult.fail("保费继续率查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人业绩保单明细")
    @GetMapping("/getPerformancePolicyInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PerformancePolicyVO>> getPerformancePolicyInfo(@ApiParam("查询类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType") String queryType,
                                                                         @ApiParam("业绩月份(实收月,续缴月)") @RequestParam(value = "performanceMonth") String performanceMonth) {
        try {
            return ApiResult.ok(performanceService.getPerformancePolicyInfo(queryType,performanceMonth));
        } catch (Exception e) {
            log.error("个人业绩保单明细查询出错", e);
            return ApiResult.fail("个人业绩保单明细查询出错," + e.getMessage());
        }
    }

    @ApiOperation("个人业绩保单明细-查看组员")
    @GetMapping("/getPerformancePolicyNoList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<String>> getPerformancePolicyNoList(@ApiParam("组员代码") @RequestParam(value = "employeeCode") String employeeCode,
                                                              @ApiParam("查询类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType") String queryType,
                                                              @ApiParam("业绩月份(实收月,续缴月)") @RequestParam(value = "performanceMonth") String performanceMonth) {
        try {
            return ApiResult.ok(performanceService.getPerformancePolicyNoList(employeeCode,queryType,performanceMonth));
        } catch (Exception e) {
            log.error("个人业绩保单明细查询出错", e);
            return ApiResult.fail("个人业绩保单明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("个人保单明细")
    @GetMapping("/getPerformancePolicyList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PerformancePolicyVO>> getPerformancePolicyList(@ApiParam("组员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                              @ApiParam("查询条件") @RequestParam(value = "qureyString") String qureyString) {
        try {
            return ApiResult.ok(performanceService.getPerformancePolicyList(employeeCode,qureyString));
        } catch (Exception e) {
            log.error("个人保单明细查询出错", e);
            return ApiResult.fail("个人保单明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("获取保单佣金信息")
    @GetMapping("/getPolicyCommissionList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PolicyCommissionVO> getPolicyCommissionList(@ApiParam("保单号") @RequestParam(value = "policyNo") String policyNo) {
        try {
            return ApiResult.ok(performanceService.getPolicyCommissionList(policyNo));
        } catch (Exception e) {
            log.error("获取保单佣金信息出错", e);
            return ApiResult.fail("获取保单佣金信息出错," + e.getMessage());
        }
    }


    @ApiOperation("个人保单明细查询")
    @GetMapping("/getPersonalPolicyInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PerformancePolicyVO>> getPersonalPolicyInfo(@ApiParam("类型:收入:income,业绩:performance") @RequestParam(value = "type") String type,
                                                                      @ApiParam("查询佣金code") @RequestParam(value = "commissionItem",required = false) String commissionItem,
                                                                      @ApiParam("查询月份") @RequestParam(value = "queryMonth") String queryMonth,
                                                                      @ApiParam("佣金类型:首年:UW,续年实收:QQD,续年未缴纳:YT,续年应收:QY,续年追加:ZP") @RequestParam(value = "queryType",required = false) String queryType) {
        try {
            if(type.equals("income")){
                return ApiResult.ok(performanceService.getSettlePolicyInfo(commissionItem,queryMonth,queryType));
            }else if (type.equals("performance")){
                return ApiResult.ok(performanceService.getPerformancePolicyInfo(queryType,queryMonth));
            }else {
                log.info("当月个人保单查询类型出错");
                return ApiResult.ok(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("个人保单明细查询出错", e);
            return ApiResult.fail("个人保单明细查询出错," + e.getMessage());
        }
    }



    @ApiOperation("个人目标业绩查询")
    @GetMapping("/getPersonalTargetPerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getPersonalTargetPerformance(
            @ApiParam("组员代码") @RequestParam(value = "employeeCode") String employeeCode,
            @ApiParam("登录人代码") @RequestParam(value = "loginEmpCode",required = false) String loginEmpCode,
            @ApiParam("按年查询业绩:2024") @RequestParam(value = "queryYear",required = false) String queryYear,
            @ApiParam("按月查询业绩:2024-01") @RequestParam(value = "queryMonth",required = false) String queryMonth) {
        try {
            return ApiResult.ok(performanceService.getPersonalPerformance(employeeCode,loginEmpCode,queryYear,queryMonth));
        } catch (Exception e) {
            log.error("个人目标业绩查询出错", e);
            return ApiResult.fail("个人目标业绩查询出错," + e.getMessage());
        }
    }




    @ApiOperation("根据代理人工号查询基本法类型(2024个团,2025金莲花)")
    @GetMapping("/getBasicLawVersionTypeList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<String>> getBasicLawVersionTypeList(@ApiParam("业务员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        List<String> versionTypeList = performanceService.getBasicLawVersionTypeList(employeeCode);
        return ApiResult.ok(versionTypeList);
    }


    @ApiOperation("根据工号或团队号查询基本法类型(暂时只返回金莲花类型'SELF_LOTUS')")
    @GetMapping("/getBasicLawVersionTypeForPerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getBasicLawVersionTypeForPerformance(@ApiParam("业务员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                              @ApiParam("团队代码") @RequestParam(value = "teamCode",required = false) String teamCode) {
        String versionType = performanceService.getBasicLawVersionType(employeeCode,teamCode,null);
        return ApiResult.ok(versionType);
    }


}



