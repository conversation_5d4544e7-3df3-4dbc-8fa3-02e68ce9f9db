package com.hqins.agent.org.dao.mapper.settle;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.model.vo.DigitalAllDataVO;
import com.hqins.agent.org.model.vo.DigitalDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreCodeVO;
import com.hqins.agent.org.model.vo.DigitalScoreDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/10 09:05
 */
@Mapper
@Repository
@DS("settle")
public interface IFPDigitalScoreMapper extends BaseMapper<DigitalAllDataVO> {

    List<DigitalAllDataVO> getAllMonthAndScoreData(@Param("agentCodeList") List<String> agentCodeList);

    List<DigitalScoreDetailVO> getScoreDetail(@Param("agentCodeList") List<String> agentCodeList);


    List<DigitalDataVO> getAllMonthAndScoreDataByAgentCode(@Param("agentCode") String agentCode, @Param("startDate") String startDate);

    List<DigitalScoreCodeVO> getMonthAndScoreByAgentCodeList(@Param("agentCodeList") List<String> agentCodeList, @Param("queryDate") String queryDate);

    List<DigitalScoreDetailVO> getScoreDetailByAgentCodeList(@Param("agentCodeList") List<String> agentCodeList,@Param("queryDate") String queryDate,@Param("recordPointsFlag") String recordPointsFlag);

}
