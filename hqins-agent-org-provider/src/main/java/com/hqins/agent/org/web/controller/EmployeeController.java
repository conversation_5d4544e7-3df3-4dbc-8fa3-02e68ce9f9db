package com.hqins.agent.org.web.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.hqins.agent.org.model.request.AllEmpByPageRequest;
import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.vo.ClueEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.MyEmployeeVO;
import com.hqins.agent.org.model.vo.SimpleEmployeeVO;
import com.hqins.agent.org.service.*;
import com.hqins.agent.org.utils.AESUtil;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.page.PageQueryRequest;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "销售员管理")
@RestController
@RequestMapping("/employees")
@Slf4j
public class EmployeeController {

    private final ChannelEmployeeService channelEmployeeService;
    private final PartnerEmployeeService partnerEmployeeService;
    private final EmployeeService employeeService;

    private final SupervisorEmployeeService supervisorEmployeeService;

    public EmployeeController(ChannelEmployeeService channelEmployeeService, PartnerEmployeeService partnerEmployeeService, EmployeeService employeeService, SupervisorEmployeeService supervisorEmployeeService) {
        this.channelEmployeeService = channelEmployeeService;
        this.partnerEmployeeService = partnerEmployeeService;
        this.employeeService = employeeService;
        this.supervisorEmployeeService = supervisorEmployeeService;
    }

    @GetMapping("/new")
    public ApiResult<EmployeeVO> getEmployeeInfo(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                 @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
        }else if (AgentOrgType.PARTNER.equals(orgType)){
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
        }
        return ApiResult.ok(employeeVO);
    }
    @GetMapping("/new-ydl")
    public ApiResult<EmployeeVO> getEmployeeInfoByYDL(
            @ApiParam("永达理销售员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        return ApiResult.ok(channelEmployeeService.getEmployeeVOByYDL(employeeCode));
    }

    @GetMapping("/encryption")
    public ApiResult<EmployeeVO> getEmployeeInfoByBncryption(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                 @ApiParam("销售员代码 密文") @RequestParam(value = "employeeCode") String employeeCode) {
        AssertUtil.notNull(employeeCode, new ApiException("工号不能为空"));
        String key = "jBRCQd6p#hurZDhO8n6t4@pKrVa8JCKG";
        String iv = "P8MxE59mFRORPVUf";
        String code = "";
        try {
            code = AESUtil.decrypt(employeeCode,key,iv);
        } catch (Exception e) {
            log.error("AES解密异常,employeeCode:{}",employeeCode,e);
            throw new ApiException("工号解密异常");
        }
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(code);
        }else if (AgentOrgType.PARTNER.equals(orgType)){
            employeeVO = partnerEmployeeService.getEmployeeVO(code);
            employeeVO = partnerEmployeeService.isTeamLeader(employeeVO);
        }
        return ApiResult.ok(employeeVO);
    }

    /**
     * 根据手机号查询代理人
     * @param orgType
     * @param mobile
     * @return
     */
    @GetMapping("queryByMobile")
    public ApiResult<EmployeeVO> getEmployeeInfoByMobile(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                         @ApiParam("销售员手机号") @RequestParam(value = "mobile") String mobile) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVOByMobile(mobile);
        }else if (AgentOrgType.PARTNER.equals(orgType)){
            employeeVO = partnerEmployeeService.getEmployeeVOByMobile(mobile);
        }
        return ApiResult.ok(employeeVO);
    }


    @ApiOperation("根据代理人工号与类型查询代理人信息")
    @GetMapping("/info")
    public ApiResult<EmployeeVO> getEmployeeByEmployeeCode(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType", required = false) AgentOrgType orgType,
                                                           @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        if (StringUtils.isEmpty(employeeCode) || "undefined".equals(employeeCode)) {
            log.info("getEmployeeByEmployeeCode employeeCode = {}", employeeCode);
            throw new BadRequestException("employeeCode入参有误!");
        }
        return ApiResult.ok(employeeService.getEmployeeByEmployeeCode(orgType, employeeCode));
    }

    @GetMapping("/info/encryption")
    public ApiResult<EmployeeVO> getEmployeeByEmployeeCodeByBncryption(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                             @ApiParam("销售员代码 密文") @RequestParam(value = "employeeCode") String employeeCode) {
        AssertUtil.notNull(employeeCode, new ApiException("工号不能为空"));
        String key = "jBRCQd6p#hurZDhO8n6t4@pKrVa8JCKG";
        String iv = "P8MxE59mFRORPVUf";
        String code = "";
        try {
            code = AESUtil.decrypt(employeeCode,key,iv);
        } catch (Exception e) {
            log.error("AES解密异常,employeeCode:{}",employeeCode,e);
            throw new ApiException("工号解密异常");
        }
        return ApiResult.ok(employeeService.getEmployeeByEmployeeCode(orgType, code));
    }

    @ApiOperation("获取当前登录销售员信息")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<MyEmployeeVO> getMyEmployeeInfo() {
        String orgType = RequestContextHolder.getOrgType();
        String employeeCode = RequestContextHolder.getEmployeeCode();
        if (AgentOrgType.CHANNEL.name().equals(orgType)) {
            return ApiResult.ok(channelEmployeeService.getMyEmployeeVO(employeeCode));
        }
        return ApiResult.ok(partnerEmployeeService.getMyEmployeeVO(employeeCode));
    }

    @ApiOperation("获取销售员")
    @GetMapping("/simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleEmployeeVO>> listEmployeeSimple(@ApiParam("合伙人编码数组") @RequestParam(value = "partnerCodes", required = false) String[] partnerCodes,
                                                                @ApiParam("渠道商编码数组") @RequestParam(value = "channelCodes", required = false) String[] channelCodes,
                                                                @ApiParam("销售员名称或者代码") @RequestParam(value = "value") String value,
                                                                @ApiParam("状态：SERVING、ALL") @RequestParam(value = "status", required = false, defaultValue = "SERVING") String status,
                                                                @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "20") long size) {
        EmployeeQueryRequest queryRequest = EmployeeQueryRequest.builder().status(status)
                .partnerCodes(partnerCodes).channelCodes(channelCodes).value(value).size(size).build();
        queryRequest.correctPageQueryParameters();
        return ApiResult.ok(employeeService.listEmployeeSimple(queryRequest));
    }

    @ApiOperation("获取销售员")
    @GetMapping("/simple-no-agent")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleEmployeeVO>> listEmployeeSimpleCopy(@ApiParam("合伙人编码数组") @RequestParam(value = "partnerCodes", required = false) String[] partnerCodes,
                                                                    @ApiParam("渠道商编码数组") @RequestParam(value = "channelCodes", required = false) String[] channelCodes,
                                                                    @ApiParam("销售员名称或者代码") @RequestParam(value = "value") String value,
                                                                    @ApiParam("状态：SERVING、ALL") @RequestParam(value = "status", required = false, defaultValue = "SERVING") String status) {
        EmployeeQueryRequest queryRequest = EmployeeQueryRequest.builder().status(status)
                .partnerCodes(partnerCodes).channelCodes(channelCodes).value(value).build();
        queryRequest.correctPageQueryParameters();
        return ApiResult.ok(employeeService.listEmployeeSimpleCopy(queryRequest));
    }

    @ApiOperation("获取所有销售员")
    @GetMapping("/allemployees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> allEmployees() {
        return ApiResult.ok(employeeService.allEmployees());
    }

    @ApiOperation("分页分类获取所有销售人员")
    @PostMapping("/all-employees-by-page")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<EmployeeVO>> allEmployeesByPageAndType(@RequestBody AllEmpByPageRequest request) {
        PageQueryRequest queryRequest = PageQueryRequest.builder()
                .current(request.getPage())
                .size(request.getSize())
                .build();
        return ApiResult.ok(employeeService.allEmployeesByPageAndType(queryRequest,request));
    }


    @ApiOperation("根据公司代码-获取所有有效的销售员")
    @GetMapping("/company/employees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<String, EmployeeVO>> allEmployeesByCompanycode(@ApiParam("公司代码") @RequestParam(value = "companycode") String companycode) {
        return ApiResult.ok(employeeService.allEmployeesByCompanycode(companycode));
    }


    @ApiOperation("根据公司代码-获取所有的销售员")
    @GetMapping("/company/allEmployees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> allEmployeesByCompanycodeAndStatus(@ApiParam("公司代码") @RequestParam(value = "companycode", required = false) String companycode,
                                                                          @ApiParam("销售员状态 SERVING-有效 INVALID-无效 null查全部") @RequestParam(value = "status", required = false) String status) {
        return ApiResult.ok(employeeService.allEmployeesByCompanycodeAndStatus(companycode, status));
    }


    @ApiOperation("查询销售员主管")
    @GetMapping("/leader")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<EmployeeVO> queryEmployeeLeader(@ApiParam("销售员employeeCode") @RequestParam(value = "employeeCode") String employeeCode) {
        return ApiResult.ok(employeeService.queryEmployeeLeader(employeeCode));
    }

    @ApiOperation("根据代理人工号集合、组织机构类型查询代理人状态信息")
    @GetMapping("/clue")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<ClueEmployeeVO>> getByEmployeeCodeList(@ApiParam("代理工号集合") @RequestParam(value = "employeeCodeList") List<String> employeeCodeList,
                                                                 @ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") String orgType) {
        if (CollectionUtils.isNotEmpty(employeeCodeList)) {
            log.info("getByEmployeeCodeList by employeeCodeList = {}", employeeCodeList);
            AssertUtil.isTrue(employeeCodeList.size() <= 500, new BadRequestException("employeeCodeList 批量最多支持500个"));
        }
        return ApiResult.ok(employeeService.getByEmployeeCodeList(employeeCodeList, orgType));
    }


    @Autowired
    private TeamService teamService;

    @ApiOperation("范围内生日员工信息查询")
    @GetMapping("/team/getBirthDayEmpList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getTeamBirthDayList(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode", required = false) String saleTeamCode,
                                                           @ApiParam("开始日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "startDate") Date startDate,
                                                           @ApiParam("结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "endDate") Date endDate
    ) {
        try {
            return ApiResult.ok(teamService.getBirthDayEmpList(saleTeamCode, startDate, endDate));
        } catch (Exception e) {
            log.error("查询生日信息出错", e);
            return ApiResult.fail("查询生日信息出错," + e.getMessage());
        }
    }

    @ApiOperation("根据业务员工号查询业务员信息")
    @GetMapping("/getEmpInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<EmployeeVO> getEmpInfo(@ApiParam("业务员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        try {
            EmployeeVO empInfo = teamService.getEmpInfo(employeeCode);
            return ApiResult.ok(empInfo);
        } catch (Exception e) {
            log.error("查询信息出错", e);
            return ApiResult.fail("查询信出错," + e.getMessage());
        }
    }


    @ApiOperation("根据生日查询代理人")
    @GetMapping("/queryByBirthday")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getEmployeeInfoByBirthday(@RequestParam(value = "birthdayList") List<String> birthdayList) {
        List<EmployeeVO> employeeVOS = employeeService.getEmployeeInfoByBirthday(birthdayList);
        return ApiResult.ok(employeeVOS);
    }

}
