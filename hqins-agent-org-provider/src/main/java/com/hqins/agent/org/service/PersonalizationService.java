package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.org.CustomMenu;
import com.hqins.agent.org.dao.entity.org.Personalization;
import com.hqins.agent.org.model.enums.AppType;
import com.hqins.agent.org.model.request.PersonalizationSaveByFeatureRequest;
import com.hqins.agent.org.model.request.PersonalizationSaveRequest;
import com.hqins.agent.org.model.request.PersonalizationrQueryRequest;
import com.hqins.agent.org.model.vo.CustomMenuVO;
import com.hqins.agent.org.model.vo.MenuGroupVO;
import com.hqins.common.base.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
public interface PersonalizationService {


    List<CustomMenuVO> getMyCustomMenu(PersonalizationrQueryRequest request);

    List<MenuGroupVO> getAllCustomMenu(PersonalizationrQueryRequest request);

    void updateCustomMenu(PersonalizationSaveRequest request);

    void updateCustomMenuByFeature(PersonalizationSaveByFeatureRequest request);

    PageInfo<CustomMenu> getCustomMenuNode(String platform, String node,String name, Long current, Long size);

    List<Personalization> getOrg(String code);

    String createMenu (CustomMenu request);

    String updateMenu(CustomMenu request);

    String deleteMenu(Long id);
}
