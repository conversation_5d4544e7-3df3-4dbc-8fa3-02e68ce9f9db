package com.hqins.agent.org.web.controller;

import cn.hutool.core.collection.CollUtil;
import com.hqins.agent.org.model.vo.BusinessOrgVO;
import com.hqins.agent.org.model.vo.EmployeeOrgVO;
import com.hqins.agent.org.service.EmployeeService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * @Author: lijian
 * @Date: 2022/12/7 9:56 上午
 */
@Api(tags = "销售员org管理")
@RestController
@RequestMapping("/employee-org")
@Slf4j
public class EmployeeOrgController {

    @Autowired
    EmployeeService employeeService;

    @ApiOperation("获取销售员及所属org信息")
    @GetMapping("/employee-info")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<EmployeeOrgVO> queryEmployeeOrgInfo(@ApiParam("销售员编码") @RequestParam(value = "employeeCode", required = true) String employeeCode) {
        return ApiResult.ok(employeeService.queryEmployeeOrgInfo(employeeCode));
    }

    @ApiOperation("获取销售员及所属org信息")
    @GetMapping("/org-employee-info")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<BusinessOrgVO> querySaleTeamEmployeeInfo(@ApiParam("团队编码") @RequestParam(value = "saleTeamCode", required = true) String saleTeamCode) {
        return ApiResult.ok(employeeService.querySaleTeamEmployeeInfo(saleTeamCode));
    }

    @ApiOperation("获取集合中所有销售员及所属org信息")
    @GetMapping("/employee-info/list")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeOrgVO>> queryEmployeeOrgInfo(@ApiParam("销售员编码") @RequestParam(value = "employeeCodeList", required = true) List<String> employeeCodeList) {
        log.info("employee info list {}", employeeCodeList);
        if (CollUtil.isNotEmpty(employeeCodeList)) {
            return ApiResult.ok(employeeService.queryEmployeeOrgInfoByList(employeeCodeList));
        }else {
            return ApiResult.ok(new ArrayList<>());
        }
    }
}
