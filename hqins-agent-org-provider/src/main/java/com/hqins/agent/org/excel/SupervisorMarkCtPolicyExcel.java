package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 指标预警--犹豫期退保
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class SupervisorMarkCtPolicyExcel implements Serializable {

    @ExcelProperty("销售机构")
    @ApiModelProperty("销售机构")
    private String instName;

    @ExcelProperty("出单业务员")
    @ApiModelProperty("出单业务员")
    private String agentName;

    @ExcelProperty("保单号")
    @ApiModelProperty("保单号")
    private String policyNo;

    @ExcelProperty("险种代码")
    @ApiModelProperty("险种代码")
    private String riskCode;

    @ExcelProperty("险种名称")
    @ApiModelProperty("险种名称")
    private String riskName;

    @ExcelProperty("交费方式")
    @ApiModelProperty("交费方式")
    private String paymentWay;

    @ExcelProperty("交费年期")
    @ApiModelProperty("交费年期")
    private String paymentYear;

    @ExcelProperty("保险期间")
    @ApiModelProperty("保险期间")
    private String insureYears;

    @ExcelProperty("保费(元)")
    @ApiModelProperty("保费(元)")
    private BigDecimal premium;

    @ExcelProperty("保额(元)")
    @ApiModelProperty("保额(元)")
    private BigDecimal amnt;

    @ExcelProperty("退保日期")
    @ApiModelProperty("退保日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate ctDate;

}
