package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 渠道商代理人查验日志数据访问接口
 * <AUTHOR>
 * @since 2023-09-11
 */
@Mapper
@DS("exms")
@Repository
public interface ChannelEmployeeCheckLogMapper extends BaseMapper<ChannelEmployeeCheckLog> {

    Long insertSelective(ChannelEmployeeCheckLog channelEmployeeCheckLog);

    int updateByPrimaryKeySelective(ChannelEmployeeCheckLog channelEmployeeCheckLog);


}

