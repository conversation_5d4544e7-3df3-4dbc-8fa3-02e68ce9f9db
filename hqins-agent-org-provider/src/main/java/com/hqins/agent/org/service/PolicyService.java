package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.ExpireQueryListRequest;
import com.hqins.agent.org.model.vo.PolicyExpireMapVO;
import com.hqins.agent.org.model.vo.PolicyExpireVO;
import com.hqins.common.base.page.PageInfo;

public interface PolicyService {

    PageInfo<PolicyExpireVO> getPartnerExpire(ExpireQueryListRequest request) ;

    PageInfo<PolicyExpireMapVO> getChannelExpire(ExpireQueryListRequest request);

    PolicyExpireVO getPolicyExpire(ExpireQueryListRequest request);
}
