package com.hqins.agent.org.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@RefreshScope
@Data
@ConfigurationProperties(prefix = "filter-channel-code")
public class FilterChannelCodeMapConfig {
    private Map<String,Object> filterChannelCodeMap;
}
