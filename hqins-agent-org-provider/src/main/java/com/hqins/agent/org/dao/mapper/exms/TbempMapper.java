package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.request.PartnerEmployeeRequest;
import com.hqins.agent.org.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-05-07
 * @Description 代理人信息
 */
@Mapper
@DS("exms")
@Repository
public interface TbempMapper extends BaseMapper<Tbemp> {

    Page<Map<String, Object>> listPage(@Param("queryRequest") PartnerEmployeeRequest queryRequest, @Param("topCodes") Set<String> topCodes, @Param("saleteamincodes") Set<String> saleteamincodes, @Param("partnerOrgCodes") List<String> partnerOrgCodes, Page<?> page);

    List<Map<String, Object>> listNoPage(@Param("queryRequest") PartnerEmployeeRequest queryRequest, @Param("topCodes") Set<String> topCodes, @Param("saleteamincodes") Set<String> saleteamincodes, @Param("partnerOrgCodes") List<String> partnerOrgCodes);

    List<Map<String, Object>> listSimple(@Param("queryRequest") EmployeeQueryRequest queryRequest, @Param("topCodes") Set<String> topCodes, @Param("saleteamincodes") Set<String> saleteamincodes, @Param("partnerOrgCodes") List<String> partnerOrgCodes);

    List<Map<String, Object>> list(@Param("employeeCode") String employeeCode);

    List<Map<String, Object>> queryByMobile(@Param("mobile") String mobile);

    List<Map<String, Object>> selectByEmployeeCode(@Param("employeeCode") String employeeCode, @Param("status") String status);

    List<Map<String, Object>> listEmployeeSimple(@Param("queryRequest") EmployeeQueryRequest queryRequest);

    /**
     * 根据参数查询
     * <p>
     * 根据证件号或执业证书号查询
     *
     * @param idCode
     * @param licenseNo
     * @return
     */
    List<Tbemp> getByParams(@Param("idCode") String idCode, @Param("licenseNo") String licenseNo);

    //入参分页
    List<Map<String, Object>> myList();

    IPage<Tbemp> myListByPage(IPage<Tbemp> page);

    /**
     * 根据执业证书号集合查询人员信息
     *
     * @param licenseNoList
     * @return
     */
    List<Tbemp> getByLicenseNoList(@Param("licenseNoList") List<String> licenseNoList);


    List<Map<String, Object>> getByMga(@Param("mobile") String mobile, @Param("idType") String idType, @Param("idCode") String idCode, @Param("licenseNo") String licenseNo);

    /**
     * 根据公司代码，查询所有有效的代理人信息
     *
     * @param companycode
     * @return
     */
    List<Tbemp> getAllTbempByCompanycode(@Param("companycode") String companycode);

    List<Tbemp> allEmployeesByCompanycodeAndStatus(@Param("companycode") String companycode, @Param("status") String status);


    List<Tbemp> selectByStartSize(@Param("start") long start, @Param("size") long size);

    Tbemp queryEmployeeLeader(@Param("employeeCode") String employeeCode);

    /**
     * 查询员工信息
     * @param employeeCode
     * @return
     */
    List<Tbemp> queryEmployee(@Param("employeeCode") String employeeCode);

    List<Tbemp> getByEmployeeCodeList(@Param("employeeCodeList") List<String> employeeCodeList);

    String queryIdCode(String idCode);

    String queryPhone(String mobile);

    Map<String,Object> getEmployeeExp(@Param("employeeCode") String employeeCode);

    List<EmployeeOrgVO> queryEmployeeOrgInfoByList(@Param("employeeCodeList") List<String> employeeCodeList);




    List<AppEmployeeRecruitmentInfoListVO> queryRecruitmentProgressList(@Param("empInCode") String empInCode,@Param("employeeName") String employeeName);

    List<AppEmployeeRecruitmentInfoListVO> queryMyAgentListByEmpInCodeAndInstCode(@Param("empInCode") String empInCode, @Param("employeeName") String employeeName);

    List<AppEmployeeRecruitmentInfoListVO> queryMyAgentListByInstCodeList(@Param("instCoeList") List<String> instCoeList,@Param("employeeCode") String employeeCode,@Param("employeeName") String employeeName);

    List<AppEmployeeInfoVO> queryAppRecruitmentEmployeeInfo(@Param("employeeCode") String employeeCode,@Param("visitorId") String visitorId);

    List<AppEmployeePersonalExperienceVO> queryEmployeePersonalExperience(@Param("empInCode") String empInCode);

    List<String> getEmployByEmployeeCode(@Param("employeeCode") String employeeCode);

    List<EmployeeOrgVO> getTeamEmpInfo(@Param("saleTeamCode") String saleTeamCode);

    HonorEmpVO getEmpInfo(@Param("empCode") String empCode);

    EmployeeVO queryEmpAreaCode(@Param("empCode") String empCode);
}

