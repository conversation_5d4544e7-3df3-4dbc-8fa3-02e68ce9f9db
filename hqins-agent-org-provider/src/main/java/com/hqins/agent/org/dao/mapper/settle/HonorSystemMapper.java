package com.hqins.agent.org.dao.mapper.settle;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory;
import com.hqins.agent.org.dao.entity.settle.HonorCurrentInfo;
import com.hqins.agent.org.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/21
 */
@Mapper
@Repository
@DS("settle")
public interface HonorSystemMapper extends BaseMapper<HonorListVO> {
    List<HonorQueryListVO> getHonorList(@Param("empCode") String empCode);

    List<HonorPeriodMetricsVO> getGuinnessMetrics(@Param("agentCode") String agentCode, @Param("checkPeriod") String checkPeriod,
                                                  @Param("honorCode") String honorCode, @Param("metricsType") String value);

    HonorCurrentInfo getQXJYHCurrentDetail(@Param("empCode") String empCode, @Param("honorType") String honorType);

    List<HonorAssessmentHistory> getQXJYHHistoryDetail(@Param("empCode") String empCode, @Param("honorType") String honorType);

    List<HonorAssessmentHistory> getQYBSXDetail(@Param("empCode") String empCode, @Param("honorType") String honorType, @Param("assessmentPeriods") List<String> assessmentPeriods);

    List<HonorPeriodMetrics> getQYBSXPeriodMetrics(@Param("empCode") String empCode, @Param("honorType") String honorType, @Param("assessmentPeriods") List<String> assessmentPeriods);

    List<HonorPeriodMetrics> getGRRYPeriodMetrics(@Param("empCode") String empCode, @Param("honorType") String honorType,
                                                  @Param("honorCodeList") List<String> honorCodeList, @Param("period") String period, @Param("teamCode") String teamCode);

    List<HonorPeriodMetrics> getImmediateHigherMetrics(@Param("honorType") String honorType, @Param("honorCode") String honorCode, @Param("period") String period, @Param("lastPosition") String lastPosition);

    List<HonorPeriodMetrics> getMDRTMetrics(@Param("empCode") String empCode, @Param("honorType") String honorType, @Param("period") String period);

    List<HonorPeriodMetrics> getQHJNXMetrics(@Param("empCode") String empCode, @Param("honorType") String honorType, @Param("period") String period);

    List<HonorAssessmentHistory> getHonorHistory(@Param("empCode") String empCode);

    List<HonorCongratulationVO> getHonorCongratulationList(@Param("employeeCodeList") List<String> employeeCodeList, @Param("honorType") String honorType, @Param("year") String year);

    List<HonoraryTitleInfoVO> getEmpHonoraryDetail(@Param("honorId") String honorId);
}
