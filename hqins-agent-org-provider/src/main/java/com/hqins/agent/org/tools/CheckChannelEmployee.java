package com.hqins.agent.org.tools;

import com.hqins.agent.org.model.request.CheckChannelEmployeeBody;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("渠道商校验实体")
@Data
public class CheckChannelEmployee extends CheckChannelEmployeeBody {

    @ApiModelProperty("代理人所属渠道商")
    private String channelCode;

    @ApiModelProperty("代理人所属渠商名称")
    private String channelName;

    @ApiModelProperty("代理人归属的法人机构代码")
    private String corporationCode;

    @ApiModelProperty("代理人归属的法人机构名称")
    private String corporationName;

    @ApiModelProperty("是否白名单校验")
    private boolean whiteFlag = false;


}
