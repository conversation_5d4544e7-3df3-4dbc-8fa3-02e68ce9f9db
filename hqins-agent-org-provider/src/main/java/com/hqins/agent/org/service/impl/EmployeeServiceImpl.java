package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.RankSequ;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.BusinessTypeEnum;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.request.AllEmpByPageRequest;
import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.ChannelEmployeeService;
import com.hqins.agent.org.service.EmployeeService;
import com.hqins.agent.org.service.PartnerEmployeeService;
import com.hqins.agent.org.service.SupervisorEmployeeService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.page.PageQueryRequest;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.um.model.dto.AccountInfoDTO;
import com.hqins.um.model.dto.AgentDTO;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {

    private final TbempMapper tbempMapper;
    private final ChannelEmployeeMapper channelEmployeeMapper;
    private final UmClient umClient;
    private final ChannelEmployeeService channelEmployeeService;
    private final PartnerEmployeeService partnerEmployeeService;
    private final EmployeeExternalService employeeExternalService;
    private final CacheService cacheService;
    private final SupervisorEmployeeService supervisorEmployeeService;

    @Autowired
    private  BaseInstMapper baseInstMapper;

    @Autowired
    private TbsaleteamMapper tbsaleteamMapper;

    @Autowired
    private SupervisorEmployeeMapper supervisorEmployeeMapper;

    public EmployeeServiceImpl(TbempMapper tbempMapper, ChannelEmployeeMapper channelEmployeeMapper, UmClient umClient, ChannelEmployeeService channelEmployeeService, PartnerEmployeeService partnerEmployeeService, EmployeeExternalService employeeExternalService, CacheService cacheService, SupervisorEmployeeService supervisorEmployeeService, TbsaleteamMapper tbsaleteamMapper) {
        this.tbempMapper = tbempMapper;
        this.channelEmployeeMapper = channelEmployeeMapper;
        this.umClient = umClient;
        this.channelEmployeeService = channelEmployeeService;
        this.partnerEmployeeService = partnerEmployeeService;
        this.employeeExternalService = employeeExternalService;
        this.cacheService = cacheService;
        this.supervisorEmployeeService = supervisorEmployeeService;
        this.tbsaleteamMapper = tbsaleteamMapper;
    }


    @Override
    public List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest) {
        List<SimpleEmployeeVO> employees = new ArrayList<>();
        if (queryRequest.getChannelCodes() != null && queryRequest.getChannelCodes().length > 0) {
            //获取渠道商销售员
            LambdaQueryWrapper<ChannelEmployee> w = new LambdaQueryWrapper<ChannelEmployee>().in(ChannelEmployee::getChannelCode, (Object) queryRequest.getChannelCodes());
            if ("SERVING".equals(queryRequest.getStatus())) {
                w.eq(ChannelEmployee::getStatus, EmployeeStatus.SERVING.name());
            }
            if (!StringUtil.isBlank(queryRequest.getValue())) {
                w.and(wrapper -> wrapper.like(ChannelEmployee::getCode, queryRequest.getValue()).or().like(ChannelEmployee::getName, queryRequest.getValue()));
            }
            w.orderByAsc(ChannelEmployee::getCode).last("limit 0," + (queryRequest.getSize() > 1000 ? 1000 : queryRequest.getSize()));
            List<ChannelEmployee> list = channelEmployeeMapper.selectList(w);
            employees.addAll(BeanCopier.copyList(list, PartnerConverter::channelEmployeeToSimpleEmployeeVO));
        }
        if (queryRequest.getPartnerCodes() != null && queryRequest.getPartnerCodes().length > 0) {
            //获取合伙人销售员
            List<Map<String, Object>> list = tbempMapper.listEmployeeSimple(queryRequest);
            employees.addAll(BeanCopier.copyList(list, PartnerConverter::mapToSimpleEmployeeVO));
        }
        if (!employees.isEmpty()) {
            List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(StreamEx.of(employees).map(SimpleEmployeeVO::getCode).toList());
            if (!CollectionUtils.isEmpty(accountInfoDTOList)) {
                Map<String, AgentDTO> agentUserInfosMap = convertData(accountInfoDTOList);
                for (SimpleEmployeeVO vo : employees) {
                    AgentDTO a = agentUserInfosMap.get(vo.getCode());
                    if (null != a && null != a.getAgentId()) {
                        vo.setAgentId(a.getAgentId());
                    }
                }
            }
        }
        //返回agentId不为空的
        return StreamEx.of(employees.stream().filter(employee -> null != employee.getAgentId())).collect(Collectors.toList());
    }


    public Map<String, AgentDTO> convertData(List<AccountInfoDTO> accountInfoDTOList) {
        Map<String, AgentDTO> agentDtoMap = new HashMap<>(accountInfoDTOList.size());
        for (AccountInfoDTO accountInfoDTO : accountInfoDTOList) {
            if (null == accountInfoDTO.getAgentInfo()) {
                continue;
            }
            agentDtoMap.put(accountInfoDTO.getAgentInfo().getEmployeeCode(), accountInfoDTO.getAgentInfo());
        }
        return agentDtoMap;
    }

    @Override
    public List<SimpleEmployeeVO> listEmployeeSimpleCopy(EmployeeQueryRequest queryRequest) {
        List<SimpleEmployeeVO> employees = new ArrayList<>();
        if (queryRequest.getChannelCodes() != null && queryRequest.getChannelCodes().length > 0) {
            //获取渠道商销售员
            LambdaQueryWrapper<ChannelEmployee> w = new LambdaQueryWrapper<ChannelEmployee>()
                    .in(ChannelEmployee::getChannelCode, (Object) queryRequest.getChannelCodes());
            if ("SERVING".equals(queryRequest.getStatus())) {
                w.eq(ChannelEmployee::getStatus, EmployeeStatus.SERVING.name());
            }
            if (!StringUtil.isBlank(queryRequest.getValue())) {
                w.and(wrapper -> wrapper.like(ChannelEmployee::getCode, queryRequest.getValue()).or().like(ChannelEmployee::getName, queryRequest.getValue()));
            }
            w.orderByAsc(ChannelEmployee::getCode);
            List<ChannelEmployee> list = channelEmployeeMapper.selectList(w);
            employees.addAll(BeanCopier.copyList(list, PartnerConverter::channelEmployeeToSimpleEmployeeVO));
        }
        if (queryRequest.getPartnerCodes() != null && queryRequest.getPartnerCodes().length > 0) {
            //获取合伙人销售员
            List<Map<String, Object>> list = tbempMapper.listEmployeeSimple(queryRequest);
            employees.addAll(BeanCopier.copyList(list, PartnerConverter::mapToSimpleEmployeeVO));
        }
        //返回agentId不为空的
        return employees;
    }

    @Override
    public List<EmployeeVO> allEmployees() {
        List<EmployeeVO> result = new ArrayList<>();

        //查询渠道商人员
        List<ChannelEmployee> channelEmployees = channelEmployeeMapper.selectList(new LambdaQueryWrapper<ChannelEmployee>().eq(ChannelEmployee::getStatus, "SERVING"));
        result = StreamEx.of(channelEmployees).map(employee -> {
            EmployeeVO vo = EmployeeVO.builder()
                    .topCode(employee.getChannelCode())
                    .code(employee.getCode())
                    .orgType(AgentOrgType.CHANNEL)
                    .build();
            return vo;
        }).toList();

        //查询合伙人人员     /**
        //     * 工作状态:SERVING-在职；LEAVING-已离职
        //     */
        //    private String status;    SupervisorEmployee
        List<Map<String, Object>> list = tbempMapper.myList();
        for (Map<String, Object> map : list) {
            if (null != map.get("topCode") && null != map.get("code") && StringUtil.isNoneEmpty(map.get("topCode").toString()) && StringUtil.isNoneEmpty(map.get("code").toString())) {
                EmployeeVO vo = EmployeeVO.builder()
                        .topCode(null == map.get("topCode") ? "" : map.get("topCode").toString())
                        .code(map.get("code").toString())
                        .orgType(AgentOrgType.PARTNER)
                        .build();
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    public Map<String, EmployeeVO> allEmployeesByCompanycode(String companycode) {
        Map<String, EmployeeVO> map = new HashMap<>();
        if (StringUtil.isEmpty(companycode)) {
            return map;
        }
        List<Tbemp> tbempList = tbempMapper.getAllTbempByCompanycode(companycode);
        if (tbempList == null || tbempList.size() == 0) {
            return map;
        }
        for (Tbemp tbemp : tbempList) {
            EmployeeVO employeeVO = new EmployeeVO();
            employeeVO.setCode(tbemp.getEmpcode());
            map.put(tbemp.getEmpcode(), employeeVO);
        }
        return map;
    }


    @Override
    public List<EmployeeVO> allEmployeesByCompanycodeAndStatus(String companycode, String status) {
        List<EmployeeVO> list = new ArrayList<>();

        List<Tbemp> tbempList = tbempMapper.allEmployeesByCompanycodeAndStatus(companycode, status);

        if (CollectionUtils.isEmpty(tbempList)) {
            return list;
        }

        for (Tbemp tbemp : tbempList) {
            EmployeeVO employeeVO = new EmployeeVO();
            employeeVO.setCode(tbemp.getEmpcode());
            employeeVO.setName(tbemp.getEmpname());
            employeeVO.setTopCode(tbemp.getCompanycode());
            employeeVO.setOrgCode(tbemp.getInstCode());
            employeeVO.setOrgName(tbemp.getInstName());
            employeeVO.setOrgType(AgentOrgType.PARTNER);
            employeeVO.setBirthday(tbemp.getBirthday());
            //00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备
            String empStatus = tbemp.getEmpstatus();
            if ("01".equals(empStatus)) {
                employeeVO.setStatus(EmployeeStatus.SERVING);
            } else if ("02".equals(empStatus)) {
                employeeVO.setStatus(EmployeeStatus.INVALID);
            } else if ("04".equals(empStatus)) {
                employeeVO.setStatus(EmployeeStatus.LEAVING);
            } else {
                employeeVO.setStatus(EmployeeStatus.INVALID);
            }
            list.add(employeeVO);
        }
        return list;
    }


    @Override
    public EmployeeVO queryEmployeeLeader(String employeeCode) {
        if (StringUtil.isEmpty(employeeCode)) {
            return null;
        }
        Tbemp tbemp = tbempMapper.queryEmployeeLeader(employeeCode);
        if (tbemp == null) {
            return null;
        }
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setCode(tbemp.getEmpcode());
        employeeVO.setName(tbemp.getEmpname());
        String empStatus = tbemp.getEmpstatus();
        //00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职 05-待报备',
        if ("01".equals(empStatus)) {
            employeeVO.setStatus(EmployeeStatus.SERVING);
        } else if ("02".equals(empStatus)) {
            employeeVO.setStatus(EmployeeStatus.INVALID);
        } else if ("04".equals(empStatus)) {
            employeeVO.setStatus(EmployeeStatus.LEAVING);
        } else {
            employeeVO.setStatus(EmployeeStatus.INVALID);
        }
        return employeeVO;
    }


    @Override
    public BusinessOrgVO querySaleTeamEmployeeInfo(String saleTeamCode) {

        Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();

        Tbsaleteam tbsaleteam = allTbsaleteamsMap.get(saleTeamCode);
        if (tbsaleteam == null) {
            return null;
        }

        //过滤 -- 只展示有效团队的数据 除00以外的数据不进行展示
        if (!AppConsts.STR_00.equals(tbsaleteam.getSaleteamstatus())) {
            return null;
        }

        //顶层
        BusinessOrgVO vo = queryBusinessOrgVOByTeam(tbsaleteam);

        //组级
        if (AppConsts.TEAM_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            return vo;
        }
        else if (AppConsts.DEPT_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            //设置下属部门 组
            vo.setBusinessOrgVOList(querySubordinate(tbsaleteam));
        }
        //区级
        else if (AppConsts.AREA_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            //设置下属部 部
            vo.setBusinessOrgVOList(querySubordinate(tbsaleteam));
            //循环部级vo 设置组
            if (CollectionUtils.isNotEmpty(vo.getBusinessOrgVOList())) {
                for (BusinessOrgVO deptOrg : vo.getBusinessOrgVOList()) {
                    Tbsaleteam deptTeam = allTbsaleteamsMap.get(deptOrg.getBusinessCode());
                    if (deptTeam != null && AppConsts.STR_00.equals(tbsaleteam.getSaleteamstatus())) {
                        deptOrg.setBusinessOrgVOList(querySubordinate(deptTeam));
                    }
                }
            }
        }

        return vo;
    }

    /**
     * 查询下属部门
     * @return
     */
    private List<BusinessOrgVO> querySubordinate(Tbsaleteam tbsaleteam) {
        List<Tbsaleteam> subTeamList = Lists.newArrayList();
        List<Tbsaleteam> allTbsaleteams = cacheService.getAllTbsaleteams();
        for (Tbsaleteam t : allTbsaleteams) {
            //过滤，只添加有效的
            if (!AppConsts.STR_00.equals(tbsaleteam.getSaleteamstatus())) {
                continue;
            }
            if (tbsaleteam.getSaleteamcode().equals(t.getSupersaleteamcode())) {
                subTeamList.add(t);
            }
        }
        if (CollectionUtils.isEmpty(subTeamList)) {
            return null;
        }

        List<BusinessOrgVO> resultList = Lists.newArrayList();

        for (Tbsaleteam t : subTeamList) {
            BusinessOrgVO businessOrgVO = queryBusinessOrgVOByTeam(t);
            if (businessOrgVO != null) {
                resultList.add(businessOrgVO);
            }
        }

        return resultList;
    }

    private BusinessOrgVO queryBusinessOrgVOByTeam(Tbsaleteam tbsaleteam) {
        if (tbsaleteam == null) {
            return null;
        }

        if (!AppConsts.STR_00.equals(tbsaleteam.getSaleteamstatus())) {
            return null;
        }

        BusinessOrgVO vo = new BusinessOrgVO();
        vo.setBusinessCode(tbsaleteam.getSaleteamcode());
        vo.setBusinessName(tbsaleteam.getSaleteamname());
        vo.setEmployeeOrgVOList(queryEmployeeOrgVOList(tbsaleteam));
        vo.setIfpFlag(AppConsts.STR_IFP.equals(tbsaleteam.getSaleteamtype()));
        //组级
        if (AppConsts.TEAM_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            vo.setBusinessType(BusinessTypeEnum.TEAM);
            return vo;
        }
        else if (AppConsts.DEPT_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            vo.setBusinessType(BusinessTypeEnum.DEPT);
        }
        //区级
        else if (AppConsts.AREA_LEVEL.equals(tbsaleteam.getTeamlevel())) {
            vo.setBusinessType(BusinessTypeEnum.AREA);
        }
        return vo;
    }

    private List<EmployeeOrgVO> queryEmployeeOrgVOList(Tbsaleteam tbsaleteam) {
        Map<String, Tbemp> allTbempInCodeMap = cacheService.getAllTbempInCodeMap();

        List<EmployeeOrgVO> resultList = Lists.newArrayList();
        for (Tbemp tbemp : allTbempInCodeMap.values()) {
            if (tbsaleteam.getSaleteamincode().equals(tbemp.getSaleteamincode())) {
                if ("01".equals(tbemp.getEmpstatus()) && ("N".equals(tbemp.getIsvirtualemp()) || "n".equals(tbemp.getIsvirtualemp()))) {
                    resultList.add(queryEmployeeOrgInfo(tbemp.getEmpcode()));
                }
            }
        }
        return resultList;
    }

    @Override
    public EmployeeOrgVO queryEmployeeOrgInfo(String employeeCode) {
        //员工缓存
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        Map<String, Tbemp> allTbempInCodeMap = cacheService.getAllTbempInCodeMap();
        //所有销售组
        List<Tbsaleteam> allTbsaleteamList = cacheService.getAllTbsaleteams();
        //所有销售组
        Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
        //所有的职级
        Map<String, RankDef> allRankDefMap = cacheService.getAllRankDefMap();
        //所有的职级序列
        Map<String, RankSequ> allRankSequMap = cacheService.getAllRankSequMap();


        Tbemp tbemp = allTbempMap.get(employeeCode);
        if (tbemp == null) {
            return null;
        }

        EmployeeOrgVO vo = new EmployeeOrgVO();
        /**
         * 设置人员信息
         */
        vo.setEmployeeCode(employeeCode);
        vo.setEmployeeName(tbemp.getEmpname());
        vo.setStatus(tbemp.getEmpstatus());
        vo.setIsVirtualEmp(tbemp.getIsvirtualemp());
        vo.setIsInsideFlag(tbemp.getIsinsideflag());
        if (StringUtils.isNotEmpty(tbemp.getSerialcode())) {
            RankSequ rankSequ = allRankSequMap.get(tbemp.getSerialcode());
            vo.setSerialCode(rankSequ == null ? tbemp.getSerialcode() : rankSequ.getRankSequCode());
            vo.setSerialName(rankSequ == null ? null : rankSequ.getRankSequName());

        }

        if (StringUtils.isNotEmpty(tbemp.getRankcode())) {
            RankDef rankDef = allRankDefMap.get(tbemp.getRankcode());
            vo.setRankCode(rankDef == null ? tbemp.getRankcode() : rankDef.getRankCode());
            vo.setRankName(rankDef == null ? null : rankDef.getRankName());
        }

        /**
         * 人员所在销售组
         */
        //当前销售人员所在的saleteam
        Tbsaleteam targetSaleTeam = null;
        for (Tbsaleteam tbsaleteam : allTbsaleteamList) {
            if (tbsaleteam.getSaleteamincode().equals(tbemp.getSaleteamincode())) {
                targetSaleTeam = tbsaleteam;
                break;
            }
        }
        if (targetSaleTeam == null) {
            return vo;
        }
        /**
         * 设置销售组信息
         */
        vo.setTeamCode(targetSaleTeam.getSaleteamcode());
        vo.setTeamName(targetSaleTeam.getSaleteamname());
        if (StringUtils.isNotEmpty(targetSaleTeam.getEmpincode())) {
            String empincode = targetSaleTeam.getEmpincode();
            vo.setTeamLeaderCode(empincode);
            vo.setTeamLeaderName(allTbempInCodeMap.get(empincode) == null ? null : allTbempInCodeMap.get(empincode).getEmpname());
        }

        /**
         * 设置区部组信息
         */
        setAreaDeptTeamInfo(vo, allTbsaleteamsMap, allTbempInCodeMap);

        return vo;
    }

    private void setAreaDeptTeamInfo(EmployeeOrgVO vo, Map<String, Tbsaleteam> allTbsaleteamsMap, Map<String, Tbemp> allTbempInCodeMap) {
        log.info("设置人员的区部组信息,入参:{}", JSONObject.toJSONString(vo));
        //员工所在 code
        String teamCode = vo.getTeamCode();
        Tbsaleteam team = null; // 组
        Tbsaleteam dept = null; // 部
        Tbsaleteam area = null; // 区

        Tbsaleteam currentTeam = allTbsaleteamsMap.get(teamCode);
        if (currentTeam.getTeamlevel().equals(AppConsts.TEAM_LEVEL)) {
            team = currentTeam;
        }
        if (currentTeam.getTeamlevel().equals(AppConsts.DEPT_LEVEL)) {
            dept = currentTeam;
        }
        if (currentTeam.getTeamlevel().equals(AppConsts.AREA_LEVEL)) {
            area = currentTeam;
        }

        //设置组信息
        if (team != null) {
            vo.setBusinessTeamCode(team.getSaleteamcode());
            vo.setBusinessTeamName(team.getSaleteamname());
            if (StringUtils.isNotEmpty(team.getEmpincode())) {
                Tbemp teamLeader = allTbempInCodeMap.get(team.getEmpincode());
                if (teamLeader != null){
                    vo.setBusinessTeamLeaderCode(teamLeader.getEmpcode());
                    vo.setBusinessTeamLeaderName(teamLeader.getEmpname());
                }
            }
        }

        //设置部信息
        if (dept != null || team != null) {
            if (dept == null) {
                String deptTeamcode = team.getSupersaleteamcode();
                if (StringUtils.isNotEmpty(deptTeamcode)) {
                    log.info("deptTeamcode:{}", deptTeamcode);
                    Tbsaleteam t = allTbsaleteamsMap.get(deptTeamcode);
                    if (t == null) {
                        log.info("未在缓存中查询到销售组信息:{}", deptTeamcode);
                        Tbsaleteam tbsaleteam = tbsaleteamMapper.selectBySaleTeamCode(deptTeamcode);
                        if(tbsaleteam == null){
                            log.info("未在数据库中查询到有效的销售组信息:{}", deptTeamcode);
                        }else {
                            if (AppConsts.DEPT_LEVEL.equals(tbsaleteam.getTeamlevel())) {
                                dept = tbsaleteam;
                            } else if (AppConsts.AREA_LEVEL.equals(tbsaleteam.getTeamlevel())) {
                                area = tbsaleteam;
                            }
                        }
                    } else {
                        /**
                         * 一般情下，组 -> 部 -> 区
                         * 这里添加特殊情况处理
                         */
                        if (AppConsts.DEPT_LEVEL.equals(t.getTeamlevel())) {
                            dept = t;
                        } else if (AppConsts.AREA_LEVEL.equals(t.getTeamlevel())) {
                            area = t;
                        }
                    }
                }
            }
            if (dept != null) {
                vo.setBusinessDeptCode(dept.getSaleteamcode());
                vo.setBusinessDeptName(dept.getSaleteamname());
                if (StringUtils.isNotEmpty(dept.getEmpincode())) {
                    Tbemp deptLeader = allTbempInCodeMap.get(dept.getEmpincode());
                    if (deptLeader != null){
                        vo.setBusinessDeptLeaderCode(deptLeader.getEmpcode());
                        vo.setBusinessDeptLeaderName(deptLeader.getEmpname());
                    }
                }
            }
        }

        //设置区信息
        if (area != null || dept != null) {
            if (area == null) {
                String areaTeamcode = dept.getSupersaleteamcode();
                if (StringUtils.isNotEmpty(areaTeamcode)) {
                    log.info("areaTeamcode:{}", areaTeamcode);
                    Tbsaleteam t = allTbsaleteamsMap.get(areaTeamcode);
                    if (t == null) {
                        log.info("未在缓存中查询到销售组信息:{}", areaTeamcode);
                        Tbsaleteam tbsaleteam = tbsaleteamMapper.selectBySaleTeamCode(areaTeamcode);
                        if(tbsaleteam != null){
                            if (AppConsts.AREA_LEVEL.equals(tbsaleteam.getTeamlevel())) {
                                area = tbsaleteam;
                            }
                        }else {
                            log.info("未在数据库中查询到有效的销售组信息:{}", areaTeamcode);
                        }
                    } else {
                        if (AppConsts.AREA_LEVEL.equals(t.getTeamlevel())) {
                            area = t;
                        }
                    }
                }
            }

            if (area != null) {
                vo.setBusinessAreaCode(area.getSaleteamcode());
                vo.setBusinessAreaName(area.getSaleteamname());
                if (StringUtils.isNotEmpty(area.getEmpincode())) {
                    Tbemp areaLeader = allTbempInCodeMap.get(area.getEmpincode());
                    if (areaLeader != null){
                        vo.setBusinessAreaLeaderCode(areaLeader.getEmpcode());
                        vo.setBusinessAreaLeaderName(areaLeader.getEmpname());
                    }
                }
            }
        }

    }
    @Override
    public EmployeeVO getEmployeeByEmployeeCode(AgentOrgType orgType, String employeeCode) {
        EmployeeVO employeeVO = null;
        if (null == orgType) {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode, null);
            if (null == employeeVO) {
                try{
                    employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
                }catch (Exception e){
                    log.error("getEmployeeByEmployeeCode_error,employeeCode:{}",employeeCode,e);
                }
            }
            if (employeeVO == null){
                employeeVO = supervisorEmployeeService.getSupervisorEmployee(employeeCode);
            }
        }

        if (AgentOrgType.PARTNER.equals(orgType)) {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode, null);
            if (employeeVO == null){
                employeeVO = supervisorEmployeeService.getSupervisorEmployee(employeeCode);
            }
            if(!StringUtil.isBlank(employeeVO.getOrgCode())){
                //获取合伙人三级管理机构
                Map<String,String> mangerMap = baseInstMapper.queryThreeMangerCodeByOrgCode(employeeVO.getOrgCode());
                if (mangerMap != null && !mangerMap.isEmpty()){
                    employeeVO.setThreeMangerCode(mangerMap.get("threeMangerCode"));
                    employeeVO.setThreeMangerName(mangerMap.get("threeMangerName"));
                }
            }
        }

        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
        }
        return employeeVO;
    }

    @Override
    public List<ClueEmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList, String orgType) {
        List<EmployeeVO> employeeVOList = Lists.newArrayList();
        if (AgentOrgType.CHANNEL.name().equals(orgType)) {
            employeeVOList = channelEmployeeService.getByEmployeeCodeList(employeeCodeList);
        }

        if (AgentOrgType.PARTNER.name().equals(orgType)) {
            employeeVOList = partnerEmployeeService.getByEmployeeCodeList(employeeCodeList);
        }
        return employeeExternalService.assembler(employeeVOList);
    }

    @Override
    public PageInfo<EmployeeVO> allEmployeesByPageAndType(PageQueryRequest queryRequest, AllEmpByPageRequest request) {
        switch (request.getOrgType()) {
            case "CHANNEL":
                // 查询渠道商人员
                Page<ChannelEmployee> channelPage = new Page<>(queryRequest.getCurrent(), queryRequest.getSize());
                IPage<ChannelEmployee> channelEmpPage = channelEmployeeMapper.getAllEmpByPage(channelPage);
                if (channelEmpPage.getRecords().size() > 0) {
                    return PageUtil.convert(channelEmpPage, all -> EmployeeVO.builder()
                            .topCode(all.getChannelCode())
                            .name(all.getName())
                            .code(all.getCode())
                            .orgType(AgentOrgType.CHANNEL)
                            .build());
                }
                break;
            case "PARTNER":
                // 查询合作伙伴人员
                Page<Tbemp> tbempPage = new Page<>(queryRequest.getCurrent(), queryRequest.getSize());
                IPage<Tbemp> tbempPages = tbempMapper.myListByPage(tbempPage);
                if (tbempPages.getRecords().size() > 0) {
                    return PageUtil.convert(tbempPages, all -> EmployeeVO.builder()
                            .topCode(all.getCompanycode())
                            .code(all.getEmpcode())
                            .name(all.getEmpname())
                            .orgType(AgentOrgType.PARTNER)
                            .isVirtualEmp(all.getIsvirtualemp())
                            .build());
                }
                break;
            default:
                // 查询体验账户人员
                Page<SupervisorEmployee> supervisorPage = new Page<>(queryRequest.getCurrent(), queryRequest.getSize());
                final IPage<SupervisorEmployee> supervisorEmpPage = supervisorEmployeeMapper.getAllEmpByPage(supervisorPage);
                if (supervisorEmpPage.getRecords().size() > 0) {
                    return PageUtil.convert(supervisorEmpPage, all -> EmployeeVO.builder()
                            .topCode(all.getTopCode())
                            .code(all.getEmployeeCode())
                            .name(all.getName())
                            .orgType(AgentOrgType.PARTNER)
                            .build());
                }
                break;
        }
        return null;
    }

    @Override
    public List<EmployeeVO> getEmployeeInfoByBirthday(List<String> birthdayList) {
        List<EmployeeVO> result = com.google.common.collect.Lists.newArrayList();
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        if(CollUtil.isEmpty(allTbempMap)){
            log.info("未获取到缓存的代理人信息");
            return result;
        }
        //根据生日作为map的key
        HashMap<String, List<Tbemp>> birthdayMap = allTbempMap.values().stream()
                //过滤生日不为空且目前为有效状态的代理人
                .filter(tbemp -> tbemp.getBirthday() != null && Objects.equals(tbemp.getEmpstatus(),"01"))
                .collect(Collectors.groupingBy(
                        tbemp -> {
                            LocalDate birthday = tbemp.getBirthday();
                            return birthday.getMonthValue() + "-" + birthday.getDayOfMonth();
                        },
                        HashMap::new,
                        Collectors.toList()
                ));
        birthdayList.forEach(each -> {
            if(birthdayMap.containsKey(each)){
                List<Tbemp> empList = birthdayMap.get(each);
                empList.forEach(emp -> {
                    EmployeeVO build = EmployeeVO.builder()
                            .code(emp.getEmpcode())
                            .name(emp.getEmpname())
                            .orgType(AgentOrgType.PARTNER)
                            .orgCode(emp.getInstCode())
                            .orgName(emp.getInstName())
                            .mobile(emp.getMaintelephone())
                            .birthday(emp.getBirthday())
                            .build();
                    result.add(build);
                });
            }
        });
        return result;
    }

    @Override
    public List<EmployeeOrgVO> queryEmployeeOrgInfoByList(List<String> employeeCodeList) {
        //通过sql直接查询代理人信息
        return tbempMapper.queryEmployeeOrgInfoByList(employeeCodeList);
    }

    @Override
    public List<EmployeeOrgVO> queryTeamEmpInfo(String saleTeamCode) {
        return tbempMapper.getTeamEmpInfo(saleTeamCode);
    }

    @Override
    public HonorEmpVO queryEmpInfo(String empCode) {
        return tbempMapper.getEmpInfo(empCode);
    }


}
