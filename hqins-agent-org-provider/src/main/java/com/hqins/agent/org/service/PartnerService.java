package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.PartnerVO;
import com.hqins.agent.org.model.vo.SimpleNodeVO;
import com.hqins.agent.org.model.vo.SimpleTreeNodeVO;
import com.hqins.common.base.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
public interface PartnerService {

    PageInfo<PartnerVO> listMy(TopQueryRequest queryRequest);

    PageInfo<SimpleNodeVO> listMySimple(TopQueryRequest queryRequest);

    PageInfo<SimpleNodeVO> listAllSimple(TopQueryRequest queryRequest);

    List<SimpleTreeNodeVO> getAllTree(OrgTreeRequest treeRequest);

    PageInfo<SimpleNodeVO> clueListMySimple(TopQueryRequest queryRequest);
}
