package com.hqins.agent.org.service;

import com.hqins.agent.org.model.vo.DigitalDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreAllDetailVO;
import com.hqins.agent.org.model.vo.DigitalScoreCodeVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/8/10 09:01
 */
public interface IFPDigitalScoreService {

    /**
     * 代理人IFP全量月份和积分数据查询
     * @param agentCode 代理人工号
     * @param startDate 开始年月
     * @return
     */
    List<DigitalDataVO> getAllMonthAndScoreData(String agentCode, String startDate);

    List<DigitalScoreCodeVO> getScore(List<String> agentCodeList, String queryDate);

    List<DigitalScoreAllDetailVO> getScoreDetail(List<String> agentCodeList, String queryDate, String recordPointsFlag);
}
