package com.hqins.agent.org.dao;

import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeWhiteList;
import com.hqins.agent.org.model.enums.zybx.WhiteStatusEnum;

import java.util.List;

/**
 * @Author: lijian
 * @Date: 2023/9/27 10:39 上午
 */
public interface WhiteListDao {

    /**
     * 查询白名单的列表
     * @param idType
     * @param idNo
     * @return
     */
    List<ChannelEmployeeWhiteList> queryWhiteListFromDB(String idType, String idNo, WhiteStatusEnum whiteStatusEnum);

    List<ChannelEmployeeWhiteList> queryWhiteListAllFromDB();

    /**
     * 新增
     * @param white
     */
    void insert (ChannelEmployeeWhiteList white);

    /**
     * 更新
     * @param white
     */
    void updateById(ChannelEmployeeWhiteList white);

    List<ChannelEmployeeWhiteList> queryWhiteListFromDBByLicense(String employee<PERSON><PERSON>, String licenseNo, WhiteStatusEnum whiteStatusEnum);
}
