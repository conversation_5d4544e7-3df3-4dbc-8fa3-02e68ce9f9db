package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeWhiteList;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 渠道商代理人白名单数据访问接口
 * <AUTHOR>
 * @since 2023-09-11
 */
@Mapper
@DS("exms")
@Repository
public interface ChannelEmployeeWhiteListMapper extends BaseMapper<ChannelEmployeeWhiteList> {

    Long insertSelective(ChannelEmployeeWhiteList channelEmployeeWhiteList);

    int updateByPrimaryKeySelective(ChannelEmployeeWhiteList channelEmployeeWhiteList);


}

