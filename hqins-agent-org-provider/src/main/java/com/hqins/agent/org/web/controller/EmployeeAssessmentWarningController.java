package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.EmployeeAssessmentInfoOrPerformanceDataEnum;
import com.hqins.agent.org.model.enums.EmployeeAssessmentParamEnum;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.EmployeeAssessmentWarningService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 员工考核预警
 */
@Api(tags = "员工考核预警")
@RestController
@RequestMapping("/assessment")
@Slf4j
public class EmployeeAssessmentWarningController {

    @Autowired
    private EmployeeAssessmentWarningService employeeAssessmentWarningService;

    @GetMapping("/checkAssessmentInfoOrPerformanceData")
    public ApiResult<AssessmentWarningResultVO> checkAssessmentInfoOrPerformanceData(@ApiParam("参数类型：1-上期/当期；2-饼图") @RequestParam(value = "paramType") String paramType,
                                                                  @ApiParam("销售员代码") @RequestParam(value = "code") String code) {
        AssessmentWarningResultVO assessmentResultVO = new AssessmentWarningResultVO();
        if(EmployeeAssessmentInfoOrPerformanceDataEnum.ASSESSMENT_INFO.getCode().equals(paramType)){
            AssessmentWarningVO currentAssessmentWarningVO = employeeAssessmentWarningService.getCurrentAssessmentInfo(code, EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode());
            AssessmentWarningVO previousAssessmentWarningVO = employeeAssessmentWarningService.getCurrentAssessmentInfo(code, EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode());
            if(!Objects.isNull(currentAssessmentWarningVO) && !Objects.isNull(previousAssessmentWarningVO)){
                assessmentResultVO.setPreviousOrCurrentFlag("ALL");
            }
            if(Objects.isNull(currentAssessmentWarningVO) && Objects.isNull(previousAssessmentWarningVO)){
                assessmentResultVO.setPreviousOrCurrentFlag("NULL");
            }
            if(!Objects.isNull(currentAssessmentWarningVO) && Objects.isNull(previousAssessmentWarningVO)){
                assessmentResultVO.setPreviousOrCurrentFlag(EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode());
            }
            if(!Objects.isNull(previousAssessmentWarningVO) && Objects.isNull(currentAssessmentWarningVO)){
                assessmentResultVO.setPreviousOrCurrentFlag(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode());
            }
        }else {
            AssessmentPerformanceDataVO currentAssessmentPerformanceDataVO =
                    employeeAssessmentWarningService.getAssessmentPerformanceDataInfo(code,EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode());
            AssessmentPerformanceDataVO previousAssessmentPerformanceDataVO =
                    employeeAssessmentWarningService.getAssessmentPerformanceDataInfo(code,EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode());
            if(!Objects.isNull(currentAssessmentPerformanceDataVO) && !Objects.isNull(previousAssessmentPerformanceDataVO)){
                assessmentResultVO.setPreviousOrCurrentFlag("ALL");
            }
            if(Objects.isNull(currentAssessmentPerformanceDataVO) && Objects.isNull(previousAssessmentPerformanceDataVO)){
                assessmentResultVO.setPreviousOrCurrentFlag("NULL");
            }
            if(!Objects.isNull(currentAssessmentPerformanceDataVO) && Objects.isNull(previousAssessmentPerformanceDataVO)){
                assessmentResultVO.setPreviousOrCurrentFlag(EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode());
            }
            if(Objects.isNull(currentAssessmentPerformanceDataVO) && !Objects.isNull(previousAssessmentPerformanceDataVO)){
                assessmentResultVO.setPreviousOrCurrentFlag(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode());
            }
        }
        return ApiResult.ok(assessmentResultVO);
    }

    @GetMapping("/getAssessmentInfo")
    public ApiResult<AssessmentWarningVO> getAssessmentInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                           @ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode) {
        AssessmentWarningVO assessmentWarningVO = employeeAssessmentWarningService.getCurrentAssessmentInfo(employeeCode, paramType);
        return ApiResult.ok(assessmentWarningVO);
    }

    @GetMapping("/getAssessmentSubordinatePersonnelInfo")
    public ApiResult<AssessmentSubordinatePersonnelVO> getAssessmentSubordinatePersonnelInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                                                             @ApiParam("销售员代码") @RequestParam(value = "employeeCode",required = false) String employeeCode,
                                                                                             @ApiParam("销售团队代码") @RequestParam(value = "teamCode",required = false) String teamCode,
                                                                                             @ApiParam("考核项code") @RequestParam(value = "code") String code) {
        AssessmentSubordinatePersonnelVO assessmentResultVO  =
                employeeAssessmentWarningService.getAssessmentSubordinatePersonnelInfo(employeeCode,teamCode,paramType,code);
        return ApiResult.ok(assessmentResultVO);
    }

    @GetMapping("/getAssessmentPerformanceDataInfo")
    public ApiResult<AssessmentPerformanceDataVO> getAssessmentPerformanceDataInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                                                             @ApiParam("销售员代码") @RequestParam(value = "teamCode") String teamCode) {
        AssessmentPerformanceDataVO assessmentPerformanceDataVO =
                employeeAssessmentWarningService.getAssessmentPerformanceDataInfo(teamCode,paramType);
        return ApiResult.ok(assessmentPerformanceDataVO);
    }

    @GetMapping("/getAssessmentTableDataInfo")
    public ApiResult<AssessmentTeamDateVO> getAssessmentTableDataInfo(@ApiParam("参数类型：1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                                      @ApiParam("销售员代码") @RequestParam(value = "teamCode") String teamCode) {
        AssessmentTeamDateVO assessmentTeamDateVO =
                employeeAssessmentWarningService.getAssessmentTableDataInfo(teamCode,paramType);
        return ApiResult.ok(assessmentTeamDateVO);
    }
}
