package com.hqins.agent.org.service;


import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.PartnerOrgVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.common.base.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface PartnerOrgService {

    PageInfo<PartnerOrgVO> listMy(OrgQueryRequest queryRequest);

    List<TreeNodeVO> listMySimple(OrgQueryRequest queryRequest);

    List<TreeNodeVO> listAllSimple(OrgQueryRequest queryRequest);

    List<TreeNodeVO> clueListMySimple(OrgQueryRequest queryRequest);
}
