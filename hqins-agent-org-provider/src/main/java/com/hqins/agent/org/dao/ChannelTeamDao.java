package com.hqins.agent.org.dao;

import com.hqins.agent.org.dao.entity.org.ChannelTeam;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/5/20
 * @Description
 */
public interface ChannelTeamDao {

    /**
     * 根据id 查询
     *
     * @param id
     * @return
     */
    ChannelTeam getById(Long id);

    /**
     * 根据code查询
     *
     * @param code
     * @return
     */
    ChannelTeam getByCode(String code);

    /**
     * 根据orgCodeSet集合查询
     *
     * @param orgCodeSet
     * @return
     */
    Map<String, ChannelTeam> selectEmployeeMapByOrgCodes(Set<String> orgCodeSet);

}
