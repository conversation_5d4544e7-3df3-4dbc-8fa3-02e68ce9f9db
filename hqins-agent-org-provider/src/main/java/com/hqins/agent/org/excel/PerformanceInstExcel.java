package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 督导业绩--按机构导出
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class PerformanceInstExcel implements Serializable {

    @ExcelProperty("机构")
    @ApiModelProperty("机构")
    private String  instName;

    @ExcelProperty("首年保费(元)")
    @ApiModelProperty("首年保费(元)")
    private String  fycPremium;

    @ExcelProperty("首年标保(元)")
    @ApiModelProperty("首年标保(元)")
    private String  dcp;

    @ExcelProperty("首年保单件数(件)")
    @ApiModelProperty("首年保单件数(件)")
    private String  fycPolicyNum;

    @ExcelProperty("期交保费(元)")
    @ApiModelProperty("期交保费(元)")
    private String  periodPremium;

    @ExcelProperty("期交标保(元)")
    @ApiModelProperty("期交标保(元)")
    private String  periodDCP;

    @ExcelProperty("期交件数(件)")
    @ApiModelProperty("期交件数(件)")
    private String  periodPolicyNum;

    @ExcelProperty("新单短险保费(元)")
    @ApiModelProperty("新单短险保费(元)")
    private String  fycMRiskPremium;

    @ExcelProperty("新单短险件数(件)")
    @ApiModelProperty("新单短险件数(件)")
    private String  fycMRiskPolicyNum;

    @ExcelProperty("新单客户数(人)")
    @ApiModelProperty("新单客户数(人)")
    private String  fycAppntNoNum;

    @ExcelProperty("期交客户数(人)")
    @ApiModelProperty("期交客户数(人)")
    private String  periodAppntNoNum;

    @ExcelProperty("增员人数(人)")
    @ApiModelProperty("增员人数(人)")
    private String  increaseNum;
}
