package com.hqins.agent.org.service.impl;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.common.collect.Lists;
import com.hqins.agent.org.constants.Constants;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.exception.AuthenticationException;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.request.OuterOrgRequest;
import com.hqins.agent.org.model.vo.AuthEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.EmployeeAuthService;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class EmployeeAuthServiceImpl implements EmployeeAuthService {

    @Resource
    private ChannelEmployeeMapper channelEmployeeMapper;

    private static final int EXPIRE = 150;

    @CreateCache(name = "AuthEmployee:employee", cacheType = CacheType.REMOTE, expire = EXPIRE, timeUnit = TimeUnit.MINUTES)
    private Cache<String, AuthEmployeeVO> authEmployeeCache;
    @CreateCache(name = "AuthEmployee:authId", cacheType = CacheType.REMOTE, expire = EXPIRE, timeUnit = TimeUnit.MINUTES)
    private Cache<Long, String> authIdCache;

    @Override
    public AuthEmployeeVO getEmployeeByAuthId(String authId) {
        //根据authId  ，并返回信息     和刷新
        AuthEmployeeVO authEmployeeVO = authEmployeeCache.get(authId);
        AssertUtil.notNull(authEmployeeVO, new AuthenticationException("授权已失效，请重新获取！"));
        if ("99999999999999999999999999".equals(authEmployeeVO.getLicenseNo())) {
            authId = "96572C378B2765B9B00127F05FDF82E2";
            //代码仅UAT DAT生效
            authEmployeeCache.put(authId, authEmployeeVO, 1000, TimeUnit.DAYS);
            authIdCache.put(authEmployeeVO.getId(), authId, 1000, TimeUnit.DAYS);
            return authEmployeeVO;
        }
        authEmployeeCache.put(authId, authEmployeeVO, EXPIRE, TimeUnit.MINUTES);
        authIdCache.put(authEmployeeVO.getId(), authId, EXPIRE, TimeUnit.MINUTES);
        return authEmployeeVO;
    }

    @Override
    public AuthEmployeeVO getEmployeeByLicenseNo(String licenseNo) {
        //根据执业证号，查询外部代理人，并查询是否存在有效authId 存在刷新，不存在生成并返回 人员信息与authId  同时查询UM获取VID
        ChannelEmployee employee = new LambdaQueryChainWrapper<>(channelEmployeeMapper)
                .eq(ChannelEmployee::getLicenseNo, licenseNo)
                .eq(ChannelEmployee::getStatus, EmployeeStatus.SERVING)
                .last(Constants.LIMIT_ONE)
                .one();
        AssertUtil.notNull(employee, new BadRequestException("该执业证号不存在或未导入！"));
        final Long id = employee.getId();
        String authId = authIdCache.get(id);
        AuthEmployeeVO authEmployeeVO = BeanCopier.copyObject(employee, AuthEmployeeVO.class);
        if (StringUtils.isEmpty(authId)) {
            authId = DigestUtils.md5Hex(Long.toString(System.currentTimeMillis()).substring(0, 6) + "_" + id).toUpperCase();
        }
        if ("99999999999999999999999999".equals(authEmployeeVO.getLicenseNo())) {
            authId = "96572C378B2765B9B00127F05FDF82E2";
            //代码仅UAT DAT生效
            authEmployeeCache.put(authId, authEmployeeVO, 1000, TimeUnit.DAYS);
            authIdCache.put(authEmployeeVO.getId(), authId,1000, TimeUnit.DAYS);
            authEmployeeVO.setAuthId(authId);
            return authEmployeeVO;
        }
        authEmployeeVO.setAuthId(authId);
        authEmployeeCache.put(authId, authEmployeeVO, EXPIRE, TimeUnit.MINUTES);
        authIdCache.put(id, authId, EXPIRE, TimeUnit.MINUTES);
        return authEmployeeVO;
    }

    @Override
    public List<EmployeeVO> getEmployeesByOrgCode(OuterOrgRequest request) {
        final List<ChannelEmployee> employees = new LambdaQueryChainWrapper<>(channelEmployeeMapper)
                .eq(ChannelEmployee::getOrgCode, request.getOrgCode())
                .in(CollectionUtils.isNotEmpty(request.getNos()), ChannelEmployee::getLicenseNo, request.getNos())
                .list();
        if (CollectionUtils.isEmpty(employees)) {
            return Lists.newArrayList();
        }
        return BeanCopier.copyList(employees, EmployeeVO.class);
    }
}
