package com.hqins.agent.org.handler;

import com.hqins.admin.cas.api.OperationLogApi;
import com.hqins.admin.cas.model.request.OperationLogAddRequest;
import com.hqins.common.boot.aspect.AdminOperationLog;
import com.hqins.common.boot.aspect.AdminOperationLogHandler;
import com.hqins.common.utils.JsonUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2021/7/15
 */
@Component
public class DefaultAdminOperationLogHandler implements AdminOperationLogHandler {

    @Autowired
    private OperationLogApi operationLogApi;

    @Override
    public void handle(AdminOperationLog adminOperationLog) {
        OperationLogAddRequest operationLogAddRequest = new OperationLogAddRequest();
        operationLogAddRequest.setAppId(adminOperationLog.getAdminAppId());
        operationLogAddRequest.setStaffId(adminOperationLog.getStaffId());
        operationLogAddRequest.setStaffUsername(adminOperationLog.getStaffUsername());
        operationLogAddRequest.setClientIp(adminOperationLog.getClientIp());
        operationLogAddRequest.setBrowser(adminOperationLog.getBrowser());
        operationLogAddRequest.setOperationName(adminOperationLog.getOperationName());
        operationLogAddRequest.setHttpMethod(adminOperationLog.getHttpMethod());
        operationLogAddRequest.setUri(adminOperationLog.getUri());
        operationLogAddRequest.setRequestParams(adminOperationLog.getRequestParams());
        operationLogAddRequest.setRequestBody(adminOperationLog.getRequestBody());
        operationLogAddRequest.setMultipartInfos(CollectionUtils.isEmpty(adminOperationLog.getMultipartInfos())
                ? Strings.EMPTY : JsonUtil.toJSON(adminOperationLog.getMultipartInfos()));
        operationLogAddRequest.setOperateTime(adminOperationLog.getOperateTime());
        operationLogApi.add(operationLogAddRequest);
    }
}
