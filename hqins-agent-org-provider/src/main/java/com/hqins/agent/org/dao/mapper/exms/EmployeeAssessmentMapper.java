package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.vo.AssessmentEmployeeInfoVO;
import com.hqins.agent.org.model.vo.AssessmentItemVO;
import com.hqins.agent.org.model.vo.AssessmentItemsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Mapper
@DS("exms")
@Repository
public interface EmployeeAssessmentMapper {
    String getCheckFrequency(@Param("employeeCode") String employeeCode);

    BigDecimal getPersonConfigValue(@Param("id") String id, @Param("checkItem") String str);

    List<RankDef> getCorrespondingRankList(@Param("basicLawVersionId") String basicLawVersionId, @Param("rankSeqCode") String rankSeqCode, @Param("partnerInstCode") String partnerInstCode);

    List<RankDef> getIfpCorrespondingRankList(@Param("basicLawVersionId") String basicLawVersionId, @Param("rankSeqCode") String rankSeqCode, @Param("partnerInstCode") String partnerInstCode);

    AssessmentItemVO getIfpAssessmentItems(@Param("basicLawVersionId")String basicLawVersionId, @Param("rankCode") String rankCode, @Param("rankSeqCode")String rankSeqCode,@Param("currentTarget") String currentTarget);

    List<AssessmentItemsVO> getAssessmentItems(@Param("id") String id);

    AssessmentItemVO getYbAssessmentItems(@Param("basicLawVersionId")String basicLawVersionId, @Param("rankCode") String rankCode, @Param("rankSeqCode")String rankSeqCode);

    List<String> getCheckMonthHistory(@Param("employeeCode") String employeeCode);

    String getCheckBatchId(@Param("checkMonth") String checkMonth, @Param("employeeCode") String employeeCode);

    List<AssessmentEmployeeInfoVO> getAllAssessmentEmployeeInfo(@Param("employeeCode") String employeeCode, @Param("checkBatchId") String checkBatchId);

    Tbsaleteam getTbSaleTeamInfo(@Param("teamCode") String teamCode);

    List<CheckBatchPersonInfo> getAllAssessmentPersonal(@Param("checkMonth") String checkMonth, @Param("teamCode") String teamCode);

    CheckBatchPersonInfo getCheckBatchPersonInfo(@Param("checkMonth")String checkMonth, @Param("employeeCode")String employeeCode);

    CheckBatchPersonInfo getMangerCheckBatchPersonInfo(@Param("teamCode") String teamCode, @Param("checkMonth") String checkMonth);

    List<Tbsaleteam> getChildrenTbSaleTeams(@Param("teamCode") String teamCode);

    List<AssessmentItemsVO> getAllItemsBySaleTeamCodes(@Param("checkMonth") String checkMonth, @Param("saleTeamCodes") HashSet<String> saleTeamCodes);

    List<AssessmentItemsVO> getAllActualItemsBySaleTeamCodes(@Param("checkMonth")String checkMonth, @Param("saleTeamCodes")HashSet<String> saleTeamCodes);

    String getEmpCodeByEmpInCode(@Param("empInCode")String empInCode);
}
