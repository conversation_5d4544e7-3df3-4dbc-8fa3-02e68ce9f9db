package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.request.TeamHonorRequest;
import com.hqins.agent.org.model.vo.EmpHonorVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@DS("exms")
@Repository
public interface TeamMapper {


    List<EmployeeVO> getTeamBirthDayEmpList(Map map);

    List<EmployeeVO> getBirthDayEmpList(Map map);

    EmployeeVO getEmployeeInfo(String employeeCode);

    Tbsaleteam getPerformanceSaleTeamByCode(String saleTeamCode);

    String getEmpTargetSaleteamCode(Map<String, Object> map);

    String getTeamSaleTeamCode(Map<String, Object> map);

    Tbsaleteam getTeamSaleTeam(Map<String, Object> map);
}
