package com.hqins.agent.org.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/11
 * @Description
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AppConsts {

    public static final String BASE_PACKAGE = "com.hqins";
    public static final String SWAGGER = "swagger";
    public static final String SWAGGER_URI = "/swagger-ui/index.html";
    public static final String SLASH = "/";
    public static final String STRING_NULL = "";
    public static final String DOUBLE_SLASH = "//";
    public static final String SERVER_SSL_KEY_STORE = "server.ssl.key-store";
    public static final String SERVER_CONTEXT_PATH = "server.servlet.context-path";
    public static final String HTTP = "http";
    public static final String HTTPS = "https";
    public static final String APP_NAME = "spring.application.name";
    public static final String APP_PORT = "server.port";
    public static final String APP_RUN_LOG = "\n---------------------------------------------------------------------------------------\n\t" +
            "Application '{}' is running! Access URLs:\n\t" +
            "Swagger-UI: {}://{}:{}{}/swagger-ui/index.html\n\t" +
            "Profile(s): \t{}" +
            "\n---------------------------------------------------------------------------------------";

    public static final String CPTYPE_CHANNEL = "CHANNEL";
    public static final String CPTYPE_PARTNER = "PARTY";

    public static final String INST_STATUS_ENABLED = "1";

    /**
     * 直销渠道商编码
     */
    public static final String CHANNEL_CODE = "C00265";

    /**
     * 渠道商类型 保险经纪
     */
    public static final String CHANNEL_TYPE_1 = "1";

    /**
     * 渠道商类型 专业代理
     */
    public static final String CHANNEL_TYPE_4 = "4";
    /**
     * 字符串常量
     */
    public static final String STRING_0 = "0";

    /**
     * 字符串常量 默认密码
     */
    public static final String DEFAULT_PASS_WORD = "hq123456";
    /**
     * 系统来源 默认IHOMEPRO
     */
    public static final String DEFAULT_SOURCE_SYSTEM = "IHOMEPRO";

    /**
     * MGA系统
     */
    public static final String MGA = "MGA";
    /**
     * OPEN 开发平台系统
     */
    public static final String OPEN = "OPEN";
    /**
     * 哆唻咪
     */
    public static final String DUOLAIMI = "DUOLAIMI";

    public static final String YES = "YES";

    public static final String NO = "NO";

    /**
     * 00
     */
    public static final String STR_00 = "00";
    /**
     * 1
     */
    public static final String STR_1 = "1";
    /**
     * 组级 01
     */
    public static final String TEAM_LEVEL = "01";

    /**
     * 部级 02
     */
    public static final String DEPT_LEVEL = "02";

    /**
     * 区级 03
     */
    public static final String AREA_LEVEL = "03";

    /**
     * 数字常量 -- 30
     */
    public static final int NUMBER_30 = 30;

    /**
     * 数字常量 -- 20
     */
    public static final int NUMBER_20 = 20;

    /**
     * 数字常量 -- 0
     */
    public static final int NUMBER_0 = 0;

    /**
     * 数字常量 -- 6
     */
    public static final int NUMBER_6 = 6;

    /**
     * 新销管人员信息状态
     * 01-有效
     * 02-失效
     * 04-离职
     */
    public static final List<String> EMP_STATUS_LIST = Arrays.asList("01", "02", "04");

    /**
     * 代理人状态 有效
     */
    public static final Integer VALID = 1;

    /**
     * 代理人状态 无效
     */
    public static final Integer INVALID = 0;

    /**
     * 代理人状态 离职
     */
    public static final Integer QUIT = 2;

    /**
     * 后台管理系统AppId
     */
    public static final Integer APP_ID = 1001;

    /**
     * ifp对应码值
     */
    public static final String STR_IFP = "03";


}
