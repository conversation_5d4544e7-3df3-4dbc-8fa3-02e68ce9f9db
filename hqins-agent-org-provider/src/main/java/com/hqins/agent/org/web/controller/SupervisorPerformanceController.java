package com.hqins.agent.org.web.controller;


import com.hqins.agent.org.model.request.SupervisorPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.SupervisorPerformanceService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "督导业绩")
@RestController
@RequestMapping("/supervisor/performance")
@RefreshScope
@Slf4j
public class SupervisorPerformanceController {

    @Autowired
    private SupervisorPerformanceService performanceService;

    @ApiOperation("督导合伙人机构查询")
    @GetMapping("/getSupervisorPartnerInstList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> getSupervisorPartnerInstList() {
        try {
            return ApiResult.ok(performanceService.getSupervisorPartnerInstList());
        } catch (Exception e) {
            log.error("督导合伙人机构查询,出错", e);
            return ApiResult.fail("督导合伙人机构查询,出错," + e.getMessage());
        }
    }


    @ApiOperation("督导团队查询")
    @GetMapping("/getSupervisorTeamList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> getSupervisorTeamList(@ApiParam("查询团队的机构") @RequestParam(value = "partnerInstCode") String partnerInstCode) {
        try {
            return ApiResult.ok(performanceService.getSupervisorTeamList(partnerInstCode));
        } catch (Exception e) {
            log.error("督导团队查询,出错", e);
            return ApiResult.fail("督导团队查询,出错," + e.getMessage());
        }
    }


    @ApiOperation("督导业绩追踪查询")
    @GetMapping("/getMonthPerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getMonthPerformance() {
        try {
            return ApiResult.ok(performanceService.getMonthPerformance());
        } catch (Exception e) {
            log.error("督导业绩追踪查询出错", e);
            return ApiResult.fail("督导业绩追踪查询出错," + e.getMessage());
        }
    }


//    @ApiOperation("督导业绩查询")
//    @GetMapping("/getPerformanceList")
//    @ResponseStatus(HttpStatus.OK)
//    public ApiResult<SupervisorPerformanceVO> getPerformanceList(@ApiParam("机构代码") @RequestParam(value = "partnerInstCode",required = false) String partnerInstCode,
//                                                                       @ApiParam("团队代码") @RequestParam(value = "teamCode",required = false) String teamCode,
//                                                                       @ApiParam("开始日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "startDate") Date startDate,
//                                                                       @ApiParam("结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "endDate",required = false) Date endDate,
//                                                                       @ApiParam("统计口径:按承保:sign,按生效:eff") @RequestParam(value = "accType") String accType,
//                                                                       @ApiParam("汇总口径:机构:inst,团队:team,人员:agent") @RequestParam(value = "groupType") String groupType
//                                                             ) {
//        try {
//            return ApiResult.ok(performanceService.getPerformanceList(partnerInstCode,teamCode,startDate,endDate,accType,groupType));
//        } catch (Exception e) {
//            log.error("督导业绩查询,出错", e);
//            return ApiResult.fail("督导业绩查询,出错," + e.getMessage());
//        }
//    }

    @ApiOperation("督导业绩查询")
    @PostMapping("/getPerformanceList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorPerformanceVO> getPerformanceList(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getPerformanceList(request));
        } catch (Exception e) {
            log.error("督导业绩查询,出错", e);
            return ApiResult.fail("督导业绩查询,出错," + e.getMessage());
        }
    }

    @ApiOperation("督导指标预警-查询全部指标")
    @PostMapping("/markWarning/getAllMark")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorMarkVO> getAllMark(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getAllMark(request));
        } catch (Exception e) {
            log.error("督导业绩追踪查询-犹退保单,出错", e);
            return ApiResult.fail("督导业绩追踪查询-犹退保单,出错," + e.getMessage());
        }
    }



    @ApiOperation("督导业绩追踪查询-自互保件")
    @GetMapping("/markWarning/getSelfPolicy")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorMarkVO> getSelfPolicy(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getSelfPolicy(request));
        } catch (Exception e) {
            log.error("督导业绩追踪查询-自互保件,出错", e);
            return ApiResult.fail("督导业绩追踪查询-自互保件,出错," + e.getMessage());
        }
    }


    @ApiOperation("督导业绩追踪查询-13cr未达标")
    @GetMapping("/markWarning/getDisCompleteCr13")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorMarkVO> getDisCompleteCr13(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getDisCompleteCr13(request));
        } catch (Exception e) {
            log.error("督导业绩追踪查询-13cr未达标,出错", e);
            return ApiResult.fail("督导业绩追踪查询-13cr未达标,出错," + e.getMessage());
        }
    }

    @ApiOperation("督导业绩追踪查询-犹退保单")
    @GetMapping("/markWarning/getCtPolicy")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorMarkVO> getCtPolicy(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getCtPolicy(request));
        } catch (Exception e) {
            log.error("督导业绩追踪查询-犹退保单,出错", e);
            return ApiResult.fail("督导业绩追踪查询-犹退保单,出错," + e.getMessage());
        }
    }

    @ApiOperation("考核查询")
    @PostMapping("/getCheckInoList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SupervisorCheckInfoVO> getCheckInoList(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getCheckInoList(request));
        } catch (Exception e) {
            log.error("考核查询异常: ", e);
            return ApiResult.fail("考核查询异常: " + e.getMessage());
        }
    }

    @ApiOperation("督导业绩导出")
    @PostMapping("/getPerformanceListExport")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getPerformanceListExport(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getPerformanceListExport(request));
        } catch (Exception e) {
            log.error("督导业绩导出,出错", e);
            return ApiResult.fail("督导业绩导出,出错," + e.getMessage());
        }
    }

    @ApiOperation("督导业绩追踪导出-自互保件")
    @PostMapping("/markWarning/getSelfPolicyExport")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getSelfPolicyExport(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getSelfPolicyExport(request));
        } catch (Exception e) {
            log.error("督导业绩追踪导出-自互保件,出错", e);
            return ApiResult.fail("督导业绩追踪导出-自互保件,出错," + e.getMessage());
        }
    }

    @ApiOperation("督导业绩追踪导出-13cr未达标")
    @PostMapping("/markWarning/getDisCompleteCr13Export")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getDisCompleteCr13Export(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getDisCompleteCr13Export(request));
        } catch (Exception e) {
            log.error("督导业绩追踪导出-13cr未达标,出错", e);
            return ApiResult.fail("督导业绩追踪导出-13cr未达标,出错," + e.getMessage());
        }
    }

    @ApiOperation("督导业绩追踪导出-犹退保单")
    @PostMapping("/markWarning/getCtPolicyExport")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getCtPolicyExport(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getCtPolicyExport(request));
        } catch (Exception e) {
            log.error("督导业绩追踪导出-犹退保单,出错", e);
            return ApiResult.fail("督导业绩追踪导出-犹退保单,出错," + e.getMessage());
        }
    }

    @ApiOperation("考核查询导出")
    @PostMapping("/getCheckInoListExport")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getCheckInoListExport(@RequestBody SupervisorPerformanceRequest request) {
        try {
            return ApiResult.ok(performanceService.getCheckInoListExport(request));
        } catch (Exception e) {
            log.error("考核导出异常", e);
            return ApiResult.fail("考核导出异常," + e.getMessage());
        }
    }
}
