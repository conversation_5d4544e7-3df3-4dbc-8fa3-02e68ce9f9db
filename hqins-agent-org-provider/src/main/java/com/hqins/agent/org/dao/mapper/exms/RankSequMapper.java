package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.dao.entity.exms.RankSequ;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: lijian
 * @Date: 2022/12/16 5:27 下午
 */

@Mapper
@DS("exms")
@Repository
public interface RankSequMapper {

    List<RankSequ> getAllUsefulRankSequ();

    List<RankSequ> getRankSequenceByCompanyCode(@Param("companyCode") String companyCode);
}
