package com.hqins.agent.org.exception;


import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.ErrorCode;

public class AuthenticationException extends ApiException {

    public AuthenticationException(String message) {
        super(ErrorCode.UNAUTHORIZED, message);
    }

    public AuthenticationException(String code, String message) {
        super(ErrorCode.UNAUTHORIZED, code, message);
    }
}
