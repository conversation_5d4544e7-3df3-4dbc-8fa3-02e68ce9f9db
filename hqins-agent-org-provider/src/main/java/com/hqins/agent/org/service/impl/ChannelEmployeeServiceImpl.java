package com.hqins.agent.org.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.cache.RedisService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.ChannelEmployeeDao;
import com.hqins.agent.org.dao.ChannelTeamDao;
import com.hqins.agent.org.dao.TbepartnerDao;
import com.hqins.agent.org.dao.WhiteListDao;
import com.hqins.agent.org.dao.converter.ChannelEmployeeConvert;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.BatchFailInfo;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.dao.entity.org.FileUploadRecord;
import com.hqins.agent.org.dao.mapper.exms.*;
import com.hqins.agent.org.dao.mapper.org.BatchFailInfoMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelTeamMapper;
import com.hqins.agent.org.dao.mapper.org.FileUploadRecordMapper;
import com.hqins.agent.org.excel.CellWriteWidthConfig;
import com.hqins.agent.org.excel.ChannelEmployeeExcel;
import com.hqins.agent.org.excel.ChannelEmployeeExcelAssembler;
import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.enums.zybx.*;
import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.mq.MqTagEnum;
import com.hqins.agent.org.mq.ProducerClient;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.rpc.client.entity.CheckInfoData;
import com.hqins.agent.org.rpc.client.entity.CheckInfoDetail;
import com.hqins.agent.org.rpc.client.entity.EmployeeCheckInfo;
import com.hqins.agent.org.service.*;
import com.hqins.agent.org.tools.CheckChannelEmployee;
import com.hqins.agent.org.tools.GenerateUUID;
import com.hqins.agent.org.tools.TempEmployeeInfo;
import com.hqins.agent.org.utils.PasswordUtil;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.IdType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.*;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import com.hqins.um.api.AdminApi;
import com.hqins.um.model.dto.AccountInfoDTO;
import com.hqins.um.model.dto.AgentStateDTO;
import com.hqins.um.model.request.AgentCreateByAdminRequest;
import com.hqins.um.model.request.VisitorUpdateByAdminRequest;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class ChannelEmployeeServiceImpl implements ChannelEmployeeService {

    private static final Logger logger = LoggerFactory.getLogger(ChannelEmployeeServiceImpl.class);

    @Value("${zybx.ipAddress}")
    private String ipAddress;
    @Value("${zybx.port}")
    private String port;
    @Value("${zybx.appKey}")
    private String appKey;
    @Value("${zybx.service}")
    private String service;
    @Value("${zybx.version}")
    private String version;
    @Value("${zybx.sm4Key}")
    private String sm4Key;
    @Value("${zybx.userName}")
    private String userName;
    @Value("${zybx.password}")
    private String password;

    public static final String CHANNEL_EMPLOYEE_LIST_EXCEL_TAG = "channel_employee_list_excel_tag_";

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ChannelEmployeeMapper channelEmployeeMapper;
    @Autowired
    private ChannelTeamDao channelTeamDao;
    @Autowired
    private TbepartnerDao tbepartnerDao;
    @Autowired
    private ChannelEmployeeDao channelEmployeeDao;
    @Autowired
    private ChannelTeamMapper channelTeamMapper;
    @Resource
    private UmClient umClient;

    @Resource
    private AdminApi adminApi;

    @Autowired
    private OrgService orgService;
    @Autowired
    private CacheService cacheService;

    @Lazy
    @Autowired
    private ChannelTeamService channelTeamService;
    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;
    @Autowired
    private TbempMapper tbempMapper;
    @Autowired
    private SensorsService sensorsService;
    @Autowired
    private BatchFailInfoMapper batchFailInfoMapper;
    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;
    @Autowired
    private ProducerClient producerClient;
    @Autowired
    private ChannelEmployeeConvert channelEmployeeConvert;
    @Autowired
    private ChannelEmployeeExcelAssembler channelEmployeeExcelAssembler;
    @Autowired
    private EmployeeExternalService employeeExternalService;
    @Autowired
    private FileApi fileApi;

    @Lazy
    @Autowired
    private ChannelEmployeeExternalService channelEmployeeExternalService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private WhiteListDao whiteListDao;
    @Autowired
    private ChannelEmployeeCheckLogMapper checkLogMapper;
    @Autowired
    private ChannelEmployeeCheckResultMapper checkResultMapper;
    @Autowired
    private ChannelEmployeeDetailMapper channelEmployeeDetailMapper;
    @Autowired
    private ChannelEmployeeCheckExceptionLogMapper exceptionLogMapper;


    public Map listTurnOn() {
        Map map = Maps.newHashMap();
        map.put("turnOn", cacheService.isTurnOn());
        return map;
    }

    @Override
    public void deleteRedisCache(String idType, String idNo) throws Exception {
        logger.info("删除缓存入参:idType={}, idNo={}", idType, idNo);
        if (StringUtils.isNotEmpty(idType) && StringUtils.isNotEmpty(idNo)) {
            redisService.removePattern(generatePatternKey(idType, idNo));
        }
    }

    @Override
    public void refreshWhiteCache(String idType, String idNo) throws Exception {
        logger.info("刷新白名单缓存入参:idType={}, idNo={}", idType, idNo);
        //目标缓存对象 -- 查询有效的且未删除的
        List<ChannelEmployeeWhiteList> whiteLists = null;

        //先删除缓存中遗留的
        if (StringUtils.isNotEmpty(idType) && StringUtils.isNotEmpty(idNo)) {
            redisService.removePattern(generatePatternKey(idType, idNo));
            whiteLists = queryWhiteListFromDB(idType, idNo, WhiteStatusEnum.VALID);
        } else {
            List<ChannelEmployeeWhiteList> all = whiteListDao.queryWhiteListAllFromDB();
            if (!CollectionUtils.isEmpty(all)) {
                //删除缓存
                for (ChannelEmployeeWhiteList white : all) {
                    redisService.removePattern(generatePatternKey(white.getIdType(), white.getIdNo()));
                }
                //过滤出 有效的且未删除的
                whiteLists = all.stream().filter(whiteList -> whiteList.getDeleteFlag().equals(WhiteDeleteEnum.UN_DELETED.getValue()))
                        .filter(whiteList -> whiteList.getStatus().equals(WhiteStatusEnum.VALID.getValue()))
                        .collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(whiteLists)) {
            logger.info("未查询到白名单:idType={}, idNo={}", idType, idNo);
            throw new Exception("未查询到白名单:idType=" + idType + ", idNo=" + idNo);
        }
        try {
            for (ChannelEmployeeWhiteList white : whiteLists) {
                String whiteKey = generateWhiteKey(white.getIdType(), white.getIdNo());
//                redisService.setWithExpire(whiteKey, white, 3);
                redisService.setWithDayExpire(whiteKey, white, 365);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 渠道商人员创建及修改时的调用中银保信查询
     *
     * @param request
     * @param team
     * @param tbepartner
     * @param checkVo
     */
    private void checkForChannel(ChannelEmployeeAddRequest request, ChannelTeam team, Tbepartner tbepartner, ChannelEmployeeCheckVo checkVo) {
        //开头
        if (!cacheService.isTurnOn()) {
            logger.info("中银保信校验开关：关闭");
            return;
        }
        String channelType = tbepartner.getChannelType();
        //渠道商类型为  保险经纪代理或保险专业代理  需进行校验
        if ("1".equals(channelType) || "4".equals(channelType)) {
            //封装参数
            CheckChannelEmployeeRequest cr = new CheckChannelEmployeeRequest();
            cr.setEntryTypeEnum(EntryTypeEnum.IHP);
            List<CheckChannelEmployeeBody> employeeList = Lists.newArrayList();
            CheckChannelEmployeeBody body = new CheckChannelEmployeeBody();
            body.setEmployeeName(request.getName());
            body.setIdType(request.getIdType());
            body.setIdNo(request.getIdCode());
            body.setLicenseNo(request.getLicenseNo());
            body.setOrgCode(team.getOrgCode());
            employeeList.add(body);
            cr.setEmployeeList(employeeList);
            //调用验证机制
            CheckEmployeeResultVO checkEmployeeResultVO = checkForChannel(cr);
            if (checkEmployeeResultVO.getCheckResult().intValue() == CheckResultEnum.ALL_SUCCESS.getValue().intValue()) {
                checkVo.setSuccess(true);
            } else {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkEmployeeResultVO.getCheckResultDetailList().get(0).getReturnMessage());
            }
        }
    }

    @Override
    public CheckEmployeeResultVO checkForChannelForInit(InitEmployeeAddRequest addRequest) {
        if (!cacheService.isTurnOn()) {
            logger.info("中银保信校验开关：关闭");
            CheckEmployeeResultVO checkVo = new CheckEmployeeResultVO();
            checkVo.setCheckResult(CheckResultEnum.ALL_SUCCESS.getValue());
            checkVo.setCheckResultMessage("校验开关已关闭，无需进行校验");
            return checkVo;
        }
        CheckChannelEmployeeRequest cr = new CheckChannelEmployeeRequest();
        cr.setEntryTypeEnum(EntryTypeEnum.IHP);
        List<CheckChannelEmployeeBody> employeeList = Lists.newArrayList();
        CheckChannelEmployeeBody body = new CheckChannelEmployeeBody();
        body.setEmployeeName(addRequest.getName());
        body.setIdType(addRequest.getIdType());
        body.setIdNo(addRequest.getIdCode());
        body.setLicenseNo(addRequest.getLicenseNo());
        body.setOrgCode(addRequest.getOrgCode());
        employeeList.add(body);
        cr.setEmployeeList(employeeList);
        //调用验证机制
        return checkForChannel(cr);
    }

    /**
     * 二次入司时调用
     *
     * @param channelEmployee
     * @return
     */
    private CheckEmployeeResultVO checkForChannel(ChannelEmployee channelEmployee) {
        if (!cacheService.isTurnOn()) {
            CheckEmployeeResultVO checkVo = new CheckEmployeeResultVO();
            checkVo.setCheckResult(CheckResultEnum.ALL_SUCCESS.getValue());
            checkVo.setCheckResultMessage("校验开关已关闭，无需进行校验");
            return checkVo;
        }
        CheckChannelEmployeeRequest cr = new CheckChannelEmployeeRequest();
        cr.setEntryTypeEnum(EntryTypeEnum.IHP);
        List<CheckChannelEmployeeBody> employeeList = Lists.newArrayList();
        CheckChannelEmployeeBody body = new CheckChannelEmployeeBody();
        body.setEmployeeName(channelEmployee.getName());
        body.setIdType(channelEmployee.getIdType());
        body.setIdNo(channelEmployee.getIdCode());
        body.setLicenseNo(channelEmployee.getLicenseNo());
        body.setOrgCode(channelEmployee.getOrgCode());
        employeeList.add(body);
        cr.setEmployeeList(employeeList);
        //调用验证机制
        return checkForChannel(cr);
    }

    @Override
    public CheckEmployeeResultVO checkForChannel(CheckChannelEmployeeRequest checkChannelEmployeeRequest) {
        //参数校验部分
        CheckEmployeeResultVO vo = new CheckEmployeeResultVO();
        List<CheckChannelEmployeeBody> employeeBodyList = checkChannelEmployeeRequest.getEmployeeList();
        if (org.springframework.util.CollectionUtils.isEmpty(employeeBodyList)) {
            vo.setCheckResult(CheckResultEnum.ALL_FAIL.getValue());
            vo.setCheckResultMessage("无需要验证的代理人信息!");
            return vo;
        }

        //校验结果详细信息
        List<CheckResultDetail> detailList = Lists.newArrayList();

        //用于记录日志的临时对象
        List<TempEmployeeInfo> infoList = Lists.newArrayList();

        for (CheckChannelEmployeeBody employeeBody : employeeBodyList) {
            logger.info("需要验证的人员信息:{}", JsonUtil.toJSON(employeeBody));
            CheckResultDetail detail = new CheckResultDetail();
            detail.setEmployeeRequest(employeeBody);
            //传输用类
            CheckChannelEmployee employee = new CheckChannelEmployee();
            BeanUtils.copyProperties(employeeBody, employee);

            //记录临时日志
            TempEmployeeInfo info = new TempEmployeeInfo();
            info.setEmployee(employee);
            info.setDetail(detail);
            infoList.add(info);

            /**
             * 为保证检查记录完整信息，将渠道商 法人机构补充完毕
             */
            //参数校验
            logger.info("参数校验:{}", JsonUtil.toJSON(employee));
            boolean through = checkParameters(employee, detail);

            //orgCode匹配
            logger.info("orgCode匹配--");
            String channelCode = matchChannel(employee.getOrgCode());
//            if (StringUtils.isEmpty(channelCode)) {
//                detail.setReturnCode(CheckResultCodeEnum.WRONG_ORG_CODE.getCode());
//                detail.setReturnMessage(CheckResultCodeEnum.WRONG_ORG_CODE.getLabel());
//            }

            //设置渠道商与法人信息
            setChannelAndCorporation(employee, channelCode);

//            if (!through || StringUtils.isEmpty(channelCode)) {
//                detailList.add(detail);
//                continue;
//            }

            //查询此人是否在黑名单中
            logger.info("查询黑名单--");
            CheckResultDetail blackDetail = checkEmployeeFromBlackList(employee);
            if (blackDetail != null) {
                logger.info("黑名单结果:{}", JsonUtil.toJSON(blackDetail));
                detail.setReturnCode(blackDetail.getReturnCode());
                detail.setReturnMessage(blackDetail.getReturnMessage());
                detailList.add(detail);
                continue;
            }
            //白名单查询
            logger.info("白名单查询:{}", JsonUtil.toJSON(employee));
            ChannelEmployeeWhiteList white = queryWhiteListObject(employee);
            if (white != null) {
                logger.info("白名单查询结果:{}", JsonUtil.toJSON(white));
                //匹配执业证号
                if (white.getLicenseNo().equals(employee.getLicenseNo())) {
                    //白名单匹配后的处理方法
                    dealWhite(employee, white, detail);
                    if (CheckResultCodeEnum.CHECK_SUCCESS.getCode().equals(detail.getReturnCode())) {
                        employee.setWhiteFlag(true);
                    } else {
                        //白名单未成功，则查询中银保信
                        dealWithOutWhite(employee, detail);
                    }
                } else {
                    //白名单中的执业证号不相同，则查询中银保信
                    dealWithOutWhite(employee, detail);
                }
            } else {
                //白名单未匹配后的处理方法
                dealWithOutWhite(employee, detail);
            }
            detailList.add(detail);
        }
        //统计结果
        calculateResult(vo, detailList);
        //异步记录日志
        saveCheckInfoLog(infoList, checkChannelEmployeeRequest.getEntryTypeEnum());

        return vo;
    }

    private void setChannelAndCorporation(CheckChannelEmployee employee, String channelCode) {
        if (StringUtils.isEmpty(channelCode)) {
            return;
        }
        //设置渠道商
        employee.setChannelCode(channelCode);
        //渠道商名称
        Tbepartner tbepartner = cacheService.getAllTbepartnersMap().get(employee.getChannelCode());
        if (tbepartner != null) {
            employee.setChannelName(tbepartner.getCompanyname());
        }

        //设置法人信息
        Map<String, BaseInst> allBaseInstsMap = cacheService.getAllBaseInstsMap();
        BaseInst baseInst = allBaseInstsMap.get(channelCode);
        if (baseInst != null) {
            employee.setCorporationCode(baseInst.getInstLicenseCode());
            employee.setCorporationName(baseInst.getInstLicenseName());
        }
    }

    /**
     * 非白名单逻辑
     *
     * @param employee
     * @param detail
     */
    private void dealWithOutWhite(CheckChannelEmployee employee, CheckResultDetail detail) {
        String infoKey = generateQueryInfoKey(employee.getIdType(), employee.getIdNo(), employee.getLicenseNo());
        logger.info("从redis中查询中银保信key：{}", infoKey);
        EmployeeCheckInfo employeeCheckInfo = (EmployeeCheckInfo) redisService.get(infoKey);
        if (employeeCheckInfo == null) {
            logger.info("从中银保信平台查询------");
            //银保信查询的id_type码值转换
            String idType = covertIDType(employee);
            if (StringUtils.isEmpty(idType)) {
                detail.setReturnCode(CheckResultCodeEnum.EXCLUDE_ID_TYPE.getCode());
                detail.setReturnMessage(CheckResultCodeEnum.EXCLUDE_ID_TYPE.getLabel());
                return;
            }
            employeeCheckInfo = queryZYBX(employee, idType);
            //存入缓存，设置1小时有效期
            if (employeeCheckInfo != null) {
//                redisService.setWithExpire(infoKey, employeeCheckInfo, 1);
                redisService.setWithDayExpire(infoKey, employeeCheckInfo, 365);
            }
        }
        logger.info("中银保信查询结果:{}", JsonUtil.toJSON(employeeCheckInfo));
        dealByQueryZYBX(employeeCheckInfo, employee, detail);
    }

    private EmployeeCheckInfo queryZYBX(CheckChannelEmployee employee, String idType) {
        logger.info("未查询到白名，转而查询中银保信----");

        //中银保返回对象
        EmployeeCheckInfo target = null;
        String resultCode = null;
        Map<String, String> paramMap = Maps.newHashMap();

        //查询银保信
        //请求地址：http：//ip:端口/api/{APP_KEY}/inspectors/1.0.0
//        String url = "http://**************:7825/api/000216/inspectors/1.0.0";
        String url = "http://" + ipAddress + ":" + port + "/api/" + appKey + "/" + service + "/" + version;
        paramMap.put("fullName", Sm4Util.encryptEcb(sm4Key, employee.getEmployeeName()));
        paramMap.put("idType", idType);
        paramMap.put("idNumber", Sm4Util.encryptEcb(sm4Key, employee.getIdNo()));
//        paramMap.put("licenseNo", employee.getLicenseNo());
//        paramMap.put("informationDate", "2023-08-01");
        Date now = new Date();
        paramMap.put("informationDate", DateFormatUtils.format(now, "yyyy-MM-dd"));
//        paramMap.put("userName", "000216");
//        paramMap.put("password", "5203D03E63ADC8BBD71B9B117A4846C3");
        paramMap.put("userName", userName);
        paramMap.put("password", password);
        paramMap.put("serialNum", generateSerialNum(userName, now));
        logger.info("中银保信请求:{}, 参数:{}", url, JsonUtil.toJSON(paramMap));
        int i = 0;
        do {
            try {
                target = queryCheckInfo(url, paramMap);
                if (target != null) {
                    resultCode = target.getResultCode();
                    if ("99".equals(resultCode) || "500".equals(resultCode)) {
                        //开启3次重次操作
                        i++;
                    } else {
                        //直接结束该do while 循环。
                        i = 3;
                    }
                } else {
                    i++;
                }
            } catch (Exception e) {
                logger.error("dealWithOutWhite occur an error : " + e.getMessage());
                i++;
            }
        } while (i <= 2);

        logger.info("中银保信查结果:{}", JsonUtil.toJSON(target));
        if (target != null) {
            if (!"100".equals(resultCode)) {
                //保存异常日志信息
                saveCheckExceptionLog(JsonUtil.toJSON(paramMap), JsonUtil.toJSON(target));
            } else {
                //异步 检查校验信息
                saveOrUpdateCheckInfo(employee, target);
            }
        }
        return target;
    }

    private void dealByQueryZYBX(EmployeeCheckInfo target, CheckChannelEmployee employee, CheckResultDetail detail) {

        String resultCode = target.getResultCode();
        if (!"100".equals(resultCode)) {
            detail.setReturnCode(resultCode);
            detail.setReturnMessage(CheckResultCodeEnum.getEnumObjectByCode(resultCode).getLabel());
        } else {
            try {
                dealQuerySuccess(target, employee, detail);
            } catch (Exception e) {
                detail.setReturnCode("999999");
                detail.setReturnMessage("错误信息：" + e.getMessage());
                logger.error("查询中银保信成功后出现异常:" + e.getMessage());
            }
        }
    }

    /**
     * 格式为：用户名+年月
     * 日+8 位流水号；每次
     * 唯一
     * 例 000083202011050000000002
     *
     * @param userName
     * @return
     */
    private String generateSerialNum(String userName, Date now) {
        if (now == null) {
            now = new Date();
        }
        String yyyyMMdd = DateFormatUtils.format(now, "yyyyMMdd");
        Random random = new Random();
        int number = random.nextInt(100000000);
        String numberString = String.format("%08d", number);
        return userName + yyyyMMdd + numberString;
    }

    /**
     * 处理返回码为 100的情况
     *
     * @param checkInfo
     * @param employee
     * @param detail
     */
    private void dealQuerySuccess(EmployeeCheckInfo checkInfo, CheckChannelEmployee employee, CheckResultDetail detail) {
        logger.info("查询中银保信成功~");
        CheckInfoData data = checkInfo.getData();
        String ifExist = data.getIfExist();
        if (!"Y".equals(ifExist) || "y".equals(ifExist)) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_EMP_INFO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_EMP_INFO.getLabel());
            saveBlackList(employee, detail);
            return;
        }
        //执业证有效状态
        CheckInfoDetail target = null;
        for (CheckInfoDetail checkInfoDetail : data.getDetailList()) {
            if (employee.getLicenseNo().equals(checkInfoDetail.getDevelopCode())) {
                target = checkInfoDetail;
                break;
            }
        }
        //不存在有效的执行业
        if (target == null) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getLabel());
            saveBlackList(employee, detail);
            return;
        }
        //执行证号失效
        if ("N".equals(target.getDevelopState()) || "n".equals(target.getDevelopState())) {
            detail.setReturnCode(CheckResultCodeEnum.INVALID_LICENSE_NO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.INVALID_LICENSE_NO.getLabel());
            return;
        }
        //匹配一级渠道商
//        BaseInst baseInst = matchByInstLicenseCode(target.getSuperOrgan());
//        if (baseInst == null) {
//            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_ORG_INFO.getCode());
//            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_ORG_INFO.getLabel());
//            return;
//        }

        //返回的执业证号是否与入参相同
        if (!target.getDevelopCode().equals(employee.getLicenseNo())) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getLabel());
            saveBlackList(employee, detail);
            return;
        }

        //组织匹配
        dealOrgAndNameInfo(employee, null, data.getName(), detail);
    }

    private BaseInst matchByInstLicenseCode(String instLicenseCode) {
        if (StringUtils.isEmpty(instLicenseCode)) {
            return null;
        }
        List<BaseInst> allBaseInsts = cacheService.getAllBaseInsts();
        for (BaseInst inst : allBaseInsts) {
            if ("1".equals(inst.getInstStatus()) &&
                    StringUtils.isNotEmpty(inst.getInstLicenseCode()) && inst.getInstLicenseCode().equals(instLicenseCode)) {
                return inst;
            }
        }
        return null;
    }

    /**
     * 存入缓存黑名单
     *
     * @param employee
     */
    private void saveBlackList(CheckChannelEmployee employee, CheckResultDetail detail) {
//        redisService.setWithExpire(generateBlackKey(employee.getIdType(), employee.getIdNo(), employee.getLicenseNo(), employee.getEmployeeName()), detail, 1);
        redisService.setWithDayExpire(generateBlackKey(employee.getIdType(), employee.getIdNo(), employee.getLicenseNo(), employee.getEmployeeName()), detail, 365);
    }

    private EmployeeCheckInfo queryCheckInfo(String url, Map paramMap) throws Exception {
        String result = null;
        try {
            result = HttpUtils.post(1, TimeUnit.MINUTES, HttpUtils.JSON, url, JsonUtil.toJSON(paramMap), null);
            logger.info("ChannelEmployeeServiceImpl #queryCheckInfo  result:{}", result);
        } catch (Exception exception) {
            logger.error("ChannelEmployeeServiceImpl #queryCheckInfo ====代理人信息校验失败===", exception);
            throw exception;
        }
        return JsonUtil.fromJSON(result, EmployeeCheckInfo.class);
    }

    /**
     * idType转换码值
     *
     * @param employee
     * @return
     */
    private String covertIDType(CheckChannelEmployee employee) {
        IdType idType = IdType.get(employee.getIdType());
        //身份证
        if (IdType.ID.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.ID.getCode();
        }
        //军官证 中国人民解放军军官证
        if (IdType.OFFICER_CERTIFICATE.name().equals(idType.name()) || IdType.SOLDIER_CERTIFICATE.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.OFFICER_CERTIFICATE.getCode();
        }
        //警官证
        if (IdType.POLICE_CERTIFICATE.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.POLICE_OFFICER.getCode();
        }
        //普通护照
        if (IdType.CHINA_PASSPORT.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.PASSPORT.getCode();
        }
        //港澳居民来往内地通行证
        if (idType.CH_HK_MACAO_TAIWAN_PASS.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.HK_MACAO_PASS.getCode();
        }
        //香港护照  无对应的
        //澳门护照  无对应的
        //台湾居民来往大陆通行证
        if (IdType.TAIWAN_CN_PASS.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.TAIWAN_CN_PASS.getCode();
        }
        //港澳居民来往内地通行证
        if (IdType.HK_MACAO_CN_PASS.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.CH_HK_MACAO_PASS.getCode();
        }
        //外国人永久居留证
        if (IdType.ID_FOREIGNER.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.ID_FOREIGNER.getCode();
        }
        //其他证件
        if (IdType.OTHER_CARD.name().equals(idType.name())) {
            return ZYBXIdTypeEnum.OTHER.getCode();
        }
        return "";
    }

    /**
     * 白名单匹配成功后的处理方法
     *
     * @param employee
     * @param white
     * @param detail
     */
    private void dealWhite(CheckChannelEmployee employee, ChannelEmployeeWhiteList white, CheckResultDetail detail) {
        dealOrgAndNameInfo(employee, white.getChannelCode(), white.getEmployeeName(), detail);
    }

    private void dealOrgAndNameInfo(CheckChannelEmployee employee, String channelCode, String employeeName, CheckResultDetail detail) {
        logger.info("匹配渠道商:{},{},{}", JsonUtil.toJSON(employee), channelCode, employeeName);
        //匹配渠道商 不进行渠道商匹配
//        if (StringUtils.isEmpty(channelCode) || !employee.getChannelCode().equals(channelCode)) {
//            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_ORG_INFO.getCode());
//            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_ORG_INFO.getLabel());
//            return;
//        }
        //校验姓名
        boolean sameName = checkSameName(employee.getEmployeeName(), employeeName);
        if (!sameName) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_EMP_INFO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_EMP_INFO.getLabel());
            return;
        }
        detail.setReturnCode(CheckResultCodeEnum.CHECK_SUCCESS.getCode());
        detail.setReturnMessage(CheckResultCodeEnum.CHECK_SUCCESS.getLabel());
    }


    /**
     * @param employeeName
     * @param employeeName1
     * @return
     */
    private boolean checkSameName(String employeeName, String employeeName1) {
        return employeeName.equals(employeeName1);
    }

    private ChannelEmployeeWhiteList queryWhiteListObject(CheckChannelEmployee employee) {
        String whiteKey = generateWhiteKey(employee.getIdType(), employee.getIdNo());
        ChannelEmployeeWhiteList white = (ChannelEmployeeWhiteList) redisService.get(whiteKey);
        if (white == null) {
            synchronized (this) {
                white = (ChannelEmployeeWhiteList) redisService.get(whiteKey);
                if (white != null) {
                    return white;
                } else {
                    List<ChannelEmployeeWhiteList> whiteList = queryWhiteListFromDB(employee.getIdType(), employee.getIdNo(), WhiteStatusEnum.VALID);
                    if (CollectionUtils.isNotEmpty(whiteList)) {
                        white = whiteList.get(0);
                        redisService.set(whiteKey, white);
                    }
                }
            }
        }
        return white;
    }

    private List<ChannelEmployeeWhiteList> queryWhiteListFromDB(String idType, String idNo, WhiteStatusEnum whiteStatusEnum) {
        return whiteListDao.queryWhiteListFromDB(idType, idNo, whiteStatusEnum);
    }

    /**
     * 黑名单校验
     *
     * @param employee
     * @return
     */
    private CheckResultDetail checkEmployeeFromBlackList(CheckChannelEmployee employee) {
        String blackKey = generateBlackKey(employee.getIdType(), employee.getIdNo(), employee.getLicenseNo(), employee.getEmployeeName());
        CheckResultDetail blackDetail = (CheckResultDetail) redisService.get(blackKey);
        //缓存中无相同的入参请求
        if (blackDetail == null) {
            return null;
        } else {
            //能够根据证件类型、证件号、执业证 找到了 已经申请过的记录。根据记录入参判断是否为相同的请求。相同则返回上次请求的结果
            CheckChannelEmployeeBody employeeRequest = blackDetail.getEmployeeRequest();
            if (
                    employee.getEmployeeName().equals(employeeRequest.getEmployeeName())
                            && employee.getOrgCode().equals(employeeRequest.getOrgCode())
                            && employee.getIdType().equals(employeeRequest.getIdType())
                            && employee.getIdNo().equals(employeeRequest.getIdNo())
                            && employee.getLicenseNo().equals(employeeRequest.getLicenseNo())
            ) {
                return blackDetail;
            }
        }
        return null;
    }

    /**
     * 匹配渠道机构
     *
     * @param orgCode
     * @return
     */
    private String matchChannel(String orgCode) {
        Map<String, BaseInst> allBaseInstsMap = cacheService.getAllBaseInstsMap();
        BaseInst baseInst = allBaseInstsMap.get(orgCode);
        if (baseInst == null) {
            return null;
        }

        List<Tbepartner> allTbepartners = cacheService.getAllTbepartners();
        for (Tbepartner tbepartner : allTbepartners) {
            if (tbepartner.getCompanyid().equals(baseInst.getCompanyid())) {
                return tbepartner.getCompanycode();
            }
        }
        return null;
    }

    /**
     * 入参校验
     *
     * @param employee
     * @return
     */
    private boolean checkParameters(CheckChannelEmployee employee, CheckResultDetail detail) {

        IdType idType = IdType.get(employee.getIdType());
        if (idType == null) {
            detail.setReturnCode(CheckResultCodeEnum.WRONG_ID_TYPE.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.WRONG_ID_TYPE.getLabel());
            return false;
        }

        //判断必填项
        String filledMsg = checkParametersFilled(employee);
        if (StringUtils.isNotEmpty(filledMsg)) {
            detail.setReturnCode(CheckResultCodeEnum.INVALID_EMPLOYEE_INFO.getCode());
            detail.setReturnMessage(filledMsg);
            return false;
        }
        //身份证长度判断 第一代身份证 15 位，第二代身份证18位
        if (employee.getIdType().equals(IdType.ID.name())) {
            int length = employee.getIdNo().length();
            if (length != 18 && length != 15) {
                detail.setReturnCode(CheckResultCodeEnum.ID_WRONG_LENGTH.getCode());
                detail.setReturnMessage(CheckResultCodeEnum.ID_WRONG_LENGTH.getLabel());
                return false;
            }
        }
        //执业证号长度26 证书编码长度应为“17”、“18”、“19”、“23”、“25”、“26” 位
        int licenseNoLength = employee.getLicenseNo().length();
        if (licenseNoLength != 17 && licenseNoLength != 18 && licenseNoLength != 19 && licenseNoLength != 23 && licenseNoLength != 25 && licenseNoLength != 26) {
            detail.setReturnCode(CheckResultCodeEnum.LICENSE_NO_WRONG_LENGTH.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.LICENSE_NO_WRONG_LENGTH.getLabel());
            return false;
        }

        return true;
    }

    private String checkParametersFilled(CheckChannelEmployee employee) {
        //代理人姓名
        if (StringUtils.isEmpty(employee.getEmployeeName())) {
            return "未传入代理人姓名!";
        }
        //代理人渠道商机构
        if (StringUtils.isEmpty(employee.getOrgCode())) {
            return "未传入渠道商机构代码!";
        }
        //idType校验
        if (employee.getIdType() == null) {
            return "未传入证件类型";
        }
        //证件号码
        if (StringUtils.isEmpty(employee.getIdNo())) {
            return "未传入证件号码";
        }
        //执业证件号
        if (StringUtils.isEmpty(employee.getLicenseNo())) {
            return "未传入执业证件号";
        }
        return null;
    }

    private void calculateResult(CheckEmployeeResultVO vo, List<CheckResultDetail> detailList) {
        int success = 0;
        int failed = 0;

        if (CollectionUtils.isNotEmpty(detailList)) {
            for (CheckResultDetail detail : detailList) {
                if (CheckResultCodeEnum.CHECK_SUCCESS.getCode().equals(detail.getReturnCode())) {
                    success++;
                } else {
                    failed++;
                }
            }
        }

        if (success != 0) {
            if (failed == 0) {
                vo.setCheckResult(CheckResultEnum.ALL_SUCCESS.getValue());
                vo.setCheckResultMessage(CheckResultEnum.ALL_SUCCESS.getLabel());
            } else {
                vo.setCheckResult(CheckResultEnum.PART_SUCCESS.getValue());
                vo.setCheckResultMessage(CheckResultEnum.PART_SUCCESS.getLabel());
            }
        } else {
            vo.setCheckResult(CheckResultEnum.ALL_FAIL.getValue());
            vo.setCheckResultMessage(CheckResultEnum.ALL_FAIL.getLabel());
        }
        vo.setCheckResultDetailList(detailList);
    }

    private String generatePatternKey(String idType, String idNo) {
        return idType + "_" + idNo + "_*";
    }

    private String generateWhiteKey(String idType, String idNo) {
        return idType + "_" + idNo + "_white";
    }

    private String generateBlackKey(String idType, String idNo, String licenseNo, String employeeName) {
        return idType + "_" + idNo + "_" + licenseNo + "_" + employeeName + "_black";
    }

    private String generateQueryInfoKey(String idType, String idNo, String licenseNo) {
        return idType + "_" + idNo + "_" + licenseNo + "_queryInfo";
    }

    /**
     * 异步新增或更新校验信息
     *
     * @param checkInfo
     */
    @Async
    public void saveOrUpdateCheckInfo(CheckChannelEmployee employee, EmployeeCheckInfo checkInfo) {

        if (!"100".equals(checkInfo.getResultCode())) {
            return;
        }
        CheckInfoData data = checkInfo.getData();
        ChannelEmployeeCheckResult checkResult = queryCheckResult(data.getCarType(), data.getCardNo());
        if (checkResult == null) {
            checkResult = new ChannelEmployeeCheckResult();
            BeanUtils.copyProperties(data, checkResult);
            checkResult.setId(GenerateUUID.generateUUIDStr());
            //设置cardType
            checkResult.setCardType(data.getCarType());
            checkResult.setCreateTime(new Date());
            checkResultMapper.insertSelective(checkResult);
        } else {
            BeanUtils.copyProperties(data, checkResult);
            //设置cardType
            checkResult.setCardType(data.getCarType());
            checkResult.setModifyTime(new Date());
            checkResultMapper.updateById(checkResult);
        }

        //详情记录
        List<CheckInfoDetail> detailList = data.getDetailList();
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        //删除原有的
        deleteChannelEmployeeDetailByCheckResultId(checkResult.getId());
        //插入现有的
        for (CheckInfoDetail detail : detailList) {
            ChannelEmployeeDetail channelEmployeeDetail = new ChannelEmployeeDetail();
            BeanUtils.copyProperties(detail, channelEmployeeDetail);
            channelEmployeeDetail.setId(GenerateUUID.generateUUIDStr());
            channelEmployeeDetail.setCheckResultId(checkResult.getId());
            channelEmployeeDetailMapper.insertSelective(channelEmployeeDetail);
        }

    }

    private void deleteChannelEmployeeDetailByCheckResultId(String checkResultId) {
        LambdaQueryWrapper<ChannelEmployeeDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ChannelEmployeeDetail::getCheckResultId, checkResultId);
        channelEmployeeDetailMapper.delete(lambdaQueryWrapper);
    }

    private ChannelEmployeeCheckResult queryCheckResult(String carType, String cardNo) {
        LambdaQueryWrapper<ChannelEmployeeCheckResult> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ChannelEmployeeCheckResult::getCardType, carType);
        lambdaQueryWrapper.eq(ChannelEmployeeCheckResult::getCardNo, cardNo);
        List<ChannelEmployeeCheckResult> existList = checkResultMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(existList)) {
            return null;
        } else {
            return existList.get(0);
        }
    }

    @Async
    public void saveCheckExceptionLog(String reqParam, String repMessage) {
        logger.info("存储错误信息,请求体:{}, {}", reqParam, repMessage);
        ChannelEmployeeCheckExceptionLog exceptionLog = new ChannelEmployeeCheckExceptionLog();
        exceptionLog.setId(GenerateUUID.generateUUIDStr());
        exceptionLog.setReqParam(reqParam);
        exceptionLog.setRepMessage(repMessage);
        exceptionLog.setCreateTime(new Date());
        exceptionLog.setCreateUserName("sys");
        exceptionLogMapper.insertSelective(exceptionLog);
    }

    /**
     * 记录查询日志
     */
    @Async
    public void saveCheckInfoLog(List<TempEmployeeInfo> infoList, EntryTypeEnum entryTypeEnum) {

        for (TempEmployeeInfo info : infoList) {
            CheckChannelEmployee employee = info.getEmployee();
            CheckResultDetail detail = info.getDetail();

            ChannelEmployeeCheckLog checkLog = new ChannelEmployeeCheckLog();
            //id
            checkLog.setId(GenerateUUID.generateUUIDStr());
            //idType
            checkLog.setIdType(employee.getIdType());
            //idNo
            checkLog.setIdNo(employee.getIdNo());
            //执业证号
            checkLog.setLicenseNo(employee.getLicenseNo());
            //查询时间
            checkLog.setQueryTime(new Date());

            //设置工号
            ChannelEmployee channelEmployee = queryChannelEmployeeByLicenseNo(employee.getLicenseNo());
            checkLog.setEmployeeCode(channelEmployee == null ? null : channelEmployee.getCode());
            //姓名
            checkLog.setEmployeeName(employee.getEmployeeName());

            //渠道商 信息
            if (StringUtils.isNotEmpty(employee.getChannelCode())) {
                //渠道商
                checkLog.setChannelCode(employee.getChannelCode());
                checkLog.setChannelName(employee.getChannelName());
                //法人机构信息
                checkLog.setCorporationCode(employee.getCorporationCode());
                checkLog.setCorporationName(employee.getCorporationName());
            }

            //返回信息
            checkLog.setRetrunCode(detail.getReturnCode());
            checkLog.setReturnMessage(detail.getReturnMessage());
            checkLog.setSource(entryTypeEnum.getCode());

            //结果
            if ("100".equals(detail.getReturnCode()) && employee.isWhiteFlag()) {
                checkLog.setQueryStatusCode(CheckLogCodeEnum.WHITE_LIST.getCode());
                checkLog.setDescription(CheckLogCodeEnum.WHITE_LIST.getLabel());
            } else if ("100".equals(detail.getReturnCode()) && !employee.isWhiteFlag()) {
                checkLog.setQueryStatusCode(CheckLogCodeEnum.SUCCESS.getCode());
                checkLog.setDescription(CheckLogCodeEnum.SUCCESS.getLabel());
            } else {
                checkLog.setQueryStatusCode(CheckLogCodeEnum.FAIL.getCode());
                checkLog.setDescription(CheckLogCodeEnum.FAIL.getLabel());
            }

            //设置创建时间
            checkLog.setCreateTime(new Date());

            checkLogMapper.insertSelective(checkLog);
        }

    }

    private ChannelEmployee queryChannelEmployeeByLicenseNo(String licenseNo) {
        if (StringUtils.isEmpty(licenseNo)) {
            return null;
        }
        return channelEmployeeDao.selectEmployeeByLicenseNo(licenseNo);
    }

    @Override
    public SimpleManager getManager() {
        String employeeCode = RequestContextHolder.getEmployeeCode();
        ChannelEmployee o = channelEmployeeDao.getByCode(employeeCode);
        List<Tbpartassignmanager> tbpartassignmanagers = tbpartassignmanagerMapper.selectList(new LambdaQueryWrapper<Tbpartassignmanager>()
                .eq(Tbpartassignmanager::getMerchantOrgCode, o.getOrgCode()).eq(Tbpartassignmanager::getMainManagerFlag, "0"));
        if (CollectionUtils.isEmpty(tbpartassignmanagers)) {
            return null;
        }
        List<Map<String, Object>> lst = tbempMapper.list(tbpartassignmanagers.get(0).getCustManagerCode());
        if (CollectionUtils.isEmpty(lst)) {
            return null;
        }
        EmployeeVO employeeVO = PartnerConverter.mapToEmployeeVO(lst.get(0));
        return SimpleManager.builder().code(employeeVO.getCode()).name(employeeVO.getName()).mobile(employeeVO.getMobile()).build();
    }

    @Override
    public PageInfo<ChannelEmployeeVO> listMy(ChannelEmployeeRequest queryRequest) {
        //转换数据结构
        return PageUtil.convert(channelEmployeeExternalService.selectPage(queryRequest), channelEmployee -> BeanCopier.copyObject(channelEmployee, ChannelEmployeeVO.class));
    }

    @Override
    public PageInfo<ChannelEmployeeVO> listPage(String codeOrNameLike, long current, long size) {
        LambdaQueryWrapper<ChannelEmployee> wrapper = new LambdaQueryWrapper();
        wrapper.like(ChannelEmployee::getCode, codeOrNameLike);
        wrapper.or();
        wrapper.like(ChannelEmployee::getName, codeOrNameLike);
        return PageUtil.convert(channelEmployeeMapper.selectPage(new Page<>(current, size), wrapper), channelEmployee -> BeanCopier.copyObject(channelEmployee, ChannelEmployeeVO.class));
    }

    @Override
    public Page<ChannelEmployee> listPage(long current, long size) {
        LambdaQueryWrapper<ChannelEmployee> wrapper = new LambdaQueryWrapper();
        return channelEmployeeMapper.selectPage(new Page<>(current, size), wrapper);

    }

    @Override
    public List<ChannelEmployee> listMyNoPage(ChannelEmployeeRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();

        List<String> orgCodes = channelEmployeeExternalService.handleOrgCodes(queryRequest, myDataAccess);

        Set<String> teamCodes = channelEmployeeExternalService.handleTeamCodes(queryRequest, myDataAccess);

        //根据经过筛选后的合伙人编码，获取团队
        LambdaQueryWrapper<ChannelEmployee> lambdaQueryWrapper = channelEmployeeExternalService.settingLambdaQueryWrapper(queryRequest, orgCodes, teamCodes);

        return channelEmployeeMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public PageInfo<AccountVO> listMyAccountVO(ChannelEmployeeRequest queryRequest) {
        //转换数据结构
        return PageUtil.convert(channelEmployeeExternalService.selectPage(queryRequest), PartnerConverter::channelEmployeeToAccountVO);
    }


    @Override
    public List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode())) {
            //按名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), null, myDataAccess.getContainsSuperAdmin(), myDataAccess.getChannelOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new ArrayList<>();
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getChannelOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new ArrayList<>();
            }
        }
        //按名称查出团队，以及其下面的子团队编码
        Set<String> teamCodes = null;
        if (!StringUtil.isBlank(queryRequest.getTeamCode())) {
            teamCodes = channelTeamService.selectAllChildCodes(queryRequest.getTeamCode(), null, myDataAccess);
            if (teamCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new ArrayList<>();
            }
        }

        //根据经过筛选后的合伙人编码，获取团队
        LambdaQueryWrapper<ChannelEmployee> w = new LambdaQueryWrapper<ChannelEmployee>().orderByDesc(ChannelEmployee::getStatus).orderByDesc(ChannelEmployee::getId);
        if (orgCodes != null && !orgCodes.isEmpty()) {
            w.in(ChannelEmployee::getOrgCode, orgCodes);
        }
        if (teamCodes != null) {
            w.in(ChannelEmployee::getTeamCode, teamCodes);
        }
        if (!StringUtil.isBlank(queryRequest.getTopCode())) {
            w.eq(ChannelEmployee::getChannelCode, queryRequest.getTopCode());
        }
        if (!StringUtil.isBlank(queryRequest.getValue())) {
            w.and(wrapper -> wrapper.like(ChannelEmployee::getCode, queryRequest.getValue()).or().like(ChannelEmployee::getName, queryRequest.getValue()));
        }
        long start = (queryRequest.getCurrent() - 1) * queryRequest.getSize();
        w.last("limit " + start + "," + queryRequest.getSize());
        List<ChannelEmployee> list = channelEmployeeMapper.selectList(w);
        return BeanCopier.copyList(list, PartnerConverter::channelEmployeeToSimpleEmployeeVO);
    }

    @Override
    @DS("org")
    @DSTransactional
    public void checkAndSave(ChannelEmployeeAddRequest request) {
        ChannelTeam team = channelTeamDao.getByCode(request.getTeamCode());
        //查询当前登录人是否有“入职的团队编码”的数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Tbepartner tbepartner = tbepartnerDao.getByCode(team.getChannelCode());
        ChannelEmployeeCheckVo checkVo = channelEmployeeExternalService.check(request, team, tbepartner, myDataAccess, true, null);
        //中银保信查询
        if (checkVo.isSuccess()) {
            checkForChannel(request, team, tbepartner, checkVo);
        }
        AssertUtil.isTrue(checkVo.isSuccess(), new ApiException(checkVo.getDescription()));
        save(request, team);
    }

    @Override
    public void checkBatch(List<ChannelEmployeeAddRequest> employees, String fileName) {
        AssertUtil.isTrue(!employees.isEmpty(), new ApiException("数据不得为空"));
        Set<String> teamCodes = StreamEx.of(employees).map(ChannelEmployeeAddRequest::getTeamCode).toSet();
        List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .eq(ChannelTeam::getStatus, TeamStatus.ENABLED)
                .in(ChannelTeam::getCode, teamCodes));
        checkBatch(employees, channelTeams, true, fileName);
    }


    @Override
    public void saveBatch(List<ChannelEmployeeAddRequest> employees) {
        Set<String> teamCodes = StreamEx.of(employees).map(ChannelEmployeeAddRequest::getTeamCode).toSet();
        List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .eq(ChannelTeam::getStatus, TeamStatus.ENABLED)
                .in(ChannelTeam::getCode, teamCodes));
        Map<String, ChannelTeam> hannelTeamMap = StreamEx.of(channelTeams).toMap(ChannelTeam::getCode, Function.identity());
        ChannelEmployeeServiceImpl impl = (ChannelEmployeeServiceImpl) AopContext.currentProxy();
        for (ChannelEmployeeAddRequest req : employees) {
            ChannelTeam channelTeam = hannelTeamMap.get(req.getTeamCode());
            impl.save(req, channelTeam);
        }
    }

    @Override
    @DS("org")
    @DSTransactional
    public void save(ChannelEmployeeAddRequest request, ChannelTeam team) {
        //生成工号
        ChannelEmployee replace = channelEmployeeExternalService.generateEmployeeCode(team.getChannelCode());
        ChannelEmployee ce = BeanCopier.copyObject(request, ChannelEmployee.class);
        if (StringUtil.isEmpty(ce.getSourceSystem())) {
            ce.setSourceSystem(AppConsts.DEFAULT_SOURCE_SYSTEM);
        }
        ce.setIsAuthorized(AppConsts.YES);
        ce.setLicenseNo(StringUtils.trimToEmpty(request.getLicenseNo()));
        ce.setLicenseStartDate(request.getLicenseStartDate());
        ce.setLicenseEndDate(request.getLicenseEndDate());
        ce.setIdType(request.getIdType());
        ce.setIdCode(StringUtils.trimToEmpty(request.getIdCode()));
        ce.setId(replace.getId());
        ce.setCode(replace.getCode());
        ce.setCreator(replace.getCreator());
        ce.setCreateTime(replace.getCreateTime());
        ce.setCreatorId(replace.getCreatorId());

        ce.setChannelCode(team.getChannelCode());
        ce.setChannelName(team.getChannelName());
        ce.setOrgCode(team.getOrgCode());
        ce.setOrgName(team.getOrgName());
        ce.setTeamCode(team.getCode());
        ce.setTeamName(team.getName());
        ce.setEntryTime(LocalDateTime.now());
        ce.setStatus(EmployeeStatus.SERVING.name());
        ce.setRoleType(request.getRoleType().name());
        //单个插入的时候这个字段是枚举英文，批量的时候写的是和否。做如下处理
        if (RoleType.TESTER.getValue().equals(ce.getRoleType())) {
            ce.setRoleType(RoleType.TESTER.name());
        }
        if (RoleType.FORMAL.getValue().equals(ce.getRoleType())) {
            ce.setRoleType(RoleType.FORMAL.name());
        }
        //更新之前先跨服务先创建账号。根据replace.getCode()
        AgentCreateByAdminRequest req = getAgentCreateByAdminRequest(ce);

        //查询此人历史入职数据，如有则获取最近一条属于无效或者离职的手机号发送给UM
        ChannelEmployee channelEmployee = channelEmployeeMapper
                .selectOne(new LambdaQueryWrapper<ChannelEmployee>()
                        .eq(ChannelEmployee::getIdCode, request.getIdCode())
                        .eq(ChannelEmployee::getStatus, "LEAVING")
                        .orderByDesc(ChannelEmployee::getCreateTime)
                        .last("limit 1"));
        if (null != channelEmployee) {
            req.setOriginalPhone(channelEmployee.getMobile());
        }

        Long umId = umClient.createAgentUser(req);
        channelEmployeeMapper.updateById(ce);

        //神策埋点
        if (null != umId) {
            sensorsService.addEmployee(ce, umId.toString());
        }
    }

    @NotNull
    private static AgentCreateByAdminRequest getAgentCreateByAdminRequest(ChannelEmployee ce) {
        AgentCreateByAdminRequest req = new AgentCreateByAdminRequest();
        req.setOrgType(AgentOrgType.CHANNEL.name());
        req.setEmployeeCode(ce.getCode());
        req.setState(AppConsts.VALID);
        req.setPhone(ce.getMobile());
        req.setPassword(PasswordUtil.generatePwd(ce.getIdCode()));
        req.setStaffId(RequestContextHolder.getStaffId());
        req.setName(ce.getName());
        return req;
    }


    @Override
    public ChannelEmployeeCheckVo update(ChannelEmployeeUpdateRequest request) {
        ChannelEmployeeCheckVo checkVo = new ChannelEmployeeCheckVo();
        //神策变量
        String name = null, gender = null, mobile = null, umId = null;
        LocalDate birthday = null;
        //1.根据id查出销售员，检测当前用户是否有权限修改
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        ChannelEmployee o = channelEmployeeDao.getById(request.getId());

        //20240223 渠道商用户更新时是否有执业证化
        String currentLicenseNo = o.getLicenseNo();
        boolean licenseNoChanged = !Objects.equals(currentLicenseNo, request.getLicenseNo());

        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(o.getOrgCode()), new ApiException("没有权限操作"));
        }
        //执业证号校验
        //      当渠道商类型为：保险经纪或专业代理时，执业证号为必录，且执业证起期及执业证止期必录。当渠道商类型为兼业代理，执业证号非必录
        //证件类型、证件号校验
        //      当渠道商类型为：保险经纪或专业代理时，证件类型、证件号码为必录

        // 202108019 禅道编号1017 校验去掉 后续可能会添加上
        Tbepartner tbepartner = tbepartnerDao.getByCode(o.getChannelCode());

        ChannelTeam team = channelTeamDao.getByCode(o.getTeamCode());
        //类型转换
        ChannelEmployeeAddRequest channelEmployeeAddRequest = BeanCopier.copyObject(request, ChannelEmployeeAddRequest.class);
        channelEmployeeAddRequest.setTeamCode(o.getTeamCode());
        channelEmployeeAddRequest.setCode(o.getCode());

        // 走统一校验逻辑
        if (!o.getMobile().equals(request.getMobile())) {
            checkVo = channelEmployeeExternalService.check(channelEmployeeAddRequest, team, tbepartner, myDataAccess, false, null);
        } else {
            checkVo = channelEmployeeExternalService.check(channelEmployeeAddRequest, team, tbepartner, myDataAccess, false, o);
        }
        //中银保信查询
        if (checkVo.isSuccess()) {
            checkForChannel(channelEmployeeAddRequest, team, tbepartner, checkVo);
        }
        if (!checkVo.isSuccess()) {
            return checkVo;
        }
        if (!o.getName().equals(request.getName())) {
            o.setName(request.getName());
            name = o.getName();
        }
        if (!o.getGender().equals(request.getGender().name())) {
            o.setGender(request.getGender().name());
            gender = o.getGender();
        }
        if (request.getBirthday() != null && !request.getBirthday().equals(o.getBirthday())) {
            o.setBirthday(request.getBirthday());
            birthday = o.getBirthday();
        }
        o.setIdType(request.getIdType());
        o.setIdCode(request.getIdCode());
        o.setLicenseNo(request.getLicenseNo());
        o.setLicenseStartDate(request.getLicenseStartDate());
        o.setLicenseEndDate(request.getLicenseEndDate());
        o.setJobNumber(request.getJobNumber());
        o.setUniversalQualification(request.getUniversalQualification());

        //是否修改手机号，修改手机号需要跨服务更新手机号，成功才能继续
        if (!o.getMobile().equals(request.getMobile())) {
            List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(o.getCode());
            if (!CollectionUtils.isEmpty(accountInfoDTOList)) {
                //跨服务把账号变成失效。成功才能保存ChannelEmployeeServiceImpl
                umClient.agentUserPhoneUpdate(accountInfoDTOList.get(0).getUserId(), o.getMobile(), request.getMobile());
                umId = accountInfoDTOList.get(0).getUserId().toString();
            }

            o.setMobile(request.getMobile());
            mobile = o.getMobile();
        }
        o.setRoleType(request.getRoleType().name());
        channelEmployeeMapper.updateById(o);

        if (licenseNoChanged) {
            try {
//                AgentCreateByAdminRequest req = getAgentCreateByAdminRequest(o);
//                Long umIdNew = umClient.createAgentUser(req);
//                if (umIdNew != null) {
//                    umId = umIdNew.toString();
//                }
                VisitorUpdateByAdminRequest req = new VisitorUpdateByAdminRequest();
                req.setBeforeIdentity(currentLicenseNo);
                req.setAfterIdentity(request.getLicenseNo());
                req.setAppId(1001);
                req.setStaffId(RequestContextHolder.getStaffId());

                adminApi.updateVisitorByAdmin(req);
            } catch (Exception e) {
                logger.error("修改人员时，当执业证号变更时，新建um账号发生异常：{}", e.getMessage());
            }
        }
        //神策埋点
        sensorsService.updateEmployee(o.getCode(), umId, name, gender, birthday, mobile);

        //同步更新marketing服务中card 信息
        channelEmployeeExternalService.syncLicenseNo(o);

        checkVo.setSuccess(true);
        checkVo.setDescription("更新成功！");
        return checkVo;

    }


    @Override
    public ChannelEmployeeCheckVo updateLicenseNo(String code, String licenseNo) {
        AssertUtil.isTrue(!StringUtil.isBlank(code), new ApiException("销售员代码不能为空"));
        AssertUtil.isTrue(!StringUtil.isBlank(licenseNo) && ReUtil.isMatch("[a-zA-Z0-9]{1,26}", licenseNo) && channelEmployeeExternalService.checkLicenseNo(licenseNo), new ApiException("中介销售人员执业证号无效，请确认"));

        ChannelEmployee o = channelEmployeeDao.getByCode(code);
        AssertUtil.isTrue(ObjectUtil.isNotNull(o), new ApiException("当前销售人员不存在"));

        //增加执业证书号唯一性校验
        ChannelEmployeeCheckVo checkVo = new ChannelEmployeeCheckVo();
        checkVo.setSuccess(true);
        checkVo.setDescription("");
        //校验执业证书号
        channelEmployeeExternalService.checkLicenseNo(licenseNo, o.getCode(), checkVo, false);
        if (!checkVo.isSuccess()) {
            return checkVo;
        }
        o.setLicenseNo(licenseNo);
        channelEmployeeMapper.updateById(o);
        checkVo.setSuccess(true);
        checkVo.setDescription("更新成功");
        return checkVo;
    }

    @Override
    public List<ChannelEmployee> getByIdCodeOrLicenseNo(String idCode, String licenseNo) {
        return channelEmployeeMapper.getByIdCodeOrLicenseNo(idCode, licenseNo);
    }

    @Override
    public List<EmployeeVO> getChannelEmployeeByOrgCodeAndStatus(String channelCode, String status) {
        QueryWrapper<ChannelEmployee> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("code,name,channel_code,channel_name,org_code,org_name,status");
        if (StringUtil.isNotBlank(channelCode)) {
            queryWrapper.eq("channel_code", channelCode);
        }
        if (StringUtil.isNotBlank(status)) {
            queryWrapper.eq("status", status);
        }
        List<ChannelEmployee> channelEmployeeList = channelEmployeeMapper.selectList(queryWrapper);

        return CollectionUtils.isEmpty(channelEmployeeList) ? new ArrayList<>() : channelEmployeeConvert.assembler(channelEmployeeList);
    }

    @Override
    public String exportData(ChannelEmployeeRequest queryRequest) {
        List<ChannelEmployee> detailList = listMyNoPage(queryRequest);

        List<ChannelEmployeeExcel> resultList = new ArrayList<>();
        detailList.forEach(channelEmployee -> resultList.add(channelEmployeeExcelAssembler.assembler(channelEmployee)));


        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            WriteCellStyle cellStyle = new WriteCellStyle();
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);

            EasyExcel.write(bos, ChannelEmployeeExcel.class)
                    .registerWriteHandler(new CellWriteWidthConfig())
                    .sheet("渠道商代理人员清单")
                    .doWrite(resultList);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("渠道商代理人员清单" + LocalDateTime.now() + ".xls")
                .source("agent")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }

    @Override
    public List<EmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList) {
        if (CollectionUtils.isEmpty(employeeCodeList)) {
            return Lists.newArrayList();
        }
        QueryWrapper<ChannelEmployee> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("code,status");
        queryWrapper.in("code", employeeCodeList);
        List<ChannelEmployee> channelEmployeeList = channelEmployeeMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(channelEmployeeList) ? new ArrayList<>() : channelEmployeeConvert.assembler(channelEmployeeList);
    }

    @Override
    public PageInfo<FileUploadRecordVO> queryFileUpload(long current, long size) {
        Page<FileUploadRecord> page = fileUploadRecordMapper.selectPage(new Page<>(current, size),
                Wrappers.lambdaQuery(FileUploadRecord.class)
                        .eq(FileUploadRecord::getCreatorId, RequestContextHolder.getStaffId())
                        .orderByDesc(FileUploadRecord::getCreateTime));
        return PageUtil.convert(page, fileUploadRecord -> BeanCopier.copyObject(fileUploadRecord, FileUploadRecordVO.class));
    }

    @Override
    public PageInfo<BatchFailInfoVO> queryFileDetailed(long id, long current, long size) {
        PageInfo<BatchFailInfoVO> pageInfo = new PageInfo<>(current, size);
        List<BatchFailInfoVO> result = new ArrayList<>();
        Page<BatchFailInfo> page = batchFailInfoMapper.selectPage(new Page<>(current, size), Wrappers.lambdaQuery(BatchFailInfo.class)
                .eq(BatchFailInfo::getFileUpdateId, id)
                .eq(BatchFailInfo::getDeleted, Boolean.FALSE));
        List<BatchFailInfo> list = page.getRecords();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                BatchFailInfoVO batchFailInfoVO = JsonUtil.fromJSON(x.getFailInfoJson(), BatchFailInfoVO.class);
                batchFailInfoVO.setErrorMessage(x.getErrorMessage());
                try {
                    ChannelTeam team = channelTeamDao.getByCode(batchFailInfoVO.getTeamCode());
                    batchFailInfoVO.setChannelCode(team.getChannelCode());
                    batchFailInfoVO.setChannelName(team.getChannelName());
                    batchFailInfoVO.setOrgCode(team.getOrgCode());
                    batchFailInfoVO.setOrgName(team.getOrgName());
                    batchFailInfoVO.setTeamCode(team.getCode());
                    batchFailInfoVO.setTeamName(team.getName());
                } catch (Exception e) {
                    batchFailInfoVO.setChannelCode("");
                    batchFailInfoVO.setChannelName("");
                    batchFailInfoVO.setOrgCode("");
                    batchFailInfoVO.setOrgName("");
                    batchFailInfoVO.setTeamName("");
                }
                result.add(batchFailInfoVO);
            });
            pageInfo.setPages(page.getPages());
            pageInfo.setCurrent(page.getCurrent());
            pageInfo.setSize(page.getSize());
            pageInfo.setTotal(page.getTotal());
            pageInfo.setRecords(result);
        }
        return pageInfo;
    }


    @Override
    public void leave(Long id) throws Exception {
        // 渠道商下销售人员停用（离职）调用接口
        //1.根据id查出销售员，检测当前用户是否有权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        ChannelEmployee o = channelEmployeeDao.getById(id);
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(o.getOrgCode()), new ApiException("没有权限操作"));
        }
        if (EmployeeStatus.SERVING.name().equals(o.getStatus())) {
            //离职
            leave(o);
        }
    }

    private void leave(ChannelEmployee o) throws Exception {
        if (EmployeeStatus.SERVING.name().equals(o.getStatus())) {
            o.setQuitTime(LocalDateTime.now());
            o.setStatus(EmployeeStatus.LEAVING.name());

            List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(o.getCode());
            if (!CollectionUtils.isEmpty(accountInfoDTOList)) {
                //跨服务把账号变成失效。成功才能保存
                AgentStateDTO agentStateDTO = new AgentStateDTO();
                agentStateDTO.setState(AppConsts.QUIT);
                agentStateDTO.setStaffId(RequestContextHolder.getStaffId());
                agentStateDTO.setUserId(accountInfoDTOList.get(0).getUserId());
                umClient.updateEmployeeStateByAdmin(agentStateDTO);
            }
            channelEmployeeMapper.updateById(o);
            //发送MQ消息
            String msgKey = o.getCode() + "_status_change_" + EmployeeStatus.LEAVING.name();
            Map<String, String> map = new HashMap<>();
            map.put("employeeCode", o.getCode());
            map.put("status", o.getStatus());
            producerClient.sendMsg(MqTagEnum.CHANNEL_EMPLOYEE_STATUS_CHANGE.name(), msgKey, JsonUtil.toJSON(map));
        }
    }

    @Override
    public void leaveForChannel(ChannelEmployeeDeleteRequest deleteRequest) throws Exception {
        String licenseNo = deleteRequest.getLicenseNo();
        ChannelCompanyEnum channelCompany = deleteRequest.getChannelCompany();

        if (StringUtils.isEmpty(licenseNo)) {
            throw new Exception("用户的执业证书号不能为null!");
        }

        ChannelEmployee channelEmployee = channelEmployeeDao.selectEmployeeByLicenseNo(licenseNo);
        if (channelEmployee == null) {
            throw new Exception("无效的执业证书号!");
        }

        if (!EmployeeStatus.SERVING.name().equals(channelEmployee.getStatus())) {
            throw new Exception("执业证书号" + licenseNo + "所对应的员工已经离职或失效!");
        }

        if (!channelCompany.getInstCode().equals(channelEmployee.getChannelCode())) {
            throw new Exception("执业证书号" + licenseNo + "所对应的员工不属于" + channelCompany.getLabel());
        }
        //离职
        leave(channelEmployee);

    }

    /**
     * 离职团队下所有的员工
     *
     * @param teamCode
     */
    @Override
    public void leaveByTeamCode(String teamCode) {
        List<ChannelEmployee> employees = channelEmployeeMapper.selectList(new LambdaQueryWrapper<ChannelEmployee>()
                .eq(ChannelEmployee::getStatus, EmployeeStatus.SERVING.name())
                .eq(ChannelEmployee::getTeamCode, teamCode));
        log.info("团队状态变更,leaveByTeamCode,---employees:{}", employees);
        if (CollectionUtils.isEmpty(employees)) {
            return;
        }

        List<String> employeeCodes = StreamEx.of(employees).map(ChannelEmployee::getCode).collect(Collectors.toList());
        List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(employeeCodes);
        Map<String, Long> agentUserInfosMap = convertData(accountInfoDTOList);
        log.info("团队状态变更,leaveByTeamCode-----------------1");
        for (ChannelEmployee o : employees) {
            if (EmployeeStatus.SERVING.name().equals(o.getStatus())) {
                Long userId = agentUserInfosMap.get(o.getCode());
                log.info("团队状态变更,leaveByTeamCode-----------------agentId:{},employeeCode:{}", userId, o);
                if (userId != null) {
                    //跨服务把账号变成失效。成功才能保存
                    AgentStateDTO agentStateDTO = new AgentStateDTO();
                    agentStateDTO.setState(AppConsts.QUIT);
                    agentStateDTO.setUserId(userId);
                    agentStateDTO.setStaffId(RequestContextHolder.getStaffId() == null ? -1L : RequestContextHolder.getStaffId());
                    log.info("团队状态变更,agentUserStateUpdate,---agentId:{},req:{}", userId, JsonUtil.toJSON(agentStateDTO));
                    umClient.updateEmployeeStateByAdmin(agentStateDTO);
                }
                o.setQuitTime(LocalDateTime.now());
                o.setStatus(EmployeeStatus.LEAVING.name());
                channelEmployeeMapper.updateById(o);
            }
        }
    }

    public Map<String, Long> convertData(List<AccountInfoDTO> accountInfoDTOList) {
        Map<String, Long> agentUserInfosMap = Maps.newHashMap();
        for (AccountInfoDTO accountInfoDTO : accountInfoDTOList) {
            if (null == accountInfoDTO.getAgentInfo()) {
                continue;
            }
            agentUserInfosMap.put(accountInfoDTO.getAgentInfo().getEmployeeCode(), accountInfoDTO.getUserId());
        }
        return agentUserInfosMap;
    }

    @Override
    public ChannelEmployeeCheckVo reentry(Long id) {
        //1.根据id查出销售员，检测当前用户是否有权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        ChannelEmployee o = channelEmployeeDao.getById(id);
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(o.getOrgCode()), new ApiException("没有权限操作"));
        }
        /**
         * 增加校验 二次入职时判断是否证件号与执业证号重复
         */
        ChannelEmployeeCheckVo checkVo = new ChannelEmployeeCheckVo();
        checkVo.setSuccess(true);
        checkVo.setDescription("");

        //校验执业证书号
        channelEmployeeExternalService.checkLicenseNo(o.getLicenseNo(), o.getCode(), checkVo, false);
        //校验身份证号
        channelEmployeeExternalService.checkIdCode(o.getIdCode(), o.getCode(), checkVo, false);
        if (!checkVo.isSuccess()) {
            return checkVo;
        }
        if (EmployeeStatus.LEAVING.name().equals(o.getStatus())) {
            //1.查询销售员的团队状态停用状态不允许重入职
            ChannelTeam team = channelTeamDao.getByCode(o.getTeamCode());
            AssertUtil.isTrue(TeamStatus.ENABLED.name().equals(team.getStatus()), new ApiException("销售员所属的团队状态不允许入职"));

            //2.所属的渠道商是否存在
            Tbepartner tbepartner = tbepartnerDao.getByCode(team.getChannelCode());
            AssertUtil.isTrue(tbepartner != null, new ApiException("销售员所属的团队状态不允许入职"));

            //3.中银保信查询
            CheckEmployeeResultVO checkEmployeeResultVO = checkForChannel(o);
            if (checkEmployeeResultVO.getCheckResult().intValue() != CheckResultEnum.ALL_SUCCESS.getValue().intValue()) {
                CheckResultDetail checkResultDetail = checkEmployeeResultVO.getCheckResultDetailList().get(0);
                AssertUtil.isTrue(CheckResultCodeEnum.CHECK_SUCCESS.getCode().equals(checkResultDetail.getReturnCode()), new ApiException(checkResultDetail.getReturnMessage()));
            }

            o.setEntryTime(LocalDateTime.now());
            o.setQuitTime(null);
            o.setStatus(EmployeeStatus.SERVING.name());

            //人员复用重新创建账号
            AgentCreateByAdminRequest request = getAgentCreateByAdminRequest(o);
            Long umId = umClient.createAgentUser(request);

            channelEmployeeMapper.updateById(o);

            //神策埋点
            sensorsService.addEmployee(o, umId.toString());

            checkVo.setSuccess(true);
            checkVo.setDescription("更新成功;");
            return checkVo;
        }
        return null;
    }


    @Override
    public ChannelEmployeeVO getMyEmployee(String teamCode, String code) {
        //检测当前用户是否有权限查看，暂时不限制权限
//        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
//        AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(orgCode), new ApiException("权限不足"));
        ChannelEmployee o = channelEmployeeDao.getByCode(code);
        if (!o.getTeamCode().equals(teamCode)) {
            //检测leaderId是否属于该组。
            Set<String> teamCodes = StreamEx.of(orgService.getTeams(AgentOrgType.CHANNEL, teamCode, true)).map(TeamVO::getCode).toSet();
            AssertUtil.isTrue(teamCodes.contains(o.getTeamCode()), new ApiException("该代理人不属于本团队"));
        }
        return BeanCopier.copyObject(o, ChannelEmployeeVO.class);
    }

    @Override
    public EmployeeVO getEmployeeVO(String employeeCode) {
        ChannelEmployee o = channelEmployeeDao.getByCode(employeeCode);
        EmployeeVO vo = BeanCopier.copyObject(o, EmployeeVO.class);
        vo.setLicenseStartDate(o.getLicenseStartDate());
        vo.setLicenseEndDate(o.getLicenseEndDate());
        vo.setTopCode(o.getChannelCode());
        vo.setTopName(o.getChannelName());
        vo.setOrgType(AgentOrgType.CHANNEL);
        return vo;
    }

    @Override
    public EmployeeVO getEmployeeVOByYDL(String employeeCode) {
        ChannelEmployee o = new LambdaQueryChainWrapper<>(channelEmployeeMapper)
                .eq(ChannelEmployee::getJobNumber, employeeCode)
                .eq(ChannelEmployee::getStatus,EmployeeStatus.SERVING.name())
                .last("limit 1")
                .one();
        if (o == null) {
            o = channelEmployeeDao.getByCode(employeeCode);
        }
        EmployeeVO vo = BeanCopier.copyObject(o, EmployeeVO.class);
        vo.setLicenseStartDate(o.getLicenseStartDate());
        vo.setLicenseEndDate(o.getLicenseEndDate());
        vo.setTopCode(o.getChannelCode());
        vo.setTopName(o.getChannelName());
        vo.setOrgType(AgentOrgType.CHANNEL);
        return vo;
    }

    @Override
    public EmployeeVO getEmployeeVOByMobile(String mobile) {
        ChannelEmployee channelEmployee = channelEmployeeDao.getByMobile(mobile);
        EmployeeVO vo = BeanCopier.copyObject(channelEmployee, EmployeeVO.class);
        vo.setLicenseStartDate(channelEmployee.getLicenseStartDate());
        vo.setLicenseEndDate(channelEmployee.getLicenseEndDate());
        vo.setTopCode(channelEmployee.getChannelCode());
        vo.setTopName(channelEmployee.getChannelName());
        vo.setOrgType(AgentOrgType.CHANNEL);
        return vo;
    }

    @Override
    public MyEmployeeVO getMyEmployeeVO(String employeeCode) {
        ChannelEmployee o = channelEmployeeDao.getByCode(employeeCode);
        MyEmployeeVO vo = BeanCopier.copyObject(o, MyEmployeeVO.class);
        if (!StringUtil.isBlank(vo.getMobile())) {
            vo.setMobile(PhoneUtil.hideBetween(vo.getMobile()).toString());
        }
        return vo;
    }

    @DS("org")
    @DSTransactional
    private void checkAndSaveCopy(ChannelEmployeeAddRequest request) {
        save(request, channelTeamDao.getByCode(request.getTeamCode()));
    }

    public void checkBatch(List<ChannelEmployeeAddRequest> employees, List<ChannelTeam> channelTeams, Boolean flag, String fileName) {
        //记录文件上传表
        FileUploadRecord fileUploadRecord = FileUploadRecord.builder()
                .fileName(fileName)
                .creator(RequestContextHolder.getStaffUsername())
                .creatorId(RequestContextHolder.getStaffId())
                .createTime(LocalDateTime.now())
                .status(FileUploadStatusType.UNFINISHED.name())
                .personNumber(employees.size()).build();
        fileUploadRecordMapper.insertSelective(fileUploadRecord);

        List<ChannelEmployeeCheckVo> voList = new ArrayList<>();
        Map<String, ChannelTeam> hannelTeamMap = StreamEx.of(channelTeams).toMap(ChannelTeam::getCode, Function.identity());
        //查询当前登录人是否有“入职的团队编码”的数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Map<String, Tbepartner> tbepartnersMap = new HashMap<>(channelTeams.size());
        if (!channelTeams.isEmpty()) {
            Set<String> topCodes = StreamEx.of(channelTeams).map(ChannelTeam::getChannelCode).toSet();
            tbepartnersMap = StreamEx.of(cacheService.getAllTbepartners()).filter(t -> AppConsts.CPTYPE_CHANNEL.equals(t.getCptype())
                    && t.getComanystatus() == 1
                    && topCodes.contains(t.getCompanycode())
            ).toMap(Tbepartner::getCompanycode, Function.identity());
        }
        for (int i = 0; i < employees.size(); i++) {
            ChannelEmployeeAddRequest req = employees.get(i);
            ChannelTeam channelTeam = hannelTeamMap.get(req.getTeamCode());
            if (channelTeam == null) {
                ChannelEmployeeCheckVo checkVo = BeanCopier.copyObject(req, ChannelEmployeeCheckVo.class);
                checkVo.setSuccess(false);
                checkVo.setDescription("团队编码错误");
                voList.add(checkVo);
                continue;
            }

            Tbepartner tbepartner = tbepartnersMap.get(channelTeam.getChannelCode());
            ChannelEmployeeCheckVo checkVo = channelEmployeeExternalService.check(req, channelTeam, tbepartner, myDataAccess, flag, null);
            //校验数据是否有重复
            if (checkVo.isSuccess()) {
                checkRepeatAddRequests(req, i, employees, checkVo);
            }
            //中银保信查询
            if (checkVo.isSuccess()) {
                checkForChannel(req, channelTeam, tbepartner, checkVo);
            }
            voList.add(checkVo);
        }

        //只拿true的存库，false的存失败表
        AtomicInteger position = new AtomicInteger();
        voList.forEach(info -> {
            log.info("批量上传同步人员当前位置-----------------------------------------------------------------------------");
            log.info("批量上传同步人员当前位置-----------------------------------------------------------------------------");
            log.info("批量上传同步人员当前位置---------------------------------------------：{}", position.getAndIncrement());
            log.info("批量上传同步人员当前位置-----------------------------------------------------------------------------");
            log.info("批量上传同步人员当前位置-----------------------------------------------------------------------------");
            if (info.isSuccess()) {
                ChannelEmployeeAddRequest channelEmployeeAddRequest = BeanCopier.copyObject(info, ChannelEmployeeAddRequest.class);
                try {
                    checkAndSaveCopy(channelEmployeeAddRequest);
                } catch (Exception e) {
                    log.error("调用UM的createAgentUser异常，异常内容{}", e.toString());
                    //异常存库
                    batchFailInfoMapper.insert(BatchFailInfo.builder()
                            .fileUpdateId(fileUploadRecord.getId())
                            .employeeName(info.getName())
                            .failInfoJson(JsonUtil.toJSON(info))
                            .idCode(info.getIdCode())
                            .errorMessage("----UM1062异常----" + e.getMessage())
                            .creator(RequestContextHolder.getStaffUsername())
                            .creatorId(RequestContextHolder.getStaffId())
                            .deleted(false)
                            .build());
                    return;
                }
                //将成功的数据从失败数据中抹除（逻辑删除）
                batchFailInfoMapper.updateByIdCode(BatchFailInfo.builder()
                        .fileUpdateId(fileUploadRecord.getId())
                        .modifier(RequestContextHolder.getStaffUsername())
                        .modifierId(RequestContextHolder.getStaffId())
                        .updateTime(LocalDateTime.now())
                        .idCode(info.getIdCode())
                        .build());
                return;
            }
            //插入错误信息
            batchFailInfoMapper.insert(BatchFailInfo.builder()
                    .fileUpdateId(fileUploadRecord.getId())
                    .employeeName(info.getName())
                    .failInfoJson(JsonUtil.toJSON(info))
                    .idCode(info.getIdCode())
                    .errorMessage(info.getDescription())
                    .creator(RequestContextHolder.getStaffUsername())
                    .creatorId(RequestContextHolder.getStaffId())
                    .deleted(false)
                    .build());
            return;
        });
        //更新文件上传表的成功失败数量
        Integer failCount = batchFailInfoMapper.selectCount(Wrappers.lambdaQuery(BatchFailInfo.class)
                .eq(BatchFailInfo::getFileUpdateId, fileUploadRecord.getId())
                .eq(BatchFailInfo::getDeleted, Boolean.FALSE));
        fileUploadRecord.setStatus(FileUploadStatusType.SUCCEED.name());
        fileUploadRecord.setSuccessNumber(employees.size() - failCount);
        fileUploadRecord.setFailNumber(failCount);
        fileUploadRecordMapper.updateById(fileUploadRecord);
    }

    /**
     * 批量创建时对校验重复数据
     *
     * @param addRequest
     * @param index
     * @param employees
     * @param checkVo
     */
    private void checkRepeatAddRequests(ChannelEmployeeAddRequest addRequest, int index, List<ChannelEmployeeAddRequest> employees, ChannelEmployeeCheckVo checkVo) {
        String licenseNo = addRequest.getLicenseNo();
        String mobile = addRequest.getMobile();
        for (int i = 0; i < employees.size(); i++) {
            //自身不校验
            if (i == index) {
                continue;
            }
            ChannelEmployeeAddRequest iRequest = employees.get(i);
            //判断相同行
            if (addRequest.equals(iRequest)) {
                checkVo.setSuccess(false);
                checkVo.setDescription("人员:" + addRequest.getName() + "有重复的行!");
                return;//结束
            }
            //判断是否有相同的执业证号
            if (StringUtils.isNotEmpty(licenseNo)) {
                if (licenseNo.equals(iRequest.getLicenseNo())) {
                    checkVo.setSuccess(false);
                    checkVo.setDescription("人员:" + addRequest.getName() + "有相同的执行证号!");
                    return;//结束
                }
            }
            //校验手机号是否有相同
            if (StringUtils.isNotEmpty(mobile)) {
                if (mobile.equals(iRequest.getMobile())) {
                    checkVo.setSuccess(false);
                    checkVo.setDescription("人员:" + addRequest.getName() + "有相同的手机号!");
                    return;//结束
                }
            }
        }
    }

    @Override
    public CheckEmployeeResultVO checkForChannelByLicense(CheckChannelEmployeeRequest checkChannelEmployeeRequest) {
        //参数校验部分
        CheckEmployeeResultVO vo = new CheckEmployeeResultVO();
        List<CheckChannelEmployeeBody> employeeBodyList = checkChannelEmployeeRequest.getEmployeeList();
        if (org.springframework.util.CollectionUtils.isEmpty(employeeBodyList)) {
            vo.setCheckResult(CheckResultEnum.ALL_FAIL.getValue());
            vo.setCheckResultMessage("无需要验证的代理人信息!");
            return vo;
        }
        //校验结果详细信息
        List<CheckResultDetail> detailList = Lists.newArrayList();
        //用于记录日志的临时对象
        List<TempEmployeeInfo> infoList = Lists.newArrayList();
        for (CheckChannelEmployeeBody employeeBody : employeeBodyList) {
            logger.info("需要验证的人员信息:{}", JsonUtil.toJSON(employeeBody));
            CheckResultDetail detail = new CheckResultDetail();
            detail.setEmployeeRequest(employeeBody);
            //传输用类
            CheckChannelEmployee employee = new CheckChannelEmployee();
            BeanUtils.copyProperties(employeeBody, employee);

            //记录临时日志
            TempEmployeeInfo info = new TempEmployeeInfo();
            info.setEmployee(employee);
            info.setDetail(detail);
            infoList.add(info);

            //白名单查询
            logger.info("白名单查询:{}", JsonUtil.toJSON(employee));
            ChannelEmployeeWhiteList white = queryWhiteListObjectByLicense(employee);

            if (white != null) {
                logger.info("白名单查询结果:{}", JsonUtil.toJSON(white));
                //匹配执业证号
                if (white.getLicenseNo().equals(employee.getLicenseNo())) {
                    //白名单匹配后的处理方法
                    dealWhite(employee, white, detail);
                    if (CheckResultCodeEnum.CHECK_SUCCESS.getCode().equals(detail.getReturnCode())) {
                        employee.setWhiteFlag(true);
                        detail.getEmployeeRequest().setWhiteFlag(true);
                    } else {
                        //白名单未成功，则查询中银保信
                        dealWithOutWhiteByLicense(employee, detail);
                    }
                } else {
                    //白名单中的执业证号不相同，则查询中银保信
                    dealWithOutWhiteByLicense(employee, detail);
                }
            } else {
                //白名单未匹配后的处理方法
                dealWithOutWhiteByLicense(employee, detail);
            }
            //存放执业证状态
            employeeBody.setLicenseStatus(employee.getLicenseStatus());
            detailList.add(detail);
        }
        //统计结果
        calculateResult(vo, detailList);
        //异步记录日志
        saveCheckInfoLog(infoList, checkChannelEmployeeRequest.getEntryTypeEnum());
        return vo;
    }

    private ChannelEmployeeWhiteList queryWhiteListObjectByLicense(CheckChannelEmployee employee) {
        String whiteKey = generateWhiteKeyByLicense(employee.getEmployeeName(), employee.getLicenseNo());
        ChannelEmployeeWhiteList white = (ChannelEmployeeWhiteList) redisService.get(whiteKey);
        if (white == null) {
            synchronized (this) {
                white = (ChannelEmployeeWhiteList) redisService.get(whiteKey);
                if (white != null) {
                    return white;
                } else {
                    List<ChannelEmployeeWhiteList> whiteList = queryWhiteListFromDBByLicense(employee.getEmployeeName(), employee.getLicenseNo(), WhiteStatusEnum.VALID);
                    if (CollectionUtils.isNotEmpty(whiteList)) {
                        white = whiteList.get(0);
                        redisService.set(whiteKey, white);
                    }
                }
            }
        }
        return white;
    }

    private List<ChannelEmployeeWhiteList> queryWhiteListFromDBByLicense(String employeeName, String licenseNo, WhiteStatusEnum whiteStatusEnum) {
        return whiteListDao.queryWhiteListFromDBByLicense(employeeName, licenseNo, whiteStatusEnum);
    }

    private String generateWhiteKeyByLicense(String employeeName, String licenseNo) {
        return employeeName + "_" + licenseNo + "_white";
    }

    private void dealWithOutWhiteByLicense(CheckChannelEmployee employee, CheckResultDetail detail) {
        String infoKey = generateQueryInfoKeyByLicense(employee.getEmployeeName(), employee.getLicenseNo());
        logger.info("从redis中查询中银保信key：{}", infoKey);
        EmployeeCheckInfo employeeCheckInfo = (EmployeeCheckInfo) redisService.get(infoKey);
        if (employeeCheckInfo == null) {
            logger.info("从中银保信平台查询------");
            employeeCheckInfo = queryZYBXByLicense(employee);
            if (employeeCheckInfo != null) {
                redisService.setWithDayExpire(infoKey, employeeCheckInfo, 365);
             }
            logger.info("中银保信查询结果:{}", JsonUtil.toJSON(employeeCheckInfo));
        }
        dealByQueryZYBXByLicense(employeeCheckInfo, employee, detail);
    }

    private String generateQueryInfoKeyByLicense(String employeeName, String licenseNo) {
        return employeeName + "_" + licenseNo + "_queryInfo";
    }

    private void dealByQueryZYBXByLicense(EmployeeCheckInfo target, CheckChannelEmployee employee, CheckResultDetail detail) {

        String resultCode = target.getResultCode() == null ? "500" : target.getResultCode();
        if (!"100".equals(resultCode)) {
            detail.setReturnCode(resultCode);
            if (!ObjectUtils.isEmpty(CheckResultCodeEnum.getEnumObjectByCode(resultCode))) {
                detail.setReturnMessage(CheckResultCodeEnum.getEnumObjectByCode(resultCode).getLabel());
            }
        } else {
            try {
                dealQuerySuccessByLicense(target, employee, detail);
            } catch (Exception e) {
                detail.setReturnCode("999999");
                detail.setReturnMessage("错误信息：" + e.getMessage());
                logger.error("查询中银保信成功后出现异常:" + e.getMessage());
            }
        }
    }

    private void dealQuerySuccessByLicense(EmployeeCheckInfo checkInfo, CheckChannelEmployee employee, CheckResultDetail detail) {
        logger.info("查询中银保信成功~");
        CheckInfoData data = checkInfo.getData();
        String ifExist = data.getIfExist();
        if (!"Y".equals(ifExist) || "y".equals(ifExist)) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_EMP.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_EMP.getLabel());
            return;
        }
        //执业证有效状态
        CheckInfoDetail target = null;
        for (CheckInfoDetail checkInfoDetail : data.getDetailList()) {
            if (employee.getLicenseNo().equals(checkInfoDetail.getDevelopCode())) {
                target = checkInfoDetail;
                break;
            }
        }
        //不存在有效的执行业
        if (target == null) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getLabel());
            return;
        }
        //设置执业证状态
        employee.setLicenseStatus(target.getDevelopState());
        //返回的执业证号是否与入参相同
        if (!target.getDevelopCode().equals(employee.getLicenseNo())) {
            detail.setReturnCode(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getCode());
            detail.setReturnMessage(CheckResultCodeEnum.DIS_MATCH_LICENCE_NO.getLabel());
            return;
        }
        //组织匹配
        dealOrgAndNameInfo(employee, null, data.getName(), detail);

        if (!ObjectUtils.isEmpty(detail.getEmployeeRequest())) {
            //设置有效起始日期
            detail.getEmployeeRequest().setRegisterTime(target.getRegisterTime());
            //设置入职机构code
            detail.getEmployeeRequest().setSuperOrgan(target.getSuperOrgan());
            //设置入职机构名称
            detail.getEmployeeRequest().setSuperOrganName(target.getSuperOrganName());
        }
    }

    private EmployeeCheckInfo queryZYBXByLicense(CheckChannelEmployee employee) {
        logger.info("未查询到白名，转而查询中银保信----");

        //中银保返回对象
        EmployeeCheckInfo target = new EmployeeCheckInfo();
        String resultCode = null;
        Map<String, String> paramMap = Maps.newHashMap();

        //查询银保信
        //请求地址：http：//ip:端口/api/{APP_KEY}/inspectors/1.0.0
//        String url = "http://**************:7825/api/000216/inspectors/1.0.0";
        String url = "http://" + ipAddress + ":" + port + "/api/" + appKey + "/" + service + "/" + version;
        paramMap.put("fullName", Sm4Util.encryptEcb(sm4Key, employee.getEmployeeName()));
        paramMap.put("licenseNo", employee.getLicenseNo());
        Date now = new Date();
        paramMap.put("informationDate", DateFormatUtils.format(now, "yyyy-MM-dd"));
        paramMap.put("userName", userName);
        paramMap.put("password", password);
        paramMap.put("serialNum", generateSerialNum(userName, now));
        logger.info("中银保信请求:{}, 参数:{}", url, JsonUtil.toJSON(paramMap));
        int i = 0;
        do {
            try {
                target = queryCheckInfo(url, paramMap);
                if (target != null) {
                    resultCode = target.getResultCode();
                    if ("99".equals(resultCode) || "500".equals(resultCode)) {
                        //开启3次重次操作
                        i++;
                    } else {
                        //直接结束该do while 循环。
                        i = 3;
                    }
                } else {
                    i++;
                }
            } catch (Exception e) {
                logger.error("dealWithOutWhite occur an error : " + e.getMessage());
                i++;
            }
        } while (i <= 2);

        logger.info("中银保信查结果:{}", JsonUtil.toJSON(target));
        if (target != null) {
            if (!"100".equals(resultCode)) {
                //保存异常日志信息
                saveCheckExceptionLog(JsonUtil.toJSON(paramMap), JsonUtil.toJSON(target));
            } else {
                //异步 检查校验信息
                saveOrUpdateCheckInfo(employee, target);
            }
        }
        return target;
    }


    @Override
    public void deleteRedisCacheByLicense(String employeeName, String licenseNo) {
        logger.info("删除缓存入参:idType={}, idNo={}", employeeName, licenseNo);
        if (StringUtils.isNotEmpty(employeeName) && StringUtils.isNotEmpty(licenseNo)) {
            redisService.removePattern(generatePatternKey(employeeName, licenseNo));
        }
    }
}
