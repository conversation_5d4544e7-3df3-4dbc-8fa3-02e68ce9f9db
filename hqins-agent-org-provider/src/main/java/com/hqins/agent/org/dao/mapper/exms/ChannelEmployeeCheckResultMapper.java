package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckResult;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 渠道代理人查询主表数据访问接口
 * <AUTHOR>
 * @since 2023-09-18
 */
@Mapper
@DS("exms")
@Repository
public interface ChannelEmployeeCheckResultMapper extends BaseMapper<ChannelEmployeeCheckResult> {

    Long insertSelective(ChannelEmployeeCheckResult channelEmployeeCheckResult);

    int updateByPrimaryKeySelective(ChannelEmployeeCheckResult channelEmployeeCheckResult);


}

