package com.hqins.agent.org.rpc.client;

import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @date 2022-09-07
 */
@Service
public class FileFeignClient {

    private final FileApi fileApi;

    public FileFeignClient(FileApi fileApi) {
        this.fileApi = fileApi;
    }

    public FileGetUrlsVO upload(String source, String filePath, String fileName, List<MultipartFile> files) {
        return fileApi.upload(source, filePath, fileName, files);
    }

}
