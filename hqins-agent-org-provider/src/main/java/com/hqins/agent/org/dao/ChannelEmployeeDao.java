package com.hqins.agent.org.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import one.util.streamex.StreamEx;

import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2021/5/20
 * @Description
 */
public interface ChannelEmployeeDao {

    /**
     * 根据id查询
     *
     * @param id 主键id
     * @return
     */
    ChannelEmployee getById(Long id);

    /**
     * 根据code 查询
     *
     * @param code
     * @return
     */
    ChannelEmployee getByCode(String code);

    ChannelEmployee getByMobile(String mobile);

    /**
     * 渠道员工查询  by licenseNo
     * @param licenseNo
     * @return
     */
    ChannelEmployee selectEmployeeByLicenseNo(String licenseNo);

    /**
     * 根据code集合查询
     *
     * @param codeSet
     * @return
     */
    Map<String, ChannelEmployee> selectEmployeeMapByCodes(Set<String> codeSet);

    /**
     * 根据licenseNoSet集合查询
     *
     * @param licenseNoSet
     * @return
     */
    Map<String, ChannelEmployee> selectEmployeeMapByLicenseNos(Set<String> licenseNoSet);

    /**
     * 根据idCodeSet集合查询
     *
     * @param idCodeSet
     * @return
     */
    Map<String, ChannelEmployee> selectEmployeeMapByIdCodes(Set<String> idCodeSet);


}
