package com.hqins.agent.org.web.filter;

import com.hqins.agent.org.cache.CacheLoader;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> <PERSON>
 * @since 2021/6/11
 */
@Slf4j
public class SystemInitFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain) throws ServletException, IOException {
        log.info("请求地址：{},请求方法：{}", request.getRequestURI(), request.getMethod());
        WebApplicationContext webApplicationContext = WebApplicationContextUtils.getWebApplicationContext(request.getServletContext());
        CacheLoader cacheLoader = webApplicationContext.getBean(CacheLoader.class);
        boolean initCacheCompleted = cacheLoader.isInitCacheCompleted();
        AssertUtil.isTrue(initCacheCompleted, new ApiException("缓存尚未初始化完成，请稍候..."));
        filterChain.doFilter(request, response);
    }

}
