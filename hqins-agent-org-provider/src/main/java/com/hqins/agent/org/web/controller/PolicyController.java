package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.ExpireQueryListRequest;
import com.hqins.agent.org.model.vo.PolicyExpireMapVO;
import com.hqins.agent.org.model.vo.PolicyExpireVO;
import com.hqins.agent.org.service.PolicyService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;


@RequestMapping("/policy")
@Api(tags = "保单控制器")
@RestController
@RefreshScope
public class PolicyController {

    @Autowired
    private PolicyService policyService;


    @ApiOperation("根据条件查询满期保单信息-自营分页")
    @PostMapping("/getPartnerExpire")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PolicyExpireVO>> getPartnerExpire(@RequestBody ExpireQueryListRequest request) {
        return ApiResult.ok(policyService.getPartnerExpire(request));
    }

    @ApiOperation("根据条件查询满期保单信息-渠道分页")
    @PostMapping("/getChannelExpire")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PolicyExpireMapVO>> getChannelExpire(@RequestBody ExpireQueryListRequest request) {
        return ApiResult.ok(policyService.getChannelExpire(request));
    }

    @ApiOperation("根据条件查询满期保单信息-保单号")
    @GetMapping("/getPolicyExpire")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PolicyExpireVO> getPolicyExpire(@RequestBody ExpireQueryListRequest request) {
        return ApiResult.ok(policyService.getPolicyExpire(request));
    }

}
