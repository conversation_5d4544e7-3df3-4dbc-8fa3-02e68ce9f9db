package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.request.PartnerTeamQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface PartnerTeamService {

    PageInfo<PartnerTeamTreeNodeVO> listMyTree(PartnerTeamQueryRequest queryRequest);

    PageInfo<SimpleTeamTreeNodeVO> listMyTreeSimple(PartnerTeamQueryRequest queryRequest);

    Set<String> selectAllChildCodes(String teamCode, String teamName, MyDataAccessVO myDataAccess);

    List<TeamVO> listManagePartnerTeams(String employeeCode);

    List<SaleTeamVO> listSaleTeam(String instCode, TeamLevel teamLevel);

    List<SaleTeamVO> listTopSaleTeam(String instCode);
}
