package com.hqins.agent.org.dao;


import com.hqins.agent.org.dao.entity.exms.Tbemp;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/5/20
 * @Description
 */
public interface TbempDao {

    /**
     * 根据工号查询
     *
     * @param codeSet
     * @return
     */
    Map<String, Tbemp> selectEmpMapByCodes(Set<String> codeSet);

    /**
     * 根据工号集合查询
     *
     * @param idCodeSet
     * @return
     */
    Map<String, Tbemp> selectEmpMapByIdCodes(Set<String> idCodeSet);

    /**
     * 根据工号集合查询
     *
     * @param licenseNoSet
     * @return
     */
    Map<String, Tbemp> selectEmpMapByLicenseNo(Set<String> licenseNoSet);

}



