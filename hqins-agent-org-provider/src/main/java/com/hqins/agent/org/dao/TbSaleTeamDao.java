package com.hqins.agent.org.dao;


import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.request.TeamHonorRequest;
import com.hqins.agent.org.model.vo.EmpHonorVO;
import com.hqins.agent.org.model.vo.EmployeeVO;

import java.util.Date;
import java.util.List;


public interface TbSaleTeamDao {



    List<EmployeeVO> getBirthDayEmpList(String saleTeamCode, Date startDate, Date endDate);


    EmployeeVO getEmployeeInfo(String employeeCode);

    Tbsaleteam getPerformanceSaleTeamByCode(String saleTeamCode);

    String getEmpTargetSaleteamCode(String empCode, String commissionMonth);

    Tbsaleteam getTeamSaleTeam(String saleTeamCode);
}



