package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.SupervisorPerformanceRequest;
import com.hqins.agent.org.model.vo.BasicLawInfoVO;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/3/18 9:06
 */
@Configuration
public interface SupervisorPerformanceBasicLawStrategy {

    List<BasicLawInfoVO> generateBasicLawInfo(SupervisorPerformanceRequest request);

}
