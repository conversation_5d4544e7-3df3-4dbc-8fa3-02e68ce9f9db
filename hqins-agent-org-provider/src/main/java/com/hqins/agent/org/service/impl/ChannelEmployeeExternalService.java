package com.hqins.agent.org.service.impl;

import cn.hutool.core.util.ReUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.marketing.api.CardApi;
import com.hqins.agent.marketing.model.request.card.SimpleCardRequest;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.request.ChannelEmployeeAddRequest;
import com.hqins.agent.org.model.request.ChannelEmployeeRequest;
import com.hqins.agent.org.model.vo.ChannelEmployeeCheckVo;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.ChannelTeamService;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ChannelEmployeeExternalService {

    private final DataAccessService dataAccessService;
    private final ChannelEmployeeMapper channelEmployeeMapper;
    private final CacheService cacheService;
    private final ChannelTeamService channelTeamService;
    private final TbempMapper tbempMapper;
    private final UmClient umClient;
    private final CardApi cardApi;

    public ChannelEmployeeExternalService(DataAccessService dataAccessService, ChannelEmployeeMapper channelEmployeeMapper, CacheService cacheService, ChannelTeamService channelTeamService, TbempMapper tbempMapper, UmClient umClient, CardApi cardApi) {
        this.dataAccessService = dataAccessService;
        this.channelEmployeeMapper = channelEmployeeMapper;
        this.cacheService = cacheService;
        this.channelTeamService = channelTeamService;
        this.tbempMapper = tbempMapper;
        this.umClient = umClient;
        this.cardApi = cardApi;
    }


    public Page<ChannelEmployee> selectPage(ChannelEmployeeRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();

        //按名称查出机构，以及其下面的子机构
        List<String> orgCodes = handleOrgCodes(queryRequest, myDataAccess);

        //按名称查出团队，以及其下面的子团队编码
        Set<String> teamCodes = handleTeamCodes(queryRequest, myDataAccess);

        //根据经过筛选后的合伙人编码，获取团队
        LambdaQueryWrapper<ChannelEmployee> w = settingLambdaQueryWrapper(queryRequest, orgCodes, teamCodes);

        return channelEmployeeMapper.selectPage(new Page<>(queryRequest.getCurrent(), queryRequest.getSize()), w);
    }

    public List<String> handleOrgCodes(ChannelEmployeeRequest queryRequest, MyDataAccessVO myDataAccess) {
        List<String> orgCodes = new ArrayList<>();
        if (!StringUtil.isBlank(queryRequest.getOrgName())) {
            //按名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(null, queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getChannelOrgCodes());

        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (CollectionUtils.isEmpty(orgCodes)) {
                orgCodes = new ArrayList(myDataAccess.getChannelOrgCodes());
            }
        }
        return orgCodes;
    }

    public Set<String> handleTeamCodes(ChannelEmployeeRequest queryRequest, MyDataAccessVO myDataAccess) {
        Set<String> teamCodes = new HashSet<>();
        if (!StringUtil.isBlank(queryRequest.getTeamName())) {
            teamCodes = channelTeamService.selectAllChildCodes(null, queryRequest.getTeamName(), myDataAccess);
        }
        return teamCodes;
    }

    public LambdaQueryWrapper<ChannelEmployee> settingLambdaQueryWrapper(ChannelEmployeeRequest queryRequest, List<String> orgCodes, Set<String> teamCodes) {
        LambdaQueryWrapper<ChannelEmployee> queryWrapper = new LambdaQueryWrapper<ChannelEmployee>().orderByDesc(ChannelEmployee::getStatus).orderByDesc(ChannelEmployee::getId);

        if (!CollectionUtils.isEmpty(orgCodes)) {
            queryWrapper.in(ChannelEmployee::getOrgCode, orgCodes);
        }
        if (!CollectionUtils.isEmpty(teamCodes)) {
            queryWrapper.in(ChannelEmployee::getTeamCode, teamCodes);
        }
        if (!StringUtil.isBlank(queryRequest.getCode())) {
            queryWrapper.eq(ChannelEmployee::getCode, queryRequest.getCode());
        }
        if (!StringUtil.isBlank(queryRequest.getIdCode())) {
            queryWrapper.eq(ChannelEmployee::getIdCode, queryRequest.getIdCode());
        }
        if (!StringUtil.isBlank(queryRequest.getName())) {
            queryWrapper.like(ChannelEmployee::getName, "%" + queryRequest.getName() + "%");
        }
        if (!StringUtils.isEmpty(queryRequest.getTeamCode())) {
            queryWrapper.eq(ChannelEmployee::getTeamCode, queryRequest.getTeamCode());
        }
        if (!StringUtils.isEmpty(queryRequest.getOrgCode())) {
            queryWrapper.eq(ChannelEmployee::getOrgCode, queryRequest.getOrgCode());
        }
        if (!StringUtils.isEmpty(queryRequest.getChannelCode())) {
            queryWrapper.eq(ChannelEmployee::getChannelCode, queryRequest.getChannelCode());
        }
        if (!StringUtil.isBlank(queryRequest.getChannelName())) {
            queryWrapper.like(ChannelEmployee::getChannelName, "%" + queryRequest.getChannelName() + "%");
        }
        if (!StringUtil.isBlank(queryRequest.getMobile())) {
            queryWrapper.eq(ChannelEmployee::getMobile, queryRequest.getMobile());
        }
        if (queryRequest.getEntryTimeStart() != null) {
            queryWrapper.ge(ChannelEmployee::getEntryTime, queryRequest.getEntryTimeStart());
        }
        if (queryRequest.getEntryTimeEnd() != null) {
            queryWrapper.le(ChannelEmployee::getEntryTime, queryRequest.getEntryTimeEnd());
        }
        if (queryRequest.getQuitTimeStart() != null) {
            queryWrapper.ge(ChannelEmployee::getQuitTime, queryRequest.getQuitTimeStart());
        }
        if (queryRequest.getQuitTimeEnd() != null) {
            queryWrapper.le(ChannelEmployee::getQuitTime, queryRequest.getQuitTimeEnd());
        }
        return queryWrapper;
    }

    /**
     * 参数校验
     *
     * @param request
     * @param team
     * @param tbepartner
     * @param myDataAccess
     * @param flag
     * @return
     */
    public ChannelEmployeeCheckVo check(ChannelEmployeeAddRequest request, ChannelTeam team, Tbepartner tbepartner, MyDataAccessVO myDataAccess, Boolean flag, ChannelEmployee o) {
        ChannelEmployeeCheckVo checkVo = BeanCopier.copyObject(request, ChannelEmployeeCheckVo.class);
        checkVo.setDescription("");
        checkVo.setSuccess(true);
        //1.查询入职的团队状态停用状态不允许入职
        if (tbepartner == null || team == null || !TeamStatus.ENABLED.name().equals(team.getStatus())) {
            checkVo.setSuccess(false);
            checkVo.setDescription("团队编码错误");
            return getChannelEmployeeCheckVo(request, checkVo);
        }
        //查询当前登录人是否有“入职的团队编码”的数据权限
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (!myDataAccess.getChannelOrgCodes().contains(team.getOrgCode())) {
                checkVo.setSuccess(false);
                checkVo.setDescription("没有该团队操作权限");
                return getChannelEmployeeCheckVo(request, checkVo);
            }
        }
        if (StringUtil.isBlank(request.getName()) || request.getName().length() > AppConsts.NUMBER_30) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "人员姓名不可为空且30个字以内；");
        }
        if (StringUtil.isBlank(request.getMobile()) || !cn.hutool.core.lang.Validator.isMobile(request.getMobile())) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "手机号格式错误；");
        }
        //无需必填，如果填了格式不能错误
        if (!StringUtil.isEmpty(request.getIdType()) && !IdType.isValid(request.getIdType())) {
            //如果证件类型不为空，需要校验是否为枚举值
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "证件类型，不在合理范围；");
        }
        if (request.getRoleType().equals(RoleType.FORMAL) &&
                !StringUtil.isEmpty(request.getLicenseNo()) &&
                !checkLicenseNo(request.getLicenseNo())) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "中介销售人员执业证号无效，请确认");
        }
        // 全局校验 证件号与 执业证书号 唯一性
        checkVo = globalCheck(checkVo, request, flag);
        if (request.getRoleType().equals(RoleType.FORMAL)
                && request.getLicenseStartDate() != null
                && request.getLicenseEndDate() != null
                && request.getLicenseEndDate().isBefore(request.getLicenseStartDate())) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "执业证号止期不能小于起期；");
        }
        if (request.getRoleType().equals(RoleType.FORMAL)
                && request.getLicenseEndDate() != null
                && !request.getLicenseEndDate().isAfter(LocalDateTime.now().toLocalDate())) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "执业证号止期不能晚于当前日期；");
        }
        if (!StringUtil.isEmpty(request.getIdCode()) && !ReUtil.isMatch("[a-zA-Z0-9]{1,18}", request.getIdCode())) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "证件号码规则为小于18位的数字字母组合；");
        }

        if (StringUtil.isNotEmpty(request.getJobNumber()) && request.getJobNumber().length() > 30) {
            checkVo.setSuccess(false);
            checkVo.setDescription(checkVo.getDescription() + "内部工号规则为30字符以内！");
        }

        /**
         * 当渠道商类型为：保险经纪或专业代理时，执业证号为必录，且执业证起期及执业证止期必录。
         * 当渠道商类型为兼业代理，执业证号非必录
         * 当渠道商类型为：保险经纪或专业代理时且渠道商编码不等于直销（C00265）时，证件类型、证件号码、出生日期、执业证书号、证书起期、证书止期为必录
         */
        if ((AppConsts.CHANNEL_TYPE_1.equals(tbepartner.getChannelType()) || AppConsts.CHANNEL_TYPE_4.equals(tbepartner.getChannelType())) && !AppConsts.CHANNEL_CODE.equals(tbepartner.getCompanycode())) {
            if (StringUtil.isBlank(request.getIdType())) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "证件类型不能为空；");
            }
            if (StringUtils.isNotBlank(request.getIdCode()) && !ReUtil.isMatch("[a-zA-Z0-9]{1,30}", request.getIdCode())) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "证件号码不能为空且规则为小于30位的数字字母组合；");
            }
            if (null == request.getBirthday()) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "出生日期不能为空；");
            }
            if (request.getRoleType().equals(RoleType.FORMAL)
                    && StringUtil.isBlank(request.getLicenseNo())
                    && !ReUtil.isMatch("[a-zA-Z0-9]{1,26}", request.getLicenseNo())) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "执业证号不能为空且规则为26位以内的数字字母组合；");
            }
            if (request.getRoleType().equals(RoleType.FORMAL)
                    && request.getLicenseStartDate() == null) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "执业证号起期不能为空；");
            }
            if (request.getRoleType().equals(RoleType.FORMAL)
                    && request.getLicenseEndDate() == null) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "执业证号止期不能为空；");
            }
            if (request.getRoleType().equals(RoleType.FORMAL)
                    && request.getLicenseStartDate() != null
                    && request.getLicenseEndDate() != null
                    && request.getLicenseEndDate().isBefore(request.getLicenseStartDate())) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "执业证号止期不能小于起期；");
            }
            if (request.getRoleType().equals(RoleType.FORMAL)
                    && request.getLicenseEndDate() != null
                    && !request.getLicenseEndDate().isAfter(LocalDateTime.now().toLocalDate())) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "执业证号止期不能晚于当前日期；");
            }
        }
        if (null != o) {
            return checkVo;
        }
        return getChannelEmployeeCheckVo(request, checkVo);
    }

    private ChannelEmployeeCheckVo getChannelEmployeeCheckVo(ChannelEmployeeAddRequest request, ChannelEmployeeCheckVo checkVo) {
        try {
            List<String> phoneList = new ArrayList<>();
            phoneList.add(request.getMobile());
            List<String> existPhones = umClient.checkPhoneList(phoneList);
            if (CollectionUtils.isNotEmpty(existPhones)) {
                checkVo.setSuccess(false);
                checkVo.setDescription(checkVo.getDescription() + "手机号码在系统中已存在；");
            }
            return checkVo;
        }catch (Exception e){
            checkVo.setSuccess(false);
            checkVo.setDescription("系统繁忙请十分钟后尝试!");
            return checkVo;
        }
    }

    /**
     * 全局校验 证件号与 执业证书号 唯一性
     *
     * @param request
     * @return
     */
    private ChannelEmployeeCheckVo globalCheck(ChannelEmployeeCheckVo checkVo, ChannelEmployeeAddRequest request, Boolean flag) {
        //证件号校验重复性校验
        checkIdCode(request.getIdCode(), request.getCode(), checkVo, flag);

        //执业证号校验重复性校验
        checkLicenseNo(request.getLicenseNo(), request.getCode(), checkVo, flag);

        return checkVo;
    }

    public ChannelEmployee generateEmployeeCode(String orgCode) {
        //渠道商编码( 多于6位则截取前6位) + C + 六位顺序号（全局）
        //如中兴经纪销售人员：C00013C000001
        ChannelEmployee ce = new ChannelEmployee();
        //时间戳占位
        ce.setCode(System.currentTimeMillis() + "");
        ce.setChannelCode("");
        ce.setChannelName("");
        ce.setOrgCode("");
        ce.setOrgName("");
        ce.setTeamCode("");
        ce.setTeamName("");
        ce.setName("");
        ce.setMobile("");
        ce.setUniversalQualification(false);
        ce.setGender(Gender.MALE.name());
        ce.setStatus(EmployeeStatus.LEAVING.name());
        ce.setSourceSystem(AppConsts.DEFAULT_SOURCE_SYSTEM);
        ce.setIsAuthorized(AppConsts.YES);
        ce.setRoleType(RoleType.FORMAL.name());
        log.info("添加进入的数据:{}", JsonUtil.toJSON(ce));
        channelEmployeeMapper.insert(ce);
        //orgCode取出前六位
        if (orgCode.length() > AppConsts.NUMBER_6) {
            orgCode = orgCode.substring(AppConsts.NUMBER_0, AppConsts.NUMBER_6);
        }
        String employeeCode = orgCode + "C" + String.format("%06d", ce.getId());
        ce.setCode(employeeCode);
        return ce;
    }

    /**
     * 校验身份证号
     *
     * @param idCode  身份证号
     * @param code    员工工号
     * @param checkVo
     */
    public void checkIdCode(String idCode, String code, ChannelEmployeeCheckVo checkVo, Boolean flag) {
        if (StringUtil.isNotEmpty(idCode)) {
            //查询合伙人中销售人员信息
            List<Tbemp> tbempList = tbempMapper.getByParams(idCode, null);
            //查询渠道商中销售人员信息
            List<ChannelEmployee> employeeList = channelEmployeeMapper.getByIdCodeOrLicenseNo(idCode, null);
            //修改时过滤掉自己
            if (!flag && StringUtils.isNotBlank(code)) {
                employeeList = employeeList.stream().filter(channelEmployee -> !channelEmployee.getCode().equals(code)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(tbempList) || CollectionUtils.isNotEmpty(employeeList)) {
                checkVo.setSuccess(false);
                checkVo.setDescription("同一证件号不能同时开多个账号，请确认");
            }
        }
    }


    /**
     * 校验执业证书号
     *
     * @param licenseNo
     * @param code
     * @param checkVo
     */
    public void checkLicenseNo(String licenseNo, String code, ChannelEmployeeCheckVo checkVo, Boolean flag) {
        if (StringUtil.isNotEmpty(licenseNo)) {
            //查询合伙人中销售人员信息
            List<Tbemp> tbempList = tbempMapper.getByParams(null, licenseNo);
            //查询渠道商中销售人员信息
            List<ChannelEmployee> employeeList = channelEmployeeMapper.getByIdCodeOrLicenseNo(null, licenseNo);
            //操作修改时过滤掉自己
            if (!flag && StringUtils.isNotBlank(code)) {
                employeeList = employeeList.stream().filter(channelEmployee -> !channelEmployee.getCode().equals(code)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(tbempList) || CollectionUtils.isNotEmpty(employeeList)) {
                checkVo.setSuccess(false);
                checkVo.setDescription("同一职业证号不能同时开多个账号，请确认");
            }
        }
    }

    /**
     * marketing服务中card中执业证书信息
     *
     * @param channelEmployee
     */
    public void syncLicenseNo(ChannelEmployee channelEmployee) {
        //同步更新marketing服务中card中执业证书信息
        //人员执业证书编号不为空时 同步更新marketing服务中card中执业证书信息
        SimpleCardRequest simpleCardRequest = new SimpleCardRequest();
        simpleCardRequest.setEmployeeCode(channelEmployee.getCode());
        simpleCardRequest.setLicenseNo(channelEmployee.getLicenseNo());
        cardApi.orgSetLicense(simpleCardRequest);
    }

    /**
     * 校验执业证书长度
     * 只能为数字且长度应为”17”、"18”、"19”、"23”、"25”、”26"位，否则阻断提示“中介销售人员执业证号无效，请确认”
     */
    public  boolean checkLicenseNo(String licenseNo){
        int length = licenseNo.length();
        if (length != 17 && length != 18 && length != 19 && length != 23 && length != 25 && length != 26) {
            return false;
        }
        return true;
    }

}
