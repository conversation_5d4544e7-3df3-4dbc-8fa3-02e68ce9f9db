package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.dao.mapper.hologres.DaDetailUwMapper;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.DaDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DaDetailServiceImpl implements DaDetailService {

    @Autowired
    private DaDetailUwMapper daDetailUwMapper;

    @Override
    public List<HoloDaDetailUwVo> selectDaDetailUwList(Map requestMap){
        return daDetailUwMapper.selectDaDetailUwList(requestMap);
    }

    @Override
    public PerformanceVO selectPerformanceAcc(List<String> instCodeList, String teamCode) {
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.withDayOfMonth(1);
        Map map = new HashMap();
        map.put("signStarDate",startDate);
        map.put("signEndDate",now);
        if (!CollectionUtils.isEmpty(instCodeList)){
            map.put("performanceGroupList",instCodeList);
        }else if (!StringUtils.isEmpty(teamCode)){
            map.put("teamCode",teamCode);
        }else{
            return new PerformanceVO();
        }
        return daDetailUwMapper.selectPerformanceByInstCode(map);
    }

    @Override
    public List<SupervisorPerformanceDetailNumberVO> selectPerformanceList(Map map) {
        return daDetailUwMapper.selectPerformanceList(map);
    }

    @Override
    public List<MarkDetailVO> getHologresSelfPolicyList(List<String> codeList, String teamCode, LocalDate startDate, LocalDate endDate) {
        Map map = new HashMap();
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        if (!CollectionUtils.isEmpty(codeList)){
            map.put("performanceGroupList",codeList);
        }else if (!StringUtils.isEmpty(teamCode)){
            map.put("teamCode",teamCode);
        }else{
            return new ArrayList<>();
        }
        return daDetailUwMapper.getHologresSelfPolicyList(map);
    }

    @Override
    public List<MarkDetailVO> getSupervisorCtList(List<String> instCodeList, String teamCode, LocalDate startDate, LocalDate endDate) {
        Map map = new HashMap();
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        if (!CollectionUtils.isEmpty(instCodeList)){
            map.put("performanceGroupList",instCodeList);
        }else if (!StringUtils.isEmpty(teamCode)){
            map.put("teamCode",teamCode);
        }else{
            return new ArrayList<>();
        }
        return daDetailUwMapper.getSupervisorCtList(map);
    }

    @Override
    public List<SupervisorPerformanceDetailNumberVO> selectAppNumList(Map map) {
        return daDetailUwMapper.selectAppNumList(map);
}


}
