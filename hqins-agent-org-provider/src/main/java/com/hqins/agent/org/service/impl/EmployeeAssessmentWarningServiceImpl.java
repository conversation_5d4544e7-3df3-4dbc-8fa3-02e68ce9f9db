package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.dao.EmployeeAssessmentDao;
import com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.EmployeeAssessmentWarningService;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class EmployeeAssessmentWarningServiceImpl implements EmployeeAssessmentWarningService {

    /**
     * 九月
     */
    private static final String SEPTEMBER = "09";

    /**
     * 三月
     */
    private static final String MARCH = "03";

    /**
     * 自营需要过滤的职级
     */
    private static final String IFP_RANK_CODE = "FWMC105";

    @Autowired
    private EmployeeAssessmentDao employeeAssessmentDao;

    /**
     * 团队详情接口
     * @param teamCode
     * @param paramType
     * @return
     */
    @Override
    public AssessmentTeamDateVO getAssessmentTableDataInfo(String teamCode, String paramType) {
        AssessmentTeamDateVO assessmentTeamDateVO = new AssessmentTeamDateVO();
        String checkMonth = getCheckMonth(CheckFrequencyEnum.QUATER_CHECK.getValue());
        String queryDate;
        List<CheckBatchPersonInfo> checkBatchPersonInfos;
        if(EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode().equals(paramType)){
            queryDate = checkMonth;
        }else {
            //根据当期考核月份获取上期考核月分
            queryDate = getPreviousCheckMonthByCurrentCheckMonth(checkMonth);
        }
        //获取团队所有人员
        checkBatchPersonInfos = getAllCheckPersonal(queryDate, teamCode, paramType);

        if (CollectionUtils.isNotEmpty(checkBatchPersonInfos)) {
            //区分银保/自营
            Boolean flag = checkPersonalByInstCode(checkBatchPersonInfos.get(0));

            //获取顶层团队信息
            Tbsaleteam tbSaleTeamInfo = employeeAssessmentDao.getTbSaleTeamInfo(teamCode);

            //获取表体内容、在职人力以及合格率
            assessmentTeamDateVO = getAssessmentTeamDateVO(checkBatchPersonInfos, flag, assessmentTeamDateVO,tbSaleTeamInfo);
            getAssessmentTeamDate(checkBatchPersonInfos,tbSaleTeamInfo,flag,assessmentTeamDateVO);

            //团队编码
            assessmentTeamDateVO.setSaleTeamCode(tbSaleTeamInfo.getSaleteamcode());
            //团队名称
            assessmentTeamDateVO.setSaleTeamName(tbSaleTeamInfo.getSaleteamname());
            //主管编码
            assessmentTeamDateVO.setEmpInCode(tbSaleTeamInfo.getEmpincode());
            //主管编码
            assessmentTeamDateVO.setDeptCurrentNum(checkBatchPersonInfos.size());
            //区部组的类型
            String saleTeamType = getSaleTeamType(tbSaleTeamInfo);
            assessmentTeamDateVO.setSaleTeamType(saleTeamType);

            //表头内容
            HashSet<AssessmentTableTitleVO> resultTableTile = new HashSet<>();
            resultTableTile = fillTableTile(assessmentTeamDateVO,resultTableTile,flag);
            HashSet<AssessmentTableTitleVO> oneTableTile = getOneTableTile(assessmentTeamDateVO.getTableValues(), resultTableTile,flag);
            if(CollectionUtils.isNotEmpty(oneTableTile)){
                resultTableTile.addAll(oneTableTile);
            }
            List<AssessmentTableTitleVO> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resultTableTile)) {
                result = sortResultTableTile(resultTableTile);
            }
            assessmentTeamDateVO.setTableTitles(result);
        }

        return assessmentTeamDateVO;
    }

    /**
     * @param resultTableTile
     * @return
     */
    private List<AssessmentTableTitleVO> sortResultTableTile(HashSet<AssessmentTableTitleVO> resultTableTile) {
        List<AssessmentTableTitleVO> result = new ArrayList<>();
        List<AssessmentTableTitleVO> deptCurrentPersonNumCollect = resultTableTile.stream().filter(t -> CheckItemEnum.dept_current_person_num.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(deptCurrentPersonNumCollect)){
            result.addAll(deptCurrentPersonNumCollect);
        }
        List<AssessmentTableTitleVO> agentPassRateCollect = resultTableTile.stream().filter(t -> CheckItemEnum.agent_pass_rate.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(agentPassRateCollect)){
            result.addAll(agentPassRateCollect);
        }
        List<AssessmentTableTitleVO> unqualifiedPersonnelCollect = resultTableTile.stream().filter(t -> "unqualifiedPersonnel".equals(t.getTitleCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unqualifiedPersonnelCollect)){
            result.addAll(unqualifiedPersonnelCollect);
        }
        List<AssessmentTableTitleVO> keepLevelCollect = resultTableTile.stream().filter(t -> CheckManageEnum.KEEP_LEVEL.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(keepLevelCollect)){
            result.addAll(keepLevelCollect);
        }
        List<AssessmentTableTitleVO> promotedLevelOneCollect = resultTableTile.stream().filter(t -> CheckManageEnum.PROMOTED_LEVEL1.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promotedLevelOneCollect)) {
            result.addAll(promotedLevelOneCollect);
        }
        List<AssessmentTableTitleVO> promotedLevelTwoCollect = resultTableTile.stream().filter(t -> CheckManageEnum.PROMOTED_LEVEL2.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promotedLevelTwoCollect)) {
            result.addAll(promotedLevelTwoCollect);
        }
        List<AssessmentTableTitleVO> notCheckCollect = resultTableTile.stream().filter(t -> CheckManageEnum.NOT_CHECK.getValue().equals(t.getTitleCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notCheckCollect)){
            result.addAll(notCheckCollect);
        }

        return result;
    }

    /**
     * 填充缺失的表头
     *
     * @param assessmentTeamDateVO
     * @param resultTableTile
     * @param flag
     */
    private HashSet<AssessmentTableTitleVO> fillTableTile(AssessmentTeamDateVO assessmentTeamDateVO, HashSet<AssessmentTableTitleVO> resultTableTile, Boolean flag) {
        List<AssessmentTeamDateVO> childrenList = assessmentTeamDateVO.getChildrenList();
        if(CollectionUtils.isNotEmpty(childrenList)){
            for (AssessmentTeamDateVO teamDateVO : childrenList) {
                List<AssessmentTableValueVO> tableValues = teamDateVO.getTableValues();
                resultTableTile = getOneTableTile(tableValues,resultTableTile, flag);
                fillTableTile(teamDateVO,resultTableTile, flag);
            }
        }
        return resultTableTile;
    }

    /**
     * 记录一条数据的表头信息
     *
     * @param tableValues
     * @param resultTableTile
     * @param flag
     * @return
     */
    private HashSet<AssessmentTableTitleVO> getOneTableTile(List<AssessmentTableValueVO> tableValues, HashSet<AssessmentTableTitleVO> resultTableTile, Boolean flag) {
        if(CollectionUtils.isNotEmpty(tableValues)){
            for (AssessmentTableValueVO tableValue : tableValues) {
                AssessmentTableTitleVO assessmentTableTitleVO = new AssessmentTableTitleVO();
                assessmentTableTitleVO.setTitleCode(tableValue.getTitleCode());
                if("unqualifiedPersonnel".equals(tableValue.getTitleCode())){
                    assessmentTableTitleVO.setCorrespondingValue("待达标/人");
                }
                if(CheckItemEnum.agent_pass_rate.getValue().equals(tableValue.getTitleCode()) || CheckItemEnum.dept_current_person_num.getValue().equals(tableValue.getTitleCode())) {
                    assessmentTableTitleVO.setCorrespondingValue(CheckItemEnum.getLabelByValue(tableValue.getTitleCode()) + "/%");
                }
                if(CheckItemEnum.dept_current_person_num.getValue().equals(tableValue.getTitleCode())) {
                    assessmentTableTitleVO.setCorrespondingValue(CheckItemEnum.getLabelByValue(tableValue.getTitleCode()) + "/人");
                }
                if(flag){
                    if(CheckManageEnum.KEEP_LEVEL.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageEnum.KEEP_LEVEL.getLabel() + "/人");
                    }
                    if(CheckManageEnum.PROMOTED_LEVEL1.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageEnum.PROMOTED_LEVEL1.getLabel() + "/人");
                    }
                    if(CheckManageEnum.PROMOTED_LEVEL2.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageEnum.PROMOTED_LEVEL2.getLabel() + "/人");
                    }
                    if(CheckManageEnum.NOT_CHECK.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageEnum.NOT_CHECK.getLabel() + "/人");
                    }
                }else {
                    if(CheckManageIFPEnum.KEEP_LEVEL.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageIFPEnum.KEEP_LEVEL.getLabel() + "/人");
                    }
                    if(CheckManageIFPEnum.PROMOTED_LEVEL1.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel() + "/人");
                    }
                    if(CheckManageIFPEnum.NOT_CHECK.getValue().equals(tableValue.getTitleCode())){
                        assessmentTableTitleVO.setCorrespondingValue(CheckManageIFPEnum.NOT_CHECK.getLabel() + "/人");
                    }
                }
                resultTableTile.add(assessmentTableTitleVO);
            }
        }
        return resultTableTile;
    }

    /**
     * 获取团队级别类型
     * @param tbSaleTeamInfo
     * @return
     */
    private String getSaleTeamType(Tbsaleteam tbSaleTeamInfo) {
        String teamLevel = tbSaleTeamInfo.getTeamlevel();
        String saleTeamType;
        if("03".equals(teamLevel)){
            saleTeamType = "区";
        } else if ("02".equals(teamLevel)) {
            saleTeamType = "部";
        }else {
            saleTeamType = "组";
        }
        return saleTeamType;
    }

    /**
     * 获取团队下级机构的信息
     * @param checkBatchPersonInfos
     * @param flag
     * @param assessmentTeamDateVO
     * @param tbSaleTeamInfo
     * @return
     */
    private AssessmentTeamDateVO getAssessmentTeamDateVO(List<CheckBatchPersonInfo> checkBatchPersonInfos, Boolean flag, AssessmentTeamDateVO assessmentTeamDateVO, Tbsaleteam tbSaleTeamInfo) {
        List<Tbsaleteam> childrenTbSaleTeams = employeeAssessmentDao.getChildrenTbSaleTeams(tbSaleTeamInfo.getSaleteamcode());
        if(CollectionUtils.isNotEmpty(childrenTbSaleTeams)){
            List<AssessmentTeamDateVO> childrenTeamDate = new ArrayList<>();
            for (Tbsaleteam childrenTbSaleTeam : childrenTbSaleTeams) {
                AssessmentTeamDateVO tmp = new AssessmentTeamDateVO();
                //递归
                tmp = getAssessmentTeamDateVO(checkBatchPersonInfos, flag, tmp, childrenTbSaleTeam);
                tmp = saveTableValues(childrenTbSaleTeam, checkBatchPersonInfos, flag, tmp);
                if(!Objects.isNull(tmp)){
                    childrenTeamDate.add(tmp);
                }
            }
            assessmentTeamDateVO.setChildrenList(childrenTeamDate);
        }
        return assessmentTeamDateVO;
    }

    /**
     * 获取团队的信息
     * @param tbSaleTeamInfo
     * @param checkBatchPersonInfos
     * @param flag
     * @param assessmentTeamDateVO
     */
    private AssessmentTeamDateVO saveTableValues(Tbsaleteam tbSaleTeamInfo, List<CheckBatchPersonInfo> checkBatchPersonInfos, Boolean flag, AssessmentTeamDateVO assessmentTeamDateVO) {
        //存储该团队下的人
        List<CheckBatchPersonInfo> batchPersonInfos = new ArrayList<>();
        String saleTeamInCode = tbSaleTeamInfo.getSaleteamincode();
        for (CheckBatchPersonInfo checkBatchPersonInfo : checkBatchPersonInfos) {
            String team1 = checkBatchPersonInfo.getTeam1();
            String team2 = checkBatchPersonInfo.getTeam2();
            String team3 = checkBatchPersonInfo.getTeam3();
            if((StringUtil.isNotEmpty(team1) && team1.equals(saleTeamInCode)) || (StringUtil.isNotEmpty(team2) && team2.equals(saleTeamInCode))
                    || (StringUtil.isNotEmpty(team3) && team3.equals(saleTeamInCode))){
                batchPersonInfos.add(checkBatchPersonInfo);
            }
        }
        if(CollectionUtils.isEmpty(batchPersonInfos)){
            return null;
        }
        String saleTeamType = getSaleTeamType(tbSaleTeamInfo);
        assessmentTeamDateVO.setSaleTeamCode(tbSaleTeamInfo.getSaleteamcode());
        assessmentTeamDateVO.setSaleTeamName(tbSaleTeamInfo.getSaleteamname());
        assessmentTeamDateVO.setSaleTeamType(saleTeamType);
        assessmentTeamDateVO.setEmpInCode(tbSaleTeamInfo.getEmpincode());
        assessmentTeamDateVO.setDeptCurrentNum(batchPersonInfos.size());
        getAssessmentTeamDate(batchPersonInfos,tbSaleTeamInfo,flag,assessmentTeamDateVO);
        return assessmentTeamDateVO;
    }

    /**
     * 获取表体内容、在职人力以及合格率
     * @param batchPersonInfos
     * @param tbSaleTeam
     * @param flag
     * @param assessmentTeamDateVO
     * @return
     */
    private void getAssessmentTeamDate(List<CheckBatchPersonInfo> batchPersonInfos, Tbsaleteam tbSaleTeam, Boolean flag, AssessmentTeamDateVO assessmentTeamDateVO) {
        //表体值
        List<AssessmentTableValueVO> tableValues = new ArrayList<>();

        //待达标数据
        List<AssessmentEmployeeInfoVO> tableValuePersonInfos = new ArrayList<>();
        //维持数据
        List<AssessmentEmployeeInfoVO> keepPersonnelList = new ArrayList<>();
        //晋升一级 || 晋升 数据
        List<AssessmentEmployeeInfoVO> promtedOnePersonnelList = new ArrayList<>();
        //晋升二级数据
        List<AssessmentEmployeeInfoVO> promtedTwoPersonnelList = new ArrayList<>();
        //不考核
        List<AssessmentEmployeeInfoVO> notCheckPersonnelList = new ArrayList<>();

        for (CheckBatchPersonInfo batchPersonInfo : batchPersonInfos) {
            String checkSettleResult = batchPersonInfo.getCheckSettleResult();
            AssessmentEmployeeInfoVO assessmentEmployeeInfoVO = new AssessmentEmployeeInfoVO();
            if (checkSettleResult.equals(String.valueOf(CheckManageEnum.PROMOTED_LEVEL2.getLevel()))) {
                assessmentEmployeeInfoVO.setName(batchPersonInfo.agentName);
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.agentCode);
                assessmentEmployeeInfoVO.setOrgCode(tbSaleTeam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbSaleTeam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbSaleTeam.getCptype());
                promtedTwoPersonnelList.add(assessmentEmployeeInfoVO);
            }
            if (checkSettleResult.equals(String.valueOf(CheckManageEnum.PROMOTED_LEVEL1.getLevel()))) {
                assessmentEmployeeInfoVO.setName(batchPersonInfo.agentName);
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.agentCode);
                assessmentEmployeeInfoVO.setOrgCode(tbSaleTeam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbSaleTeam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbSaleTeam.getCptype());
                promtedOnePersonnelList.add(assessmentEmployeeInfoVO);
            }
            if (checkSettleResult.equals(String.valueOf(CheckManageEnum.KEEP_LEVEL.getLevel()))) {
                assessmentEmployeeInfoVO.setName(batchPersonInfo.agentName);
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.agentCode);
                assessmentEmployeeInfoVO.setOrgCode(tbSaleTeam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbSaleTeam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbSaleTeam.getCptype());
                keepPersonnelList.add(assessmentEmployeeInfoVO);
            }
            if(checkSettleResult.equals(String.valueOf(CheckManageEnum.QUIT.getLevel())) || checkSettleResult.equals(String.valueOf(CheckManageEnum.REDUCE_LEVEL2.getLevel()))
                    || checkSettleResult.equals(String.valueOf(CheckManageEnum.REDUCE_LEVEL1.getLevel())) ){
                assessmentEmployeeInfoVO.setName(batchPersonInfo.agentName);
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.agentCode);
                assessmentEmployeeInfoVO.setOrgCode(tbSaleTeam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbSaleTeam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbSaleTeam.getCptype());
                tableValuePersonInfos.add(assessmentEmployeeInfoVO);
            }
            if(checkSettleResult.equals(String.valueOf(CheckManageEnum.NOT_CHECK.getLevel()))){
                assessmentEmployeeInfoVO.setName(batchPersonInfo.agentName);
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.agentCode);
                assessmentEmployeeInfoVO.setOrgCode(tbSaleTeam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbSaleTeam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbSaleTeam.getCptype());
                notCheckPersonnelList.add(assessmentEmployeeInfoVO);
            }
        }

        if (CollectionUtils.isNotEmpty(keepPersonnelList)) {
            //表体内容
            AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
            tableValueVO.setTitleCode(CheckManageEnum.KEEP_LEVEL.getValue());
            tableValueVO.setCorrespondingValue(String.valueOf(keepPersonnelList.size()));
            tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
            tableValueVO.setJumpData(keepPersonnelList);
            tableValues.add(tableValueVO);
        }

        if (CollectionUtils.isNotEmpty(notCheckPersonnelList)) {
            //表体内容
            AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
            tableValueVO.setTitleCode(CheckManageEnum.NOT_CHECK.getValue());
            tableValueVO.setCorrespondingValue(String.valueOf(notCheckPersonnelList.size()));
            tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
            tableValueVO.setJumpData(notCheckPersonnelList);
            tableValues.add(tableValueVO);
        }

        if (CollectionUtils.isNotEmpty(tableValuePersonInfos)) {
            //表体内容
            AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
            tableValueVO.setTitleCode("unqualifiedPersonnel");
            tableValueVO.setCorrespondingValue(String.valueOf(tableValuePersonInfos.size()));
            tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
            tableValueVO.setJumpData(tableValuePersonInfos);
            tableValues.add(tableValueVO);
        }

        if (flag) {
            if (CollectionUtils.isNotEmpty(promtedOnePersonnelList)) {
                //表体内容
                AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
                tableValueVO.setTitleCode(CheckManageEnum.PROMOTED_LEVEL1.getValue());
                tableValueVO.setCorrespondingValue(String.valueOf(promtedOnePersonnelList.size()));
                tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
                tableValueVO.setJumpData(promtedOnePersonnelList);
                tableValues.add(tableValueVO);
            }

            if (CollectionUtils.isNotEmpty(promtedTwoPersonnelList)) {
                //表体内容
                AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
                tableValueVO.setTitleCode(CheckManageEnum.PROMOTED_LEVEL2.getValue());
                tableValueVO.setCorrespondingValue(String.valueOf(promtedTwoPersonnelList.size()));
                tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
                tableValueVO.setJumpData(promtedTwoPersonnelList);
                tableValues.add(tableValueVO);
            }
        }else {
            if (CollectionUtils.isNotEmpty(promtedOnePersonnelList)) {
                //表体内容
                AssessmentTableValueVO tableValueVO = new AssessmentTableValueVO();
                tableValueVO.setTitleCode(CheckManageIFPEnum.PROMOTED_LEVEL1.getValue());
                tableValueVO.setCorrespondingValue(String.valueOf(promtedOnePersonnelList.size()));
                tableValueVO.setJumpType(CheckJumpTypeEnum.NAME_POP_UP.getCode());
                tableValueVO.setJumpData(promtedOnePersonnelList);
                tableValues.add(tableValueVO);
            }
        }

        //分子 (在职人力数 - 待达标 - 不考核)
        BigDecimal molecule = new BigDecimal(batchPersonInfos.size()).subtract(new BigDecimal(tableValuePersonInfos.size())).subtract(new BigDecimal(notCheckPersonnelList.size()));
        //分母
        BigDecimal denominator;
        if(CollectionUtils.isNotEmpty(notCheckPersonnelList)){
            denominator = new BigDecimal(batchPersonInfos.size()).subtract(new BigDecimal(notCheckPersonnelList.size()));
        }else {
            denominator = new BigDecimal(batchPersonInfos.size());
        }
        BigDecimal qualificationRate = BigDecimal.ZERO;
        if(molecule.compareTo(BigDecimal.ZERO) > 0 && denominator.compareTo(BigDecimal.ZERO) > 0){
            //合格率
            qualificationRate = molecule.divide(denominator,2,RoundingMode.HALF_UP);
        }
        //表体内容
        AssessmentTableValueVO qualificationRateTableValueVO = new AssessmentTableValueVO();
        qualificationRateTableValueVO.setTitleCode(CheckItemEnum.agent_pass_rate.getValue());
        if(qualificationRate.compareTo(BigDecimal.ZERO) > 0){
            qualificationRate = qualificationRate.multiply(new BigDecimal(100)).setScale(0);
            qualificationRateTableValueVO.setCorrespondingValue(qualificationRate.toString());
        }else {
            qualificationRateTableValueVO.setCorrespondingValue(BigDecimal.ZERO.toString());
        }
        tableValues.add(qualificationRateTableValueVO);

        //在职人力
        //表体内容
        AssessmentTableValueVO deptCurrentPersonTableValueVO = new AssessmentTableValueVO();
        deptCurrentPersonTableValueVO.setTitleCode(CheckItemEnum.dept_current_person_num.getValue());
        deptCurrentPersonTableValueVO.setCorrespondingValue(String.valueOf(batchPersonInfos.size()));
        tableValues.add(deptCurrentPersonTableValueVO);

        //表体内容
        assessmentTeamDateVO.setTableValues(tableValues);
    }

    /**
     * 饼图
     * @param teamCode
     * @param paramType
     * @return
     */
    @Override
    public AssessmentPerformanceDataVO getAssessmentPerformanceDataInfo(String teamCode, String paramType) {
        AssessmentPerformanceDataVO assessmentPerformanceDataVO = null;
        String checkMonth = getCheckMonth(CheckFrequencyEnum.QUATER_CHECK.getValue());
        if(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode().equals(paramType)){
            checkMonth = getPreviousCheckMonthByCurrentCheckMonth(checkMonth);
        }

        //团队员工
        List<CheckBatchPersonInfo> checkBatchPersonInfos = getAllCheckPersonal(checkMonth, teamCode, paramType);

        if(CollectionUtils.isEmpty(checkBatchPersonInfos)){
            return null;
        }

        //过去考核人信息用与区分自营或银保
        CheckBatchPersonInfo checkBatchPersonInfo = checkBatchPersonInfos.get(0);

        //初始化结果对象
        assessmentPerformanceDataVO = new AssessmentPerformanceDataVO();

        //实际在职人力
        assessmentPerformanceDataVO.setActiveWorkforce(Double.valueOf(String.valueOf(checkBatchPersonInfos.size())));

        //获取饼图信息以及合格率
        assessmentPerformanceDataVO = getPieChartData(checkBatchPersonInfos, checkBatchPersonInfo, assessmentPerformanceDataVO);


        //待达标
        List<AssessmentPieChartDataVO> unqualifiedPersonnel = assessmentPerformanceDataVO.getPieChartData().stream().filter(t -> "待达标".equals(t.getName())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(unqualifiedPersonnel)){
            assessmentPerformanceDataVO.setUnqualifiedPersonnel(unqualifiedPersonnel.get(0).getValue());
        }else {
            assessmentPerformanceDataVO.setUnqualifiedPersonnel(0);
        }


        return assessmentPerformanceDataVO;
    }

    /**
     * 获取当期或上期的考核人员
     * @param checkMonth
     * @param teamCode
     * @param paramType
     * @return
     */
    private List<CheckBatchPersonInfo> getAllCheckPersonal(String checkMonth, String teamCode, String paramType) {
        List<CheckBatchPersonInfo> resultList = new ArrayList<>();
        if(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode().equals(paramType)){
            //上期 月份为 03 或 09 需要再往上找一个考核期
            if(isMonth(checkMonth)){
                List<CheckBatchPersonInfo> currentList = employeeAssessmentDao.getAllAssessmentPersonal(checkMonth, teamCode);
                String previousPeriod = getPreviousCheckMonthByCurrentCheckMonth(checkMonth);
                List<CheckBatchPersonInfo> previousPeriodList = employeeAssessmentDao.getAllAssessmentPersonal(previousPeriod, teamCode);
                if(CollectionUtils.isNotEmpty(currentList) && CollectionUtils.isNotEmpty(previousPeriodList)){
                    List<CheckBatchPersonInfo> collect = previousPeriodList.
                            stream().
                            filter(t -> CheckManageEnum.AgentTypeEnum.SALES_MANAGER.getRankSeq().equals(t.getRankSeqCode()) || CheckManageEnum.AgentTypeEnum.BUSINESS_DISTRICT_DIRECTOR.getRankSeq().equals(t.getRankSeqCode()) || IFP_RANK_CODE.equals(t.getRankCode()))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect)){
                        for (CheckBatchPersonInfo checkBatchPersonInfo : collect) {
                            List<CheckBatchPersonInfo> requiredRemoveList = currentList.stream().filter(t -> checkBatchPersonInfo.getAgentCode().equals(t.getAgentCode())).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(requiredRemoveList)){
                                collect.removeAll(requiredRemoveList);
                            }
                        }
                    }
                    resultList.addAll(currentList);
                    resultList.addAll(collect);
                }else if(CollectionUtils.isNotEmpty(currentList)){
                    resultList.addAll(currentList);
                }else if(CollectionUtils.isNotEmpty(currentList)){
                    resultList.addAll(previousPeriodList);
                }
            }else {
                resultList = employeeAssessmentDao.getAllAssessmentPersonal(checkMonth, teamCode);
            }
        }else {
            //当期 月份为 03 或 09 需要再往下找一个考核期
            if(isMonth(checkMonth)){
                List<CheckBatchPersonInfo> currentList = employeeAssessmentDao.getAllAssessmentPersonal(checkMonth, teamCode);
                String nextPeriod = getNextCheckMonthByCurrentCheckMonth(checkMonth);
                List<CheckBatchPersonInfo> nextPeriodList = employeeAssessmentDao.getAllAssessmentPersonal(nextPeriod, teamCode);
                if(CollectionUtils.isNotEmpty(currentList) && CollectionUtils.isNotEmpty(nextPeriodList)){
                    List<CheckBatchPersonInfo> collect = nextPeriodList
                            .stream()
                            .filter(t -> CheckManageEnum.AgentTypeEnum.SALES_MANAGER.getRankSeq().equals(t.getRankSeqCode()) || CheckManageEnum.AgentTypeEnum.BUSINESS_DISTRICT_DIRECTOR.getRankSeq().equals(t.getRankSeqCode()) || IFP_RANK_CODE.equals(t.getRankCode()))
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect)){
                        for (CheckBatchPersonInfo checkBatchPersonInfo : collect) {
                            List<CheckBatchPersonInfo> requiredRemoveList = currentList.stream().filter(t -> checkBatchPersonInfo.getAgentCode().equals(t.getAgentCode())).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(requiredRemoveList)){
                                collect.removeAll(requiredRemoveList);
                            }
                        }
                    }
                    resultList.addAll(currentList);
                    resultList.addAll(collect);
                }else if(CollectionUtils.isNotEmpty(currentList)){
                    resultList.addAll(currentList);
                }else if(CollectionUtils.isNotEmpty(nextPeriodList)){
                    resultList.addAll(nextPeriodList);
                }
            }else {
                resultList = employeeAssessmentDao.getAllAssessmentPersonal(checkMonth, teamCode);
            }
        }
        return resultList;
    }

    /**
     * 获取饼图信息以及合格率
     *
     * @param checkBatchPersonInfos
     * @param checkBatchPersonInfo
     * @param assessmentPerformanceDataVO
     * @return
     */
    private AssessmentPerformanceDataVO getPieChartData(List<CheckBatchPersonInfo> checkBatchPersonInfos, CheckBatchPersonInfo checkBatchPersonInfo, AssessmentPerformanceDataVO assessmentPerformanceDataVO) {
        List<AssessmentPieChartDataVO> pieChartData = new ArrayList<>();

        //在职人力数量
        int activeWorkforce = checkBatchPersonInfos.size();

        //维持
        List<CheckBatchPersonInfo> keepLevelList =
                checkBatchPersonInfos.stream().filter(t -> t.getCheckSettleResult().equals(String.valueOf(CheckManageEnum.KEEP_LEVEL.getLevel()))).collect(Collectors.toList());
        //晋升一级 || 晋升
        List<CheckBatchPersonInfo> promotedOneLevelList =
                checkBatchPersonInfos.stream().filter(t -> t.getCheckSettleResult().equals(String.valueOf(CheckManageEnum.PROMOTED_LEVEL1.getLevel()))).collect(Collectors.toList());
        //晋升二级
        List<CheckBatchPersonInfo> promotedTwoLevelList =
                checkBatchPersonInfos.stream().filter(t -> t.getCheckSettleResult().equals(String.valueOf(CheckManageEnum.PROMOTED_LEVEL2.getLevel()))).collect(Collectors.toList());
        //不考核
        List<CheckBatchPersonInfo> notCheckPersonnelList =
                checkBatchPersonInfos.stream().filter(t -> t.getCheckSettleResult().equals(String.valueOf(CheckManageEnum.NOT_CHECK.getLevel()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keepLevelList)) {
            checkBatchPersonInfos.removeAll(keepLevelList);
        }
        if (CollectionUtils.isNotEmpty(promotedOneLevelList)) {
            checkBatchPersonInfos.removeAll(promotedOneLevelList);
        }
        if (CollectionUtils.isNotEmpty(promotedTwoLevelList)) {
            checkBatchPersonInfos.removeAll(promotedTwoLevelList);
        }
        if(CollectionUtils.isNotEmpty(notCheckPersonnelList)){
            checkBatchPersonInfos.removeAll(notCheckPersonnelList);
        }
        //待达标
        List<CheckBatchPersonInfo> unqualifiedPersonnelList = checkBatchPersonInfos;

        Boolean flag = checkPersonalByInstCode(checkBatchPersonInfo);
        AssessmentPieChartDataVO keepPieChartDataVO = new AssessmentPieChartDataVO();
        keepPieChartDataVO.setName(CheckManageEnum.KEEP_LEVEL.getLabel());
        keepPieChartDataVO.setValue(keepLevelList.size());

        AssessmentPieChartDataVO unqualifiedChartDataVO = new AssessmentPieChartDataVO();
        unqualifiedChartDataVO.setName("待达标");
        unqualifiedChartDataVO.setValue(unqualifiedPersonnelList.size());

        AssessmentPieChartDataVO notCheckChartDataVO = new AssessmentPieChartDataVO();
        notCheckChartDataVO.setName(CheckManageEnum.NOT_CHECK.getLabel());
        notCheckChartDataVO.setValue(notCheckPersonnelList.size());

        AssessmentPieChartDataVO promotedOnePieChartDataVO = new AssessmentPieChartDataVO();
        if(flag){
            promotedOnePieChartDataVO.setName(CheckManageEnum.PROMOTED_LEVEL1.getLabel());
            promotedOnePieChartDataVO.setValue(promotedOneLevelList.size());
            pieChartData.add(promotedOnePieChartDataVO);

            AssessmentPieChartDataVO promotedTwoPieChartDataVO = new AssessmentPieChartDataVO();
            promotedTwoPieChartDataVO.setName(CheckManageEnum.PROMOTED_LEVEL2.getLabel());
            promotedTwoPieChartDataVO.setValue(promotedTwoLevelList.size());
            pieChartData.add(promotedTwoPieChartDataVO);
        }else {
            promotedOnePieChartDataVO.setName(CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel());
            promotedOnePieChartDataVO.setValue(promotedOneLevelList.size());
            pieChartData.add(promotedOnePieChartDataVO);
        }
        pieChartData.add(keepPieChartDataVO);
        pieChartData.add(unqualifiedChartDataVO);
        pieChartData.add(notCheckChartDataVO);

        //饼图信息
        assessmentPerformanceDataVO.setPieChartData(pieChartData);
        //合格率
        BigDecimal qualificationRate = getQualificationRate(activeWorkforce, unqualifiedPersonnelList.size(), notCheckPersonnelList.size());
        assessmentPerformanceDataVO.setQualificationRate(qualificationRate.doubleValue());

        return assessmentPerformanceDataVO;
    }

    /**
     * 合格率
     * @param totalCheckPerson
     * @param unqualifiedCheckPerson
     * @param notCheckCheckPerson
     * @return
     */
    private BigDecimal getQualificationRate(int totalCheckPerson, int unqualifiedCheckPerson, int notCheckCheckPerson) {
        BigDecimal qualificationRate = BigDecimal.ZERO;
        //分子 (在职人力数 - 待达标 - 不考核)
        BigDecimal molecule = new BigDecimal(totalCheckPerson).subtract(new BigDecimal(unqualifiedCheckPerson)).subtract(new BigDecimal(notCheckCheckPerson));
        //分母 (在职人力数 - 不考核)
        BigDecimal denominator;
        if(notCheckCheckPerson > 0){
            denominator = new BigDecimal(totalCheckPerson).subtract(new BigDecimal(notCheckCheckPerson));
        }else {
            denominator = new BigDecimal(totalCheckPerson);
        }
        if(molecule.compareTo(BigDecimal.ZERO) > 0 && denominator.compareTo(BigDecimal.ZERO) >0){
            qualificationRate = molecule.divide(denominator,2,RoundingMode.HALF_UP);
        }
        if(qualificationRate.compareTo(BigDecimal.ZERO) > 0){
            qualificationRate = qualificationRate.multiply(new BigDecimal(100)).setScale(0);
        }else {
            qualificationRate = BigDecimal.ZERO;
        }
        return qualificationRate;
    }

    /**
     * 下辖人员清单弹窗接口
     *
     * @param employeeCode
     * @param teamCode
     * @param paramType
     * @param code
     * @return
     */
    @Override
    public AssessmentSubordinatePersonnelVO getAssessmentSubordinatePersonnelInfo(String employeeCode, String teamCode, String paramType, String code) {
        AssessmentSubordinatePersonnelVO assessmentSubordinatePersonnelVO = new AssessmentSubordinatePersonnelVO();
        //团队人员
        List<AssessmentEmployeeInfoVO> assessmentEmployeeInfoVOS = new ArrayList<>();
        //获取主管的信息
        CheckBatchPersonInfo checkBatchPersonInfo = null;
        //获取考核周期
        String checkFrequency;
        if (StringUtil.isNotEmpty(employeeCode)) {
            checkFrequency = employeeAssessmentDao.getCheckFrequency(employeeCode);
            if(StringUtil.isEmpty(checkFrequency)){
                return assessmentSubordinatePersonnelVO;
            }
            //考核日期
            String checkMonth  = getCheckMonth(checkFrequency);
            if(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode().equals(paramType)){
                checkMonth = getPreviousCheckMonth(null,checkMonth, checkFrequency);
            }
            checkBatchPersonInfo = employeeAssessmentDao.getCheckBatchPersonInfo(checkMonth, employeeCode);
            //获取所有下级员工
            String checkBatchId = employeeAssessmentDao.getCheckBatchId(checkMonth,employeeCode);
            assessmentEmployeeInfoVOS =
                    employeeAssessmentDao.getAllAssessmentEmployeeInfo(checkBatchPersonInfo.getAgentCode(),checkBatchId);
        }else {
            String checkMonth = getCheckMonth(CheckFrequencyEnum.QUATER_CHECK.getValue());
            if(EmployeeAssessmentParamEnum.PREVIOUS_PERIOD.getCode().equals(paramType)){
                checkMonth = getPreviousCheckMonthByCurrentCheckMonth(checkMonth);
            }
            //获取团队信息
            Tbsaleteam tbsaleteam = employeeAssessmentDao.getTbSaleTeamInfo(teamCode);
            List<CheckBatchPersonInfo> allAssessmentPersonal = getAllCheckPersonal(checkMonth,teamCode,paramType);
            for (CheckBatchPersonInfo batchPersonInfo : allAssessmentPersonal) {
                AssessmentEmployeeInfoVO assessmentEmployeeInfoVO = new AssessmentEmployeeInfoVO();
                assessmentEmployeeInfoVO.setName(batchPersonInfo.getAgentName());
                assessmentEmployeeInfoVO.setEmployeeId(batchPersonInfo.getAgentCode());
                assessmentEmployeeInfoVO.setFinalResult(batchPersonInfo.getCheckSettleResult());
                assessmentEmployeeInfoVO.setRankSeqCode(batchPersonInfo.getRankSeqCode());
                assessmentEmployeeInfoVO.setOrgCode(tbsaleteam.getSaleteamincode());
                assessmentEmployeeInfoVO.setOrgName(tbsaleteam.getSaleteamname());
                assessmentEmployeeInfoVO.setOrgType(tbsaleteam.getCptype());
                assessmentEmployeeInfoVOS.add(assessmentEmployeeInfoVO);
            }
        }

        if(CollectionUtils.isEmpty(assessmentEmployeeInfoVOS)){
            return assessmentSubordinatePersonnelVO;
        }

        if(CheckItemEnum.dept_current_person_num.getValue().equals(code)){
            assessmentSubordinatePersonnelVO.setAllPersonnelList(assessmentEmployeeInfoVOS);
        }else {
            //不合格列表
            List<AssessmentEmployeeInfoVO> unqualifiedPersonnelList = new ArrayList<>();
            //合格列表
            List<AssessmentEmployeeInfoVO> qualifiedPersonnelList = new ArrayList<>();
            //不考核
            List<AssessmentEmployeeInfoVO> notEvaluatePersonnelList = new ArrayList<>();

            //待遍历的符合要求的数据
            List<AssessmentEmployeeInfoVO> collect;
            if(StringUtil.isNotEmpty(employeeCode) && CheckManageEnum.AgentTypeEnum.BUSINESS_DISTRICT_DIRECTOR.getRankSeq().equals(checkBatchPersonInfo.getRankSeqCode())){
                collect  = assessmentEmployeeInfoVOS.stream().filter(t -> CheckManageEnum.AgentTypeEnum.SALES_MANAGER.getRankSeq().equals(t.getRankSeqCode())).collect(Collectors.toList());
            }else {
                collect = assessmentEmployeeInfoVOS;
            }

            if(CollectionUtils.isNotEmpty(collect)){
                for (AssessmentEmployeeInfoVO assessmentEmployeeInfoVO : collect) {
                    if(String.valueOf(CheckManageEnum.NOT_CHECK.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        notEvaluatePersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.QUIT.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        unqualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.REDUCE_LEVEL2.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        unqualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.REDUCE_LEVEL1.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        unqualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.KEEP_LEVEL.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        qualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.PROMOTED_LEVEL1.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        qualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                    if(String.valueOf(CheckManageEnum.PROMOTED_LEVEL2.getLevel()).equals(assessmentEmployeeInfoVO.getFinalResult())){
                        qualifiedPersonnelList.add(assessmentEmployeeInfoVO);
                    }
                }
                //参与考核数人数
                assessmentSubordinatePersonnelVO.setActiveWorkforce(new BigDecimal(collect.size()).subtract(new BigDecimal(notEvaluatePersonnelList.size())));
            }
            assessmentSubordinatePersonnelVO.setQualifiedPersonnelList(qualifiedPersonnelList);
            assessmentSubordinatePersonnelVO.setUnqualifiedPersonnelList(unqualifiedPersonnelList);
            assessmentSubordinatePersonnelVO.setNotEvaluatePersonnelList(notEvaluatePersonnelList);
            if(CollectionUtils.isNotEmpty(notEvaluatePersonnelList)){
                collect.removeAll(notEvaluatePersonnelList);
            }
            assessmentSubordinatePersonnelVO.setAllPersonnelList(collect);
        }
        return assessmentSubordinatePersonnelVO;
    }


    /**
     * 上期当期考核接口
     * @param employeeCode
     * @param paramType
     * @return
     */
    @Override
    public AssessmentWarningVO getCurrentAssessmentInfo(String employeeCode, String paramType) {
        AssessmentWarningVO assessmentWarningVO = null;
        String checkFrequency = employeeAssessmentDao.getCheckFrequency(employeeCode);
        if(StringUtil.isEmpty(checkFrequency)){
            return null;
        }
        //当期考核月
        String checkMonth  = getCheckMonth(checkFrequency);
        //获取考核人员的信息
        CheckBatchPersonInfo checkBatchPersonInfo;
        if(EmployeeAssessmentParamEnum.CURRENT_PERIOD.getCode().equals(paramType)){
            checkBatchPersonInfo = employeeAssessmentDao.getCheckBatchPersonInfo(checkMonth, employeeCode);
        }else {
            String previousCheckMonth = getPreviousCheckMonth(employeeCode, checkMonth, checkFrequency);
            if(StringUtil.isEmpty(previousCheckMonth)){
                return null;
            }
            checkBatchPersonInfo = employeeAssessmentDao.getCheckBatchPersonInfo(previousCheckMonth, employeeCode);
        }
        //无法获取当前考核人信息 或 当前考核人不参与考核
        if(Objects.isNull(checkBatchPersonInfo) || String.valueOf(CheckManageEnum.NOT_CHECK.getLevel()).equals(checkBatchPersonInfo.getCheckSettleResult())){
            return null;
        }

        //判断自营/银保
        Boolean flag = checkPersonalByInstCode(checkBatchPersonInfo);

        //获取当期目标、上期目标、业绩达成、距离目标 以及百分比
        assessmentWarningVO = new AssessmentWarningVO();
        assessmentWarningVO = getSomeInformationOnAssessmentWarning(checkBatchPersonInfo , flag, assessmentWarningVO);

        //获取职级名称、考核目标集合、考核项目以及是否所有目标
        assessmentWarningVO = getRemainingInformation(checkBatchPersonInfo,assessmentWarningVO,flag);

        //获取考核期间时间(S-E)
        assessmentWarningVO = getAssessmentDuration(checkBatchPersonInfo, assessmentWarningVO);

        return assessmentWarningVO;
    }

    /**
     * 获取考核期间时间(S-E)
     * @param checkBatchPersonInfo
     * @param assessmentWarningVO
     * @return
     */
    private AssessmentWarningVO getAssessmentDuration(CheckBatchPersonInfo checkBatchPersonInfo, AssessmentWarningVO assessmentWarningVO) {
        //考核开始日期
        Date checkStartDate = checkBatchPersonInfo.getCheckStartDate();
        String beginDate = checkStartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toString().replace("-",".");
        //考核截止日期
        String checkEndMonth = checkBatchPersonInfo.getCheckEndMonth() + "-01";
        LocalDate localDate = LocalDate.parse(checkEndMonth,DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endDate = localDate.with(TemporalAdjusters.lastDayOfMonth()).toString().replace("-",".");

        StringBuilder stringBuilder = new StringBuilder();
        assessmentWarningVO.setAssessmentDuration(stringBuilder.append(beginDate).append("-").append(endDate).toString());
        return assessmentWarningVO;
    }

    /**
     * 获取职级名称、考核目标集合以及考核项目
     * @param checkBatchPersonInfo
     * @param assessmentWarningVO
     * @param flag
     * @return
     */
    private AssessmentWarningVO getRemainingInformation(CheckBatchPersonInfo checkBatchPersonInfo, AssessmentWarningVO assessmentWarningVO, Boolean flag) {
        //当前目标
        Integer currentTargetLevel;
        if(flag){
            currentTargetLevel = CheckManageEnum.getEnumByLabel(assessmentWarningVO.getCurrentTarget()).getLevel();
        }else {
            currentTargetLevel = CheckManageIFPEnum.getEnumByLabel(assessmentWarningVO.getCurrentTarget()).getLevel();
        }
        //获取当前目标的职级名称
        String correspondingRank;
        //银保/自营的当前考核人员的所有职级列表
        List<RankDef> correspondingRankList;
        if(flag){
            correspondingRankList = employeeAssessmentDao.getCorrespondingRankList(checkBatchPersonInfo.basicLawVersionId,checkBatchPersonInfo.rankSeqCode,checkBatchPersonInfo.partnerInstCode);
        }else {
            correspondingRankList = employeeAssessmentDao.getIfpCorrespondingRankList(checkBatchPersonInfo.basicLawVersionId,checkBatchPersonInfo.rankSeqCode,checkBatchPersonInfo.partnerInstCode);
        }
        //最大的职级
        RankDef maxRankDef = correspondingRankList.get(correspondingRankList.size() - 1);
        //目前考核人员的职级
        RankDef actualRankDef = correspondingRankList.stream().filter(t -> t.getRankCode().equals(checkBatchPersonInfo.rankCode)).collect(Collectors.toList()).get(0);

        Integer rankLevel = actualRankDef.getRankLevel();
        if(CheckManageEnum.PROMOTED_LEVEL1.getLevel() == currentTargetLevel){
            rankLevel = actualRankDef.getRankLevel() + 1;
        }
        if(CheckManageEnum.PROMOTED_LEVEL2.getLevel() == currentTargetLevel){
            rankLevel = actualRankDef.getRankLevel() + 2;
        }
        if(rankLevel >= maxRankDef.getRankLevel()){
            correspondingRank = maxRankDef.getRankName();
        }else {
            int finalCurrentLevel = rankLevel;
            correspondingRank = correspondingRankList.stream().filter(t->t.getRankLevel() == finalCurrentLevel).collect(Collectors.toList()).get(0).getRankName();
        }
        assessmentWarningVO.setCorrespondingRank(correspondingRank);

        //获取考核目标集合
        List<AssessmentRankDefVO> assessmentRankDefVOS  = new ArrayList<>();
        //获取所有考核目标
        List<AssessmentItemsVO> assessmentItems = employeeAssessmentDao.getAssessmentItems(checkBatchPersonInfo.getId());
        for (AssessmentItemsVO assessmentItem : assessmentItems) {
            if(CheckItemEnum.keep_level_standard.getValue().equals(assessmentItem.getName())){
                if(!Objects.isNull(assessmentItem.getX())){
                    AssessmentRankDefVO keepRankDef = new AssessmentRankDefVO();
                    keepRankDef.setCurrentTargetName(CheckManageEnum.KEEP_LEVEL.getLabel());
                    keepRankDef.setRankName(actualRankDef.getRankName());
                    keepRankDef.setRankMoney(BigDecimal.valueOf(assessmentItem.getX()));
                    keepRankDef.setRankLevel(actualRankDef.getRankLevel());
                    keepRankDef.setIsCurrentTarget(CheckManageEnum.KEEP_LEVEL.getLabel().equals(assessmentWarningVO.getCurrentTarget()));
                    keepRankDef.setIsCompleteCurrentTarget(assessmentWarningVO.getPerformanceAchieved().subtract(BigDecimal.valueOf(assessmentItem.getX())).compareTo(BigDecimal.ZERO) >= 0);
                    assessmentRankDefVOS.add(keepRankDef);
                }
            }

            if(CheckItemEnum.promoted_level1_standard.getValue().equals(assessmentItem.getName())){
                if(!Objects.isNull(assessmentItem.getX())){
                    AssessmentRankDefVO promotedOneRankDef = new AssessmentRankDefVO();
                    Integer finalCurrentLevel = actualRankDef.getRankLevel() + 1;
                    if(finalCurrentLevel >= maxRankDef.getRankLevel()){
                        promotedOneRankDef.setRankLevel(maxRankDef.getRankLevel());
                        promotedOneRankDef.setRankName(maxRankDef.getRankName());
                    }else {
                        RankDef rankDef = correspondingRankList.stream().filter(t->t.getRankLevel() == finalCurrentLevel).collect(Collectors.toList()).get(0);
                        promotedOneRankDef.setRankLevel(rankDef.getRankLevel());
                        promotedOneRankDef.setRankName(rankDef.getRankName());
                    }
                    if(flag){
                        promotedOneRankDef.setCurrentTargetName(CheckManageEnum.PROMOTED_LEVEL1.getLabel());
                        promotedOneRankDef.setIsCurrentTarget(CheckManageEnum.PROMOTED_LEVEL1.getLabel().equals(assessmentWarningVO.getCurrentTarget()));
                    }else {
                        promotedOneRankDef.setIsCurrentTarget(CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel().equals(assessmentWarningVO.getCurrentTarget()));
                        promotedOneRankDef.setCurrentTargetName(CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel());
                    }
                    promotedOneRankDef.setIsCompleteCurrentTarget(assessmentWarningVO.getPerformanceAchieved().subtract(BigDecimal.valueOf(assessmentItem.getX())).compareTo(BigDecimal.ZERO) >= 0);
                    promotedOneRankDef.setRankMoney(BigDecimal.valueOf(assessmentItem.getX()));
                    assessmentRankDefVOS.add(promotedOneRankDef);
                }
            }

            if(CheckItemEnum.promoted_level2_standard.getValue().equals(assessmentItem.getName())){
                if(!Objects.isNull(assessmentItem.getX())){
                    AssessmentRankDefVO promotedTwoRankDef = new AssessmentRankDefVO();
                    Integer finalCurrentLevel = actualRankDef.getRankLevel() + 2;
                    if(finalCurrentLevel >= maxRankDef.getRankLevel()){
                        promotedTwoRankDef.setRankLevel(maxRankDef.getRankLevel());
                        promotedTwoRankDef.setRankName(maxRankDef.getRankName());
                    }else {
                        RankDef rankDef = correspondingRankList.stream().filter(t->t.getRankLevel() == finalCurrentLevel).collect(Collectors.toList()).get(0);
                        promotedTwoRankDef.setRankLevel(rankDef.getRankLevel());
                        promotedTwoRankDef.setRankName(rankDef.getRankName());
                    }
                    promotedTwoRankDef.setCurrentTargetName(CheckManageEnum.PROMOTED_LEVEL2.getLabel());
                    promotedTwoRankDef.setIsCurrentTarget(CheckManageEnum.PROMOTED_LEVEL2.getLabel().equals(assessmentWarningVO.getCurrentTarget()));
                    promotedTwoRankDef.setIsCompleteCurrentTarget(assessmentWarningVO.getPerformanceAchieved().subtract(BigDecimal.valueOf(assessmentItem.getX())).compareTo(BigDecimal.ZERO) >= 0);
                    promotedTwoRankDef.setRankMoney(BigDecimal.valueOf(assessmentItem.getX()));
                    promotedTwoRankDef.setRankMoney(BigDecimal.valueOf(assessmentItem.getX()));
                    assessmentRankDefVOS.add(promotedTwoRankDef);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(assessmentRankDefVOS)){
            //排序 从低到高
            List<AssessmentRankDefVO> collect = assessmentRankDefVOS.stream().sorted(Comparator.comparing(AssessmentRankDefVO::getRankLevel)).collect(Collectors.toList());
            assessmentWarningVO.setAssessmentRankDefVOS(collect);

            //是否完成所有目标
            Boolean isCompleteAllTarget = true;

            for (AssessmentRankDefVO assessmentRankDefVO : collect) {
                Boolean isCompleteCurrentTarget = assessmentRankDefVO.getIsCompleteCurrentTarget();
                if(!isCompleteCurrentTarget){
                    isCompleteAllTarget = false;
                }
            }
            assessmentWarningVO.setIsCompleteAllTarget(isCompleteAllTarget);
        }else {
            assessmentWarningVO.setAssessmentRankDefVOS(new ArrayList<>());
            assessmentWarningVO.setIsCompleteAllTarget(false);
        }

        //考核项目
        RankDef rankDef = correspondingRankList.stream().filter(t -> correspondingRank.equals(t.getRankName())).collect(Collectors.toList()).get(0);
        assessmentWarningVO = getAllAssessmentItems(checkBatchPersonInfo, assessmentWarningVO, assessmentItems, flag, rankDef);

        return assessmentWarningVO;
    }

    /**
     * 考核项目
     *
     * @param checkBatchPersonInfo
     * @param assessmentWarningVO
     * @param actualItems
     * @param flag
     * @param rankDef
     * @return
     */
    private AssessmentWarningVO getAllAssessmentItems(CheckBatchPersonInfo checkBatchPersonInfo, AssessmentWarningVO assessmentWarningVO, List<AssessmentItemsVO> actualItems, Boolean flag, RankDef rankDef) {
        List<AssessmentItemsVO> resultItems = new ArrayList<>();
        AssessmentItemVO currentAssessmentItemVO;
        if(flag){
            currentAssessmentItemVO = employeeAssessmentDao.getYbAssessmentItems(checkBatchPersonInfo.basicLawVersionId,rankDef.getRankCode(),rankDef.getRankSequCode());
        }else {
            currentAssessmentItemVO = employeeAssessmentDao.getIfpAssessmentItems(checkBatchPersonInfo.basicLawVersionId,rankDef.getRankCode(),rankDef.getRankSequCode(),assessmentWarningVO.getCurrentTarget());
        }

        //获取所有的考核目标
        Double m13Cr = currentAssessmentItemVO.getM13Cr();
        Double m25Cr = currentAssessmentItemVO.getM25Cr();
        Double deptCurrentPersonNum = currentAssessmentItemVO.getDeptCurrentPersonNum();
        Double agentPassRate = currentAssessmentItemVO.getAgentPassRate();
        Double householdAccountExtensions = currentAssessmentItemVO.getHouseholdAccountExtensions();
        Double digitalBehavioralPoints = currentAssessmentItemVO.getDigitalBehavioralPoints();

        //为考核项目赋值
        for (AssessmentItemsVO actualItem : actualItems) {
            AssessmentItemsVO result = null;
            //13个月继续率
            if (CheckItemEnum.m13_cr.getValue().equals(actualItem.getName())) {
                if(!Objects.isNull(m13Cr)){
                    result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.m13_cr.getLabel());
                    if(!Objects.isNull(actualItem.getX())){
                        result.setX(actualItem.getX());
                        result.setXUnit("%");
                    }
                    result.setY(m13Cr);
                    result.setYUnit("%");
                    result.setCanJump(false);
                    Boolean isQualified = getIsQualified(actualItems, CheckItemEnum.m13_cr_check_complete.getValue());
                    result.setIsQualified(isQualified);
                    result.setCode(CheckItemEnum.m13_cr.getValue());
                }
            }

            //25个月继续率
            if (CheckItemEnum.m25_cr.getValue().equals(actualItem.getName())) {
                if(!Objects.isNull(m25Cr)){
                    result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.m25_cr.getLabel());
                    if(!Objects.isNull(actualItem.getX())){
                        result.setX(actualItem.getX());
                        result.setXUnit("%");
                    }
                    result.setY(m25Cr);
                    result.setYUnit("%");
                    result.setCanJump(false);
                    Boolean isQualified = getIsQualified(actualItems, CheckItemEnum.m25_cr_check_complete.getValue());
                    result.setIsQualified(isQualified);
                    result.setCode(CheckItemEnum.m25_cr.getValue());
                }
            }

            //下辖在职人力
            if (CheckItemEnum.dept_current_person_num.getValue().equals(actualItem.getName())) {
                if(!Objects.isNull(deptCurrentPersonNum)){
                    result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.dept_current_person_num.getLabel());
                    if(!Objects.isNull(actualItem.getX())){
                        result.setX(actualItem.getX());
                        result.setXUnit("人");
                    }
                    result.setY(deptCurrentPersonNum);
                    result.setYUnit("人");
                    result.setCanJump(true);
                    Boolean isQualified = getIsQualified(actualItems, CheckItemEnum.dept_person_num_meet_flag.getValue());
                    result.setIsQualified(isQualified);
                    result.setCode(CheckItemEnum.dept_current_person_num.getValue());
                }
            }

            //合格率
            if (CheckItemEnum.agent_pass_rate.getValue().equals(actualItem.getName())) {
                if(!Objects.isNull(agentPassRate)){
                    result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.agent_pass_rate.getLabel());
                    if(!Objects.isNull(actualItem.getX())){
                        result.setX(actualItem.getX());
                        result.setXUnit("%");
                    }
                    result.setY(agentPassRate);
                    result.setYUnit("%");
                    result.setCanJump(true);
                    Boolean isQualified = getIsQualified(actualItems, CheckItemEnum.agent_pass_rate_meet_flag.getValue());
                    result.setIsQualified(isQualified);
                    result.setCode(CheckItemEnum.agent_pass_rate.getValue());
                }
            }
            if(!Objects.isNull(result)){
                resultItems.add(result);
            }
        }

        //家庭账户拓展数
        List<AssessmentItemsVO> actualHouseholdAccountExtensions =
                actualItems.stream()
                        .filter(t -> CheckItemEnum.household_account_extensions.getValue().equals(t.getName()))
                        .collect(Collectors.toList());
        if(!Objects.isNull(householdAccountExtensions)){
            if(CollectionUtils.isNotEmpty(actualHouseholdAccountExtensions)){
                List<AssessmentItemsVO> collect = actualHouseholdAccountExtensions.stream().sorted(Comparator.comparing(AssessmentItemsVO::getCode)).collect(Collectors.toList());
                for (AssessmentItemsVO actualItem : collect) {
                    AssessmentItemsVO result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.household_account_extensions.getLabel() + "（" + actualItem.getXUnit() + "月）");
                    if (!Objects.isNull(actualItem.getX())) {
                        result.setX(actualItem.getX());
                        result.setXUnit("个");
                        if ((actualItem.getX() / householdAccountExtensions) >= 1) {
                            result.setIsQualified(true);
                        } else {
                            result.setIsQualified(false);
                        }
                    } else {
                        result.setIsQualified(false);
                    }
                    result.setY(householdAccountExtensions);
                    result.setYUnit("个");
                    result.setCanJump(false);
                    result.setCode(CheckItemEnum.household_account_extensions.getValue());
                    resultItems.add(result);
                }
            }
        }


        //数字化行为积分
        List<AssessmentItemsVO> actualDigitalBehavioralPointsExtensions =
                actualItems.stream()
                        .filter(t -> CheckItemEnum.digital_behavioral_points.getValue().equals(t.getName()))
                        .collect(Collectors.toList());
        if(!Objects.isNull(digitalBehavioralPoints)){
            if(CollectionUtils.isNotEmpty(actualDigitalBehavioralPointsExtensions)){
                List<AssessmentItemsVO> collect = actualDigitalBehavioralPointsExtensions.stream().sorted(Comparator.comparing(AssessmentItemsVO::getCode)).collect(Collectors.toList());
                for (AssessmentItemsVO actualItem : collect) {
                    AssessmentItemsVO result = new AssessmentItemsVO();
                    result.setName(CheckItemEnum.digital_behavioral_points.getLabel()  + "（" + actualItem.getXUnit() + "月）" );
                    if(!Objects.isNull(actualItem.getX())){
                        result.setX(actualItem.getX());
                        result.setXUnit("分");
                        if ((actualItem.getX() / currentAssessmentItemVO.getHouseholdAccountExtensions()) >= 1) {
                            result.setIsQualified(true);
                        } else {
                            result.setIsQualified(false);
                        }
                    }else {
                        result.setIsQualified(false);
                    }
                    result.setY(digitalBehavioralPoints);
                    result.setYUnit("分");
                    result.setCanJump(false);
                    result.setCode(CheckItemEnum.digital_behavioral_points.getValue());
                    resultItems.add(result);
                }
            }
        }

        //考核项目
        assessmentWarningVO.setAssessmentItems(resultItems);
        return assessmentWarningVO;
    }

    /**
     * 获取当期目标、上期目标、业绩达成以及距离目标
     * @param checkBatchPersonInfo
     * @param flag
     * @param assessmentWarningVO
     * @return
     */
    private AssessmentWarningVO getSomeInformationOnAssessmentWarning(CheckBatchPersonInfo checkBatchPersonInfo, Boolean flag, AssessmentWarningVO assessmentWarningVO) {
        //获取考核人的考核初算结果
        String checkSettleResult = checkBatchPersonInfo.getCheckSettleResult();

        //银保/自营的当前考核人员的所有职级列表
        List<RankDef> correspondingRankList;
        if(flag){
            correspondingRankList = employeeAssessmentDao.getCorrespondingRankList(checkBatchPersonInfo.basicLawVersionId,checkBatchPersonInfo.rankSeqCode,checkBatchPersonInfo.partnerInstCode);
        }else {
            correspondingRankList = employeeAssessmentDao.getIfpCorrespondingRankList(checkBatchPersonInfo.basicLawVersionId,checkBatchPersonInfo.rankSeqCode,checkBatchPersonInfo.partnerInstCode);
        }
        //最大的职级
        RankDef maxRankDef = correspondingRankList.get(correspondingRankList.size() - 1);
        //目前考核人员的职级
        RankDef actualRankDef = correspondingRankList.stream().filter(t -> t.getRankCode().equals(checkBatchPersonInfo.rankCode)).collect(Collectors.toList()).get(0);

        //当前目标
        String currentTarget = null;
        int rankLevel;
        if(flag){
            //考核初算结果为清退或者降一级或者降两级 当前目标为 维持
            if(String.valueOf(CheckManageEnum.QUIT.getLevel()).equals(checkSettleResult) || String.valueOf(CheckManageEnum.REDUCE_LEVEL2.getLevel()).equals(checkSettleResult) || String.valueOf(CheckManageEnum.REDUCE_LEVEL1.getLevel()).equals(checkSettleResult)){
                currentTarget = CheckManageEnum.KEEP_LEVEL.getLabel();
            }

            //考核初算结果为维持 当前目标为 晋升一级
            if(String.valueOf(CheckManageEnum.KEEP_LEVEL.getLevel()).equals(checkSettleResult)){
                rankLevel = actualRankDef.getRankLevel() + 1;
                if(rankLevel <= maxRankDef.getRankLevel()){
                    currentTarget = CheckManageEnum.PROMOTED_LEVEL1.getLabel();
                }else {
                    currentTarget = CheckManageEnum.KEEP_LEVEL.getLabel();
                }
            }

            //考核初算结果为晋升一级 当前目标为 晋升两级
            if(String.valueOf(CheckManageEnum.PROMOTED_LEVEL1.getLevel()).equals(checkSettleResult)){
                rankLevel = actualRankDef.getRankLevel() + 1;
                if(rankLevel <= maxRankDef.getRankLevel()){
                    currentTarget = CheckManageEnum.PROMOTED_LEVEL2.getLabel();
                }else {
                    currentTarget = CheckManageEnum.PROMOTED_LEVEL1.getLabel();
                }
            }

            //考核初算结果为晋升两级 当前目标为 晋升两级
            if(String.valueOf(CheckManageEnum.PROMOTED_LEVEL2.getLevel()).equals(checkSettleResult)){
                currentTarget = CheckManageEnum.PROMOTED_LEVEL2.getLabel();
            }
        }else {
            //考核初算结果为清退或者降一级或者降两级 当前目标为 维持
            if(String.valueOf(CheckManageIFPEnum.QUIT.getLevel()).equals(checkSettleResult) || String.valueOf(CheckManageIFPEnum.REDUCE_LEVEL1.getLevel()).equals(checkSettleResult)){
                currentTarget = CheckManageIFPEnum.KEEP_LEVEL.getLabel();
            }

            //考核初算结果为维持 当前目标为 晋升
            if(String.valueOf(CheckManageIFPEnum.KEEP_LEVEL.getLevel()).equals(checkSettleResult)){
                rankLevel = actualRankDef.getRankLevel() + 1;
                if(rankLevel <= maxRankDef.getRankLevel()){
                    currentTarget = CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel();
                }else {
                    currentTarget = CheckManageIFPEnum.KEEP_LEVEL.getLabel();
                }
            }

            //考核初算结果为晋升 当前目标为 晋升
            if(String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL1.getLevel()).equals(checkSettleResult)){
                currentTarget = CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel();
            }
        }
        assessmentWarningVO.setCurrentTarget(currentTarget);

        //上一阶段目标 根据当前目标判断上一目标(维持及以上)
        String previousStageGoal = null;
        if(flag){
            if(CheckManageEnum.KEEP_LEVEL.getLabel().equals(currentTarget)){
                previousStageGoal = CheckManageEnum.KEEP_LEVEL.getLabel();
            }

            if(CheckManageEnum.PROMOTED_LEVEL1.getLabel().equals(currentTarget)){
                previousStageGoal = CheckManageEnum.KEEP_LEVEL.getLabel();
            }

            if(CheckManageEnum.PROMOTED_LEVEL2.getLabel().equals(currentTarget)){
                previousStageGoal = CheckManageEnum.PROMOTED_LEVEL1.getLabel();
            }
        }else {
            previousStageGoal = CheckManageIFPEnum.KEEP_LEVEL.getLabel();
        }
        assessmentWarningVO.setPreviousStageGoal(previousStageGoal);

        //业绩达成
        BigDecimal performanceAchieved;
        if (flag) {
            performanceAchieved = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(), CheckItemEnum.check_acc_premium.getValue());
        }else {
            if(String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL1.getLevel()).equals(checkSettleResult)){
                performanceAchieved = checkBatchPersonInfo.getPromoteActualSTP();
            }else {
                performanceAchieved = checkBatchPersonInfo.getKeepActualSTP();
            }
        }
        assessmentWarningVO.setPerformanceAchieved(performanceAchieved.setScale(2));

        //距离目标
        BigDecimal distanceFromTarget;
        //获取当前目标的考核标准
        BigDecimal currentTargetValue = null;
        int level;
        if(flag){
            level = CheckManageEnum.getEnumByLabel(currentTarget).getLevel();
        }else {
            level = CheckManageIFPEnum.getEnumByLabel(currentTarget).getLevel();
        }
        if(CheckManageEnum.PROMOTED_LEVEL2.getLevel() == level){
            currentTargetValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.promoted_level2_standard.getValue());
        }
        if(CheckManageEnum.PROMOTED_LEVEL1.getLevel() == level){
            currentTargetValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.promoted_level1_standard.getValue());
        }
        if(CheckManageEnum.KEEP_LEVEL.getLevel() == level){
            currentTargetValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.keep_level_standard.getValue());
        }

        if(currentTargetValue == null){
            currentTargetValue = BigDecimal.ZERO;
        }
        distanceFromTarget = currentTargetValue.subtract(performanceAchieved);
        if(distanceFromTarget.compareTo(BigDecimal.ZERO) < 0){
            distanceFromTarget = BigDecimal.ZERO;
        }
        assessmentWarningVO.setDistanceFromTarget(distanceFromTarget.setScale(2));

        //百分比 (业绩达成 - 上一阶段目标考核标准) / (当前目标 - 上一阶段目标考核标准)
        BigDecimal percentage = BigDecimal.ZERO;
        //上一阶段目标考核标准
        BigDecimal previousStageGoalValue = null;
        if(flag){
            if(CheckManageEnum.PROMOTED_LEVEL2.getLabel().equals(currentTarget)){
                previousStageGoalValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.promoted_level1_standard.getValue());
            }
            if(CheckManageEnum.PROMOTED_LEVEL1.getLabel().equals(currentTarget)){
                previousStageGoalValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.keep_level_standard.getValue());
            }
        }else {
            if(CheckManageIFPEnum.PROMOTED_LEVEL1.getLabel().equals(currentTarget)){
                previousStageGoalValue = employeeAssessmentDao.getPersonConfigValue(checkBatchPersonInfo.getId(),CheckItemEnum.keep_level_standard.getValue());
            }
        }
        if(previousStageGoalValue == null){
            previousStageGoalValue = BigDecimal.ZERO;
        }
        //分子 (业绩达成 - 上一阶段目标考核标准)
        BigDecimal molecule = performanceAchieved.subtract(previousStageGoalValue);
        //分母 (当前目标 - 上一阶段目标考核标准)
        BigDecimal denominator = currentTargetValue.subtract(previousStageGoalValue);
        if(molecule.compareTo(BigDecimal.ZERO) > 0 && denominator.compareTo(BigDecimal.ZERO) > 0){
            percentage = molecule.divide(denominator,2, RoundingMode.HALF_UP);
        }
        if(percentage.compareTo(BigDecimal.ONE) >= 0){
            assessmentWarningVO.setPercentage(BigDecimal.ONE.setScale(2));
        }else {
            assessmentWarningVO.setPercentage(percentage);
        }

        return assessmentWarningVO;
    }



    /**
     * 根据当期考核月份获取下一季度考核月份
     * @param checkMonth
     * @return
     */
    private String getNextCheckMonthByCurrentCheckMonth(String checkMonth) {
        checkMonth = checkMonth + "-01";
        LocalDate localDate = LocalDate.parse(checkMonth,DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 增加 3 个月
        LocalDate previousCheckMonth = localDate.plusMonths(3);
        String dateStr = previousCheckMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return dateStr;
    }

    /**
     * 判断是否为规则中的月份
     * @param date
     * @return
     */
    public static boolean isMonth(String date) {
        String substring = date.substring(5, 7);
        if(MARCH.equals(substring) || SEPTEMBER.equals(substring)){
            return true;
        }else {
            return false;
        }
    }

    /**
     * 根据当期考核月份获取上一季度考核月份
     * @param checkMonth
     * @return
     */
    private String getPreviousCheckMonthByCurrentCheckMonth(String checkMonth) {
        checkMonth = checkMonth + "-01";
        LocalDate localDate = LocalDate.parse(checkMonth,DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 减去 3 个月
        LocalDate previousCheckMonth = localDate.minusMonths(3);
        String dateStr = previousCheckMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return dateStr;
    }


    /**
     * 判断是否合格
     *
     * @param assessmentItems
     * @param value
     * @return
     */
    private Boolean getIsQualified(List<AssessmentItemsVO> assessmentItems, String value) {
        List<AssessmentItemsVO> checkComplete = assessmentItems.stream().filter(t -> value.equals(t.getName())).collect(Collectors.toList());
        Boolean isQualified = false;
        if(CollectionUtils.isNotEmpty(checkComplete) && !Objects.isNull(checkComplete.get(0).getX()) && checkComplete.get(0).getX() != 0){
            isQualified = true;
        }
        return isQualified;
    }


    /**
     * 区分银保/自营
     * @param checkBatchPersonInfo
     * @return
     */
    private Boolean checkPersonalByInstCode(CheckBatchPersonInfo checkBatchPersonInfo) {
        String partnerInstCode = checkBatchPersonInfo.getPartnerInstCode();
        Boolean flag = false;
        if(partnerInstCode.startsWith("P00001")){
            flag = true;
        }
        return flag;
    }

    /**
     * 获取上期考核月份
     *
     * @param employeeCode
     * @param checkMonth
     * @param checkFrequency
     * @return
     */
    private String getPreviousCheckMonth(String employeeCode, String checkMonth, String checkFrequency) {
        checkMonth = checkMonth + "-01";
        LocalDate localDate = LocalDate.parse(checkMonth,DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate previousCheckMonth;
        if(CheckFrequencyEnum.QUATER_CHECK.getValue().equals(checkFrequency)){
            previousCheckMonth = localDate.minusMonths(3);
        }else {
            previousCheckMonth = localDate.minusMonths(6);
        }
        String dateStr = previousCheckMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return dateStr;
    }

    /**
     * 获取当期考核月份
     * @param checkFrequency
     * @return
     */
    private String getCheckMonth(String checkFrequency) {
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        int month = currentDate.getMonthValue();
        String dateStr;
        if(CheckFrequencyEnum.QUATER_CHECK.getValue().equals(checkFrequency)){
            if (month <= 3) {
                dateStr = String.format("%d-03", year);
            } else if (month <= 6) {
                dateStr = String.format("%d-06", year);
            } else if (month <= 9) {
                dateStr = String.format("%d-09", year);
            } else {
                dateStr = String.format("%d-12", year);
            }
        }else {
            if (month <= 6) {
                dateStr = String.format("%d-06", year);
            } else {
                dateStr = String.format("%d-12", year);
            }
        }
        return dateStr;
    }
}
