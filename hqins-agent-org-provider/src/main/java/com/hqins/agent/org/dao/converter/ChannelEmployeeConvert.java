package com.hqins.agent.org.dao.converter;

import com.google.common.collect.Lists;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.common.base.enums.AgentOrgType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @describle
 * @Date
 */
@Service
@RequiredArgsConstructor
public class ChannelEmployeeConvert {

    public EmployeeVO assembler(ChannelEmployee channelEmployee) {
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setCode(channelEmployee.getCode());
        employeeVO.setName(channelEmployee.getName());
        employeeVO.setTopCode(channelEmployee.getChannelCode());
        employeeVO.setTopName(channelEmployee.getChannelName());
        employeeVO.setOrgCode(channelEmployee.getOrgCode());
        employeeVO.setOrgName(channelEmployee.getOrgName());
        employeeVO.setOrgType(AgentOrgType.CHANNEL);
        employeeVO.setStatus("SERVING".equals(channelEmployee.getStatus()) ? EmployeeStatus.SERVING : EmployeeStatus.LEAVING);
        return employeeVO;
    }

    public List<EmployeeVO> assembler(List<ChannelEmployee> channelEmployeeList) {
        if (CollectionUtils.isEmpty(channelEmployeeList)) {
            return Lists.newArrayList();
        }
        List<EmployeeVO> employeeVOList = new ArrayList<>();
        channelEmployeeList.forEach(channelEmployee -> {
            employeeVOList.add(assembler(channelEmployee));
        });

        return employeeVOList;
    }

}
