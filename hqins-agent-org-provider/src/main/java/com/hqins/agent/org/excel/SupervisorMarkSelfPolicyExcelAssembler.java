package com.hqins.agent.org.excel;
import com.hqins.agent.org.model.vo.MarkDetailVO;
import com.hqins.agent.org.model.vo.SupervisorPerformanceDetailVO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 类型转换
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class SupervisorMarkSelfPolicyExcelAssembler {

    public SupervisorMarkSelfPolicyExcel assemblerSelfPolicy(MarkDetailVO vo) {
        SupervisorMarkSelfPolicyExcel excel = new SupervisorMarkSelfPolicyExcel();
        //销售机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //出单业务员
        excel.setAgentName(StringUtils.isNotEmpty(vo.getAgentName()) ? vo.getAgentName():" ");
        //保单号
        excel.setPolicyNo(StringUtils.isNotEmpty(vo.getPolicyNo()) ? vo.getPolicyNo():" ");
        //投保人
        excel.setAppntName(StringUtils.isNotEmpty(vo.getAppntName()) ? vo.getAppntName():" ");
        //被保人
        excel.setInsuredName(StringUtils.isNotEmpty(vo.getInsuredName()) ? vo.getInsuredName():" ");
        //类型
        excel.setTypeName(StringUtils.isNotEmpty(vo.getTypeName()) ? vo.getTypeName():" ");
        return excel;
    }


    public SupervisorMarkCr13PolicyExcel assemblerCr13Policy(MarkDetailVO vo) {
        SupervisorMarkCr13PolicyExcel excel = new SupervisorMarkCr13PolicyExcel();
        //销售机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //出单业务员
        excel.setAgentName(StringUtils.isNotEmpty(vo.getAgentName()) ? vo.getAgentName():" ");
        //13个月应收保费
        excel.setCr13Premium(ObjectUtils.isNotEmpty(vo.getCr13Premium()) ? vo.getCr13Premium(): BigDecimal.ZERO);
        //13个月实收收保费
        excel.setCr13ActualPremium(ObjectUtils.isNotEmpty(vo.getCr13ActualPremium()) ? vo.getCr13ActualPremium(): BigDecimal.ZERO);
        //13个月继续率
        excel.setCr13(ObjectUtils.isNotEmpty(vo.getCr13()) ? vo.getCr13(): BigDecimal.ZERO);
        return excel;
    }



    public SupervisorMarkCtPolicyExcel assemblerCtPolicy(MarkDetailVO vo) {
        SupervisorMarkCtPolicyExcel excel = new SupervisorMarkCtPolicyExcel();
        //销售机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //出单业务员
        excel.setAgentName(StringUtils.isNotEmpty(vo.getAgentName()) ? vo.getAgentName():" ");
        //保单号
        excel.setPolicyNo(StringUtils.isNotEmpty(vo.getPolicyNo()) ? vo.getPolicyNo():" ");
        //险种代码
        excel.setRiskCode(StringUtils.isNotEmpty(vo.getRiskCode()) ? vo.getRiskCode():" ");
        //险种名称
        excel.setRiskName(StringUtils.isNotEmpty(vo.getRiskName()) ? vo.getRiskName():" ");
        //交费方式
        excel.setPaymentWay(StringUtils.isNotEmpty(vo.getPaymentWay()) ? vo.getPaymentWay():" ");
        //交费年期
        excel.setPaymentYear(StringUtils.isNotEmpty(vo.getPaymentYear()) ? vo.getPaymentYear():" ");
        //保险期间
        excel.setInsureYears(StringUtils.isNotEmpty(vo.getInsureYears()) ? vo.getInsureYears():" ");
        //保费
        excel.setPremium(ObjectUtils.isNotEmpty(vo.getPremium()) ? vo.getPremium(): BigDecimal.ZERO);
        //保额
        excel.setAmnt(ObjectUtils.isNotEmpty(vo.getAmnt()) ? vo.getAmnt(): BigDecimal.ZERO);
        //退保日期
        excel.setCtDate(ObjectUtils.isNotEmpty(vo.getCtDate()) ? vo.getCtDate(): null);
        return excel;
    }
}
