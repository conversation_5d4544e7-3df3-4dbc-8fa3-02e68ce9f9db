package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.ChannelEmployeeDao;
import com.hqins.agent.org.dao.ChannelTeamDao;
import com.hqins.agent.org.dao.TbempDao;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.*;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.iips.TbepartnerMapper;
import com.hqins.agent.org.dao.mapper.org.*;
import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.enums.zybx.CheckResultEnum;
import com.hqins.agent.org.model.request.ChannelTeamAddRequest;
import com.hqins.agent.org.model.request.InitEmployeeAddCastRequest;
import com.hqins.agent.org.model.request.InitEmployeeAddRequest;
import com.hqins.agent.org.model.vo.CheckEmployeeResultVO;
import com.hqins.agent.org.model.vo.EmployeeAddVO;
import com.hqins.agent.org.model.vo.InitResultVo;
import com.hqins.agent.org.mq.vo.HomePartnerInterMessageVo;
import com.hqins.agent.org.mq.vo.HomeSyncDataHeadVo;
import com.hqins.agent.org.mq.vo.TbEmpVO;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.ChannelEmployeeService;
import com.hqins.agent.org.service.ChannelTeamService;
import com.hqins.agent.org.service.DataProcessService;
import com.hqins.agent.org.service.SensorsService;
import com.hqins.agent.org.utils.PasswordUtil;
import com.hqins.agent.org.web.config.FilterChannelCodeMapConfig;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.utils.TimeUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.um.model.dto.AccountInfoDTO;
import com.hqins.um.model.dto.AgentStateDTO;
import com.hqins.um.model.request.AgentCreateByAdminRequest;
import com.hqins.um.model.request.VisitorCreateByAdminRequest;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class DataProcessServiceImpl implements DataProcessService {

    private TbepartnerMapper tbepartnerMapper;
    private BaseInstMapper baseInstMapper;
    private MqMonitorTopMapper mqMonitorTopMapper;
    private MqMonitorOrgMapper mqMonitorOrgMapper;
    private MqMonitorEmployeeMapper mqMonitorEmployeeMapper;
    private ChannelTeamMapper channelTeamMapper;
    private ChannelEmployeeMapper channelEmployeeMapper;
    private ChannelDealerMapper channelDealerMapper;
    private ChannelTeamService channelTeamService;
    private TbempMapper tbempMapper;
    private Executor executor;
    private ChannelEmployeeDao channelEmployeeDao;
    private ChannelTeamDao channelTeamDao;
    private TbempDao tbempDao;
    private SensorsService sensorsService;
    private UmClient umClient;
    private FilterChannelCodeMapConfig filterChannelCodeMapConfig;
    private ChannelEmployeeService channelEmployeeService;

    public DataProcessServiceImpl(TbepartnerMapper tbepartnerMapper, BaseInstMapper baseInstMapper, MqMonitorTopMapper mqMonitorTopMapper,
                                  MqMonitorOrgMapper mqMonitorOrgMapper, MqMonitorEmployeeMapper mqMonitorEmployeeMapper, ChannelTeamMapper channelTeamMapper,
                                  ChannelEmployeeMapper channelEmployeeMapper, ChannelDealerMapper channelDealerMapper, ChannelTeamService channelTeamService,
                                  TbempMapper tbempMapper, Executor executor, ChannelEmployeeDao channelEmployeeDao, ChannelTeamDao channelTeamDao, TbempDao tbempDao,
                                  SensorsService sensorsService, UmClient umClient, FilterChannelCodeMapConfig filterChannelCodeMapConfig, ChannelEmployeeService channelEmployeeService) {
        this.tbepartnerMapper = tbepartnerMapper;
        this.baseInstMapper = baseInstMapper;
        this.mqMonitorTopMapper = mqMonitorTopMapper;
        this.mqMonitorOrgMapper = mqMonitorOrgMapper;
        this.mqMonitorEmployeeMapper = mqMonitorEmployeeMapper;
        this.channelTeamMapper = channelTeamMapper;
        this.channelEmployeeMapper = channelEmployeeMapper;
        this.channelDealerMapper = channelDealerMapper;
        this.channelTeamService = channelTeamService;
        this.tbempMapper = tbempMapper;
        this.executor = executor;
        this.channelEmployeeDao = channelEmployeeDao;
        this.channelTeamDao = channelTeamDao;
        this.tbempDao = tbempDao;
        this.sensorsService = sensorsService;
        this.umClient = umClient;
        this.filterChannelCodeMapConfig = filterChannelCodeMapConfig;
        this.channelEmployeeService = channelEmployeeService;
    }

    @Override
    @Async("executor")
    public void downloadProcessOrg(long staffId, String testFlag) {
        log.info("downloadProcessOrg testFlag:{}", testFlag);
        downloadTopBatch(1L, 1000L);
        downloadOrgBatch(1L, 1000L);
        if (!"true".equals(testFlag)) {
            proccessTop(1L, 1000L, false);
            proccessOrg(1L, 1000L, false);
            return;
        }
        //测试数据
        proccessTop(1L, 10L, true);
        proccessOrg(1L, 10L, true);
    }

    @Override
    @Async("executor")
    public void downloadProcessEmployee(long staffId, String testFlag) {
        log.info("downloadProcessEmployee testFlag:{}", testFlag);
        downloadEmployeeBatch(1L, 500L);
        //查询总数
        Integer totalCount = mqMonitorEmployeeMapper.selectCount(new LambdaQueryWrapper<MqMonitorEmployee>().eq(MqMonitorEmployee::getSync, false));
        if (null != totalCount && totalCount <= 0) {
            return;
        }
        //向上取整
        long totalPage = (long) Math.ceil((double) totalCount / 500);

        processEmployee(1L, 500L, staffId, totalPage);

        log.info("downloadProcessEmployee end:{}", testFlag);
    }

    @Override
    public void mqProcessEmployee(HomePartnerInterMessageVo homePartnerInterMessageVo) {
        Long staffId = RequestContextHolder.getStaffId() == null ? -1L : RequestContextHolder.getStaffId();
        if(homePartnerInterMessageVo == null) {
            return;
        }
        List<TbEmpVO> dataContent = homePartnerInterMessageVo.getDataContent();
        if (CollUtil.isEmpty(dataContent)) {
            return;
        }
        log.info("代理人信息通过MQ实时进行同步开始");
        //批量处理
        List<MqMonitorEmployee> mlist = new ArrayList<>();

        Set<String> employeeCodes = StreamEx.of(dataContent).map(TbEmpVO::getEMPCODE).toSet();
        List<MqMonitorEmployee> mqMonitorEmployees = mqMonitorEmployeeMapper.selectList(new LambdaQueryWrapper<MqMonitorEmployee>().in(MqMonitorEmployee::getCode, employeeCodes));

        Map<String, MqMonitorEmployee> mqMonitorEmployeesMap = StreamEx.of(mqMonitorEmployees).toMap(MqMonitorEmployee::getCode, Function.identity());

        for (TbEmpVO t : dataContent) {
            MqMonitorEmployee m = mqMonitorEmployeesMap.get(t.getEMPCODE());
            if (m != null) {
                if ("01".equals(t.getEMPSTATUS())) {
                    if (!EmployeeStatus.SERVING.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.SERVING.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                } else if ("04".equals(t.getEMPSTATUS())) {
                    if (!EmployeeStatus.LEAVING.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.LEAVING.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                } else {
                    if (!EmployeeStatus.INVALID.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.INVALID.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                }
            } else {
                m = new MqMonitorEmployee();
                m.setCode(t.getEMPCODE());
                m.setName(t.getEMPNAME());
                m.setIdCode(t.getIDCODE());
                m.setMobile(t.getMOBILE());
                //销管zh_exms库tbemp表，'empstatus：00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备'
                if ("01".equals(t.getEMPSTATUS())) {
                    m.setStatus(EmployeeStatus.SERVING.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                } else if ("04".equals(t.getEMPSTATUS())) {
                    m.setStatus(EmployeeStatus.LEAVING.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                } else {
                    m.setStatus(EmployeeStatus.INVALID.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                }
                //下面代码保留，dat和uat环境需要替换**** 1111的情况
//                if (!StringUtil.isBlank(m.getMobile()) && m.getMobile().indexOf("1111") > -1) {
//                    m.setMobile(m.getMobile().replace("1111", String.format("%04d", new Random().nextInt(9999))));
//                }
//                if (!StringUtil.isBlank(m.getMobile()) && m.getMobile().indexOf("****") > -1) {
//                    m.setMobile(m.getMobile().replace("****", String.format("%04d", new Random().nextInt(9999))));
//                }
                if (StringUtil.isBlank(m.getMobile()) || !cn.hutool.core.lang.Validator.isMobile(m.getMobile())) {
                    //只有正确的手机号才需要同步
                    m.setSyncResult("手机号格式不正确");
                    m.setSync(true);
                }
                m.setSyncStatus("DEFAULT");
                mlist.add(m);
            }
        }

        if (CollectionUtils.isNotEmpty(mlist)) {
            log.info("新增代理人信息: {}", JsonUtil.toJSON(mlist));
            mqMonitorEmployeeMapper.insertBatchSomeColumn(mlist);
        }

        Page<MqMonitorEmployee> p = mqMonitorEmployeeMapper.selectPage(new Page<>(0, dataContent.size()), new LambdaQueryWrapper<MqMonitorEmployee>()
                .eq(MqMonitorEmployee::getSync, false).in(MqMonitorEmployee::getCode, employeeCodes));
        processEmployee(p, staffId);
        log.info("代理人信息通过MQ实时进行同步结束");
    }


    @Override
    public void uploadSensors() {
        LambdaQueryWrapper<Tbemp> w = new LambdaQueryWrapper<Tbemp>().eq(Tbemp::getCptype, "PARTY")
                .orderByAsc(Tbemp::getEmpcode);
        List<Tbemp> tbemps = tbempMapper.selectList(w);
        if (CollectionUtils.isEmpty(tbemps)) {
            return;
        }
        sensorsService.addEmployee(tbemps);

        //update channel employee  info 因现有渠道商员工记录为 6W+ 为防止内存溢出，这里进行分页操作
        long current = 1L;
        long size = 500L;
        updateChannelEmployeeInfo(current, size);

    }

    private void updateChannelEmployeeInfo(long current, long size) {
        Page<ChannelEmployee> channelEmployeePage = channelEmployeeService.listPage(current, size);
        List<ChannelEmployee> records = channelEmployeePage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        //批量更新
        sensorsService.updateEmployeeList(records);
        //递归调用
        current = current + 1L;
        updateChannelEmployeeInfo(current, size);
    }

    /**
     * 批量向账号服务，上传账号
     *
     * @param current
     * @param size
     */
    private void processEmployee(Long current, Long size, Long staffId, Long totalPage) {
        log.info("processEmployee start");

        List<CompletableFuture> futures = Lists.newArrayList();
        for (Long i = current; i <= totalPage; i++) {
            Long finalI = i;
            futures.add(CompletableFuture.supplyAsync(() -> {
                Page<MqMonitorEmployee> p = mqMonitorEmployeeMapper.selectPage(new Page<>(finalI, size), new LambdaQueryWrapper<MqMonitorEmployee>()
                        .eq(MqMonitorEmployee::getSync, false));
                processEmployee(p, staffId);
                return p.getRecords();
            }, executor));
        }
        futures.forEach(CompletableFuture::join);

        log.info("processEmployee end");
    }

    private void processEmployee(Page<MqMonitorEmployee> p, Long staffId) {
        if (CollectionUtils.isEmpty(p.getRecords())) {
            return;
        }
        for (MqMonitorEmployee o : p.getRecords()) {
            log.info("process :{} ------ {}", o.getCode(), o.getMobile());
            try {
                if (o.getStatus().equals(o.getStatusOld())) {
                    //新建用户
                    AgentCreateByAdminRequest req = assemblyAgentUserCreateRequest(o, staffId);
                    umClient.createAgentUser(req);
                } else {
                    List<AccountInfoDTO> agentUserInfos = umClient.getAgentsBatch(o.getCode());
                    if (EmployeeStatus.SERVING.name().equals(o.getStatus())) {
                        //让账户可用
                        if (CollectionUtils.isEmpty(agentUserInfos)) {
                            //如果不存在，直接创建
                            AgentCreateByAdminRequest req = assemblyAgentUserCreateRequest(o, staffId);
                            umClient.createAgentUser(req);
                        } else {
                            //让用户状态变成可用
                            Long userId = agentUserInfos.get(0).getUserId();
                            //跨服务把销售员账号有效
                            AgentStateDTO request = new AgentStateDTO();
                            request.setState(AppConsts.VALID);
                            request.setStaffId(staffId);
                            request.setUserId(userId);
                            umClient.updateEmployeeStateByAdmin(request);
                        }
                    } else {
                        if (!CollectionUtils.isEmpty(agentUserInfos)) {
                            Long userId = agentUserInfos.get(0).getUserId();
                            //跨服务把销售员账号停用
                            AgentStateDTO request = new AgentStateDTO();
                            if (EmployeeStatus.LEAVING.name().equals(o.getStatus())) {
                                request.setState(AppConsts.QUIT);
                            } else {
                                request.setState(AppConsts.INVALID);
                            }
                            request.setStaffId(staffId);
                            request.setUserId(userId);
                            umClient.updateEmployeeStateByAdmin(request);
                        }
                    }
                }
                o.setSync(true);
                o.setSyncStatus("SUCCESS");
                mqMonitorEmployeeMapper.updateById(o);
            } catch (ApiException e) {
                o.setSync(true);
                o.setSyncStatus("FAIL");
                o.setSyncResult(e.getMessage());
                mqMonitorEmployeeMapper.updateById(o);
                log.error("processEmployee_error,ApiException", e);
            } catch (Exception e) {
                o.setSync(true);
                o.setSyncStatus("FAIL");
                o.setSyncResult(e.getMessage());
                mqMonitorEmployeeMapper.updateById(o);
                log.error("processEmployee_error,Exception", e);
            }
        }
    }

    private AgentCreateByAdminRequest assemblyAgentUserCreateRequest(MqMonitorEmployee o, Long staffId) {
        AgentCreateByAdminRequest request = new AgentCreateByAdminRequest();
        request.setOrgType(AgentOrgType.PARTNER.name());
        request.setEmployeeCode(o.getCode());
        request.setState(AppConsts.VALID);
        request.setPhone(o.getMobile());
        request.setPassword(PasswordUtil.generatePwd(o.getIdCode()));
        request.setStaffId(staffId);
        request.setName(o.getName());
        return request;
    }

    private List<InitEmployeeAddRequest> duolamiSync(List<InitEmployeeAddRequest> request, Long staffId, Map<String, ChannelTeam> teamsMap) {
        List<InitEmployeeAddRequest> resList = new ArrayList<>();
        try {
            Set<String> codes = new HashSet<>();
            for (InitEmployeeAddRequest initEmployeeAddRequest : request) {
                codes.add(initEmployeeAddRequest.getCode());
            }
            //通过工号查询渠道商人员信息
            Map<String, ChannelEmployee> channelEmployeeMap = channelEmployeeDao.selectEmployeeMapByCodes(codes);
            for (InitEmployeeAddRequest req : request) {
                String employeeCode = req.getCode();
                ChannelTeam team = teamsMap.get(req.getOrgCode());
                if (null != channelEmployeeMap.get(req.getCode())) {
                    //工号在ORG存在，则丢弃
                    log.info("duolamiSync,工号在ORG存在,employeeCode:{}", employeeCode);
                    continue;
                }
                log.info("duolamiSync,工号在ORG不存在,employeeCode:{}", employeeCode);
                //工号在ORG不存在
                //判断工号在UM里是否存在
                List<AccountInfoDTO> agentUserInfoList = umClient.getAgentsBatch(employeeCode);
                if (agentUserInfoList != null && agentUserInfoList.size() > 0) {
                    log.info("duolamiSync,UM存在工号,employeeCode:{}", employeeCode);
                    //UM存在工号,则补充channelEmployee数据
                    ChannelEmployee ce = BeanCopier.copyObject(BeanCopier.copyObject(req, InitEmployeeAddCastRequest.class), ChannelEmployee.class);
                    ce.setChannelCode(team.getChannelCode());
                    ce.setChannelName(team.getChannelName());
                    ce.setOrgCode(team.getOrgCode());
                    ce.setOrgName(team.getOrgName());
                    ce.setTeamCode(team.getCode());
                    ce.setTeamName(team.getName());
                    ce.setEntryTime(LocalDateTime.now());
                    ce.setStatus(EmployeeStatus.SERVING.name());
                    ce.setSourceSystem("DUOLAIMI");
                    //哆啦咪同步的，则为非体验账号
                    ce.setRoleType(RoleType.FORMAL.name());
                    ce.setIsAuthorized(AppConsts.YES);
                    List<ChannelEmployee> addChannelEmployees = new ArrayList<>();
                    addChannelEmployees.add(ce);
                    channelEmployeeMapper.insertBatchSomeColumn(addChannelEmployees);
                    log.info("duolamiSync,channelEmployeeMapper插入数据,addChannelEmployees:{}", addChannelEmployees);
                    continue;
                } else {
                    //UM不存在工号
                    resList.add(req);
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("duolamiSync,_error", e);
        }
        return resList;
    }

    /**
     * 接收哆唻咪、i兴小保、外部渠道用户，且创建账号、存储。
     *
     * @param request
     * @param staffId
     */
    @Override
    public List<InitResultVo> saveEmployeeBatch(@Valid List<InitEmployeeAddRequest> request, Long staffId) {
        //查询渠道商团队信息
        Map<String, ChannelTeam> teamsMap = channelTeamDao.selectEmployeeMapByOrgCodes(request.stream().map(InitEmployeeAddRequest::getOrgCode).collect(Collectors.toSet()));
//        String sourceSystem = CollectionUtils.isNotEmpty(request) ? request.get(0).getSourceSystem() : "";
//        if ("DUOLAIMI".equals(sourceSystem)){
//            //哆啦咪同步的，执行特殊处理
//            request = duolamiSync(request,staffId,teamsMap);
//        }
        //参数校验
        EmployeeAddVO employeeAddVO = checkParam(request, teamsMap);
        List<InitResultVo> resultList = employeeAddVO.getResults();
        request = employeeAddVO.getRequests();
        List<InitResultVo> failResultList = new ArrayList<>();
        List<ChannelEmployee> addChannelEmployees = new ArrayList<>();
        List<AgentCreateByAdminRequest> addAgentUser = new ArrayList<>();
        List<String> nos = new ArrayList<>();
        for (int i = 0, requestSize = request.size(); i < requestSize; i++) {
            InitEmployeeAddRequest addRequest = request.get(i);
            ChannelTeam team = teamsMap.get(addRequest.getOrgCode());
            if (team == null) {
                failResultList.add(InitResultVo.builder()
                        .employeeCode(addRequest.getCode())
                        .licenseNo(addRequest.getLicenseNo())
                        .result("渠道商" + addRequest.getOrgCode() + "不存在，请确认后重试！")
                        .build());
                continue;
            }
            CheckEmployeeResultVO checkEmployeeResultVO = channelEmployeeService.checkForChannelForInit(addRequest);
            if (checkEmployeeResultVO.getCheckResult().intValue() != CheckResultEnum.ALL_SUCCESS.getValue().intValue()) {
                failResultList.add(InitResultVo.builder()
                        .employeeCode(addRequest.getCode())
                        .licenseNo(addRequest.getLicenseNo())
//                        .result("中银保信息校验未能通过，请确认后重试！")
                        .result("中银保信息校验未能通过:" + checkEmployeeResultVO.getCheckResultDetailList().get(0).getReturnMessage())
                        .build());
                continue;
            }
            ChannelEmployee ce = BeanCopier.copyObject(BeanCopier.copyObject(addRequest, InitEmployeeAddCastRequest.class), ChannelEmployee.class);
            ce.setChannelCode(team.getChannelCode());
            ce.setChannelName(team.getChannelName());
            ce.setOrgCode(team.getOrgCode());
            ce.setOrgName(team.getOrgName());
            ce.setTeamCode(team.getCode());
            ce.setTeamName(team.getName());
            ce.setEntryTime(LocalDateTime.now());
            ce.setStatus(EmployeeStatus.SERVING.name());
            ce.setSourceSystem(StringUtil.isNotEmpty(ce.getSourceSystem()) ? ce.getSourceSystem() : AppConsts.DEFAULT_SOURCE_SYSTEM);
            if ("DUOLAIMI".equals(ce.getSourceSystem())) {
                //哆啦咪同步的，则为非体验账号
                ce.setRoleType(RoleType.FORMAL.name());
            }
            //目前根据系统来源判断是否授权 开放平台逻辑  MGA 未来会同步到OPEN
            if (AppConsts.MGA.equals(ce.getSourceSystem()) || ce.getSourceSystem().startsWith(AppConsts.OPEN)) {
                //重新生成员工编码
                ce.setJobNumber(addRequest.getCode());
                ce.setIsAuthorized(AppConsts.NO);
                ce.setName(StringUtil.isNotEmpty(ce.getName()) ? ce.getName() : "");
                ce.setMobile(StringUtil.isNotEmpty(ce.getMobile()) ? ce.getMobile() : "");
                ce.setUniversalQualification(Boolean.TRUE.equals(ce.getUniversalQualification()));
                ce.setGender(StringUtil.isNotEmpty(ce.getGender()) ? ce.getGender() : Gender.MALE.name());
                String orgCode = ce.getOrgCode();
                //渠道商编码( 多于6位则截取前6位) + C + 六位顺序号（全局）
                if (orgCode.length() > AppConsts.NUMBER_6) {
                    //orgCode 取出前六位
                    orgCode = orgCode.substring(AppConsts.NUMBER_0, AppConsts.NUMBER_6);
                }
                //如中兴经纪销售人员：C00013C_时间戳占位
                ce.setCode(orgCode + "C_" + System.currentTimeMillis() + "_" + i);
                ce.setIsWaitCreatCode(AppConsts.YES);
                addChannelEmployees.add(ce);
                //调用UM创建visitorId
                nos.add(addRequest.getLicenseNo());
            } else {
                ce.setIsAuthorized(AppConsts.YES);
                AgentCreateByAdminRequest req = new AgentCreateByAdminRequest();
                req.setOrgType(AgentOrgType.CHANNEL.name());
                req.setEmployeeCode(ce.getCode());
                req.setState(AppConsts.VALID);
                req.setPhone(ce.getMobile());
                req.setPassword(PasswordUtil.generatePwd(ce.getIdCode()));
                req.setStaffId(staffId);
                req.setName(ce.getName());
                addAgentUser.add(req);
                addChannelEmployees.add(ce);
            }
        }
        //创建失败的 执业证号
        List<String> failNos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(nos)) {
            log.info("外部渠道插入数量：{} 并批量生成 访客ID", nos.size());
            for (String no : nos) {
                VisitorCreateByAdminRequest createByAdminRequest = new VisitorCreateByAdminRequest();
                createByAdminRequest.setIdentity(no);
                createByAdminRequest.setStaffId(staffId);
                try {
                    umClient.createVisitorByAdmin(createByAdminRequest);
                } catch (Exception e) {
                    log.error("新增访客异常", e);
                    if (!e.getMessage().contains("已存在")) {
                        failNos.add(no);
                    }
                }
            }
        }
        //创建失败的 传递来的工号
        List<String> failCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addAgentUser)) {
            for (AgentCreateByAdminRequest agentCreateByAdminRequest : addAgentUser) {
                try {
                    umClient.createAgentUser(agentCreateByAdminRequest);
                } catch (Exception e) {
                    log.error("新增代理人信息异常", e);
                    failCodes.add(agentCreateByAdminRequest.getEmployeeCode());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(failNos)) {
            //存在创建失败的职业证号 需要移除
            if (CollectionUtils.isNotEmpty(addChannelEmployees)) {
                List<ChannelEmployee> copyAddEmployees = new ArrayList<>(addChannelEmployees);
                for (ChannelEmployee add : addChannelEmployees) {
                    if (failNos.contains(add.getLicenseNo())) {
                        copyAddEmployees.remove(add);
                    }
                }
                addChannelEmployees = copyAddEmployees;
            }
            failResultList.addAll(failNos.stream().map(no -> InitResultVo.builder()
                    .licenseNo(no)
                    .result("代理人执业证号" + no + "创建访客不成功，请稍后重试！")
                    .build()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(failCodes)) {
            //存在创建失败的工号 需要移除
            if (CollectionUtils.isNotEmpty(addChannelEmployees)) {
                List<ChannelEmployee> copyAddEmployees = new ArrayList<>(addChannelEmployees);
                for (ChannelEmployee add : addChannelEmployees) {
                    if (failCodes.contains(add.getCode())) {
                        copyAddEmployees.remove(add);
                    }
                }
                addChannelEmployees = copyAddEmployees;
            }
            failResultList.addAll(failCodes.stream().map(code -> InitResultVo.builder()
                    .employeeCode(code)
                    .result("代理人" + code + "创建访客不成功，请稍后重试！")
                    .build()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(addChannelEmployees)) {
            log.info("同步人员插入数量：{}", addChannelEmployees.size());
            channelEmployeeMapper.insertBatchSomeColumn(addChannelEmployees);
        }
        channelEmployeeMapper.creatCode();
        resultList.addAll(failResultList);
        return resultList;
    }


    /**
     * 参数校验
     *
     * @param request  加人请求
     * @param teamsMap 渠道商团队信息
     * @return
     */
    public EmployeeAddVO checkParam(List<InitEmployeeAddRequest> request, Map<String, ChannelTeam> teamsMap) {
        //判断同步信息集合大小  系统来源为MGA时限制 每次同步信息最大为100
        String sourceSystem = CollectionUtils.isNotEmpty(request) ? request.get(0).getSourceSystem() : AppConsts.STRING_NULL;
        //开放平台逻辑  MGA 未来会同步到OPEN
        if (AppConsts.MGA.equals(sourceSystem) || sourceSystem.startsWith(AppConsts.OPEN)) {
            AssertUtil.isTrue(request.size() <= 100, new ApiException("每次同步信息,最大限制不能超过100"));
        }
        Set<String> codes = new HashSet<>();
        Set<String> idCodeSet = new HashSet<>();
        Set<String> licenseNoSet = new HashSet<>();
        for (InitEmployeeAddRequest initEmployeeAddRequest : request) {
            codes.add(initEmployeeAddRequest.getCode());
            idCodeSet.add(initEmployeeAddRequest.getIdCode());
            licenseNoSet.add(initEmployeeAddRequest.getLicenseNo());
        }
        //通过工号查询渠道商人员信息
        Map<String, ChannelEmployee> channelEmployeeMap = channelEmployeeDao.selectEmployeeMapByCodes(codes);
        //通过证件号查询渠道商人员信息
        Map<String, ChannelEmployee> channelEmployeeIdCodeMap = channelEmployeeDao.selectEmployeeMapByIdCodes(idCodeSet);
        //通过执业证号查询渠道商人员信息
        Map<String, ChannelEmployee> channelEmployeeLicenseNoMap = channelEmployeeDao.selectEmployeeMapByLicenseNos(licenseNoSet);
        //通过工号查询合伙人人员信息
        Map<String, Tbemp> partnerEmployeesMap = tbempDao.selectEmpMapByCodes(codes);
        //通过证件号查询合伙人人员信息
        Map<String, Tbemp> partnerEmployeeIdCodeMap = tbempDao.selectEmpMapByIdCodes(idCodeSet);
        //通过执业证号查询合伙人人员信息
        Map<String, Tbemp> partnerEmployeeLicenseNoMap = tbempDao.selectEmpMapByLicenseNo(licenseNoSet);
        List<InitResultVo> resultList = new ArrayList<>();
        ArrayList<InitEmployeeAddRequest> copyRequest = new ArrayList<>(request);
        for (InitEmployeeAddRequest a : request) {
            //新增的机构。如果是可销售的机构，自动创建默认团队
            ChannelTeam team = teamsMap.get(a.getOrgCode());
            if (null == team) {
                InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "机构不存在或机构编码错误");
                resultList.add(vo);
                copyRequest.remove(a);
                continue;
            }
            //哆啦咪同步账号，过滤filter-channel-code配置的渠道编码
            String channelCode = team.getChannelCode();
            if (filterChannelCodeMapConfig.getFilterChannelCodeMap() != null
                    && filterChannelCodeMapConfig.getFilterChannelCodeMap().containsKey(channelCode) && AppConsts.DUOLAIMI.equals(sourceSystem)) {
                log.info("saveEmployeeBatch_过滤渠道账号，channelCode:{}", channelCode);
                InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "哆唻咪该渠道商被过滤");
                resultList.add(vo);
                //过滤数据  先校验在同步数据 因此需移除需要过滤的数据 然后统一保存 此处直接下一步即可
                copyRequest.remove(a);
                continue;
            }
            //MGA逻辑 开放平台逻辑  MGA 未来会同步到OPEN
            if (AppConsts.MGA.equals(sourceSystem) || sourceSystem.startsWith(AppConsts.OPEN)) {
                //销管在职
                List<Map<String, Object>> tbemps = tbempMapper.getByMga(a.getMobile(), IdTypeMapping.getHqCore(a.getIdType()), a.getIdCode(), a.getLicenseNo());
                if (CollectionUtils.isNotEmpty(tbemps)) {
                    for (Map<String, Object> tbemp : tbemps) {
                        if (StringUtil.isNotEmpty(a.getIdCode()) && a.getIdCode().equals(tbemp.get("idcode"))) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该证件号在销管已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该证件号在销管已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                        if (StringUtil.isNotEmpty(a.getMobile()) && a.getMobile().equals(tbemp.get("maintelephone"))) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该手机号在销管已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该手机号在销管已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                        if (StringUtil.isNotEmpty(a.getLicenseNo()) && a.getLicenseNo().equals(tbemp.get("CERCODE"))) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该职业证号在销管已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该职业证号在销管已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                    }
                    continue;
                }
                //ORG在职
                List<ChannelEmployee> channelEmployees = channelEmployeeMapper.getByMga(a.getIdType(), a.getIdCode(), a.getMobile(), a.getLicenseNo());
                if (CollectionUtils.isNotEmpty(channelEmployees)) {
                    for (ChannelEmployee employee : channelEmployees) {
                        if (StringUtil.isNotEmpty(a.getIdCode()) && a.getIdCode().equals(employee.getIdCode())) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该证件号在org已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该证件号在org已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                        if (StringUtil.isNotEmpty(a.getMobile()) && a.getMobile().equals(employee.getMobile())) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该手机号在org已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该手机号在org已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                        if (StringUtil.isNotEmpty(a.getLicenseNo()) && a.getLicenseNo().equals(employee.getLicenseNo())) {
                            InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该职业证号在org已存在");
                            resultList.add(vo);
                            copyRequest.remove(a);
                            log.info("该职业证号在org已存在,数据为:{}", JsonUtil.toJSON(a));
                            break;
                        }
                    }
                    continue;
                }
            } else {
                if (StringUtil.isNotBlank(a.getCode())) {
                    //检查渠道商下工号是否存在
                    if (null != channelEmployeeMap.get(a.getCode())) {
                        InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该工号在渠道商下已经存在");
                        resultList.add(vo);
                        copyRequest.remove(a);
                        log.info("该工号在渠道商下已经存在,数据为:{}", JsonUtil.toJSON(a));
                        continue;
                    }
                    //判断合伙人下工号是否存在
                    if (null != partnerEmployeesMap.get(a.getCode())) {
                        InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该工号在合伙人下已经存在");
                        resultList.add(vo);
                        copyRequest.remove(a);
                        log.info("该工号在合伙人下已经存在,数据为:{}", JsonUtil.toJSON(a));
                        continue;
                    }
                }
                //判断渠道商下证件号是否存在
                if (null != channelEmployeeIdCodeMap.get(a.getIdCode())) {
                    InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该证件号在渠道商下已经存在");
                    resultList.add(vo);
                    copyRequest.remove(a);
                    log.info("该证件号在渠道商下已经存在,数据为:{}", JsonUtil.toJSON(a));
                    continue;
                }
                //判断渠道商下执业证号是否存在
                if (null != channelEmployeeLicenseNoMap.get(a.getLicenseNo())) {
                    InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该执业证号在渠道商下已经存在");
                    resultList.add(vo);
                    copyRequest.remove(a);
                    log.info("该执业证号在渠道商下已经存在,数据为:{}", JsonUtil.toJSON(a));
                    continue;
                }
                //判断合伙人下证件号是否存在
                if (null != partnerEmployeeIdCodeMap.get(a.getIdCode())) {
                    InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该证件号在合伙人下已经存在");
                    resultList.add(vo);
                    copyRequest.remove(a);
                    log.info("该证件号在合伙人下已经存在,数据为:{}", JsonUtil.toJSON(a));
                    continue;
                }
                //判断合伙人下执业证号是否存在
                if (null != partnerEmployeeLicenseNoMap.get(a.getLicenseNo())) {
                    InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该执业证号在合伙人下已经存在");
                    resultList.add(vo);
                    copyRequest.remove(a);
                    log.info("该执业证号在合伙人下已经存在,数据为:{}", JsonUtil.toJSON(a));
                    continue;
                }
                List<ChannelEmployee> channelEmployees = channelEmployeeMapper.getByMga(a.getIdType(), a.getIdCode(), a.getMobile(), a.getLicenseNo());
                for (ChannelEmployee employee : channelEmployees) {
                    if (StringUtil.isNotEmpty(a.getMobile()) && a.getMobile().equals(employee.getMobile())) {
                        InitResultVo vo = new InitResultVo(a.getCode(), a.getLicenseNo(), "该手机号在org已存在");
                        resultList.add(vo);
                        copyRequest.remove(a);
                        log.info("该手机号在org已存在,数据为:{}", JsonUtil.toJSON(a));
                        continue;
                    }
                }
                //判断数据校验结果 不为空继续校验  统一返回校验结果
                if (CollectionUtils.isNotEmpty(resultList)) {
                    continue;
                }
            }
        }

        return EmployeeAddVO.builder().results(resultList).requests(copyRequest).build();
    }


    /**
     * 批量下载销售员
     *
     * @param current
     * @param size
     */
    private void downloadEmployeeBatch(Long current, Long size) {
        log.info("downloadEmployee page:" + current);

        LambdaQueryWrapper<Tbemp> w = new LambdaQueryWrapper<Tbemp>().eq(Tbemp::getCptype, "PARTY").in(Tbemp::getEmpstatus, AppConsts.EMP_STATUS_LIST).orderByAsc(Tbemp::getEmpcode);
        Page<Tbemp> p = tbempMapper.selectPage(new Page<>(current, size), w);
        if (null == p || CollectionUtils.isEmpty(p.getRecords())) {
            return;
        }

        //批量处理
        List<MqMonitorEmployee> mlist = new ArrayList<>();

        List<Tbemp> tbempList = p.getRecords();
        Set<String> employeeCodes = StreamEx.of(tbempList).map(Tbemp::getEmpcode).toSet();
        List<MqMonitorEmployee> mqMonitorEmployees = mqMonitorEmployeeMapper.selectList(new LambdaQueryWrapper<MqMonitorEmployee>().in(MqMonitorEmployee::getCode, employeeCodes));

        Map<String, MqMonitorEmployee> mqMonitorEmployeesMap = StreamEx.of(mqMonitorEmployees).toMap(MqMonitorEmployee::getCode, Function.identity());

        for (Tbemp t : tbempList) {
            MqMonitorEmployee m = mqMonitorEmployeesMap.get(t.getEmpcode());
            if (m != null) {
                if ("01".equals(t.getEmpstatus())) {
                    if (!EmployeeStatus.SERVING.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.SERVING.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                } else if ("04".equals(t.getEmpstatus())) {
                    if (!EmployeeStatus.LEAVING.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.LEAVING.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                } else {
                    if (!EmployeeStatus.INVALID.name().equals(m.getStatus())) {
                        m.setStatusOld(m.getStatus());
                        m.setStatus(EmployeeStatus.INVALID.name());
                        m.setSync(false);
                        m.setSyncStatus("DEFAULT");
                        mqMonitorEmployeeMapper.updateById(m);
                    }
                }
            } else {
                m = new MqMonitorEmployee();
                m.setCode(t.getEmpcode());
                m.setName(t.getEmpname());
                m.setIdCode(t.getIdcode());
                m.setMobile(t.getMaintelephone());
                //销管zh_exms库tbemp表，'empstatus：00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备'
                if ("01".equals(t.getEmpstatus())) {
                    m.setStatus(EmployeeStatus.SERVING.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                } else if ("04".equals(t.getEmpstatus())) {
                    m.setStatus(EmployeeStatus.LEAVING.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                } else {
                    m.setStatus(EmployeeStatus.INVALID.name());
                    m.setStatusOld(m.getStatus());
                    m.setSync(false);
                }
                //下面代码保留，dat和uat环境需要替换**** 1111的情况
//                if (!StringUtil.isBlank(m.getMobile()) && m.getMobile().indexOf("1111") > -1) {
//                    m.setMobile(m.getMobile().replace("1111", String.format("%04d", new Random().nextInt(9999))));
//                }
//                if (!StringUtil.isBlank(m.getMobile()) && m.getMobile().indexOf("****") > -1) {
//                    m.setMobile(m.getMobile().replace("****", String.format("%04d", new Random().nextInt(9999))));
//                }
                if (StringUtil.isBlank(m.getMobile()) || !cn.hutool.core.lang.Validator.isMobile(m.getMobile())) {
                    //只有正确的手机号才需要同步
                    m.setSyncResult("手机号格式不正确");
                    m.setSync(true);
                }
                m.setSyncStatus("DEFAULT");
                mlist.add(m);
            }
        }

        if (CollectionUtils.isNotEmpty(mlist)) {
            log.info("新增代理人信息: {}", JsonUtil.toJSON(mlist));
            mqMonitorEmployeeMapper.insertBatchSomeColumn(mlist);
        }

        downloadEmployeeBatch(current + 1, 500L);
    }


    /**
     * 批量下载销售机构
     *
     * @param current
     * @param size
     */
    private void downloadOrgBatch(Long current, Long size) {
        log.info("downloadOrg page:" + current);
        LambdaQueryWrapper<BaseInst> w = new LambdaQueryWrapper<BaseInst>()
                .orderByAsc(BaseInst::getInstCode);
        Page<BaseInst> p = baseInstMapper.selectPage(new Page<>(current, size), w);
        if (p.getRecords().isEmpty()) {
            return;
        }
        Set<String> instcodes = StreamEx.of(p.getRecords()).map(BaseInst::getInstCode).toSet();
        List<MqMonitorOrg> mqMonitorOrgs = mqMonitorOrgMapper.selectList(new LambdaQueryWrapper<MqMonitorOrg>()
                .in(MqMonitorOrg::getCode, instcodes));

        Map<String, MqMonitorOrg> mqMonitorOrgMap = StreamEx.of(mqMonitorOrgs).toMap(MqMonitorOrg::getCode, Function.identity());

        List<MqMonitorOrg> mlist = new ArrayList<>();
        for (BaseInst t : p.getRecords()) {
            MqMonitorOrg m = mqMonitorOrgMap.get(t.getInstCode());
            if (m != null) {
                boolean falg = false;
                if (!m.getName().equals(t.getInstName())) {
                    falg = true;
                    m.setNameOld(m.getName());
                    m.setName(t.getInstName());
                }
                if (!m.getStatus().equals(t.getInstStatus())) {
                    falg = true;
                    m.setStatusOld(m.getStatus());
                    m.setStatus(t.getInstStatus());
                }
                if (!m.getCanSold().equals(t.getCanSold()) && t.getCanSold() != null) {
                    falg = true;
                    m.setCanSoldOld(m.getCanSold());
                    m.setCanSold(t.getCanSold());
                }
                if (falg) {
                    m.setSync(false);
                    m.setSyncStatus("DEFAULT");
                    mqMonitorOrgMapper.updateById(m);
                }
            } else {
                m = new MqMonitorOrg();
                m.setCompanyid(t.getCompanyid());
                m.setCode(t.getInstCode());
                m.setName(t.getInstName());
                m.setNameOld(t.getInstName());
                m.setStatus(t.getInstStatus());
                m.setStatusOld(t.getInstStatus());
                if ("Y".equals(t.getCanSold())) {
                    m.setCanSold("Y");
                } else {
                    m.setCanSold("N");
                }
                m.setCanSoldOld(m.getCanSold());
                if ("Y".equals(t.getCanSold())) {
                    //可销售的需要处理，创建默认销售团队
                    m.setSync(false);
                } else {
                    m.setSync(true);
                }
                m.setSyncStatus("DEFAULT");
                mlist.add(m);
            }
        }
        if (!mlist.isEmpty()) {
            mqMonitorOrgMapper.insertBatchSomeColumn(mlist);
        }
        downloadOrgBatch(current + 1, size);
    }

    /**
     * 批量下载顶层机构哦
     * 监测名称变化
     *
     * @param current
     * @param size
     */
    private void downloadTopBatch(Long current, Long size) {
        log.info("downloadTop page:" + current);
        LambdaQueryWrapper<Tbepartner> w = new LambdaQueryWrapper<Tbepartner>()
                .orderByAsc(Tbepartner::getCompanycode);
        Page<Tbepartner> p = tbepartnerMapper.selectPage(new Page<>(current, size), w);
        if (p.getRecords().isEmpty()) {
            return;
        }
        Set<String> topCodes = StreamEx.of(p.getRecords()).map(Tbepartner::getCompanycode).toSet();

        List<MqMonitorTop> mqMonitorTops = mqMonitorTopMapper.selectList(new LambdaQueryWrapper<MqMonitorTop>()
                .in(MqMonitorTop::getCode, topCodes));
        Map<String, MqMonitorTop> custManageCodeMap = StreamEx.of(mqMonitorTops).toMap(MqMonitorTop::getCode, Function.identity());

        List<MqMonitorTop> mlist = new ArrayList<>();
        for (Tbepartner t : p.getRecords()) {
            MqMonitorTop m = custManageCodeMap.get(t.getCompanycode());
            if (m != null) {
                boolean flag = false;
                if (!m.getName().equals(t.getCompanyname())) {
                    flag = true;
                    m.setNameOld(m.getName());
                    m.setName(t.getCompanyname());
                }
                if (!m.getStatus().equals(t.getComanystatus() + "")) {
                    flag = true;
                    m.setStatusOld(m.getStatus());
                    m.setStatus(t.getComanystatus() + "");
                }
                if (flag) {
                    m.setSync(false);
                    m.setSyncStatus("DEFAULT");
                    mqMonitorTopMapper.updateById(m);
                }
            } else {
                m = new MqMonitorTop();
                m.setCompanyid(t.getCompanyid());
                m.setCode(t.getCompanycode());
                m.setName(t.getCompanyname());
                m.setNameOld(t.getCompanyname());
                m.setStatus(t.getComanystatus() + "");
                m.setStatusOld(t.getComanystatus() + "");
                if (AppConsts.CPTYPE_CHANNEL.equals(t.getCptype())) {
                    m.setOrgType(AgentOrgType.CHANNEL.name());
                } else {
                    m.setOrgType(AgentOrgType.PARTNER.name());
                }
                m.setSync(true);
                m.setSyncStatus("DEFAULT");
                mlist.add(m);
            }
        }
        if (!mlist.isEmpty()) {
            mqMonitorTopMapper.insertBatchSomeColumn(mlist);
        }
        downloadTopBatch(current + 1, size);
    }

    /**
     * 处理顶层机构名称变化
     *
     * @param current
     * @param size
     */
    private void proccessTop(Long current, Long size, boolean testFlag) {
        log.info("proccessTop start");
        Page<MqMonitorTop> p = mqMonitorTopMapper.selectPage(new Page<>(current, size), new LambdaQueryWrapper<MqMonitorTop>()
                .eq(MqMonitorTop::getSync, false));
        if (p.getRecords().isEmpty()) {
            log.info("proccessTop end");
            return;
        }
        for (MqMonitorTop o : p.getRecords()) {
            if (AgentOrgType.CHANNEL.name().equals(o.getOrgType())) {
                //渠道商的需要处理。合伙人的变化忽略。
                if (!o.getName().equals(o.getNameOld())) {
                    log.info("proccessTop :" + o.getName());
                    //机构名字调整，更新团队组织名称，更新销售员组织名称
                    channelTeamMapper.updateTopNameByTopCode(o.getCode(), o.getName());
                    channelEmployeeMapper.updateTopNameByTopCode(o.getCode(), o.getName());
                    channelDealerMapper.updateTopNameByTopCode(o.getCode(), o.getName());
                    o.setNameOld(o.getName());
                }
                //顶层组织状态变化忽略。
                //if(!o.getStatus().equals(o.getStatusOld())){}
            }
            o.setSync(true);
            o.setSyncStatus("SUCCESS");
            mqMonitorTopMapper.updateById(o);
        }
        log.info("proccessTop end");
        if (!testFlag) {
            proccessTop(current, size, testFlag);
        }
    }

    /**
     * 处理机构名称、状态变化
     *
     * @param current
     * @param size
     */
    private void proccessOrg(Long current, Long size, boolean testFlag) {
        log.info("proccessOrg start");
        Page<MqMonitorOrg> p = mqMonitorOrgMapper.selectPage(new Page<>(current, size), new LambdaQueryWrapper<MqMonitorOrg>()
                .eq(MqMonitorOrg::getSync, false));
        if (p.getRecords().isEmpty()) {
            log.info("proccessOrg end");
            return;
        }
        Set<String> orgCodes = StreamEx.of(p.getRecords()).map(MqMonitorOrg::getCode).toSet();
        List<ChannelTeam> teams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .in(ChannelTeam::getOrgCode, orgCodes).eq(ChannelTeam::getStatus, TeamStatus.ENABLED.name()));
        Map<String, ChannelTeam> teamMap = StreamEx.of(teams).toMap(ChannelTeam::getOrgCode, Function.identity(), (key1, key2) -> key1);

        for (MqMonitorOrg o : p.getRecords()) {
            try {
                if ("Y".equals(o.getCanSold())) {
                    //新增的机构。如果是可销售的机构，自动创建默认团队
                    if (teamMap.get(o.getCode()) == null) {
                        log.info("create default team:" + o.getCode());
                        //创建默认团队
                        ChannelTeamAddRequest addReq = new ChannelTeamAddRequest();
                        addReq.setOrgCode(o.getCode());
                        addReq.setName(o.getName() + "团队");
                        addReq.setLevel(TeamLevel.AREA);
                        channelTeamService.save(addReq, false);
                    }
                }
                if (!o.getName().equals(o.getNameOld())) {
                    //机构名字调整，更新团队机构名称，更新销售员机构名称
                    channelTeamMapper.updateOrgNameByOrgCode(o.getCode(), o.getName());
                    channelEmployeeMapper.updateOrgNameByOrgCode(o.getCode(), o.getName());
                    channelDealerMapper.updateOrgNameByOrgCode(o.getCode(), o.getName());
                    o.setNameOld(o.getName());
                }
                if (!o.getStatus().equals(o.getStatusOld())) {
                    if (!"1".equals(o.getStatus())) {
                        //从有效状态变成失效，把机构下的团队失效，团队下的人员失效，人员下的账号失效
                        channelTeamService.disableTeamByOrgCode(o.getCode());
                        //如果有机构把该机构配置成了经销商，需要删除该关系。
                        channelDealerMapper.delete(new LambdaQueryWrapper<ChannelDealer>()
                                .eq(ChannelDealer::getOrgCode, o.getCode()));
                    }
                    o.setStatusOld(o.getStatus());
                }
                o.setSync(true);
                o.setSyncStatus("SUCCESS");
                mqMonitorOrgMapper.updateById(o);
            } catch (ApiException e) {
                o.setSync(true);
                o.setSyncStatus("FAIL");
                o.setSyncResult(e.getMessage());
                mqMonitorOrgMapper.updateById(o);
                e.printStackTrace();
            } catch (Exception e) {
                o.setSync(true);
                o.setSyncStatus("FAIL");
                o.setSyncResult(e.getMessage());
                mqMonitorOrgMapper.updateById(o);
                e.printStackTrace();
            }
        }
        log.info("proccessOrg end");
        if (!testFlag) {
            proccessOrg(current, size, testFlag);
        }
    }


}
