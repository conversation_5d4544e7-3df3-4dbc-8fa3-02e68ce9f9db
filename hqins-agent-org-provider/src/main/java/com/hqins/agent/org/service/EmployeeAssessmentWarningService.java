package com.hqins.agent.org.service;

import com.hqins.agent.org.model.vo.AssessmentPerformanceDataVO;
import com.hqins.agent.org.model.vo.AssessmentSubordinatePersonnelVO;
import com.hqins.agent.org.model.vo.AssessmentTeamDateVO;
import com.hqins.agent.org.model.vo.AssessmentWarningVO;

public interface EmployeeAssessmentWarningService {

    /**
     * 获取当期考核预警信息
     * @param employeeCode
     * @return
     */
    AssessmentWarningVO getCurrentAssessmentInfo(String employeeCode, String paramType);

    /**
     * 获取下辖人员清单信息
     *
     * @param employeeCode
     * @param teamCode
     * @param paramType
     * @param code
     * @return
     */
    AssessmentSubordinatePersonnelVO getAssessmentSubordinatePersonnelInfo(String employeeCode, String teamCode, String paramType, String code);

    AssessmentPerformanceDataVO getAssessmentPerformanceDataInfo(String teamCode, String paramType);

    AssessmentTeamDateVO getAssessmentTableDataInfo(String teamCode, String paramType);
}
