package com.hqins.agent.org.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Timer;

/**
 * <AUTHOR>
 * @since 2021/6/11
 */
@Component
@Slf4j
public class CacheManager {

    /**
     * 每隔10分钟刷新缓存数据
     */
    private static final long INTERVAL = 30 * 60 * 1000L;//线上环境差不多每10分钟发生fullGC，20240222改为每30分钟执行一次缓存刷新

    @Autowired
    private CacheLoader cacheLoader;

    @PostConstruct
    public void initCache() {
        Timer timer = new Timer("org-refresh-thread");
        timer.scheduleAtFixedRate(cacheLoader, 0L, INTERVAL);
    }

}
