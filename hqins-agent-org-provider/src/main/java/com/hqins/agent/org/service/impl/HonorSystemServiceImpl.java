package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.hqins.agent.org.dao.HonorSystemDao;
import com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory;
import com.hqins.agent.org.dao.entity.settle.HonorCurrentInfo;
import com.hqins.agent.org.model.enums.HonorItemEnum;
import com.hqins.agent.org.model.enums.HonorSystemConstants;
import com.hqins.agent.org.model.enums.CheckMetricsItemEnum;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.EmployeeService;
import com.hqins.agent.org.service.HonorSystemService;
import com.hqins.agent.org.service.PartnerEmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.ResolverStyle;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HonorSystemServiceImpl implements HonorSystemService {

    final DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Autowired
    EmployeeService employeeService;

    private final HonorSystemDao honorSystemDao;

    private final PartnerEmployeeService partnerEmployeeService;

    @Override
    public HonoraryTitleVO getTeamHonoraryTitleList(String saleTeamCode, String honorType, String year) {

        log.info("getTeamHonoraryTitleList param: saleTeamCode={}, honorType={}, year={}", saleTeamCode, honorType, year);

        // 1. 初始化返回对象
        HonoraryTitleVO honoraryTitleVO = new HonoraryTitleVO();

        // 2. 根据区代码查询 根据区代码获取组代码
        List<EmployeeOrgVO> employeeOrgVOList = employeeService.queryTeamEmpInfo(saleTeamCode);
        if (employeeOrgVOList == null || employeeOrgVOList.isEmpty()) {
            return honoraryTitleVO;
        }

        // 3. 获取代理人工号列表
        List<String> employeeCodeList = employeeOrgVOList.stream()
                .map(EmployeeOrgVO::getEmployeeCode)
                .collect(Collectors.toList());

        // 4. 查询贺报数据
        List<HonorCongratulationVO> honorCongratulationVOList = honorSystemDao.queryHonorCongratulationList(employeeCodeList, honorType, year);

        // 5. 根据不同类型处理数据
        if (StringUtils.isBlank(honorType)) {
            // 处理所有荣誉类型
            honoraryTitleVO.setHonoraryTitleInfoMap(processAllHonors(honorCongratulationVOList));
        } else if (HonorItemEnum.QXJYH.getValue().equals(honorType)) {
            honoraryTitleVO.setHonoraryTitleInfoMap(processMemberHonors(honorCongratulationVOList));
        } else if (HonorItemEnum.QYBSX.getValue().equals(honorType)) {
            honoraryTitleVO.setHonoraryTitleInfoMap(processSemesterHonors(honorCongratulationVOList));
        } else if (HonorItemEnum.MDRT.getValue().equals(honorType)) {
            honoraryTitleVO.setHonoraryTitleInfoMap(processMDRTHonors(honorCongratulationVOList));
        } else if (HonorItemEnum.QHJNX.getValue().equals(honorType)) {
            honoraryTitleVO.setHonoraryTitleInfoMap(processPremiumMasterHonors(honorCongratulationVOList));
        }

        return honoraryTitleVO;

    }

    // 处理所有荣誉类型
    private LinkedHashMap<String, List<HonoraryTitleInfoVO>> processAllHonors(List<HonorCongratulationVO> voList) {
        // 先按荣誉类型分组
        Map<String, List<HonorCongratulationVO>> groupedByType = voList.stream()
                .collect(Collectors.groupingBy(HonorCongratulationVO::getHonorType));

        LinkedHashMap<String, List<HonoraryTitleInfoVO>> result = new LinkedHashMap<>();

        // 对每种荣誉类型分别处理
        groupedByType.forEach((type, list) -> {
            switch (type) {
                case "QXJYH":
                    result.putAll(processMemberHonors(list));
                    break;
                case "QYBSX":
                    result.putAll(processSemesterHonors(list));
                    break;
                case "MDRT":
                    result.putAll(processMDRTHonors(list));
                    break;
                case "QHJNX":
                    result.putAll(processPremiumMasterHonors(list));
                    break;
                default:
                    // 其他未定义类型按默认方式处理
                    Map<String, List<HonoraryTitleInfoVO>> defaultMap = list.stream()
                            .collect(Collectors.groupingBy(
                                    HonorCongratulationVO::getHonorsAwards,
                                    Collectors.mapping(this::buildHonoraryTitleInfoVO,
                                            Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    this::sortByDateDesc
                                            )
                                    )
                            ));
                    result.putAll(defaultMap);
            }
        });

        return result;
    }

    // 处理琴星精英会贺报列表
    private LinkedHashMap<String, List<HonoraryTitleInfoVO>> processMemberHonors(List<HonorCongratulationVO> voList) {
        Map<String, List<HonoraryTitleInfoVO>> titleMap = voList.stream()
                .collect(Collectors.groupingBy(
                        HonorCongratulationVO::getHonorsAwards,
                        Collectors.mapping(this::buildHonoraryTitleInfoVO,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        this::sortByDateDesc
                                )
                        )
                ));

        LinkedHashMap<String, List<HonoraryTitleInfoVO>> orderedMap = new LinkedHashMap<>();
        // 预定义的会员等级顺序
        List<String> levels = Arrays.asList("初级会员", "黄金会员", "白金会员", "钻石会员", "顶尖会员", "终身会员");
        // 遍历预定义的等级
        levels.forEach(level -> {
            // 在titleMap的key中查找包含当前等级的key
            Optional<String> matchingKey = titleMap.keySet().stream()
                    .filter(key -> key.contains(level))
                    .findFirst();

            // 如果找到匹配的key，则添加到orderedMap中
            matchingKey.ifPresent(key -> orderedMap.put(level, titleMap.get(key)));

            // 如果没有找到匹配的key，则放入空列表
            if (!matchingKey.isPresent()) {
                orderedMap.put(level, Collections.emptyList());
            }
        });
        return orderedMap;
    }

    // 处理琴韵博识轩贺报列表
    private LinkedHashMap<String, List<HonoraryTitleInfoVO>> processSemesterHonors(List<HonorCongratulationVO> voList) {
        Map<String, List<HonoraryTitleInfoVO>> periodMap = voList.stream()
                .collect(Collectors.groupingBy(
                        congratulation -> {
                            LocalDateTime awardTime = parseDateSafe(congratulation.getHonorsAwardsTime());
                            return awardTime.getMonthValue() <= 6 ?
                                    "游学荣誉奖（上半年度）" :
                                    "游学荣誉奖（下半年度）";
                        },
                        Collectors.mapping(congratulation -> {
                            LocalDateTime awardsTime = parseDateSafe(congratulation.getHonorsAwardsTime());
                            return HonoraryTitleInfoVO.builder()
                                    .empCode(congratulation.getAgentCode())
                                    .empName(congratulation.getAgentName())
                                    .createDate(awardsTime.format(inputFormatter))
                                    .honorId(congratulation.getHonorId())
                                    .awardName("游学荣誉奖（" + (awardsTime.getMonthValue() <= 6 ? "上半年度" : "下半年度") + ")")
                                    .build();
                        }, Collectors.collectingAndThen(
                                Collectors.toList(),
                                this::sortByDateDesc
                        ))
                ));

        LinkedHashMap<String, List<HonoraryTitleInfoVO>> orderedMap = new LinkedHashMap<>();
        Arrays.asList(
                "游学荣誉奖（上半年度）",
                "游学荣誉奖（下半年度）"
        ).forEach(period -> orderedMap.put(period, periodMap.getOrDefault(period, Collections.emptyList())));
        return orderedMap;
    }

    // 处理百万年桌系列贺报列表
    private LinkedHashMap<String, List<HonoraryTitleInfoVO>> processMDRTHonors(List<HonorCongratulationVO> voList) {
        Map<String, List<HonoraryTitleInfoVO>> awardMap = voList.stream()
                .collect(Collectors.groupingBy(
                        HonorCongratulationVO::getHonorCode,
                        Collectors.mapping(this::buildHonoraryTitleInfoVO,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        this::sortByDateDesc
                                )
                        )
                ));

        // 定义奖项名称的映射关系（awardMap中的key → orderedMap中的key）
        Map<String, String> awardNameMapping = new HashMap<>();
        // 映射 "MDRT百万圆桌" → "MDRT普通会员"
        awardNameMapping.put("MDRT_MDRT", "MDRT普通会员");
        awardNameMapping.put("MDRT_COT", "COT内阁会员");
        awardNameMapping.put("MDRT_TOT", "TOT顶尖会员");

        LinkedHashMap<String, List<HonoraryTitleInfoVO>> orderedMap = new LinkedHashMap<>();
        Arrays.asList("MDRT普通会员", "COT内阁会员", "TOT顶尖会员")
                .forEach(desiredAward -> {
                    // 检查awardMap是否有直接匹配的key
                    if (awardMap.containsKey(desiredAward)) {
                        orderedMap.put(desiredAward, awardMap.get(desiredAward));
                    } else {
                        // 如果没有直接匹配，检查是否有映射关系
                        Optional<String> matchingKey = awardMap.keySet().stream()
                                .filter(key -> awardNameMapping.getOrDefault(key, key).equals(desiredAward))
                                .findFirst();

                        if (matchingKey.isPresent()) {
                            orderedMap.put(desiredAward, awardMap.get(matchingKey.get()));
                        } else {
                            orderedMap.put(desiredAward, Collections.emptyList());
                        }
                    }
                });
        return orderedMap;
    }

    // 处理琴海吉尼斯贺报列表
    private LinkedHashMap<String, List<HonoraryTitleInfoVO>> processPremiumMasterHonors(List<HonorCongratulationVO> voList) {
        Map<String, List<HonoraryTitleInfoVO>> awardMap = voList.stream()
                .collect(Collectors.groupingBy(
                        HonorCongratulationVO::getHonorsAwards,
                        Collectors.mapping(this::buildHonoraryTitleInfoVO,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        this::sortByDateDesc
                                )
                        )
                ));

        LinkedHashMap<String, List<HonoraryTitleInfoVO>> orderedMap = new LinkedHashMap<>();

        // 预定义的会员等级顺序
        List<String> levels = Arrays.asList("千万期缴大师", "千万标保大师");
        // 遍历预定义的等级
        levels.forEach(level -> {
            // 在titleMap的key中查找包含当前等级的key
            Optional<String> matchingKey = awardMap.keySet().stream()
                    .filter(key -> key.contains(level))
                    .findFirst();

            // 如果找到匹配的key，则添加到orderedMap中
            matchingKey.ifPresent(key -> orderedMap.put(level, awardMap.get(key)));

            // 如果没有找到匹配的key，则放入空列表
            if (!matchingKey.isPresent()) {
                orderedMap.put(level, Collections.emptyList());
            }
        });
        return orderedMap;
    }

    // 构建HonoraryTitleInfoVO
    private HonoraryTitleInfoVO buildHonoraryTitleInfoVO(HonorCongratulationVO congratulation) {
        return HonoraryTitleInfoVO.builder()
                .empCode(congratulation.getAgentCode())
                .empName(congratulation.getAgentName())
                .createDate(congratulation.getHonorsAwardsTime())
                .honorId(congratulation.getHonorId())
                .awardName(congratulation.getHonorsAwards())
                .build();
    }

    // 按日期降序排序
    private List<HonoraryTitleInfoVO> sortByDateDesc(List<HonoraryTitleInfoVO> list) {
        return list.stream()
                .sorted(Comparator.comparing(
                        vo -> parseDateSafe(vo.getCreateDate()),
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());
    }

    // 安全解析日期
    private LocalDateTime parseDateSafe(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, inputFormatter);
        } catch (DateTimeParseException e) {
            log.info("Invalid date format: {}", dateStr);
            return null;
        }
    }


    @Override
    public HonorListVO getHonorList(String empCode) {
        HonorListVO honorListVO = new HonorListVO();

        List<HonorQueryListVO> honorListVOList = honorSystemDao.queryHonorList(empCode);
        Map<String, List<HonorQueryListVO>> honorClassMap = honorListVOList.stream()
                .collect(Collectors.groupingBy(HonorQueryListVO::getHonorClass));

        for (String key : honorClassMap.keySet()) {
            List<HonorQueryListVO> value = honorClassMap.get(key);

            switch (key) {
                case HonorSystemConstants.QXJYH:
                    // 琴星精英会
                    honorListVO.setQinxingElite(HonorListVO.QinxingEliteVO.builder()
                            .status(value.get(0).getIsAchievedCurrent())
                            .currentLevel(value.get(0).getHonorsAwards())
                            .continuousStars(Integer.valueOf(value.get(0).getCurrentStar()))
                            .build());
                    break;
                case HonorSystemConstants.QYBSX:
                    // 琴韵博识轩
                    honorListVO.setQinyunScholar(HonorListVO.QinyunScholarVO.builder()
                            .status(value.get(0).getIsAchievedCurrent())
                            .period(getCurrentHalfYearPeriod())
                            .build());
                    break;
                case HonorSystemConstants.QHRYT:
                    // 琴辉荣耀堂
                    List<HonorListVO.GloryHallVO.AwardVO> awardsGlory = processAwards(value, item ->
                            HonorListVO.GloryHallVO.AwardVO.builder()
                                    .name(item.getHonorsAwards())
                                    .status(item.getIsAchievedCurrent())
                                    .build()
                    );
                    honorListVO.setGloryHall(HonorListVO.GloryHallVO.builder()
                            .year(value.get(0).getCheckYear())
                            .awards(awardsGlory)
                            .build());
                    break;
                case HonorSystemConstants.MDRT:
                    // 百万圆桌
                    List<HonorListVO.MillionTableVO.MdrAwardVO> awardsMillion = processAwards(value, item ->
                            HonorListVO.MillionTableVO.MdrAwardVO.builder()
                                    .name(item.getHonorsAwards())
                                    .status(item.getIsAchievedCurrent())
                                    .build()
                    );
                    honorListVO.setMillionTable(HonorListVO.MillionTableVO.builder()
                            .year(String.valueOf(Integer.parseInt(value.get(0).getCheckYear()) + 1) + "年度")
                            .awards(awardsMillion)
                            .build());
                    break;
                case HonorSystemConstants.QHJNX:
                    // 琴海吉尼斯
                    if (CollectionUtil.isNotEmpty(value)) {
                        List<HonorListVO.GuinnessVO.AchievementVO> awardsGuinness = new ArrayList<>();

                        // 遍历所有value条目
                        for (HonorQueryListVO vo : value) {
                            // 为每个条目单独查询指标
                            List<HonorPeriodMetricsVO> metrics = honorSystemDao.queryGuinnessMetrics(
                                    vo.getAgentCode(),
                                    vo.getCheckPeriod(),
                                    vo.getHonorCode(),
                                    CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()
                            );

                            // 处理每个条目的奖项
                            awardsGuinness.addAll(processAwards(
                                    Collections.singletonList(vo), // 改为处理单个条目
                                    item -> {
                                        String requiredValue = getRequiredValue(metrics, item.getHonorCode());
                                        return HonorListVO.GuinnessVO.AchievementVO.builder()
                                                .name(item.getHonorsAwards())
                                                .status(item.getIsAchievedCurrent())
                                                .requiredValue(requiredValue)
                                                .build();
                                    }
                            ));
                        }

                        honorListVO.setGuinness(HonorListVO.GuinnessVO.builder()
                                .year(value.get(0).getCheckYear())
                                .achievements(awardsGuinness)
                                .build());
                    }
                    break;
                default:
                    log.info("未知荣誉类型: {}", key);
                    break;
            }
        }

        return honorListVO;
    }

    // 处理过滤和转换逻辑
    private <T> List<T> processAwards(List<HonorQueryListVO> value, Function<HonorQueryListVO, T> converter) {
        List<HonorQueryListVO> filtered = value.stream()
                .filter(each -> Objects.equals(each.getIsAchievedCurrent(), "1"))
                .collect(Collectors.toList());

        List<T> awards = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(filtered)) {
            for (HonorQueryListVO item : filtered) {
                awards.add(converter.apply(item));
            }
        }
        return awards;
    }

    private String getRequiredValue(List<HonorPeriodMetricsVO> honorPeriodMetricsVOList, String honorsAwards) {
        for (HonorPeriodMetricsVO metricsVO : honorPeriodMetricsVOList) {
            if (metricsVO.getHonorCode().equals(honorsAwards)) {
                return metricsVO.getMetricsValue();
            }
        }
        return null;
    }


    private String getCurrentHalfYearPeriod() {
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();

        if (currentMonth <= 6) {
            return String.format("%d上半年度（1-6月）", currentYear);
        } else {
            return String.format("%d下半年度（7-12月）", currentYear);
        }
    }

    @Override
    public HonorDetailVO getHonorDetail(String empCode, String honorType) throws Exception {

        HonorDetailVO honorDetailVO = new HonorDetailVO();

        log.info("获取荣誉详情, empCode: {}, honorType: {}", empCode, honorType);

        switch (honorType) {
            case HonorSystemConstants.QXJYH:
                honorDetailVO.setQinxingEliteDetail(handleQXJYHDetail(empCode, honorType));
                break;

            case HonorSystemConstants.QYBSX:
                honorDetailVO.setQinyunScholardDetail(handleQYBSXDetail(empCode, honorType));
                break;

            case HonorSystemConstants.QHRYT:
                honorDetailVO.setGloryHallderDetail(handleQHRYTDetail(empCode, honorType));
                break;

            case HonorSystemConstants.MDRT:
                honorDetailVO.setMillionTabledDetail(handleMDRTDetail(empCode, honorType));
                break;

            case HonorSystemConstants.QHJNX:
                honorDetailVO.setGuinnessDetail(handleQHJNXDetail(empCode, honorType));
                break;

            default:
                log.warn("未支持的荣誉类型: {}", honorType);
                break;
        }

        return honorDetailVO;
    }

    private GloryHallDetailVO handleQHRYTDetail(String empCode, String honorType) throws Exception {

        GloryHallDetailVO gloryHallDetailVO = new GloryHallDetailVO();
        // 需新增获取当前周期方法
        String period = String.valueOf(LocalDate.now().getYear());
        // 初始化已获奖对象
        List<GloryHallDetailVO.AwardRecord> awardRecordList = new ArrayList<>();

        // 个人荣誉
        handleAgentDetail(empCode, honorType, gloryHallDetailVO, period, awardRecordList);

        // 团队荣誉
        handleTeamHonorsDetail(empCode, honorType, gloryHallDetailVO, period, awardRecordList);

        // 专项荣誉
        handleSpecialHonorsDetail(empCode, honorType, gloryHallDetailVO, period, awardRecordList);

        gloryHallDetailVO.setYear(period);

        // 取最近一次获奖名称
        if (CollectionUtil.isNotEmpty(awardRecordList)) {
            awardRecordList.sort(Comparator.comparing(
                    GloryHallDetailVO.AwardRecord::getAwardTime,
                    Comparator.reverseOrder()
            ));

            GloryHallDetailVO.AwardRecord firstAward = awardRecordList.get(0);
            if (firstAward != null && StringUtils.isNotBlank(firstAward.getAwardName())) {
                gloryHallDetailVO.setLastAward(firstAward.getAwardName());
            } else {
                gloryHallDetailVO.setLastAward(null);
            }
        } else {
            gloryHallDetailVO.setLastAward(null);
        }

        return gloryHallDetailVO;
    }

    private GuinnessDetailVO handleQHJNXDetail(String empCode, String honorType) {

        GuinnessDetailVO guinnessDetailVO = new GuinnessDetailVO();
        // 获取考核周期
        String period = String.valueOf(LocalDate.now().getYear());

        log.info("琴海吉尼斯详情查询,empCode:{},honorType:{},period:{}", empCode, honorType, period);
        // 查询荣誉信息
        List<HonorPeriodMetrics> honorPeriodMetrics = honorSystemDao.queryQHJNXDetail(empCode, honorType, period);

        // 根据荣誉项进行分组
        Map<String, List<HonorPeriodMetrics>> honorCodeMap = honorPeriodMetrics.stream()
                .collect(Collectors.groupingBy(HonorPeriodMetrics::getHonorCode));

        guinnessDetailVO.setPremiumMaster(processQHJNXHonorItem(honorCodeMap.get(HonorItemEnum.QHJNX_QWBBDS.getValue())));
        guinnessDetailVO.setStandardMaster(processQHJNXHonorItem(honorCodeMap.get(HonorItemEnum.QHJNX_QWQJDS.getValue())));

        guinnessDetailVO.setYear(period);

        return guinnessDetailVO;
    }

    private GuinnessDetailVO.MasterAwardVO processQHJNXHonorItem(List<HonorPeriodMetrics> metrics) {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildGuinnessDefaultNewcomer(); // 返回默认值
        }

        HonorPeriodMetrics metric = findMetricByType(metrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(metric.getMetricsValue())) {
            return buildGuinnessDefaultNewcomer(); // 返回默认值
        }

        String awardTime = null;
        if (StringUtils.isNotBlank(metric.getHonorsAwardsTime())) {
            awardTime = metric.getHonorsAwardsTime().split(" ")[0];
        }

        return GuinnessDetailVO.MasterAwardVO.builder()
                .status(metric.getIsAchievedCurrent())
                .honorId(metric.getHonorId())
                .requiredValue(metric.getMetricsValue())
                .awardTime(awardTime)
                .standard(HonorSystemConstants.QHJNX_STANDARD)
                .standardUnit(HonorSystemConstants.UNIT_WAN)
                .build();
    }

    private GuinnessDetailVO.MasterAwardVO buildGuinnessDefaultNewcomer() {
        return GuinnessDetailVO.MasterAwardVO.builder()
                .status("0")
                .requiredValue("0")
                .standard(HonorSystemConstants.QHJNX_STANDARD)
                .standardUnit(HonorSystemConstants.UNIT_WAN)
                .build();
    }

    private MDRTDetailVO handleMDRTDetail(String empCode, String honorType) {

        MDRTDetailVO mdrtDetailVO = new MDRTDetailVO();
        // 获取考核周期
        String period = String.valueOf(LocalDate.now().getYear());

        log.info("MDRT详情查询,empCode:{},honorType:{},period:{}", empCode, honorType, period);
        // 查询荣誉信息
        List<HonorPeriodMetrics> honorPeriodMetrics = honorSystemDao.queryMDRTDetail(empCode, honorType, period);

        // 根据荣誉项进行分组
        Map<String, List<HonorPeriodMetrics>> honorCodeMap = honorPeriodMetrics.stream()
                .collect(Collectors.groupingBy(HonorPeriodMetrics::getHonorCode));

        // 设置三个荣誉项数据
        mdrtDetailVO.setMDRT(processMDRTHonorItem(honorCodeMap.get(HonorItemEnum.MDRT_MDRT.getValue()), empCode, honorType, period, HonorItemEnum.MDRT_MDRT.getValue()));
        mdrtDetailVO.setCOT(processMDRTHonorItem(honorCodeMap.get(HonorItemEnum.MDRT_COT.getValue()), empCode, honorType, period, HonorItemEnum.MDRT_COT.getValue()));
        mdrtDetailVO.setTOT(processMDRTHonorItem(honorCodeMap.get(HonorItemEnum.MDRT_TOT.getValue()), empCode, honorType, period, HonorItemEnum.MDRT_TOT.getValue()));


        if (!CollectionUtils.isEmpty(honorPeriodMetrics)) {
            // 根据指标类型分组 取每个组指标值最高的一条数据
            List<HonorPeriodMetrics> collect = honorPeriodMetrics.stream()
                    // 按指标类型分组，保留每组指标值最大的记录
                    .collect(Collectors.groupingBy(HonorPeriodMetrics::getMetricsType,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(m ->
                                            new BigDecimal(StringUtils.isNotBlank(m.getMetricsValue()) ? m.getMetricsValue() : "0")
                                    )),
                                    Optional::get
                            )))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            // 设置最高佣金和最高保费及获奖年度
            for (HonorPeriodMetrics periodMetrics : collect) {
                if (CheckMetricsItemEnum.MDRT_COMMISSION_TOTAL.getValue().equals(periodMetrics.getMetricsType())) {
                    mdrtDetailVO.setAchievedCommission(periodMetrics.getMetricsValue());
                }
                if (CheckMetricsItemEnum.MDRT_PREMIUM_TOTAL.getValue().equals(periodMetrics.getMetricsType())) {
                    mdrtDetailVO.setAchievedPremium(periodMetrics.getMetricsValue());
                }
            }
        }
        // 年度+1 period为2025 则这里设置2026
        mdrtDetailVO.setYear(String.valueOf(Integer.parseInt(period) + 1));
        return mdrtDetailVO;
    }

    private MDRTDetailVO.MDRTAwardVO processMDRTHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) {

        String commissionStandard = null;
        String commissionStandardUnit = null;
        String premiumStandard = null;
        String premiumStandardUnit = null;
        switch (honorCode) {
            case HonorSystemConstants.MDRT_MDRT:
                commissionStandard = HonorSystemConstants.MDRT_MDRT_YJ_STANDARD;
                commissionStandardUnit = HonorSystemConstants.UNIT_WAN;
                premiumStandard = HonorSystemConstants.MDRT_MDRT_BF_STANDARD;
                premiumStandardUnit = HonorSystemConstants.UNIT_WAN;
                break;
            case HonorSystemConstants.MDRT_COT:
                commissionStandard = HonorSystemConstants.MDRT_COT_YJ_STANDARD;
                commissionStandardUnit = HonorSystemConstants.UNIT_WAN;
                premiumStandard = HonorSystemConstants.MDRT_COT_BF_STANDARD;
                premiumStandardUnit = HonorSystemConstants.UNIT_WAN;
                break;
            case HonorSystemConstants.MDRT_TOT:
                commissionStandard = HonorSystemConstants.MDRT_TOT_YJ_STANDARD;
                commissionStandardUnit = HonorSystemConstants.UNIT_WAN;
                premiumStandard = HonorSystemConstants.MDRT_TOT_BF_STANDARD;
                premiumStandardUnit = HonorSystemConstants.UNIT_WAN;
                break;
        }

        // 如果数据为空初始化数据返回默认值
        if (CollectionUtils.isEmpty(metrics)) {
            return MDRTDetailVO.MDRTAwardVO.builder()
                    .status("0")
                    .commissionStandard(commissionStandard)
                    .commissionStandardUnit(commissionStandardUnit)
                    .premiumStandard(premiumStandard)
                    .premiumStandardUnit(premiumStandardUnit)
                    .build();
        }

        return MDRTDetailVO.MDRTAwardVO.builder()
                .status(metrics.get(0).getIsAchievedCurrent())
                .honorId(metrics.get(0).getHonorId())
                .commissionStandard(commissionStandard)
                .commissionStandardUnit(commissionStandardUnit)
                .premiumStandard(premiumStandard)
                .premiumStandardUnit(premiumStandardUnit)
                .awardDate(metrics.get(0).getHonorsAwardsTime())
                .build();
    }

    private void handleSpecialHonorsDetail(String empCode, String honorType, GloryHallDetailVO gloryHallDetailVO, String period, List<GloryHallDetailVO.AwardRecord> awardRecordList) throws Exception {
        List<String> honorCodes = Arrays.asList(HonorItemEnum.QHRYT_NDFHXXSZX.getValue(), HonorItemEnum.QHRYT_NDFXBZZX.getValue(), HonorItemEnum.QHRYT_NDHKZX.getValue());

        // 查询并分组指标数据
        Map<String, List<HonorPeriodMetrics>> honorCodeMap = queryAndGroupMetrics(empCode, honorType, period, honorCodes, awardRecordList, null);

        GloryHallDetailVO.SpecialHonorsVO specialHonorsVO = new GloryHallDetailVO.SpecialHonorsVO();

        // 分红险销售之星
        specialHonorsVO.setDividendStar(processDividendStarHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_NDFHXXSZX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_NDFHXXSZX.getValue()));

        // 风险保障之星
        specialHonorsVO.setRiskCoverageStar(processRiskCoverageStarHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_NDFXBZZX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_NDFXBZZX.getValue()));

        // 获客之星
        specialHonorsVO.setCustomerAcquisitionStar(processCustomerAcquisitionStarHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_NDHKZX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_NDHKZX.getValue()));

        gloryHallDetailVO.setSpecialHonors(specialHonorsVO);
    }

    private GloryHallDetailVO.DividendStarVO processCustomerAcquisitionStarHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.NEW_BUSINESS_CUSTOMER_COUNT.getValue()).orElse(new HonorPeriodMetrics());
        HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());

        if (StringUtils.isEmpty(currentPremium.getMetricsValue()) || StringUtils.isEmpty(currentPolicyCount.getMetricsValue())) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        GloryHallDetailVO.DividendStarVO vo = setDividendStarVO(currentPremium, honorCode, currentPolicyCount);

        // 只有入围情况才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(vo.getShortlisted()) && HonorSystemConstants.SHORTLISTED.equals(currentPremium.getIsQualified())) {
            // 取第一名对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, "1");
            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.NEW_BUSINESS_CUSTOMER_COUNT.getValue()).get();

            String gapValue;
            String unit;
            // 如果钱数相同比件数
            if (currentPremium.getMetricsValue().equals(higherPremium.getMetricsValue())) {
                // 比较件数
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_JIAN;
            } else {
                // 比较客户数
                gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_CUSTOMER;
            }

            vo.setGapVO(buildSDJSGapVO(gapValue, unit, "1"));
        }

        return vo;
    }

    private GloryHallDetailVO.DividendStarVO processRiskCoverageStarHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.NEW_BUSINESS_RISK_COVERAGE_AMOUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());

        if (StringUtils.isEmpty(currentPremium.getMetricsValue()) || StringUtils.isEmpty(currentPolicyCount.getMetricsValue())) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        GloryHallDetailVO.DividendStarVO vo = setDividendStarVO(currentPremium, honorCode, currentPolicyCount);

        // 只有入围情况才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(vo.getShortlisted()) && !HonorSystemConstants.SHORTLISTED.equals(currentPremium.getCurrentPosition())) {

            // 取第一名对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, "1");
            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.NEW_BUSINESS_RISK_COVERAGE_AMOUNT_TOTAL.getValue()).get();

            String gapValue;
            String unit;
            // 如果钱数相同比件数
            if (currentPremium.getMetricsValue().equals(higherPremium.getMetricsValue())) {
                // 比较件数
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.NEW_BUSINESS_ANNUAL_POLICY_COUNT_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_JIAN;
            } else {
                // 比较钱数
                gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            }

            vo.setGapVO(buildSDJSGapVO(gapValue, unit, "1"));
        }

        return vo;
    }

    private GloryHallDetailVO.DividendStarVO processDividendStarHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.DIVIDEND_PRODUCT_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.DIVIDEND_INSURANCE_POLICY_COUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());

        if (StringUtils.isEmpty(currentPremium.getMetricsValue()) || StringUtils.isEmpty(currentPolicyCount.getMetricsValue())) {
            return buildDividendStarHonorItemDefaultNewcomer(honorCode);
        }

        GloryHallDetailVO.DividendStarVO vo = setDividendStarVO(currentPremium, honorCode, currentPolicyCount);

        // 只有入围情况才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(vo.getShortlisted()) && !HonorSystemConstants.SHORTLISTED.equals(currentPremium.getCurrentPosition())) {

            // 取第一名对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, "1");
            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.DIVIDEND_PRODUCT_PREMIUM_STANDARD_TOTAL.getValue()).get();

            String gapValue;
            String unit;
            // 如果钱数相同比件数
            if (currentPremium.getMetricsValue().equals(higherPremium.getMetricsValue())) {
                // 比较件数
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.DIVIDEND_INSURANCE_POLICY_COUNT_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_JIAN;
            } else {
                // 比较钱数
                gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            }

            vo.setGapVO(buildSDJSGapVO(gapValue, unit, "1"));
        }

        return vo;

    }

    private GloryHallDetailVO.DividendStarVO buildDividendStarHonorItemDefaultNewcomer(String honorCode) {

        return GloryHallDetailVO.DividendStarVO.builder()
                .status("0")
                .currentRank("0")
                .shortlisted("0")
                .shortlistedCount("0")
                .standard(HonorSystemConstants.QHRYT_NDFHXXSZX_STANDARD)
                .standardUnit(HonorSystemConstants.UNIT_JIAN)
                .completedPolicyCount("0")
                .totalPremiumAmount("0")
                .build();
    }

    private GloryHallDetailVO.DividendStarVO setDividendStarVO(HonorPeriodMetrics current, String honorCode, HonorPeriodMetrics currentCount) {
        GloryHallDetailVO.DividendStarVO vo = new GloryHallDetailVO.DividendStarVO();

        // 基础字段设置
        vo.setStatus(current.getIsAchievedCurrent());
        vo.setCurrentRank(current.getCurrentPosition());
        vo.setShortlisted(current.getIsQualified());
        vo.setShortlistedCount(current.getTotalQualifiedCount());
        vo.setCompletedPolicyCount(currentCount.getMetricsValue());
        if (honorCode.equals(HonorItemEnum.QHRYT_NDHKZX.getValue())) {
            vo.setTotalPremiumAmount(String.valueOf((int) Double.parseDouble(current.getMetricsValue())));
        } else {
            vo.setTotalPremiumAmount(current.getMetricsValue());
        }
        vo.setStandard(HonorSystemConstants.QHRYT_NDFHXXSZX_STANDARD);
        vo.setStandardUnit(HonorSystemConstants.UNIT_JIAN);


        return vo;
    }

    private void handleTeamHonorsDetail(String empCode, String honorType, GloryHallDetailVO gloryHallDetailVO, String period, List<GloryHallDetailVO.AwardRecord> awardRecordList) throws Exception {
        List<String> honorCodes = Arrays.asList(HonorItemEnum.QHRYT_NDCZTD.getValue(), HonorItemEnum.QHRYT_NDRYTD.getValue());

        // 获取团队代码 根据团队代码查询团队荣誉
        EmployeeVO employeeVO = null;
        if (empCode.startsWith("S")) {
            // 督导账号查询区团队编码
            employeeVO = partnerEmployeeService.getEmployeeVO(empCode);
        } else {
            // 根据代理人查询区团队编码
            employeeVO = partnerEmployeeService.getEmpAreaCode(empCode);
        }

        if (StringUtils.isEmpty(employeeVO.getTeamCode())) {
            throw new Exception("获取团队代码失败");
        }
        // 如果不是团队长就返回
        if (!Objects.equals(employeeVO.getCode(), empCode)) {
            return;
        }

        // 查询并分组指标数据
        Map<String, List<HonorPeriodMetrics>> honorCodeMap = queryAndGroupMetrics(null, honorType, period, honorCodes, awardRecordList, employeeVO.getTeamCode());

        GloryHallDetailVO.TeamHonorsVO teamHonors = new GloryHallDetailVO.TeamHonorsVO();

        // 年度荣耀团队
        teamHonors.setGloriousTeam(processGloriousTeamHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_NDRYTD.getValue()), empCode, honorType, period));
        // 年度成长团队
        teamHonors.setGrowthTeam(processGrowthTeamHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_NDCZTD.getValue()), empCode, honorType, period));

        gloryHallDetailVO.setTeamHonors(teamHonors);
    }

    private GloryHallDetailVO.GrowthTeamVO processGrowthTeamHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildGrowthTeamHonorItemDefaultNewcomer();
        }

        HonorPeriodMetrics currentTeam = findMetricByType(metrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_COUNT.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(currentTeam.getMetricsValue())) {
            return buildGrowthTeamHonorItemDefaultNewcomer();
        }
        GloryHallDetailVO.GrowthTeamVO vo = new GloryHallDetailVO.GrowthTeamVO();

        // 基础字段设置
        vo.setStatus(currentTeam.getIsAchievedCurrent());
        vo.setCurrentRank(currentTeam.getCurrentPosition());
        vo.setQualifiedTeams(currentTeam.getTotalQualifiedCount());
        vo.setAchievedMembers(String.valueOf((int) Double.parseDouble(currentTeam.getMetricsValue())));

        if (!HonorSystemConstants.SHORTLISTED.equals(vo.getCurrentRank())) {
            // 差距分析
            int currentRank = Integer.parseInt(currentTeam.getCurrentPosition());
            String lastPosition = String.valueOf(Math.max(0, currentRank - 1));
            // 根据名次取对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentTeam.getHonorCode(), period, lastPosition);
            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherTeam = findMetricByType(higherMetrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_COUNT.getValue()).get();

            String gapValue = null;
            String unit = null;
            if (currentTeam.getMetricsValue().equals(higherTeam.getMetricsValue())) {
                // 比较有效增员钱数
                HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).get();
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            } else {
                // 比较有效人力
                gapValue = subtractStringNumbers(higherTeam.getMetricsValue(), currentTeam.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_PERSON;
            }

            String message;
            int last = Integer.parseInt(lastPosition);
            if (last <= 5) {
                message = "距离第" + numberToChinese(Integer.parseInt(lastPosition)) + "名";
            } else {
                message = "距离前一名";
            }

            vo.setGapVO(GloryHallDetailVO.GapVO.builder()
                    .message(message)
                    .value(gapValue)
                    .unit(unit)
                    .build());
        }
        return vo;
    }

    private GloryHallDetailVO.GrowthTeamVO buildGrowthTeamHonorItemDefaultNewcomer() {
        return GloryHallDetailVO.GrowthTeamVO.builder()
                .status("0")
                .currentRank("0")
                .qualifiedTeams("0")
                .achievedMembers("0")
                .build();
    }

    private GloryHallDetailVO.GloriousTeamVO processGloriousTeamHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildGloriousTeamHonorItemDefaultNewcomer();
        }

        HonorPeriodMetrics currentTeam = findMetricByType(metrics, CheckMetricsItemEnum.TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(currentTeam.getMetricsValue())) {
            return buildGloriousTeamHonorItemDefaultNewcomer();
        }

        GloryHallDetailVO.GloriousTeamVO vo = new GloryHallDetailVO.GloriousTeamVO();
        // 基础字段设置
        vo.setStatus(currentTeam.getIsAchievedCurrent());
        vo.setCurrentRank(currentTeam.getCurrentPosition());
        vo.setShortlisted(currentTeam.getIsQualified());
        vo.setStandard(HonorSystemConstants.QHRYT_NDRYTD_STANDARD);
        vo.setStandardUnit(HonorSystemConstants.UNIT_WAN);
        vo.setQualifiedTeams(currentTeam.getTotalQualifiedCount());
        vo.setTeamPremium(currentTeam.getMetricsValue());

        // 只有入围情况才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(currentTeam.getIsQualified()) && !HonorSystemConstants.SHORTLISTED.equals(vo.getCurrentRank())) {
            // 根据当前名次判断 取第几名
            int currentRank = Integer.parseInt(currentTeam.getCurrentPosition());
            String lastPosition = String.valueOf(Math.max(0, currentRank - 1));

            // 根据名次取对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentTeam.getHonorCode(), period, lastPosition);

            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherTeam = findMetricByType(higherMetrics, CheckMetricsItemEnum.TEAM_ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).get();

            String gapValue = null;
            String unit = null;
            // 如果钱数相同比件数
            if (currentTeam.getMetricsValue().equals(higherTeam.getMetricsValue())) {
                // 比较件数
                HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.TEAM_ANNUAL_POLICY_COUNT_TOTAL.getValue()).get();
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.TEAM_ANNUAL_POLICY_COUNT_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_JIAN;
            } else {
                // 比较钱数
                gapValue = subtractStringNumbers(higherTeam.getMetricsValue(), currentTeam.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            }
            String message;
            int last = Integer.parseInt(lastPosition);
            if (last <= 5) {
                message = "距离第" + numberToChinese(Integer.parseInt(lastPosition)) + "名";
            } else {
                message = "距离前一名";
            }

            vo.setGapVO(GloryHallDetailVO.GapVO.builder()
                    .message(message)
                    .value(gapValue)
                    .unit(unit)
                    .build());
        }

        return vo;

    }

    private GloryHallDetailVO.GloriousTeamVO buildGloriousTeamHonorItemDefaultNewcomer() {
        return GloryHallDetailVO.GloriousTeamVO.builder()
                .status("0")
                .currentRank("0")
                .shortlisted("0")
                .qualifiedTeams("0")
                .standard(HonorSystemConstants.QHRYT_NDRYTD_STANDARD)
                .standardUnit(HonorSystemConstants.UNIT_WAN)
                .teamPremium("0")
                .build();
    }


    private void handleAgentDetail(String empCode, String honorType, GloryHallDetailVO gloryHallDetailVO, String period, List<GloryHallDetailVO.AwardRecord> awardRecordList) throws Exception {

        // 初始化个人荣誉项 一次查出所有指标数据
        List<String> honorCodes = Arrays.asList(HonorItemEnum.QHRYT_SDXRMX.getValue(), HonorItemEnum.QHRYT_SDJSMX.getValue(), HonorItemEnum.QHRYT_SDZYMX.getValue(),
                HonorItemEnum.QHRYT_RYTTZ.getValue(), HonorItemEnum.QHRYT_NDYCMX.getValue(), HonorItemEnum.QHRYT_JSZX.getValue());

        // 查询并分组指标数据
        Map<String, List<HonorPeriodMetrics>> honorCodeMap = queryAndGroupMetrics(empCode, honorType, period, honorCodes, awardRecordList, null);

        // 构建个人荣誉
        GloryHallDetailVO.PersonalHonorsVO personalHonors = new GloryHallDetailVO.PersonalHonorsVO();
        // 查询代理人入司时间
        HonorEmpVO empInfo = employeeService.queryEmpInfo(empCode);
        // 判断是否为新人
        if (null != empInfo && isValidDate(empInfo.getEntryTime())) {
            // 十大新人明星
            personalHonors.setTopNewcomer(processHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_SDXRMX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_SDXRMX.getValue()));
        }
        // 十大件数明星
        personalHonors.setTopPolicyCount(processSDJSHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_SDJSMX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_SDJSMX.getValue()));
        // 十大展业明星
        personalHonors.setTopPerformer(processHonorItem(honorCodeMap.get(HonorItemEnum.QHRYT_SDZYMX.getValue()), empCode, honorType, period, HonorItemEnum.QHRYT_SDZYMX.getValue()));

        // 琴辉荣誉堂
        personalHonors.setHallMaster(processHallMaster(honorCodeMap.get(HonorItemEnum.QHRYT_RYTTZ.getValue()), empCode, honorType, period));

        // 年度引才明星
        personalHonors.setTalentRecruiter(processTalentRecruiter(honorCodeMap.get(HonorItemEnum.QHRYT_NDYCMX.getValue()), empCode, honorType, period));

        // 晋升之星
        personalHonors.setPromotionStar(processPromotionStar(honorCodeMap.get(HonorItemEnum.QHRYT_JSZX.getValue())));

        gloryHallDetailVO.setPersonalHonors(personalHonors);
    }

    public static boolean isValidDate(String input) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("uuuu-MM-dd")
                    .withResolverStyle(ResolverStyle.STRICT); // 严格模式（拒绝 2024-02-30）
            LocalDate date = LocalDate.parse(input, formatter);
            LocalDate target = LocalDate.of(2024, 10, 31);
            return date.isAfter(target);
        } catch (Exception e) {
            return false; // 格式无效或解析失败
        }
    }

    private GloryHallDetailVO.TopNewcomerVO processSDJSHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildDefaultNewcomer(honorCode);
        }
        /**
         * 按照逻辑十大件数明星会有三个指标 页面展示和下面逻辑判断只需 个人累计期缴件数和个人累计期交标准保费两个指标
         */
        HonorPeriodMetrics current = findMetricByType(metrics, CheckMetricsItemEnum.ANNUAL_POLICY_COUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        HonorPeriodMetrics premiumCurrent = findMetricByType(metrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());

        if (StringUtils.isEmpty(current.getMetricsValue()) || StringUtils.isEmpty(premiumCurrent.getMetricsValue())) {
            return buildDefaultNewcomer(honorCode);
        }

        GloryHallDetailVO.TopNewcomerVO vo = new GloryHallDetailVO.TopNewcomerVO();
        // 基础字段设置
        vo.setStatus(current.getIsAchievedCurrent());
        vo.setCurrentRank(current.getCurrentPosition());
        vo.setShortlisted(current.getIsQualified());
        vo.setStandard(HonorSystemConstants.QHRYT_SDJSMX_STANDARD);
        vo.setStandardUnit(HonorSystemConstants.UNIT_JIAN);
        vo.setShortlistedCount(current.getTotalQualifiedCount());
        vo.setMetricsValue(String.valueOf((int) Double.parseDouble(current.getMetricsValue())));

        // 只有入围情况 且 不是第一名 才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(current.getIsQualified()) && !HonorSystemConstants.SHORTLISTED.equals(current.getCurrentPosition())) {
            // 根据当前名次判断 取第几名
            int currentRank = Integer.parseInt(current.getCurrentPosition());
            String lastPosition = String.valueOf(Math.max(0, currentRank - 1));

            // 根据名次取对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, current.getHonorCode(), period, lastPosition);

            if (CollectionUtils.isEmpty(higherMetrics)) {
                throw new Exception("获取排名数据失败");
            }

            HonorPeriodMetrics policyHigherMetrics = findMetricByType(higherMetrics, CheckMetricsItemEnum.ANNUAL_POLICY_COUNT_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
            HonorPeriodMetrics premiumHigherMetrics = findMetricByType(higherMetrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());

            String gapValue;
            String unit;
            if (policyHigherMetrics.getMetricsValue().equals(current.getMetricsValue())) {
                // 如果件数相同比钱数
                gapValue = subtractStringNumbers(premiumHigherMetrics.getMetricsValue(), premiumCurrent.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            } else {
                gapValue = subtractStringNumbers(policyHigherMetrics.getMetricsValue(), current.getMetricsValue());
                unit = HonorSystemConstants.UNIT_JIAN;
            }
            vo.setGapVO(buildSDJSGapVO(gapValue, unit, lastPosition));
        }

        return vo;
    }

    /**
     * @param gapValue     具体差距数值
     * @param unit         差距单位
     * @param lastPosition 上一名的排名
     * @return
     */
    private GloryHallDetailVO.GapVO buildSDJSGapVO(String gapValue, String unit, String lastPosition) {
        String message;
        int last = Integer.parseInt(lastPosition);
        if (last <= 10) {
            message = "距离第" + numberToChinese(Integer.parseInt(lastPosition)) + "名";
        } else {
            message = "距离前一名";
        }

        return GloryHallDetailVO.GapVO.builder()
                .message(message)
                .value(gapValue)
                .unit(unit)
                .build();
    }

    // 数字转中文大写工具方法
    public static String numberToChinese(int number) {
        String[] chineseNumbers = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

        if (number >= 0 && number <= 10) {
            return chineseNumbers[number];
        } else if (number > 10 && number < 20) {
            return "十" + chineseNumbers[number % 10];
        } else if (number >= 20 && number <= 99) {
            int tens = number / 10;
            int units = number % 10;
            return chineseNumbers[tens] + "十" + (units != 0 ? chineseNumbers[units] : "");
        }
        return String.valueOf(number); // 超出范围返回原数字
    }

    private GloryHallDetailVO.PromotionStarVO processPromotionStar(List<HonorPeriodMetrics> metrics) {

        if (CollectionUtils.isEmpty(metrics) || "0".equals(metrics.get(0).getIsAchievedCurrent())) {
            return buildPromotionStarDefaultNewcomer(); // 返回默认值
        }
        return GloryHallDetailVO.PromotionStarVO.builder()
                .status(metrics.get(0).getIsAchievedCurrent())
                .awardTime(Optional.ofNullable(metrics.get(0).getHonorsAwardsTime())
                        .map(timeStr -> {
                            LocalDateTime dt = parseDateSafe(timeStr);
                            return (dt != null) ? dt.getYear() + "年度" : "";
                        })
                        .orElse(""))
                .build();
    }

    private GloryHallDetailVO.PromotionStarVO buildPromotionStarDefaultNewcomer() {
        return GloryHallDetailVO.PromotionStarVO.builder()
                .status("0")
                .awardTime(String.valueOf(LocalDate.now().getYear()) + "年度")
                .build();
    }

    private GloryHallDetailVO.TalentRecruiterVO processTalentRecruiter(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildTalentRecruiterDefaultNewcomer();
        }

        // 获取对应指标信息
        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_COUNT.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(currentPremium.getMetricsValue())) {
            return buildTalentRecruiterDefaultNewcomer(); // 返回默认值
        }

        GloryHallDetailVO.TalentRecruiterVO vo = new GloryHallDetailVO.TalentRecruiterVO();

        vo.setStatus(currentPremium.getIsAchievedCurrent());
        vo.setCurrentRank(currentPremium.getCurrentPosition());
        vo.setShortlistedCount(currentPremium.getTotalQualifiedCount());
        vo.setAchievedMembers(String.valueOf((int) Double.parseDouble(currentPremium.getMetricsValue())));

        if (!HonorSystemConstants.SHORTLISTED.equals(currentPremium.getCurrentPosition())) {
            // 差距分析  根据当前排名查询上一名 指标值相减如果为0 取第二指标相减
            // 根据当前名次判断 取第几名
            int currentRank = Integer.parseInt(currentPremium.getCurrentPosition());
            String lastPosition = String.valueOf(Math.max(0, currentRank - 1));

            // 根据名次取对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, lastPosition);
            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_COUNT.getValue()).get();

            String gapValue;
            String unit;
            // 如果增员人数相同比有效增员期缴标准保费
            if (currentPremium.getMetricsValue().equals(higherPremium.getMetricsValue())) {
                // 比较有效增员期缴标准保费
                HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_PREMIUM_STANDARD.getValue()).get();
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.EFFECTIVE_NEW_RECRUIT_PREMIUM_STANDARD.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            } else {
                // 比较有效增员人数
                gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
                gapValue = String.valueOf((int) Double.parseDouble(gapValue));
                unit = HonorSystemConstants.UNIT_PERSON;
            }

            String message;
            int last = Integer.parseInt(lastPosition);
            if (last <= 10) {
                message = "距离第" + numberToChinese(Integer.parseInt(lastPosition)) + "名";
            } else {
                message = "距离前一名";
            }

            vo.setGapVO(GloryHallDetailVO.GapVO.builder()
                    .message(message)
                    .value(gapValue)
                    .unit(unit)
                    .build());
        }

        return vo;
    }

    private GloryHallDetailVO.TalentRecruiterVO buildTalentRecruiterDefaultNewcomer() {

        return GloryHallDetailVO.TalentRecruiterVO.builder()
                .status("0")
                .currentRank("0")
                .shortlistedCount("0")
                .achievedMembers("0")
                .build();
    }

    private GloryHallDetailVO.HallMasterVO processHallMaster(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildHallMasterDefaultNewcomer();
        }

        // 获取对应指标信息
        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(currentPremium.getMetricsValue())) {
            return buildHallMasterDefaultNewcomer(); // 返回默认值
        }

        GloryHallDetailVO.HallMasterVO vo = new GloryHallDetailVO.HallMasterVO();
        // 基础字段设置
        vo.setStatus(currentPremium.getIsAchievedCurrent());
        vo.setCurrentRank(currentPremium.getCurrentPosition());
        vo.setShortlistedCount(currentPremium.getTotalQualifiedCount());
        vo.setAchievedPremium(currentPremium.getMetricsValue());
        vo.setStandard(calculateExceededPercentage(currentPremium.getCurrentPosition(), currentPremium.getTotalQualifiedCount()));

        if (!HonorSystemConstants.SHORTLISTED.equals(currentPremium.getCurrentPosition())) {
            // 获取第一名指标 分析差距
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, "1");

            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).get();

            // 比较钱数
            String gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
            String unit = HonorSystemConstants.UNIT_YUAN;

            vo.setGapVO(buildSDJSGapVO(gapValue, unit, "1"));
        }

        return vo;
    }

    private GloryHallDetailVO.HallMasterVO buildHallMasterDefaultNewcomer() {

        return GloryHallDetailVO.HallMasterVO.builder()
                .status("0")
                .currentRank("0")
                .shortlistedCount("0")
                .achievedPremium("0")
                .standard("0")
                .build();
    }

    // 计算超过多少同行
    private String calculateExceededPercentage(String currentPosition, String totalQualifiedCount) {

        if (StringUtils.isBlank(currentPosition) || StringUtils.isBlank(totalQualifiedCount)) {
            return "0.0";
        }

        try {
            int currentRank = Integer.parseInt(currentPosition);
            int totalPeers = Integer.parseInt(totalQualifiedCount);

            // 边界条件：分母必须 >=1（即总人数至少2人时计算才有意义）
            if (totalPeers <= 1 || currentRank <= 0 || currentRank > totalPeers) {
                return "0.0";
            }

            double percentage = (totalPeers - currentRank) * 100.0 / (totalPeers - 1);
            return String.format(Locale.US, "%.1f", percentage);

        } catch (NumberFormatException e) {
            return "0.0";
        }
    }


    private Optional<HonorPeriodMetrics> findMetricByType(List<HonorPeriodMetrics> metrics,
                                                          String metricsType) {
        return metrics.stream()
                .filter(m -> metricsType.equals(m.getMetricsType()))
                .findFirst();
    }

    // 构建GapVO封装
    private GloryHallDetailVO.GapVO buildGapVO(int currentRank, String gapValue, String honorCode) {
        String message;
        switch (currentRank) {
            case 11:
                message = "距离第十名";
                break;
            default:
                if (currentRank <= 10) {
                    message = "距离第一名";
                } else {
                    message = "距离前一名";
                }
                break;
        }
        return GloryHallDetailVO.GapVO.builder()
                .message(message)
                .value(gapValue)
                .unit(HonorSystemConstants.UNIT_WAN)
                .build();
    }

    // 默认值构建
    private GloryHallDetailVO.TopNewcomerVO buildDefaultNewcomer(String honorCode) {

        // 0 默认值
        String standard = "0";
        String standardUnit = "0";
        if (HonorItemEnum.QHRYT_SDXRMX.getValue().equals(honorCode)) {
            standard = HonorSystemConstants.QHRYT_SDXRMX_STANDARD;
            standardUnit = HonorSystemConstants.UNIT_WAN;
        } else if (HonorItemEnum.QHRYT_SDJSMX.getValue().equals(honorCode)) {
            standard = HonorSystemConstants.QHRYT_SDJSMX_STANDARD;
            standardUnit = HonorSystemConstants.UNIT_JIAN;
        } else if (HonorItemEnum.QHRYT_SDZYMX.getValue().equals(honorCode)) {
            standard = HonorSystemConstants.QHRYT_SDZYMX_STANDARD;
            standardUnit = HonorSystemConstants.UNIT_WAN;
        }

        return GloryHallDetailVO.TopNewcomerVO.builder()
                .status("0")
                .currentRank("0")
                .shortlisted("0")
                .standard(standard)
                .standardUnit(standardUnit)
                .shortlistedCount("0")
                .metricsValue("0")
                .build();
    }

    // 通用荣誉项处理方法
    private GloryHallDetailVO.TopNewcomerVO processHonorItem(List<HonorPeriodMetrics> metrics, String empCode, String honorType, String period, String honorCode) throws Exception {

        if (CollectionUtils.isEmpty(metrics)) {
            return buildDefaultNewcomer(honorCode);
        }

        HonorPeriodMetrics currentPremium = findMetricByType(metrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).orElse(new HonorPeriodMetrics());
        if (StringUtils.isEmpty(currentPremium.getMetricsValue())) {
            return buildDefaultNewcomer(honorCode); // 返回默认值
        }
        GloryHallDetailVO.TopNewcomerVO vo = new GloryHallDetailVO.TopNewcomerVO();

        // 基础字段设置
        vo.setStatus(currentPremium.getIsAchievedCurrent());
        vo.setCurrentRank(currentPremium.getCurrentPosition());
        vo.setShortlisted(currentPremium.getIsQualified());
        vo.setShortlistedCount(currentPremium.getTotalQualifiedCount());
        vo.setMetricsValue(currentPremium.getMetricsValue());

        if (HonorItemEnum.QHRYT_SDXRMX.getValue().equals(honorCode)) {
            vo.setStandard(HonorSystemConstants.QHRYT_SDXRMX_STANDARD);
            vo.setStandardUnit(HonorSystemConstants.UNIT_WAN);
        } else if (HonorItemEnum.QHRYT_SDZYMX.getValue().equals(honorCode)) {
            vo.setStandard(HonorSystemConstants.QHRYT_SDZYMX_STANDARD);
            vo.setStandardUnit(HonorSystemConstants.UNIT_WAN);
        }

        // 只有入围情况才看差距
        if (HonorSystemConstants.SHORTLISTED.equals(currentPremium.getIsQualified()) && !HonorSystemConstants.SHORTLISTED.equals(currentPremium.getCurrentPosition())) {

            // 根据当前名次判断 取第几名
            int currentRank = Integer.parseInt(currentPremium.getCurrentPosition());
            String lastPosition = String.valueOf(Math.max(0, currentRank - 1));

            // 根据名次取对应指标
            List<HonorPeriodMetrics> higherMetrics = honorSystemDao.queryImmediateHigherMetrics(honorType, currentPremium.getHonorCode(), period, lastPosition);

            if (higherMetrics.isEmpty()) {
                throw new Exception("获取排名数据失败");
            }
            HonorPeriodMetrics higherPremium = findMetricByType(higherMetrics, CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue()).get();

            String gapValue;
            String unit;
            // 如果钱数相同比件数
            if (currentPremium.getMetricsValue().equals(higherPremium.getMetricsValue())) {
                // 比较件数
                HonorPeriodMetrics currentPolicyCount = findMetricByType(metrics, CheckMetricsItemEnum.UNDERWRITTEN_POLICY_COUNT_TOTAL.getValue()).get();
                HonorPeriodMetrics higherPolicyCount = findMetricByType(higherMetrics, CheckMetricsItemEnum.UNDERWRITTEN_POLICY_COUNT_TOTAL.getValue()).get();
                gapValue = subtractStringNumbers(higherPolicyCount.getMetricsValue(), currentPolicyCount.getMetricsValue());
                unit = HonorSystemConstants.UNIT_JIAN;
            } else {
                // 比较钱数
                gapValue = subtractStringNumbers(higherPremium.getMetricsValue(), currentPremium.getMetricsValue());
                unit = HonorSystemConstants.UNIT_YUAN;
            }
            vo.setGapVO(buildSDJSGapVO(gapValue, unit, lastPosition));
        }

        return vo;
    }


    private Map<String, List<HonorPeriodMetrics>> queryAndGroupMetrics(String empCode, String honorType, String period,
                                                                       List<String> honorCodes, List<GloryHallDetailVO.AwardRecord> awardRecordList, String teamCode) {

        log.info("琴辉荣誉堂指标查询 empCode:{}, honorType:{}, period:{}, honorCodes:{}, awardRecordList:{}", empCode, honorType, period, honorCodes, JSON.toJSON(awardRecordList));
        List<HonorPeriodMetrics> metricsList = honorSystemDao.queryGRRYPeriodMetrics(empCode, honorType, honorCodes, period, teamCode);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 获取每个荣誉类型的最新获奖记录
        Map<String, Optional<HonorPeriodMetrics>> latestAwards = metricsList.stream()
                .filter(o -> o.getIsAchievedCurrent().equals("1"))
                .collect(Collectors.groupingBy(
                        HonorPeriodMetrics::getHonorCode,
                        Collectors.maxBy(Comparator.comparing(
                                m -> LocalDateTime.parse(m.getHonorsAwardsTime(), formatter),
                                Comparator.reverseOrder()
                        ))
                ));

        // 填充到awardRecordList
        latestAwards.forEach((honorCode, optionalMetrics) ->
                optionalMetrics.ifPresent(metrics ->
                        awardRecordList.add(new GloryHallDetailVO.AwardRecord(
                                metrics.getHonorsAwardsTime(),
                                metrics.getHonorsAwards()
                        ))
                )
        );

        return metricsList.stream().collect(Collectors.groupingBy(HonorPeriodMetrics::getHonorCode));
    }


    private QinyunScholarDetailVO handleQYBSXDetail(String empCode, String honorType) {

        // 获取考核周期并验证数据
        List<String> assessmentPeriods = getAssessmentPeriods();

        log.info("QYBSX当前考核周期数据 periods:{} empCode:{} honorType:{}", JSON.toJSON(assessmentPeriods), empCode, honorType);
        List<HonorAssessmentHistory> currentDetails = honorSystemDao.queryQYBSXDetail(empCode, honorType, assessmentPeriods);
        List<HonorPeriodMetrics> periodMetrics = honorSystemDao.queryQYBSXPeriodMetrics(empCode, honorType, assessmentPeriods);

        if (invalidQYBSXData(currentDetails, periodMetrics)) {
            return buildQYBSXDefaultNewcomer(assessmentPeriods);
        }

        // 处理赛期数据
        Map<String, List<HonorPeriodMetrics>> periodMetricsMap = groupMetricsByPeriod(periodMetrics);
        Map<String, List<HonorAssessmentHistory>> detailMap = groupDetailsByPeriod(currentDetails);

        // 构建赛期数据
        QinyunScholarDetailVO scholarDetail = buildScholarDetail(assessmentPeriods, periodMetricsMap, detailMap);

        return scholarDetail;
    }

    private QinyunScholarDetailVO buildQYBSXDefaultNewcomer(List<String> assessmentPeriods) {

        // 初始化返回对象
        return QinyunScholarDetailVO.builder()
                .currentSeason(QinyunScholarDetailVO.SeasonDataVO.builder()
                        .status("0")
                        .year(convertToHalfYearPeriod(assessmentPeriods.get(0)))
                        .accumulatedPremium(QinyunScholarDetailVO.AccumulatedPremiumVO.builder()
                                .currentPrem("0")
                                .benchmarkPremium(HonorSystemConstants.QYBSX_BF_STANDARD)
                                .metricsStandard(HonorSystemConstants.UNIT_WAN)
                                .build())
                        .monthlyContinuity(QinyunScholarDetailVO.MonthlyContinuityVO.builder()
                                .monthlyStatus(new ArrayList<>())
                                .currentMonths("0")
                                .build())
                        .build())
                .build();
    }

    // 数据校验方法
    private boolean invalidQYBSXData(List<HonorAssessmentHistory> details, List<HonorPeriodMetrics> metrics) {
        boolean invalid = CollectionUtil.isEmpty(details) || CollectionUtil.isEmpty(metrics);
        if (invalid) {
            log.info("QYBSX数据查询结果为空");
        }
        return invalid;
    }

    // 构建详情
    private QinyunScholarDetailVO buildScholarDetail(List<String> periods,
                                                     Map<String, List<HonorPeriodMetrics>> metricsMap,
                                                     Map<String, List<HonorAssessmentHistory>> detailMap) {
        List<QinyunScholarDetailVO.SeasonDataVO> seasons = new ArrayList<>(2);

        // 处理当前赛季和上赛季数据
        for (int i = 0; i < Math.min(periods.size(), 2); i++) {
            String period = periods.get(i);
            List<HonorPeriodMetrics> metrics = metricsMap.getOrDefault(period, Collections.emptyList());

            List<HonorPeriodMetrics> collect = metrics.stream().filter(m -> m.getMetricsType().equals(CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue())).collect(Collectors.toList());

            QinyunScholarDetailVO.SeasonDataVO season;
            // 构建赛期数据
            if (!collect.isEmpty()) {
                season = buildSeasonData(metrics, detailMap.get(period), period);
            } else {
                season = createDefaultSeason(period);
            }
            seasons.add(season);
        }

        QinyunScholarDetailVO vo = new QinyunScholarDetailVO();
        if (!seasons.isEmpty()) {
            vo.setCurrentSeason(seasons.get(0));
            if (seasons.size() > 1) {
                vo.setLastSeason(seasons.get(1));
            }
        }
        return vo;
    }

    // 创建默认赛季数据
    private QinyunScholarDetailVO.SeasonDataVO createDefaultSeason(String period) {
        return QinyunScholarDetailVO.SeasonDataVO.builder()
                .status("0")
                .year(convertToHalfYearPeriod(period))
                .accumulatedPremium(createDefaultAccumulatedPremium())
                .monthlyContinuity(createDefaultMonthlyContinuity())
                .build();
    }

    // 创建默认累计保费数据
    private QinyunScholarDetailVO.AccumulatedPremiumVO createDefaultAccumulatedPremium() {
        return QinyunScholarDetailVO.AccumulatedPremiumVO.builder()
                .currentPrem("0")
                .benchmarkPremium(HonorSystemConstants.QYBSX_BF_STANDARD)
                .metricsStandard(HonorSystemConstants.UNIT_WAN)
                .build();
    }

    // 创建默认月度连续性数据
    private QinyunScholarDetailVO.MonthlyContinuityVO createDefaultMonthlyContinuity() {
        return QinyunScholarDetailVO.MonthlyContinuityVO.builder()
                .monthlyStatus(Collections.emptyList())
                .currentMonths("0")
                .build();
    }

    // 构建单个赛期数据
    private QinyunScholarDetailVO.SeasonDataVO buildSeasonData(List<HonorPeriodMetrics> metrics,
                                                               List<HonorAssessmentHistory> histories, String period) {
        QinyunScholarDetailVO.SeasonDataVO season = new QinyunScholarDetailVO.SeasonDataVO();

        // 处理累计保费指标
        processAccumulatedPremium(metrics).ifPresent(season::setAccumulatedPremium);

        // 处理月度连续性指标
        processMonthlyContinuity(metrics).ifPresent(season::setMonthlyContinuity);

        // 设置荣誉信息
        if (CollectionUtil.isNotEmpty(histories)) {
            setHonorInfo(season, histories.get(0), period);
        }

        return season;
    }

    // 处理累计保费指标
    private Optional<QinyunScholarDetailVO.AccumulatedPremiumVO> processAccumulatedPremium(List<HonorPeriodMetrics> metrics) {

        return metrics.stream()
                .filter(m -> CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD_TOTAL.getValue().equals(m.getMetricsType()))
                .findFirst()
                .map(m -> QinyunScholarDetailVO.AccumulatedPremiumVO.builder()
                        .currentPrem(m.getMetricsValue())
                        .benchmarkPremium(HonorSystemConstants.QYBSX_BF_STANDARD)
                        .metricsStandard(HonorSystemConstants.UNIT_WAN)
                        .build());
    }

    // 处理月度连续性指标
    private Optional<QinyunScholarDetailVO.MonthlyContinuityVO> processMonthlyContinuity(List<HonorPeriodMetrics> metrics) {
        List<HonorPeriodMetrics> filtered = metrics.stream()
                .filter(m -> CheckMetricsItemEnum.ANNUAL_PREMIUM_STANDARD.getValue().equals(m.getMetricsType()))
                .sorted(Comparator.comparing(HonorPeriodMetrics::getMetricsMonth))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(filtered)) {
            return Optional.empty();
        }

        List<QinyunScholarDetailVO.MonthStatusVO> monthlyStatus = new ArrayList<>();
        int maxConsecutive = calculateConsecutiveMonths(filtered, monthlyStatus);

        return Optional.of(QinyunScholarDetailVO.MonthlyContinuityVO.builder()
                .currentMonths(String.valueOf(maxConsecutive))
                .monthlyStatus(monthlyStatus)
                .build());
    }

    // 计算连续达成月数
    private int calculateConsecutiveMonths(List<HonorPeriodMetrics> metrics,
                                           List<QinyunScholarDetailVO.MonthStatusVO> statusList) {
        int consecutiveCount = 0;
        int maxConsecutive = 0;

        for (HonorPeriodMetrics metric : metrics) {
            String month = extractMetricsMonth(metric);
            String status = calculateStatus(metric);

            statusList.add(new QinyunScholarDetailVO.MonthStatusVO(month, status));

            if ("1".equals(status)) {
                maxConsecutive = Math.max(maxConsecutive, ++consecutiveCount);
            } else {
                consecutiveCount = 0;
            }
        }

        return maxConsecutive;
    }

    // 提取月份信息
    private String extractMetricsMonth(HonorPeriodMetrics metric) {
        return metric.getMetricsMonth().length() > 4 ?
                metric.getMetricsMonth().substring(5) :
                metric.getMetricsMonth();
    }

    // 计算指标状态
    private String calculateStatus(HonorPeriodMetrics metric) {
        try {
            BigDecimal value = new BigDecimal(metric.getMetricsValue());
            BigDecimal standard = new BigDecimal(HonorSystemConstants.QYBSX_MONTH_STANDARD);
            return value.compareTo(standard) >= 0 ? "1" : "0";
        } catch (NumberFormatException e) {
            log.error("指标值格式异常: {}", metric);
            return "0";
        }
    }

    // 设置荣誉信息
    private void setHonorInfo(QinyunScholarDetailVO.SeasonDataVO season, HonorAssessmentHistory history, String period) {
        if (history != null) {
            season.setYear(convertToHalfYearPeriod(period));
            season.setStatus(history.getIsAchievedCurrent());
            season.setHonorId(Optional.ofNullable(history.getCongratulationId())
                    .map(Object::toString)
                    .orElse(""));
        }
    }

    public String convertToHalfYearPeriod(String periodStr) {
        // 分割字符串获取年份和月份标识
        String[] parts = periodStr.split("-");
        if (parts.length != 2 || !(parts[1].endsWith("H"))) {
            throw new IllegalArgumentException("Invalid period format");
        }

        String year = parts[0];
        String monthFlag = parts[1].substring(0, 2); // 截取前两位数字

        if ("06".equals(monthFlag)) {
            return year + "年上半年度";
        } else if ("12".equals(monthFlag)) {
            return year + "年下半年度";
        } else {
            throw new IllegalArgumentException("琴韵博识轩考核周期转换失败");
        }
    }

    // 辅助方法：按周期分组
    private Map<String, List<HonorPeriodMetrics>> groupMetricsByPeriod(List<HonorPeriodMetrics> metrics) {
        return metrics.stream().collect(Collectors.groupingBy(HonorPeriodMetrics::getCheckPeriod));
    }

    private Map<String, List<HonorAssessmentHistory>> groupDetailsByPeriod(List<HonorAssessmentHistory> details) {
        return details.stream().collect(Collectors.groupingBy(HonorAssessmentHistory::getCheckPeriod));
    }


    private QinxingEliteDetailVO handleQXJYHDetail(String empCode, String honorType) {

        log.info("琴星精英会详情查询: {}", empCode);
        // 1. 查询代理人当前荣誉信息
        HonorCurrentInfo currentQXJYHDetail = honorSystemDao.queryQXJYHCurrentDetail(empCode, honorType);

        // 2. 查询代理人历史荣誉信息
        List<HonorAssessmentHistory> historyQXJYHDetail = honorSystemDao.queryQXJYHHistoryDetail(empCode, honorType);

        if (null == currentQXJYHDetail || CollectionUtil.isEmpty(historyQXJYHDetail)) {
            log.info("代理人{}，{}荣誉信息为空", empCode, honorType);
            return buildQXJYHDefaultNewcomer(); // 返回默认值
        }

        // 历史数据分组处理
        Map<String, List<HonorAssessmentHistory>> yearHistoriesMap = historyQXJYHDetail.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(HonorAssessmentHistory::getCheckYear));

        // 过滤并处理重复奖项
        List<HonorAssessmentHistory> filteredHistory = processFilteredHistory(historyQXJYHDetail);

        // 3. 构建详情对象
        QinxingEliteDetailVO detailVO = buildQinxingEliteDetail(currentQXJYHDetail, yearHistoriesMap, filteredHistory);

        log.info("代理人{},琴星精英会：{}", empCode, JSON.toJSON(detailVO));
        return detailVO;

    }

    private QinxingEliteDetailVO buildQXJYHDefaultNewcomer() {
        return QinxingEliteDetailVO.builder()
                .year(null)
                .currentAchievement(QinxingEliteDetailVO.CurrentAchievementVO.builder().build())
                .annualAchievements(new ArrayList<>())
                .medalWall(new ArrayList<>())
                .build();
    }


    // 处理历史数据过滤
    private List<HonorAssessmentHistory> processFilteredHistory(List<HonorAssessmentHistory> history) {
        return history.stream()
                .filter(o -> "1".equals(o.getIsAchievedCurrent()))
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(HonorAssessmentHistory::getHonorsAwards),
                        map -> map.entrySet().stream()
                                .map(entry -> entry.getValue().stream()
                                        .max(Comparator.comparing(
                                                h -> {
                                                    String timeStr = h.getHonorsAwardsTime();
                                                    if (StringUtils.isBlank(timeStr)) return null;
                                                    try {
                                                        return LocalDateTime.parse(timeStr, outputFormatter);
                                                    } catch (DateTimeParseException e) {
                                                        log.warn("Invalid time format: {}", timeStr);
                                                        return null;
                                                    }
                                                },
                                                Comparator.nullsLast(Comparator.naturalOrder())
                                        ))
                                        .orElse(null))
                                .filter(Objects::nonNull)
                                .sorted(Comparator.comparing(
                                        h -> {
                                            String timeStr = h.getHonorsAwardsTime();
                                            if (StringUtils.isBlank(timeStr)) return null;
                                            try {
                                                return LocalDateTime.parse(timeStr, outputFormatter);
                                            } catch (DateTimeParseException e) {
                                                log.warn("Invalid time format: {}", timeStr);
                                                return null;
                                            }
                                        },
                                        Comparator.nullsLast(Comparator.reverseOrder())
                                ))
                                .collect(Collectors.toList())
                ));
    }

    // 构建详情对象
    private QinxingEliteDetailVO buildQinxingEliteDetail(HonorCurrentInfo currentInfo,
                                                         Map<String, List<HonorAssessmentHistory>> yearHistoriesMap,
                                                         List<HonorAssessmentHistory> filteredHistory) {
        return QinxingEliteDetailVO.builder()
                .year(currentInfo.getCheckYear())
                .currentAchievement(buildCurrentAchievement(currentInfo))
                .annualAchievements(buildAnnualAchievements(yearHistoriesMap))
                .medalWall(buildMedalWall(filteredHistory))
                .build();
    }

    // 构建当前成就
    private QinxingEliteDetailVO.CurrentAchievementVO buildCurrentAchievement(HonorCurrentInfo info) {
        return QinxingEliteDetailVO.CurrentAchievementVO.builder()
                .currentLevel(info.getHonorsAwards())
                .continuousStars(info.getCurrentStar())
                .highestRecord(info.getLongestConsecutiveStar())
                .shieldCard(info.getCurrentFieldCount())
                .build();
    }

    // 构建年度成就
    private List<QinxingEliteDetailVO.AnnualAchievementVO> buildAnnualAchievements(Map<String, List<HonorAssessmentHistory>> yearHistoriesMap) {
        return yearHistoriesMap.entrySet().stream()
                .map(entry -> QinxingEliteDetailVO.AnnualAchievementVO.builder()
                        .year(entry.getKey())
                        .months(buildMonthStatusList(entry.getValue()))
                        .build())
                .collect(Collectors.toList());
    }

    // 构建月份状态列表
    private List<QinxingEliteDetailVO.MonthStatusVO> buildMonthStatusList(List<HonorAssessmentHistory> histories) {
        return histories.stream()
                .map(history -> QinxingEliteDetailVO.MonthStatusVO.builder()
                        .month(extractMonthFromPeriod(history.getCheckPeriod()))
                        .status(history.getIsAchievedCurrent())
                        .isShieldCard(history.getIsUseField())
                        .build())
                .collect(Collectors.toList());
    }

    // 构建勋章墙
    private List<QinxingEliteDetailVO.MedalVO> buildMedalWall(List<HonorAssessmentHistory> filteredHistory) {
        return filteredHistory.stream()
                .map(history -> QinxingEliteDetailVO.MedalVO.builder()
                        .medalName(history.getHonorsAwards())
                        .achievedDate(String.valueOf(history.getHonorsAwardsTime()))
                        .status(history.getIsAchievedCurrent())
                        .honorId(String.valueOf(history.getCongratulationId()))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 字符串数值减法计算（支持大数运算）
     *
     * @param num1 被减数字符串
     * @param num2 减数字符串
     * @return 差值字符串，若输入无效返回null
     */
    public static String subtractStringNumbers(String num1, String num2) {
        try {
            BigDecimal bigDecimal1 = new BigDecimal(num1);
            BigDecimal bigDecimal2 = new BigDecimal(num2);
            return bigDecimal1.subtract(bigDecimal2).stripTrailingZeros().toPlainString();
        } catch (NumberFormatException | NullPointerException e) {
            log.error("数值格式错误: num1={}, num2={}", num1, num2, e);
            return null;
        }
    }


    /**
     * 获取琴韵博识轩考核周期
     *
     * @return
     */
    public List<String> getAssessmentPeriods() {
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();

        String currentPeriod;
        String previousPeriod;

        if (month <= 6) {
            // 当前为上半年 (1-6月)
            currentPeriod = year + "-06H";
            previousPeriod = (year - 1) + "-12H";
        } else {
            // 当前为下半年 (7-12月)
            currentPeriod = year + "-12H";
            previousPeriod = year + "-06H";
        }

        return Arrays.asList(currentPeriod, previousPeriod);
    }

    /**
     * 取考核周期月份值
     *
     * @param checkPeriod
     * @return
     */
    private String extractMonthFromPeriod(String checkPeriod) {
        if (StringUtils.isBlank(checkPeriod) || checkPeriod.length() < 6) {
            return "01"; // 默认值处理
        }
        return checkPeriod.substring(5);
    }

    @Override
    public HistoryHonorDetailVO getHonorHistory(String empCode) {

        // 初始化年度荣誉容器
        List<HistoryHonorDetailVO.YearHonorGroup> historyHonors = new ArrayList<>();

        log.info("历史荣誉查询入参:{}", empCode);
        // 查询历史荣誉基础数据
        List<HonorAssessmentHistory> historyList = honorSystemDao.queryHonorHistory(empCode);

        // 按考核年份分组
        Map<String, List<HonorAssessmentHistory>> yearHistoriesMap = historyList.stream()
                .collect(Collectors.groupingBy(HonorAssessmentHistory::getCheckYear));

        // 构建年度荣誉结构
        yearHistoriesMap.entrySet().stream()
                // 将年份字符串转换为整数进行降序排序
                .sorted(Map.Entry.<String, List<HonorAssessmentHistory>>comparingByKey()
                        .reversed())
                // 遍历处理每个已排序的年度条目
                .forEach(entry -> historyHonors.add(HistoryHonorDetailVO.YearHonorGroup.builder()
                        .year(entry.getKey())
                        .honors(handleHistoryHonorItem(entry.getValue()))
                        .build()));

        return HistoryHonorDetailVO.builder()
                .historyHonors(historyHonors)
                .build();
    }

    private List<HistoryHonorDetailVO.HonorItem> handleHistoryHonorItem(List<HonorAssessmentHistory> honorAssessmentHistories) {

        List<HistoryHonorDetailVO.HonorItem> honorItems = new ArrayList<>();

        for (HonorAssessmentHistory history : honorAssessmentHistories) {
            if (history.getHonorCode().equals(HonorItemEnum.QXJYH.getValue()) && null == history.getCongratulationId()) {
                continue;
            }
            // 创建荣誉项VO对象
            HistoryHonorDetailVO.HonorItem honorItem = new HistoryHonorDetailVO.HonorItem();
            honorItem.setAwardName(history.getHonorsAwards());
            honorItem.setHonorId(String.valueOf(history.getCongratulationId()));
            honorItems.add(honorItem);
        }
        return honorItems;
    }

    @Override
    public HonoraryTitleInfoVO getEmpHonoraryDetail(String honorId) {

        // 根据贺报id查询贺报详情数据
        List<HonoraryTitleInfoVO> honoraryDetail = honorSystemDao.queryEmpHonoraryDetail(honorId);

        if (CollectionUtils.isEmpty(honoraryDetail)) {
            log.info("未找到对应贺报信息，honorId: {}", honorId);
            return new HonoraryTitleInfoVO(); // 返回空对象或根据业务需求处理
        }

        HonoraryTitleInfoVO detail = honoraryDetail.get(0);
        String awardName = detail.getAwardName();
        String type = determineHonorType(awardName);

        return HonoraryTitleInfoVO.builder()
                .empCode(detail.getEmpCode())
                .empName(detail.getEmpName())
                .createDate(detail.getCreateDate())
                .honorId(detail.getHonorId())
                .awardName(awardName)
                .type(type)
                .build();
    }

    private String determineHonorType(String awardName) {
        if (awardName == null) return "unknown";

        // 处理等级荣誉
        if (awardName.contains("琴星精英会初级会员")) return "level1";
        if (awardName.contains("琴星精英会黄金会员")) return "level2";
        if (awardName.contains("琴星精英会白金会员")) return "level3";
        if (awardName.contains("琴星精英会钻石会员")) return "level4";
        if (awardName.contains("琴星精英会顶尖会员")) return "level5";
        if (awardName.contains("琴星精英会终身会员")) return "level6";

        // 处理特殊奖项
        if (awardName.contains("琴韵博识轩")) return "qyKnowledgeable";
        if (awardName.contains("MDRT")) return "MDRT";
        if (awardName.contains("COT")) return "COT";
        if (awardName.contains("TOT")) return "TOT";
        if (awardName.contains("琴海吉尼斯千万期缴大师") || awardName.contains("琴海吉尼斯千万标保大师"))
            return "qhGuinness";

        return "unknown";
    }
}
