package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.ChannelOrgVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.agent.org.service.ChannelOrgService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "渠道商-销售机构管理")
@RestController
@RequestMapping("/channel/orgs")
@RefreshScope
@Slf4j
public class ChannelOrgController {

    @Autowired
    private ChannelOrgService channelOrgService;

    @ApiOperation("分页查询渠道商销售机构")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelOrgVO>> listMy(
            @ApiParam("归属渠道商名称") @RequestParam(value = "channelCode", required = false) String channelCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(channelCode)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(channelOrgService.listMy(queryRequest));
    }

    /**
     * 获取渠道商销售机构简化版，只展示名称编码
     * 有管理权限的且生效状态的渠道商销售机构
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取有管理权限的渠道商销售机构，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> listMySimple(
            @ApiParam("渠道商代码") @RequestParam(value = "channelCode", required = false) String channelCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "50") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(channelCode).size(size).current(1L).status(status).build();

        return ApiResult.ok(channelOrgService.listMySimple(queryRequest));
    }

    /**
     * 获取渠道商销售机构简化版，只展示名称编码
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取所有的渠道商销售机构，简化版下拉框里使用")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> listAll(
            @ApiParam("渠道商代码") @RequestParam(value = "channelCode") String channelCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "200") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(channelCode).status(status).size(size).current(1L).build();

        return ApiResult.ok(channelOrgService.listAllSimple(queryRequest));
    }

}
