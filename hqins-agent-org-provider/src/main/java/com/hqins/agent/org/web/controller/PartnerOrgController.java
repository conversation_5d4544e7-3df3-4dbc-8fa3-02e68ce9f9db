package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.PartnerOrgVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.agent.org.service.PartnerOrgService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "合伙人-销售机构管理")
@RestController
@RequestMapping("/partner/orgs")
@RefreshScope
@Slf4j
public class PartnerOrgController {

    @Autowired
    private PartnerOrgService partnerOrgService;

    @ApiOperation("分页查询合伙人销售机构")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PartnerOrgVO>> listMy(
            @ApiParam("归属合伙人名称") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(partnerCode)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(partnerOrgService.listMy(queryRequest));
    }

    /**
     * 获取合伙人销售机构简化版，只展示名称编码
     * 有管理权限的且生效状态的合伙人销售机构
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取有管理权限的合伙人销售机构，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> listMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "200") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(partnerCode).status(status).current(1L).size(size).build();

        return ApiResult.ok(partnerOrgService.listMySimple(queryRequest));
    }

    @ApiOperation("获取有管理权限的合伙人销售机构，督导账号H5使用")
    @GetMapping("/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> frontListMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "200") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(partnerCode).status(status).current(1L).size(size).build();
        RequestContextHolder.setAdminAppIdLocal(1L);
        RequestContextHolder.setStaffIdLocal(1L);
        return ApiResult.ok(partnerOrgService.listMySimple(queryRequest));
    }


    @ApiOperation("获取合伙人销售机构，简化版下拉框里使用,线索后台使用")
    @GetMapping("/clue/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> clueListMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "200") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder().code(code).name(name).topCode(partnerCode).status(status).current(1L).size(size).build();

        return ApiResult.ok(partnerOrgService.clueListMySimple(queryRequest));
    }

    /**
     * 获取合伙人销售机构简化版，只展示名称编码
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取所有的合伙人销售机构，简化版下拉框里使用")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> listAllSimple(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode") String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "50") long size) {

        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(partnerCode).status(status).current(1L).size(size).build();

        return ApiResult.ok(partnerOrgService.listAllSimple(queryRequest));
    }
}
