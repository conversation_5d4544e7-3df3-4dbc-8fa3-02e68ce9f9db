package com.hqins.agent.org.web.controller;

import cn.hutool.core.util.StrUtil;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.RankSequ;
import com.hqins.agent.org.model.vo.AppEmployeeInfoVO;
import com.hqins.agent.org.model.vo.AppEmployeeRecruitmentInfoListVO;
import com.hqins.agent.org.model.vo.AppEmployeeRecruitmentProgressVO;
import com.hqins.agent.org.service.AppEmployeeRecruitmentService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app人员招募管理交互层.
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 9:35
 */
@Api(tags = "App人员招募管理")
@RestController
@RequestMapping("/app/employee")
@Slf4j
@RequiredArgsConstructor
public class AppEmployeeRecruitmentController {

    private final AppEmployeeRecruitmentService recruitmentService;

    @ApiOperation("查询招募进度列表")
    @GetMapping("/recruitment-progress-list")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AppEmployeeRecruitmentProgressVO>  queryRecruitmentProgressList(@ApiParam("登录人员编码") @RequestParam(value = "employeeCode", required = true) String employeeCode,
                                                                                     @ApiParam("模糊查询人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName) {
        String code = RequestContextHolder.getEmployeeCode();
        if(StrUtil.isEmpty(code)){
            if(StrUtil.isEmpty(employeeCode)){
                return ApiResult.fail("获取当前登录人员工号为空,无法继续获取信息");
            }else {
                log.info("queryRecruitmentProgressList employeeCode {}, employeeName {}", employeeCode, employeeName);
                return ApiResult.ok(recruitmentService.queryRecruitmentProgressList(employeeCode, employeeName));
            }
        }else {
            log.info("queryRecruitmentProgressList code {}", code);
            return ApiResult.ok(recruitmentService.queryRecruitmentProgressList(code, employeeName));
        }
    }

    @ApiOperation("我的代办")
    @GetMapping("/my-agent")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<AppEmployeeRecruitmentInfoListVO>>  queryMyAgentList(@ApiParam("登录人员编码") @RequestParam(value = "employeeCode", required = true) String employeeCode,
                                                                               @ApiParam("模糊查询人员姓名") @RequestParam(value = "employeeName", required = false) String employeeName) {
        String code = RequestContextHolder.getEmployeeCode();
        if(StrUtil.isEmpty(code)){
            if(StrUtil.isEmpty(employeeCode)){
                return ApiResult.fail("获取当前登录人员工号为空,无法继续获取信息");
            }else {
                log.info("queryMyAgentList employeeCode {}", employeeCode);
                return ApiResult.ok(recruitmentService.queryMyAgentList(employeeCode, employeeName));
            }
        }else {
            log.info("queryMyAgentList code {}", code);
            return ApiResult.ok(recruitmentService.queryMyAgentList(code, employeeName));
        }
    }

    @ApiOperation("查询招募人员详情")
    @GetMapping("/recruitmentEmployeeInfo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AppEmployeeInfoVO>  queryAppRecruitmentEmployeeInfo(@ApiParam("被招募人员编码") @RequestParam(value = "employeeCode", required = false) String employeeCode,
                                                                               @ApiParam("被招募人员访客Id") @RequestParam(value = "visitorId", required = false) String visitorId) {
        log.info("queryAppRecruitmentEmployeeInfo employeeCode {} visitorId {}", employeeCode, visitorId);
        return ApiResult.ok(recruitmentService.queryAppRecruitmentEmployeeInfo(employeeCode, visitorId));
    }

    @ApiOperation("查询职级序列")
    @GetMapping("/getRankSequence")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<RankSequ>>  queryAppRankSequence(@ApiParam("顶级团队编码") @RequestParam(value = "companyCode", required = true) String companyCode) {
        return ApiResult.ok(recruitmentService.queryAppRankSequence(companyCode));
    }

    @ApiOperation("查询职级")
    @GetMapping("/getRankCode")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<RankDef>>  queryAppRankCode(@ApiParam("职级序列编码") @RequestParam(value = "rankSequenceCode", required = true) String rankSequenceCode,
                                                      @ApiParam("顶级团队编码") @RequestParam(value = "companyCode", required = true) String companyCode) {
        log.info("queryAppRankCode rankSequenceCode {}  companyCode {}", rankSequenceCode, companyCode);
        return ApiResult.ok(recruitmentService.queryAppRankCode(rankSequenceCode, companyCode));
    }

    @ApiOperation("判断是否拥有审核权限")
    @GetMapping("/isAuditFlag")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean>  isAuditFlag(@ApiParam("被招募人员编码") @RequestParam(value = "employeeCode", required = true) String employeeCode) {
        log.info("isAuditFlag employeeCode {} ", employeeCode);
        return ApiResult.ok(recruitmentService.isAuditFlag(employeeCode));
    }
}
