package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbpartassignmanagerMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.OrgService;
import com.hqins.agent.org.service.PartnerChannelRelationService;
import com.hqins.common.helper.BeanCopier;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
@Service
@Slf4j
public class PartnerChannelRelationServiceImpl implements PartnerChannelRelationService {

    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;
    @Autowired
    private ChannelEmployeeMapper channelEmployeeMapper;
    @Autowired
    private OrgService orgService;
    @Autowired
    private CacheService cacheService;

    /**
     * 查询网点下的客户经理列表
     * @param orgCode   网点code编码
     * @return
     */
    @Override
    public List<PartassignmanagerVO> getCustManagerListByOrgCode(String orgCode) {
        List<PartassignmanagerVO> resList = new ArrayList<>();
        if (StringUtils.isEmpty(orgCode)){
            return resList;
        }
        try{

            List<Tbpartassignmanager> tbpartassignmanagerList = tbpartassignmanagerMapper.getValidCustManagerListByOrgCode(orgCode);
            if (tbpartassignmanagerList == null || tbpartassignmanagerList.size() == 0){
                return new ArrayList<>(0);
            }
            for(Tbpartassignmanager tbpartassignmanager : tbpartassignmanagerList){
//                PartassignmanagerVO vo = new PartassignmanagerVO();
//                vo.setPkid(tbpartassignmanager.getPkid());
//                vo.setCustManagerCode(tbpartassignmanager.getCustManagerCode());
//                vo.setCustManagerName(tbpartassignmanager.getCustManagerName());
//                vo.setMerchantOrgCode(tbpartassignmanager.getMerchantOrgCode());
//                vo.setMerchantOrgName(tbpartassignmanager.getMerchantOrgName());
//                vo.setMainManagerFlag(tbpartassignmanager.getMainManagerFlag());
//                resList.add(vo);
                resList.add(convertPartassignmanagerVO(tbpartassignmanager));
            }
        }catch (Exception e){
            log.error("getCustManagerListByOrgCode_error,orgCode:{}",orgCode,e);
        }
        return resList;
    }

    private PartassignmanagerVO convertPartassignmanagerVO(Tbpartassignmanager tbpartassignmanager) {
        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(tbpartassignmanager.getMerchantOrgCode());
        //过滤无效或过期的组织机构信息
        if (baseInst != null && !AppConsts.STR_1.equals(baseInst.getInstStatus())) {
            return null;
        }

        PartassignmanagerVO vo = new PartassignmanagerVO();
        vo.setPkid(tbpartassignmanager.getPkid());
        vo.setCustManagerCode(tbpartassignmanager.getCustManagerCode());
        vo.setCustManagerName(tbpartassignmanager.getCustManagerName());
        vo.setMainManagerFlag(tbpartassignmanager.getMainManagerFlag());
        orgService.fillMerchantOrgInfo(vo, tbpartassignmanager);
        return vo;
    }

    /**
     * 根据机构编码（orgCode）获取销售员（理财经理）详情
     * @param orgCode
     * @return
     */
    @Override
    public List<EmployeeVO> getEmployeeInfoByOrgCode(String orgCode){
        List<EmployeeVO> result = new ArrayList<>();
        try{
            List<ChannelEmployee> channelEmployeeList = channelEmployeeMapper.selectList(new LambdaQueryWrapper<ChannelEmployee>()
                    .eq(ChannelEmployee::getStatus, "SERVING")
                    .eq(ChannelEmployee::getOrgCode,orgCode));
            if (channelEmployeeList == null || channelEmployeeList.size() == 0){
                return result;
            }
            channelEmployeeList.forEach(employee -> result.add(BeanCopier.copyObject(employee, EmployeeVO.class)));
        }catch (Exception e){
            log.error("getEmployeeInfoByOrgCode_error,orgCode:{}",orgCode,e);
        }
        return result;
    }

    /**
     * 查询客户经理下的网点列表
     * @param custManagerCode    客户经理代码
     * @return
     */
    @Override
    public List<PartassignmanagerVO> getMerchantOrgListByCustManagerCode(String custManagerCode){
        List<PartassignmanagerVO> resList = new ArrayList<>();
        if (StringUtils.isEmpty(custManagerCode)){
            return resList;
        }
        try{
            List<Tbpartassignmanager> tbpartassignmanagerList = tbpartassignmanagerMapper.selectList(
                    new LambdaQueryWrapper<Tbpartassignmanager>().eq(Tbpartassignmanager::getCustManagerCode, custManagerCode));
            if (tbpartassignmanagerList == null || tbpartassignmanagerList.size() == 0){
                return resList;
            }
            for(Tbpartassignmanager tbpartassignmanager : tbpartassignmanagerList){
//                PartassignmanagerVO vo = new PartassignmanagerVO();
//                vo.setPkid(tbpartassignmanager.getPkid());
//                vo.setCustManagerCode(tbpartassignmanager.getCustManagerCode());
//                vo.setCustManagerName(tbpartassignmanager.getCustManagerName());
//                vo.setMerchantOrgCode(tbpartassignmanager.getMerchantOrgCode());
//                vo.setMerchantOrgName(tbpartassignmanager.getMerchantOrgName());
//                resList.add(vo);
                PartassignmanagerVO partassignmanagerVO = convertPartassignmanagerVO(tbpartassignmanager);
                if (partassignmanagerVO != null) {
                    resList.add(convertPartassignmanagerVO(tbpartassignmanager));
                }
            }
        }catch (Exception e){
            log.error("getMerchantOrgListByCustManagerCode_error,custManagerCode:{}",custManagerCode,e);
        }
        return resList;
    }

    /**
     * 根据机构编码（orgCode）获取销售员（理财经理）详情
     * @param custManagerCode
     * @return
     */
    @Override
    public List<EmployeeVO> getEmployeeListByCustManagerCode(String custManagerCode){
        List<EmployeeVO> resList = new ArrayList<>();
        try{
            List<Tbpartassignmanager> tbpartassignmanagerList = tbpartassignmanagerMapper.selectList(
                    new LambdaQueryWrapper<Tbpartassignmanager>().eq(Tbpartassignmanager::getCustManagerCode, custManagerCode));
            if (tbpartassignmanagerList == null || tbpartassignmanagerList.size() == 0){
                return resList;
            }
            Set<String> orgCodeSet = StreamEx.of(tbpartassignmanagerList).map(Tbpartassignmanager::getMerchantOrgCode).toSet();
            List<ChannelEmployee> channelEmployeeList = channelEmployeeMapper.selectList(new LambdaQueryWrapper<ChannelEmployee>()
                    .eq(ChannelEmployee::getStatus, "SERVING")
                    .in(ChannelEmployee::getOrgCode,orgCodeSet));
            if (channelEmployeeList == null || channelEmployeeList.size() == 0){
                return resList;
            }
            channelEmployeeList.forEach(employee -> resList.add(BeanCopier.copyObject(employee, EmployeeVO.class)));
        }catch (Exception e){
            log.error("getEmployeeListByCustManagerCode_error,custManagerCode:{}",custManagerCode,e);
        }
        return resList;
    }

    /**
     * 根据“商户代码（银行）”和“商户组织机构代码（网点）”查询客户经理列表
     * @param merchantCodes
     * @param orgCodes
     * @return
     */
    @Override
    public List<PartassignmanagerVO> getCustManagerListByMerchantCodeAndOrgCode(String[] merchantCodes,String[] orgCodes){
        List<PartassignmanagerVO> resList = new ArrayList<>();
        if (merchantCodes == null && orgCodes == null){
            return resList;
        }
        List<String> merchantCodeList = null;
        List<String> orgCodeList = null;
        if(merchantCodes != null && merchantCodes.length > 0){
            merchantCodeList = Arrays.asList(merchantCodes);
        }
        if(orgCodes != null && orgCodes.length > 0){
            orgCodeList = Arrays.asList(orgCodes);
        }
        try{
//            Set<String> accessOrgCodes = new HashSet<>();
            //获取当前登录人权限
//            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
//            accessOrgCodes = myDataAccess.getChannelOrgCodes();
//            List<String> orgCodeList = calcTopCodesRange(new ArrayList<>(accessOrgCodes), Arrays.asList(orgCodes));
            List<Tbpartassignmanager> tbpartassignmanagerList = tbpartassignmanagerMapper.getValidCustManagerList(
                    merchantCodeList , orgCodeList);
            tbpartassignmanagerList = tbpartassignmanagerList.stream().filter(distinctByKey(t -> t.getCustManagerCode())).collect(Collectors.toList());
            if (tbpartassignmanagerList == null || tbpartassignmanagerList.size() == 0){
                return resList;
            }
            for(Tbpartassignmanager tbpartassignmanager : tbpartassignmanagerList){
                PartassignmanagerVO vo = new PartassignmanagerVO();
                vo.setPkid(tbpartassignmanager.getPkid());
                vo.setCustManagerCode(tbpartassignmanager.getCustManagerCode());
                vo.setCustManagerName(tbpartassignmanager.getCustManagerName());
                vo.setMerchantOrgCode(tbpartassignmanager.getMerchantOrgCode());
                vo.setMerchantOrgName(tbpartassignmanager.getMerchantOrgName());
                resList.add(vo);
            }
        }catch (Exception e){
            log.error("getCustManagerListByMerchantCodeAndOrgCode_error,merchantCodeList:{},orgCodeList:{}",merchantCodes,orgCodes,e);
        }
        return resList;
    }
    private List<String> calcTopCodesRange(List<String> list1, List<String> list2) {
        if (CollectionUtils.isEmpty(list2)) {
            return list1;
        }
        if (CollectionUtils.isEmpty(list1)) {
            return Lists.newArrayList();
        }
        List<String> list = Lists.newArrayList();
        for (String s : list1) {
            if (list2.contains(s)) {
                list.add(s);
            }
        }
        return list;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
