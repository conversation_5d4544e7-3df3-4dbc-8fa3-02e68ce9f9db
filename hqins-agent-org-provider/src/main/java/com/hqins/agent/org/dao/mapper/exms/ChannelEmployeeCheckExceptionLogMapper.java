package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckExceptionLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 渠道商代理人查验异常日志数据访问接口
 * <AUTHOR>
 * @since 2023-10-07
 */
@Mapper
@DS("exms")
@Repository
public interface ChannelEmployeeCheckExceptionLogMapper extends BaseMapper<ChannelEmployeeCheckExceptionLog> {

    Long insertSelective(ChannelEmployeeCheckExceptionLog channelEmployeeCheckExceptionLog);

    int updateByPrimaryKeySelective(ChannelEmployeeCheckExceptionLog channelEmployeeCheckExceptionLog);


}

