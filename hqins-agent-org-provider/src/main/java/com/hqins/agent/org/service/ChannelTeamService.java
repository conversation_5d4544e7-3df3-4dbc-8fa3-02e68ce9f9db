package com.hqins.agent.org.service;

import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.request.ChannelTeamAddRequest;
import com.hqins.agent.org.model.request.ChannelTeamQueryRequest;
import com.hqins.agent.org.model.vo.ChannelTeamTreeNodeVO;
import com.hqins.agent.org.model.vo.ChannelTeamVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.model.vo.SimpleTeamTreeNodeVO;
import com.hqins.common.base.page.PageInfo;

import java.util.Set;

/**
 * <AUTHOR> <PERSON>o
 * @date 2021/5/7
 * @Description
 */
public interface ChannelTeamService {

    PageInfo<ChannelTeamTreeNodeVO> listMyTree(ChannelTeamQueryRequest queryRequest);

    PageInfo<SimpleTeamTreeNodeVO> listMyTreeSimple(ChannelTeamQueryRequest queryRequest);

    void save(ChannelTeamAddRequest request, boolean auth);

    void updateLeader(Long id, Long leaderId);

    void updateStatus(Long id, TeamStatus status);

    ChannelTeamVO getById(Long id);

    void disableTeamByOrgCode(String orgCode);

    Set<String> selectAllChildCodes(String teamCode,String teamName, MyDataAccessVO myDataAccess);
}
