package com.hqins.agent.org.excel;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.SupervisorPerformanceDetailVO;
import com.hqins.common.base.enums.IdType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 类型转换
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class PerformanceInstExcelAssembler {


    public PerformanceInstExcel assemblerInst(SupervisorPerformanceDetailVO vo) {
        PerformanceInstExcel excel = new PerformanceInstExcel();
        //机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //首年保费
        excel.setFycPremium(StringUtils.isNotEmpty(vo.getFycPremium()) ? vo.getFycPremium():" ");
        //首年标保
        excel.setDcp(StringUtils.isNotEmpty(vo.getDcp()) ? vo.getDcp():" ");
        //首年保单件数
        excel.setFycPolicyNum(StringUtils.isNotEmpty(vo.getFycPolicyNum()) ? vo.getFycPolicyNum():" ");
        //期交保费
        excel.setPeriodPremium(StringUtils.isNotEmpty(vo.getPeriodPremium()) ? vo.getPeriodPremium():" ");
        //期交标保
        excel.setPeriodDCP(StringUtils.isNotEmpty(vo.getPeriodDCP()) ? vo.getPeriodDCP():" ");
        //期交件数
        excel.setPeriodPolicyNum(StringUtils.isNotEmpty(vo.getPeriodPolicyNum()) ? vo.getPeriodPolicyNum():" ");
        //新单短险保费
        excel.setFycMRiskPremium(StringUtils.isNotEmpty(vo.getFycMRiskPremium()) ? vo.getFycMRiskPremium():" ");
        //新单短险件数
        excel.setFycMRiskPolicyNum(StringUtils.isNotEmpty(vo.getFycMRiskPolicyNum()) ? vo.getFycMRiskPolicyNum():" ");
        //新单客户数
        excel.setFycAppntNoNum(StringUtils.isNotEmpty(vo.getFycAppntNoNum()) ? vo.getFycAppntNoNum():" ");
        //期交客户数
        excel.setPeriodAppntNoNum(StringUtils.isNotEmpty(vo.getPeriodAppntNoNum()) ? vo.getPeriodAppntNoNum():" ");
        //增员人数
        excel.setIncreaseNum(StringUtils.isNotEmpty(vo.getIncreaseNum()) ? vo.getIncreaseNum():" ");
        return excel;
    }

    public PerformanceTeamExcel assemblerTeam(SupervisorPerformanceDetailVO vo) {
        PerformanceTeamExcel excel = new PerformanceTeamExcel();
        //机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //团队
        excel.setTeamName(StringUtils.isNotEmpty(vo.getTeamName()) ? vo.getTeamName():" ");
        //首年保费
        excel.setFycPremium(StringUtils.isNotEmpty(vo.getFycPremium()) ? vo.getFycPremium():" ");
        //首年标保
        excel.setDcp(StringUtils.isNotEmpty(vo.getDcp()) ? vo.getDcp():" ");
        //首年保单件数
        excel.setFycPolicyNum(StringUtils.isNotEmpty(vo.getFycPolicyNum()) ? vo.getFycPolicyNum():" ");
        //期交保费
        excel.setPeriodPremium(StringUtils.isNotEmpty(vo.getPeriodPremium()) ? vo.getPeriodPremium():" ");
        //期交标保
        excel.setPeriodDCP(StringUtils.isNotEmpty(vo.getPeriodDCP()) ? vo.getPeriodDCP():" ");
        //期交件数
        excel.setPeriodPolicyNum(StringUtils.isNotEmpty(vo.getPeriodPolicyNum()) ? vo.getPeriodPolicyNum():" ");
        //新单短险保费
        excel.setFycMRiskPremium(StringUtils.isNotEmpty(vo.getFycMRiskPremium()) ? vo.getFycMRiskPremium():" ");
        //新单短险件数
        excel.setFycMRiskPolicyNum(StringUtils.isNotEmpty(vo.getFycMRiskPolicyNum()) ? vo.getFycMRiskPolicyNum():" ");
        //新单客户数
        excel.setFycAppntNoNum(StringUtils.isNotEmpty(vo.getFycAppntNoNum()) ? vo.getFycAppntNoNum():" ");
        //期交客户数
        excel.setPeriodAppntNoNum(StringUtils.isNotEmpty(vo.getPeriodAppntNoNum()) ? vo.getPeriodAppntNoNum():" ");
        //增员人数
        excel.setIncreaseNum(StringUtils.isNotEmpty(vo.getIncreaseNum()) ? vo.getIncreaseNum():" ");
        return excel;
    }

    public PerformanceAgentExcel assemblerAgent(SupervisorPerformanceDetailVO vo) {
        PerformanceAgentExcel excel = new PerformanceAgentExcel();
        //机构
        excel.setInstName(StringUtils.isNotEmpty(vo.getInstName()) ? vo.getInstName():" ");
        //团队
        excel.setTeamName(StringUtils.isNotEmpty(vo.getTeamName()) ? vo.getTeamName():" ");
        //代理人
        excel.setAgentName(StringUtils.isNotEmpty(vo.getAgentName()) ? vo.getAgentName():" ");
        //首年保费
        excel.setFycPremium(StringUtils.isNotEmpty(vo.getFycPremium()) ? vo.getFycPremium():" ");
        //首年标保
        excel.setPeriodDCP(StringUtils.isNotEmpty(vo.getPeriodDCP()) ? vo.getPeriodDCP():" ");
        //首年保单件数
        excel.setFycPolicyNum(StringUtils.isNotEmpty(vo.getFycPolicyNum()) ? vo.getFycPolicyNum():" ");
        //期交保费
        excel.setPeriodPremium(StringUtils.isNotEmpty(vo.getPeriodPremium()) ? vo.getPeriodPremium():" ");
        //期交标保
        excel.setDcp(StringUtils.isNotEmpty(vo.getDcp()) ? vo.getDcp():" ");
        //期交件数
        excel.setPeriodPolicyNum(StringUtils.isNotEmpty(vo.getPeriodPolicyNum()) ? vo.getPeriodPolicyNum():" ");
        //新单短险保费
        excel.setFycMRiskPremium(StringUtils.isNotEmpty(vo.getFycMRiskPremium()) ? vo.getFycMRiskPremium():" ");
        //新单短险件数
        excel.setFycMRiskPolicyNum(StringUtils.isNotEmpty(vo.getFycMRiskPolicyNum()) ? vo.getFycMRiskPolicyNum():" ");
        //新单客户数
        excel.setFycAppntNoNum(StringUtils.isNotEmpty(vo.getFycAppntNoNum()) ? vo.getFycAppntNoNum():" ");
        //期交客户数
        excel.setPeriodAppntNoNum(StringUtils.isNotEmpty(vo.getPeriodAppntNoNum()) ? vo.getPeriodAppntNoNum():" ");
        //增员人数
        excel.setIncreaseNum(StringUtils.isNotEmpty(vo.getIncreaseNum()) ? vo.getIncreaseNum():" ");
        return excel;
    }
}
