package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.ChannelEmployeeDao;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.ChannelLogo;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.mapper.ChannelLogoMapper;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.DataType;
import com.hqins.agent.org.model.enums.NodeLevel;
import com.hqins.agent.org.model.enums.PartnerEmployeeStatus;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.ChannelService;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ChannelLogoMapper channelLogoMapper;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ChannelEmployeeDao channelEmployeeDao;

    @Override
    public List<ChannelOrgVO> listSubEmpChannel(String teamLeaderCode, boolean includeLeader) {
        //查找emp
        Tbemp teamLeader = cacheService.getAllTbempMap().get(teamLeaderCode);
        if (teamLeader == null) {
            return null;
        }
        //如果部长无效，则直接返回空
        if (!PartnerEmployeeStatus.SERVING.getCode().equals(teamLeader.getEmpstatus())) {
            return null;
        }
        //全部员工
        List<Tbemp> allTbempList = Lists.newArrayList();
        //如果包括当前领导，则将领导添加至最终员工列表
        if (includeLeader) {
            allTbempList.add(teamLeader);
        }

        //查找
        List<Tbsaleteam> allTbsaleteams = cacheService.getAllTbsaleteams();

        //step 1 下级团队 且为有效团队
        List<Tbsaleteam> firstStepList = allTbsaleteams.stream().filter(
                tbsaleteam -> teamLeader.getEmpincode().equals(tbsaleteam.getEmpincode())
                        && "00".equals(tbsaleteam.getSaleteamstatus())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(firstStepList)) {
            //添加第一级下属团队
            List<Tbsaleteam> allTeamList = Lists.newArrayList();
            allTeamList.addAll(firstStepList);
            //查询所有下属团队的下属 团队
            for (Tbsaleteam parentTeam : firstStepList) {
                List<Tbsaleteam> allSubTeamList = getAllSubTeam(parentTeam);
                if (CollectionUtils.isNotEmpty(allSubTeamList)) {
                    allTeamList.addAll(allSubTeamList);
                }
            }

            //step 2 获得 saleTeamInCode Set
            Set<String> saleTeamInCodeSet = allTeamList.stream().map(team -> team.getSaleteamincode()).collect(Collectors.toSet());

            //step 3 所有下属的经理人 且 有效的代理人
            List<Tbemp> subEmpList = Lists.newArrayList();
            for (Tbemp emp : cacheService.getAllTbempMap().values()) {
                if (StringUtils.isNotEmpty(emp.getSaleteamincode())
                        && saleTeamInCodeSet.contains(emp.getSaleteamincode())
                        && PartnerEmployeeStatus.SERVING.getCode().equals(emp.getEmpstatus())
                ) {
                    subEmpList.add(emp);
                }
            }

            if (CollectionUtils.isNotEmpty(subEmpList)) {
                allTbempList.addAll(subEmpList);
            }
        }

        //step 4
        if (CollectionUtils.isEmpty(allTbempList)) {
            return null;
        }

        Set<String> empCodeSet = allTbempList.stream().map(emp -> emp.getEmpcode()).collect(Collectors.toSet());
        List<Tbpartassignmanager> resultList = Lists.newArrayList();
        for (Tbpartassignmanager manager : cacheService.getAllAssignManagers()) {

            BaseInst baseInst = cacheService.getAllBaseInstsMap().get(manager.getMerchantOrgCode());
            if (empCodeSet.contains(manager.getCustManagerCode())
                && (
                        //过滤有效的网点机构
                        baseInst != null
                        &&
                        "1".equals(baseInst.getInstStatus())
                        &&
                        "1".equals(baseInst.getInstTypeFlag())
                    )
            ) {
                resultList.add(manager);
            }
        }
        List<ChannelOrgVO> channelOrgVOList = new ArrayList<>();
        for (Tbpartassignmanager tbpartassignmanager : resultList){
            ChannelOrgVO channelOrgVO = new ChannelOrgVO();
            //部门可能改名，展示最新的名称
            BaseInst baseInst = cacheService.getAllBaseInstsMap().get(tbpartassignmanager.getMerchantOrgCode());
            if (baseInst != null) {
                channelOrgVO.setCode(baseInst.getInstCode());
                channelOrgVO.setName(baseInst.getInstName());
            } else {
                channelOrgVO.setCode(tbpartassignmanager.getMerchantOrgCode());
                channelOrgVO.setName(tbpartassignmanager.getMerchantOrgName());
            }
            channelOrgVOList.add(channelOrgVO);
        }
        return channelOrgVOList;
    }



    private List<Tbsaleteam> getAllSubTeam(Tbsaleteam parentTeam) {

        List<Tbsaleteam> list = Lists.newArrayList();

        for (Tbsaleteam team : cacheService.getAllTbsaleteams()) {
            //找到该团队的下级
            if (StringUtils.isNotEmpty(team.getSupersaleteamcode())
                    && parentTeam.getSaleteamcode().equals(team.getSupersaleteamcode())
                    && "00".equals(team.getSaleteamstatus())
            ) {
                list.add(team);
                List<Tbsaleteam> subTeamList = getAllSubTeam(team);
                if (CollectionUtils.isNotEmpty(subTeamList)) {
                    list.addAll(subTeamList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return null;
    }



    /**
     * 获取团队下所有人员所被指派的一级渠道商及5级渠道商列表明
     * @param teamCode 可以为区部组下面的任一个code
     * @return
     */
    @Override
    public Collection<SimpleChannelOrgVO> list1st5thOrgs(String teamCode) {

        List<Tbemp> teamMembers = getMembersFromTeamCode(teamCode);

        Map<String, SimpleChannelOrgVO> resultMap = Maps.newHashMap();

        //所有客户经理指派信息
        List<Tbpartassignmanager> allAssignManagers = cacheService.getAllAssignManagers();
        for (Tbemp member : teamMembers) {
            for (Tbpartassignmanager manager : allAssignManagers) {
                if (member.getEmpcode().equals(manager.getCustManagerCode())) {
                    saveAssignManagerIntoMap(manager, resultMap);
                }
            }
        }
        return resultMap.values();
    }

    /**
     * 传入的有可能是区部组，需要进递归
     * @param teamCode
     * @return
     */
    private List<Tbemp> getMembersFromTeamCode(String teamCode) {

        List<Tbemp> targetTeamMembers = Lists.newArrayList();

        //所属的销售组
        Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
        Tbsaleteam tbsaleteam = allTbsaleteamsMap.get(teamCode);
        //如果查不到销售团队或销售团队为失效状态时，不进行后续计算
        if (tbsaleteam == null || !AppConsts.STR_00.equals(tbsaleteam.getSaleteamstatus())) {
            return targetTeamMembers;
        }

        String saleteamincode = tbsaleteam.getSaleteamincode();
        if (StringUtils.isEmpty(saleteamincode)) {
            //数据有问题，需要排查
            return targetTeamMembers;
        }
        //获取组员
        List<Tbemp> teamMembers = cacheService.getAllTbempMap().values().stream()
                .filter(emp -> StringUtils.isNotEmpty(emp.getSaleteamincode()))
                .filter(emp -> saleteamincode.equals(emp.getSaleteamincode()))
                .collect(Collectors.toList());

        //有组员的情况
        if (CollectionUtils.isNotEmpty(teamMembers)) {
            targetTeamMembers.addAll(teamMembers);
        }

        //递归调用 区 03， 部 02， 组 01
        int teamLevel = Integer.parseInt(tbsaleteam.getTeamlevel());
        if (teamLevel - 1 > 0) {
            for (Tbsaleteam subTeam : cacheService.getAllTbsaleteams()) {
                //如果是该传入组的子组，则进行递归调用
                if (teamCode.equals(subTeam.getSupersaleteamcode())) {
                    List<Tbemp> subMembers = getMembersFromTeamCode(subTeam.getSaleteamcode());
                    if (CollectionUtils.isNotEmpty(subMembers)) {
                        targetTeamMembers.addAll(subMembers);
                    }
                }
            }
        }

        return targetTeamMembers;
    }

    private void saveAssignManagerIntoMap(Tbpartassignmanager manager, Map<String, SimpleChannelOrgVO> resultMap) {

        String channelCode = manager.getMerchantCode();
        String channelName = manager.getMerchantName();
        String channelOrgCode = manager.getMerchantOrgCode();
        String channelOrgName = manager.getMerchantOrgName();

        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(channelOrgCode);

        //如果机构没有查到 或者  机构为 非生效 的情况，不进行后续计算
        if (baseInst == null || !AppConsts.STR_1.equals(baseInst.getInstStatus())) {
            return;
        }

        //组装最外层
        SimpleChannelOrgVO simpleChannelOrgVO = resultMap.get(channelCode);
        if (simpleChannelOrgVO == null) {
            simpleChannelOrgVO = new SimpleChannelOrgVO();
            simpleChannelOrgVO.setChannelOrgCode(channelCode);
            simpleChannelOrgVO.setChannelOrgName(channelName);
            resultMap.put(channelCode, simpleChannelOrgVO);
        }
        //子层级
        SimpleChannelOrgVO subOrgVO = new SimpleChannelOrgVO();
        subOrgVO.setChannelOrgCode(channelOrgCode);
        subOrgVO.setChannelOrgName(channelOrgName);

        //组装子层级
        List<SimpleChannelOrgVO> subChannelOrgList = simpleChannelOrgVO.getSubChannelOrgList();
        if (CollectionUtils.isEmpty(subChannelOrgList)) {
            subChannelOrgList = Lists.newArrayList();
            simpleChannelOrgVO.setSubChannelOrgList(subChannelOrgList);
        }

        if (CollectionUtils.isEmpty(subChannelOrgList)) {
            subChannelOrgList.add(subOrgVO);
        } else {
            boolean addFlag = true;
            for (SimpleChannelOrgVO vo : subChannelOrgList) {
                if (vo.getChannelOrgCode().equals(channelOrgCode)) {
                    addFlag = false;
                    break;
                }
            }
            if (addFlag) {
                subChannelOrgList.add(subOrgVO);
            }
        }
    }

    /**
     * 获取当前登录人有权限管理的合伙人列表
     *
     * @param queryRequest
     * @return
     */

    @Override
    public PageInfo<ChannelVO> listMy(TopQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        log.info("myDataAccess:{}", myDataAccess);
        Page<Tbepartner> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, queryRequest, myDataAccess.getChannelCodes());
            log.info("p:{}", p);
        } else {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        }
        return PageUtil.convert(p, PartnerConverter::tbepartnerToChannelVO);
    }


    /**
     * 获取当前登录人有权限管理的合伙人列表
     * 简化版，只获取code、name
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<SimpleNodeVO> listMySimple(TopQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<Tbepartner> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, queryRequest, myDataAccess.getSelectChannelCodes());
        } else {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        }
        return PageUtil.convert(p, PartnerConverter::tbepartnerToSimpleNodeVO);
    }

    /**
     * 获取所有的合伙人，简化版下拉框里使用
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<SimpleNodeVO> listAllSimple(TopQueryRequest queryRequest) {
        Page<Tbepartner> p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        return PageUtil.convert(p, PartnerConverter::tbepartnerToSimpleNodeVO);
    }

    /**
     * 获取有效的组织机构树,包含合伙人、合伙人销售机构
     * 数据授权时使用
     *
     * @param treeRequest
     * @return
     */
    @Override
    public List<SimpleTreeNodeVO> getAllTree(OrgTreeRequest treeRequest) {
        //加载第一层树
        List<Tbepartner> tbepartners = cacheService.selectTbepartners(AppConsts.CPTYPE_CHANNEL, TopQueryRequest.builder().code(treeRequest.getTopCode()).build(), null);

        //机构map
        Map<String, List<BaseInst>> baseInstsMap = null;
        if (!NodeLevel.TOP.equals(treeRequest.getNodeLevel())) {
            List<BaseInst> baseInstList = StreamEx.of(cacheService.getAllBaseInsts()).filter(t ->
                    "1".equals(t.getInstStatus())
                            && (StringUtil.isBlank(treeRequest.getOrgCode()) || treeRequest.getOrgCode().equals(t.getInstCode()))
            ).toList();
            baseInstsMap = StreamEx.of(baseInstList).groupingBy(BaseInst::getCompanyid);
        }

        Map<String, Tbepartner> tbepartnersMap = StreamEx.of(tbepartners).toMap(Tbepartner::getCompanycode, Function.identity());
        List<SimpleTreeNodeVO> topTreeNodeVOs = BeanCopier.copyList(tbepartners, PartnerConverter::tbepartnerToSimpleTreeNodeVO);
        //加载机构层
        for (SimpleTreeNodeVO top : topTreeNodeVOs) {
            if (NodeLevel.TOP.equals(treeRequest.getNodeLevel())) {
                continue;
            }
            top.setDataType(DataType.CHANNEL);
            List<BaseInst> baseInsts = baseInstsMap.getOrDefault(tbepartnersMap.get(top.getCode()).getCompanyid(), new ArrayList<>());
            if (CollectionUtils.isEmpty(baseInsts)) {
                continue;
            }
            List<SimpleTreeNodeVO> orgTreeNodeVOs = BeanCopier.copyList(baseInsts, PartnerConverter::instToSimpleTreeNodeVo);
            for (SimpleTreeNodeVO org : orgTreeNodeVOs) {
                org.setDataType(DataType.CHANNEL_ORG);
            }
            top.setChildren(TreeUtils.buildTrees(orgTreeNodeVOs));
        }

        for (SimpleTreeNodeVO nodeVO : topTreeNodeVOs) {
            initCodeDateTypeValue(nodeVO);
            List<SimpleTreeNodeVO> children = nodeVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                for (SimpleTreeNodeVO vo : children) {
                    initCodeDateTypeValue(vo);
                }
            }
        }

        return topTreeNodeVOs;
    }

    private void initCodeDateTypeValue(SimpleTreeNodeVO nodeVO) {
        String codeDateType = nodeVO.getCode() + "-" + nodeVO.getDataType();
        nodeVO.setCodeDateType(codeDateType);
    }

    @Override
    public void configLogo(ChannelConfigLogoRequest request) {
        ChannelLogo channelLogo = new ChannelLogo();
        channelLogo.setCode(request.getCode());
        channelLogo.setUrl(request.getLogo());
        channelLogoMapper.insert(channelLogo);
    }

    @Override
    public String getLogo(String employeeCode) {
        ChannelEmployee o = channelEmployeeDao.getByCode(employeeCode);
        String channelCode = o.getChannelCode();
        ChannelLogo channelLogo = channelLogoMapper
                .selectOne(new LambdaQueryWrapper<ChannelLogo>()
                        .eq(ChannelLogo::getCode, channelCode));
        return null == channelLogo ? "" : channelLogo.getUrl();
    }

    @Override
    public List<OrgVO> findChannelTypeInsts(String channelType) {
        log.info("findChannelTypeInsts:{}", channelType);
        //所有渠道商类型 下面的 tbepartner
        List<Tbepartner> channelTypeTbepartnerList = cacheService.getAllTbepartners().stream().filter(tbepartner -> channelType.equals(tbepartner.getChannelType())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(channelTypeTbepartnerList)) {
            log.info("未查询到channelType = {} 的公司信息!", channelType);
            return null;
        }
        //找到公司下所有的ubaseInst
        List<OrgVO> resultList = Lists.newArrayList();
        for (Tbepartner tbepartner : channelTypeTbepartnerList) {
            if (StringUtils.isEmpty(tbepartner.getCompanyid())) {
                continue;
            }
            //过滤 -- 当前渠道商下的所有机构，且机构状态 为 1(0:未生效 1:生效 2:失效)
            List<BaseInst> partBaseInstList = cacheService.getAllBaseInsts().stream()
                    .filter(baseInst -> tbepartner.getCompanyid().equals(baseInst.getCompanyid()))
                    .filter(baseInst -> "1".equals(baseInst.getInstStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(partBaseInstList)) {
                continue;
            }
            List<OrgVO> partList = partBaseInstList.stream().map(b -> {
                OrgVO orgVO = OrgVO.builder()
                        .code(b.getInstCode())
                        .name(b.getInstName())
                        .parentCode(b.getParentInstCode())
                        .build();
                //渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理  以上值代表渠道
                switch (channelType) {
                    case "1":
                    case "2":
                    case "3":
                    case "4":
                    case "10":
                        orgVO.setOrgType(AgentOrgType.CHANNEL);
                        break;
                    default:
                        orgVO.setOrgType(AgentOrgType.PARTNER);
                }
                return orgVO;
            }).collect(Collectors.toList());
            resultList.addAll(partList);
        }
        return resultList;
    }

    @Override
    public Boolean ifpTeam(String teamCode) {
        List<Tbsaleteam> tbsaleteamList = cacheService.getAllTbsaleteams();
        for (Tbsaleteam tbsaleteam : tbsaleteamList) {
            if (teamCode.equals(tbsaleteam.getSaleteamcode())){
                if (AppConsts.STR_IFP.equals(tbsaleteam.getSaleteamtype())){
                    return true;
                }
            }
        }
        return false;
    }
}
