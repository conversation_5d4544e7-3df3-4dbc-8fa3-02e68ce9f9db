package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: lijian
 * @Date: 2022/12/16 5:25 下午
 */
@Mapper
@DS("exms")
@Repository
public interface RankDefMapper {

    List<RankDef> getAllUsefulRankDef();

    List<RankDef> queryRankCodeByRankSequenceCode(@Param("rankSequenceCode") String rankSequenceCode,@Param("companyCode") String companyCode);
}
