package com.hqins.agent.org.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.ChannelDealer;
import com.hqins.agent.org.dao.mapper.exms.TbpartassignmanagerMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelDealerMapper;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.request.InsureOrgQueryRequest;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.ChannelOrgVO;
import com.hqins.agent.org.model.vo.InsureOrgMgrVO;
import com.hqins.agent.org.model.vo.InsureOrgVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.agent.org.service.InsureOrgService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class InsureOrgServiceImpl implements InsureOrgService {

    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;
    @Autowired
    private ChannelDealerMapper channelDealerMapper;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private CacheService cacheService;

    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCode
     * @return
     */
    @Override
    public InsureOrgVO selectInsureOrgsByCode(AgentOrgType orgType, String orgCode) {
        String cptype = AppConsts.CPTYPE_PARTNER;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            cptype = AppConsts.CPTYPE_CHANNEL;
        }
        List<BaseInst> baseInstList = cacheService.selectBaseInsts(cptype, OrgQueryRequest.builder().status("ENABLED").build(), new HashSet<>(Collections.singleton(orgCode)));
        List<InsureOrgVO> orgs = BeanCopier.copyList(baseInstList, inst -> PartnerConverter.instToInsureOrgVO(inst, orgType, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            orgs = setCustManageCode2(orgs);
        }
        return orgs.stream().findFirst().orElse(null);
    }
    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCodes
     * @return
     */
    @Override
    public List<InsureOrgVO> selectInsureOrgsByCodes(AgentOrgType orgType, List<String> orgCodes) {
        String cptype = AppConsts.CPTYPE_PARTNER;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            cptype = AppConsts.CPTYPE_CHANNEL;
        }
        List<BaseInst> baseInstList = cacheService.selectBaseInsts(cptype, OrgQueryRequest.builder().status("ENABLED").build(), new HashSet<>(orgCodes));
        List<InsureOrgVO> orgs = BeanCopier.copyList(baseInstList, inst -> PartnerConverter.instToInsureOrgVO(inst, orgType, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            orgs = setCustManageCode2(orgs);
        }
        return orgs;
    }

    /**
     * 根据条件获取投保专用机构信息
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<InsureOrgMgrVO> selectInsureOrgsAll(InsureOrgQueryRequest queryRequest) {
        Set<String> orgCodes = new HashSet<>();
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        String cptype = AppConsts.CPTYPE_PARTNER;
        //渠道商
        if (AgentOrgType.CHANNEL.equals(queryRequest.getOrgType())) {
            cptype = AppConsts.CPTYPE_CHANNEL;
            orgCodes = myDataAccess.getChannelOrgCodes();
        } else {
            orgCodes = myDataAccess.getPartnerOrgCodes();
        }
        Page<BaseInst> p = null;
        //判断是否是超级管理员
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectBaseInstPage(cptype, queryRequest, orgCodes);
        } else {
            p = cacheService.selectBaseInstPage(cptype, queryRequest, null);
        }

//        Page<BaseInst> p = cacheService.selectBaseInstPage(cptype, queryRequest);

        //转换数据结构
        PageInfo<InsureOrgMgrVO> result = PageUtil.convert(p, inst -> PartnerConverter.instToInsureOrgMgrVO(inst, queryRequest.getOrgType(), cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
        if (!result.getRecords().isEmpty()) {
            if (AgentOrgType.CHANNEL.equals(queryRequest.getOrgType())) {
                result.setRecords(setCustManageCode(result.getRecords()));
            }
            result.setRecords(setDealer(result.getRecords()));
            result.setRecords(TreeUtils.buildTrees(result.getRecords()));
        }
        return result;
    }

    @Override
    @DS("org")
    @DSTransactional
    public void updateDealer(List<String> ownerOrgCodes, String orgCode) {
        Set<String> set = new HashSet<String>();
        set.addAll(ownerOrgCodes);
        set.add(orgCode);
        List<BaseInst> baseInstList = cacheService.selectBaseInsts(AppConsts.CPTYPE_CHANNEL, OrgQueryRequest.builder().status("ENABLED").build(), set);

        AssertUtil.isTrue(baseInstList != null && baseInstList.size() == ownerOrgCodes.size() + 1, new ApiException("机构编码错误"));
        //鉴权
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().containsAll(set), new ApiException("没有权限操作该机构"));
        }
        List<ChannelOrgVO> orgs = BeanCopier.copyList(baseInstList, inst -> PartnerConverter.instToChannelOrgVO(inst, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
        ChannelOrgVO dealer = null;
        for (ChannelOrgVO vo : orgs) {
            if (orgCode.equals(vo.getCode())) {
                dealer = vo;
            }
        }
        //设置分销商
        channelDealerMapper.delete(new LambdaQueryWrapper<ChannelDealer>()
                .in(ChannelDealer::getOwnerOrgCode, ownerOrgCodes));
        for (String ownerOrgCode : ownerOrgCodes) {
            ChannelDealer o = new ChannelDealer();
            o.setChannelName(dealer.getChannelName());
            o.setChannelCode(dealer.getChannelCode());
            o.setOrgCode(dealer.getCode());
            o.setOrgName(dealer.getName());
            o.setOwnerOrgCode(ownerOrgCode);
            channelDealerMapper.insert(o);
        }
    }

    /**
     * 设置项目经理编码
     *
     * @param list
     * @return
     */
    private List<InsureOrgVO> setCustManageCode2(List<InsureOrgVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Set<String> orgCodes = StreamEx.of(list).map(InsureOrgVO::getCode).toSet();
        AssertUtil.notEmpty(orgCodes, new ApiException("机构不存在，请确认后重试！"));
        List<Tbpartassignmanager> tbpartassignmanagers = tbpartassignmanagerMapper.selectList(new LambdaQueryWrapper<Tbpartassignmanager>()
                .in(Tbpartassignmanager::getMerchantOrgCode, orgCodes).eq(Tbpartassignmanager::getMainManagerFlag, "0"));
        Map<String, String> custManageCodeMap = StreamEx.of(tbpartassignmanagers).toMap(Tbpartassignmanager::getMerchantOrgCode, Tbpartassignmanager::getCustManagerCode, (key1, key2) -> key2);
        for (InsureOrgVO vo : list) {
            vo.setCustManageCode(custManageCodeMap.get(vo.getCode()));
        }
        return list;
    }

    /**
     * 设置项目经理编码
     *
     * @param list
     * @return
     */
    private List<InsureOrgMgrVO> setCustManageCode(List<InsureOrgMgrVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Set<String> orgCodes = StreamEx.of(list).map(InsureOrgMgrVO::getCode).toSet();
        AssertUtil.notEmpty(orgCodes, new ApiException("机构不存在，请确认后重试！"));
        List<Tbpartassignmanager> tbpartassignmanagers = tbpartassignmanagerMapper.selectList(new LambdaQueryWrapper<Tbpartassignmanager>()
                .in(Tbpartassignmanager::getMerchantOrgCode, orgCodes).eq(Tbpartassignmanager::getMainManagerFlag, "0"));
        Map<String, String> custManageCodeMap = StreamEx.of(tbpartassignmanagers).toMap(Tbpartassignmanager::getMerchantOrgCode, Tbpartassignmanager::getCustManagerCode, (key1, key2) -> key2);
        for (InsureOrgMgrVO vo : list) {
            vo.setCustManageCode(custManageCodeMap.get(vo.getCode()));
        }
        return list;
    }

    /**
     * 设置经销商
     *
     * @param list
     * @return
     */
    private List<InsureOrgMgrVO> setDealer(List<InsureOrgMgrVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Set<String> orgCodes = StreamEx.of(list).map(InsureOrgMgrVO::getCode).toSet();
        AssertUtil.notEmpty(orgCodes, new ApiException("机构不存在，请确认后重试！"));
        List<ChannelDealer> channelDealers = channelDealerMapper.selectList(new LambdaQueryWrapper<ChannelDealer>()
                .in(ChannelDealer::getOwnerOrgCode, orgCodes));
        Map<String, ChannelDealer> dealersMap = StreamEx.of(channelDealers).toMap(ChannelDealer::getOwnerOrgCode, Function.identity());
        for (InsureOrgMgrVO vo : list) {
            ChannelDealer dealer = dealersMap.get(vo.getCode());
            if (dealer != null) {
                vo.setDealerOrgCode(dealer.getOrgCode());
                vo.setDealerOrgName(dealer.getOrgName());
            }
        }
        return list;
    }

}
