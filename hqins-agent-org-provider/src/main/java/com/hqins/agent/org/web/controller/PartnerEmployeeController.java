package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.request.PartnerEmployeeRequest;
import com.hqins.agent.org.model.vo.PartnerEmployeeVO;
import com.hqins.agent.org.model.vo.SimpleEmployeeVO;
import com.hqins.agent.org.service.PartnerEmployeeService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "合伙人-销售员管理")
@RestController
@RequestMapping("/partner/employees")
@RefreshScope
@Slf4j
public class PartnerEmployeeController {

    @Autowired
    private PartnerEmployeeService partnerEmployeeService;

    @ApiOperation("分页查询合伙人销售员")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PartnerEmployeeVO>> listMy(
            @ApiParam("归属合伙人名称") @RequestParam(value = "partnerName", required = false) String partnerName,
            @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
            @ApiParam("归属销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("归属销售团队名称") @RequestParam(value = "teamName", required = false) String teamName,
            @ApiParam("销售员代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售员证件号") @RequestParam(value = "idCode", required = false) String idCode,
            @ApiParam("销售员名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售员手机号") @RequestParam(value = "mobile", required = false) String mobile,
            @ApiParam("入职日期-开始") @RequestParam(value = "entryTimeStart", required = false) LocalDate entryTimeStart,
            @ApiParam("入职日期-结束") @RequestParam(value = "entryTimeEnd", required = false) LocalDate entryTimeEnd,
            @ApiParam("离职日期-开始") @RequestParam(value = "quitTimeStart", required = false) LocalDate quitTimeStart,
            @ApiParam("离职日期-结束") @RequestParam(value = "quitTimeEnd", required = false) LocalDate quitTimeEnd,
            @ApiParam("代理人状态 SERVING-有效 INVALID-无效 null查全部") @RequestParam(value = "status", required = false) String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        PartnerEmployeeRequest queryRequest = PartnerEmployeeRequest.builder()
                .code(code).idCode(idCode).name(name).partnerName(partnerName).orgName(orgName).orgCode(orgCode).teamName(teamName).mobile(mobile)
                .entryTimeStart(entryTimeStart).entryTimeEnd(entryTimeEnd).quitTimeStart(quitTimeStart).quitTimeEnd(quitTimeEnd).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(partnerEmployeeService.listMy(queryRequest));
    }

    @ApiOperation("合伙人下代理人员导出")
    @GetMapping("/export")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> export( @ApiParam("归属合伙人名称") @RequestParam(value = "partnerName", required = false) String partnerName,
                                     @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
                                     @ApiParam("归属销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
                                     @ApiParam("归属销售团队名称") @RequestParam(value = "teamName", required = false) String teamName,
                                     @ApiParam("销售员代码") @RequestParam(value = "code", required = false) String code,
                                     @ApiParam("销售员证件号") @RequestParam(value = "idCode", required = false) String idCode,
                                     @ApiParam("销售员名称") @RequestParam(value = "name", required = false) String name,
                                     @ApiParam("销售员手机号") @RequestParam(value = "mobile", required = false) String mobile,
                                     @ApiParam("入职日期-开始") @RequestParam(value = "entryTimeStart", required = false) LocalDate entryTimeStart,
                                     @ApiParam("入职日期-结束") @RequestParam(value = "entryTimeEnd", required = false) LocalDate entryTimeEnd,
                                     @ApiParam("离职日期-开始") @RequestParam(value = "quitTimeStart", required = false) LocalDate quitTimeStart,
                                     @ApiParam("离职日期-结束") @RequestParam(value = "quitTimeEnd", required = false) LocalDate quitTimeEnd) {
        PartnerEmployeeRequest queryRequest = PartnerEmployeeRequest.builder()
                .code(code).idCode(idCode).name(name).partnerName(partnerName).orgName(orgName).orgCode(orgCode).teamName(teamName).mobile(mobile)
                .entryTimeStart(entryTimeStart).entryTimeEnd(entryTimeEnd).quitTimeStart(quitTimeStart).quitTimeEnd(quitTimeEnd).build();

        return ApiResult.ok(partnerEmployeeService.exportData(queryRequest));
    }

    @ApiOperation("获取有管理权限的合伙人销售员，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleEmployeeVO>> listMyEmployeeSimple(@ApiParam("合伙人编码") @RequestParam(value = "topCode", required = false) String topCode,
                                                                  @ApiParam("合伙人机构编码") @RequestParam(value = "orgCode", required = false) String orgCode,
                                                                  @ApiParam("团队编码") @RequestParam(value = "teamCode", required = false) String teamCode,
                                                                  @ApiParam("销售员名称或者代码") @RequestParam(value = "value", required = false) String value,
                                                                  @ApiParam("状态：SERVING、ALL") @RequestParam(value = "status", required = false, defaultValue = "SERVING") String status,
                                                                  @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current",required = false, defaultValue = "1") long current,
                                                                  @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "20") long size) {

        EmployeeQueryRequest queryRequest = EmployeeQueryRequest.builder().status(status)
                .topCode(topCode).orgCode(orgCode).teamCode(teamCode).value(value).current(current).size(size).build();
        queryRequest.correctPageQueryParameters();
        return ApiResult.ok(partnerEmployeeService.listEmployeeSimple(queryRequest));
    }

    @PutMapping(value = "/change-mobile")
    @ResponseStatus(HttpStatus.OK)
    ApiResult<String> changeMobile(@RequestBody String message) {
        log.info("PartnerEmployeeController #change  message:{}", message);
        return ApiResult.ok(partnerEmployeeService.changeMobile(message));
    }
}
