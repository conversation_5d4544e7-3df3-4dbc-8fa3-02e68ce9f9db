package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.RankSequ;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.mapper.exms.RankDefMapper;
import com.hqins.agent.org.dao.mapper.exms.RankSequMapper;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.AppEmployeeStatusEnum;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.AppEmployeeRecruitmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * app人员招募接口层.
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 9:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AppEmployeeRecruitmentServiceImpl implements AppEmployeeRecruitmentService {

    private final TbempMapper tbempMapper;

    private final SupervisorEmployeeMapper supervisorEmployeeMapper;

    private final CacheService cacheService;

    private final RankSequMapper rankSequMapper;

    private final RankDefMapper rankDefMapper;

    @Override
    public AppEmployeeRecruitmentProgressVO queryRecruitmentProgressList(String employeeCode, String employeeName) {
        //缓存中获取当前登录人信息
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        if(allTbempMap.containsKey(employeeCode)){
            Tbemp tbemp = allTbempMap.get(employeeCode);
            List<AppEmployeeRecruitmentInfoListVO> appEmployeeRecruitmentInfoListVOS = tbempMapper.queryRecruitmentProgressList(tbemp.getEmpincode(), employeeName);
            if(CollUtil.isNotEmpty(appEmployeeRecruitmentInfoListVOS)){
                //计算年龄
                appEmployeeRecruitmentInfoListVOS.forEach(each -> {
                    Date birthday = each.getBirthday();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String format = sdf.format(birthday);
                    Integer age = calculateAgeWithMultipleFormats(format);
                    each.setAge(age);
                });
                //根据被招募人员状态进行分组
                Map<String, List<AppEmployeeRecruitmentInfoListVO>> statusMap = appEmployeeRecruitmentInfoListVOS.stream()
                        .collect(Collectors.groupingBy(AppEmployeeRecruitmentInfoListVO::getEmployeeStatus));
                AppEmployeeRecruitmentProgressVO progressVO = new AppEmployeeRecruitmentProgressVO();
                //面谈
                if(statusMap.containsKey(AppEmployeeStatusEnum.PENDING_INTERVIEW.getCode())){
                    progressVO.setInterviewEmployeeInfoList(sortRecruitmentInfoListVO(statusMap.get(AppEmployeeStatusEnum.PENDING_INTERVIEW.getCode())));
                }else {
                    progressVO.setInterviewEmployeeInfoList(new ArrayList<>());
                }
                //一面
                if(statusMap.containsKey(AppEmployeeStatusEnum.FIRST_INTERVIEW_PENDING.getCode())){
                    progressVO.setFirstRoundInterviewEmployeeInfoList(sortRecruitmentInfoListVO(statusMap.get(AppEmployeeStatusEnum.FIRST_INTERVIEW_PENDING.getCode())));
                }else {
                    progressVO.setFirstRoundInterviewEmployeeInfoList(new ArrayList<>());
                }
                //二面
                if(statusMap.containsKey(AppEmployeeStatusEnum.PENDING_SECOND_INTERVIEW.getCode())){
                    progressVO.setSecondRoundInterviewEmployeeInfoList(sortRecruitmentInfoListVO(statusMap.get(AppEmployeeStatusEnum.PENDING_SECOND_INTERVIEW.getCode())));
                }else {
                    progressVO.setSecondRoundInterviewEmployeeInfoList(new ArrayList<>());
                }
                //offer
                if(statusMap.containsKey(AppEmployeeStatusEnum.OFFER_PENDING_CONFIRMATION.getCode())){
                    progressVO.setOfferEmployeeInfoList(sortRecruitmentInfoListVO(statusMap.get(AppEmployeeStatusEnum.OFFER_PENDING_CONFIRMATION.getCode())));
                }else {
                    progressVO.setOfferEmployeeInfoList(new ArrayList<>());
                }
                //审核
                if(statusMap.containsKey(AppEmployeeStatusEnum.UNDER_REVIEW.getCode()) ||
                        statusMap.containsKey(AppEmployeeStatusEnum.PENDING_PRELIMINARY_REVIEW.getCode()) ||
                        statusMap.containsKey(AppEmployeeStatusEnum.REVIEW_FAILED.getCode()) ||
                        statusMap.containsKey(AppEmployeeStatusEnum.PRELIMINARY_REVIEW_TERMINATED.getCode()) ||
                        statusMap.containsKey((AppEmployeeStatusEnum.REVIEW_TERMINATED.getCode()))){
                    List<AppEmployeeRecruitmentInfoListVO> auditEmployeeInfoList = new ArrayList<>();
                    //审核中
                    List<AppEmployeeRecruitmentInfoListVO> underReviewList = statusMap.get(AppEmployeeStatusEnum.UNDER_REVIEW.getCode());
                    //待初审
                    List<AppEmployeeRecruitmentInfoListVO> preliminaryReviewList = statusMap.get(AppEmployeeStatusEnum.PENDING_PRELIMINARY_REVIEW.getCode());
                    //审核失败
                    List<AppEmployeeRecruitmentInfoListVO> reviewFailedList = statusMap.get(AppEmployeeStatusEnum.REVIEW_FAILED.getCode());
                    //初审终止
                    List<AppEmployeeRecruitmentInfoListVO> reviewterminatedList = statusMap.get(AppEmployeeStatusEnum.PRELIMINARY_REVIEW_TERMINATED.getCode());
                    //终审终止
                    List<AppEmployeeRecruitmentInfoListVO> terminatedList = statusMap.get(AppEmployeeStatusEnum.REVIEW_TERMINATED.getCode());
                    if(CollUtil.isNotEmpty(underReviewList)){
                        auditEmployeeInfoList.addAll(underReviewList);
                    }
                    if(CollUtil.isNotEmpty(preliminaryReviewList)){
                        auditEmployeeInfoList.addAll(preliminaryReviewList);
                    }
                    if(CollUtil.isNotEmpty(reviewFailedList)){
                        auditEmployeeInfoList.addAll(reviewFailedList);
                    }
                    if(CollUtil.isNotEmpty(reviewterminatedList)){
                        auditEmployeeInfoList.addAll(reviewterminatedList);
                    }
                    if(CollUtil.isNotEmpty(terminatedList)){
                        auditEmployeeInfoList.addAll(terminatedList);
                    }
                    if(CollUtil.isNotEmpty(auditEmployeeInfoList)){
                        progressVO.setAuditEmployeeInfoList(sortRecruitmentInfoListVO(auditEmployeeInfoList));
                    }else {
                        progressVO.setAuditEmployeeInfoList(new ArrayList<>());
                    }
                }else {
                    progressVO.setAuditEmployeeInfoList(new ArrayList<>());
                }
                //不合适
                if(statusMap.containsKey(AppEmployeeStatusEnum.FIRST_INTERVIEW_FAILED.getCode()) ||
                        statusMap.containsKey(AppEmployeeStatusEnum.SECOND_INTERVIEW_FAILED.getCode())){
                    List<AppEmployeeRecruitmentInfoListVO> interviewFailedList = new ArrayList<>();
                    //一面不合适
                    List<AppEmployeeRecruitmentInfoListVO> firstList = statusMap.get(AppEmployeeStatusEnum.FIRST_INTERVIEW_FAILED.getCode());
                    //二面不合适
                    List<AppEmployeeRecruitmentInfoListVO> secondList = statusMap.get(AppEmployeeStatusEnum.SECOND_INTERVIEW_FAILED.getCode());
                    if(CollUtil.isNotEmpty(firstList)){
                        interviewFailedList.addAll(firstList);
                    }
                    if(CollUtil.isNotEmpty(secondList)){
                        interviewFailedList.addAll(secondList);
                    }
                    if(CollUtil.isNotEmpty(interviewFailedList)){
                        progressVO.setInappropriateEmployeeInfoList(sortRecruitmentInfoListVO(interviewFailedList));
                    }else {
                        progressVO.setInappropriateEmployeeInfoList(new ArrayList<>());
                    }
                }else {
                    progressVO.setInappropriateEmployeeInfoList(new ArrayList<>());
                }
                //入职
                if(statusMap.containsKey(AppEmployeeStatusEnum.STAFF_VALID.getCode())){
                    progressVO.setOnboardingEmployeeInfoList(sortRecruitmentInfoListVO(statusMap.get(AppEmployeeStatusEnum.STAFF_VALID.getCode())));
                }else {
                    progressVO.setOnboardingEmployeeInfoList(new ArrayList<>());
                }
                //全部
                progressVO.setAllEmployeeInfoList(sortRecruitmentInfoListVO(appEmployeeRecruitmentInfoListVOS));
                return progressVO;
            }else {
                return null;
            }
        }else {
            return null;
        }
    }

    private List<AppEmployeeRecruitmentInfoListVO> sortRecruitmentInfoListVO(List<AppEmployeeRecruitmentInfoListVO> appEmployeeRecruitmentInfoListVOS) {
        return appEmployeeRecruitmentInfoListVOS.stream()
                .sorted((o1, o2) -> o2.getModifyTime().compareTo(o1.getModifyTime()))
                .collect(Collectors.toList());
    }

    @Override
    public List<AppEmployeeRecruitmentInfoListVO> queryMyAgentList(String employeeCode, String employeeName) {
        //判断人员是否为督导人员
        if(employeeCode.startsWith("S")){
            List<String> instCoeList = new ArrayList<>();
//            //督导人员需要区分 总部督导或非总部督导
//            SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
//            log.info("queryMyAgentList-supervisorEmployee {}", JSONObject.toJSONString(supervisorEmployee));
//            if(SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType())){
//                //总部督导
//                String topCodeList = supervisorEmployee.getTopCodeList();
//                if(StrUtil.isNotBlank(topCodeList)){
//                    List<String> strings = convertToList(topCodeList);
//                    instCoeList.addAll(strings);
//                }
//            }else {
//                //非总部督导
//                String orgCodeList = supervisorEmployee.getOrgCodeList();
//                if(StrUtil.isNotBlank(orgCodeList)){
//                    List<String> strings = convertToList(orgCodeList);
//                    instCoeList.addAll(strings);
//                }
//            }
//            if(CollUtil.isNotEmpty(instCoeList)){
//                //数据库查询
//                List<AppEmployeeRecruitmentInfoListVO> resultList = tbempMapper.queryMyAgentListByInstCodeList(instCoeList, employeeCode);
//                dataProcessing(resultList);
//                return resultList;
//            }else {
//                return null;
//            }
            //数据库查询
            List<AppEmployeeRecruitmentInfoListVO> resultList = tbempMapper.queryMyAgentListByInstCodeList(instCoeList, employeeCode, employeeName);
            dataProcessing(resultList);
            return resultList;
        }else {
            //缓存中获取当前登录人信息
            Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
            if(allTbempMap.containsKey(employeeCode)){
                Tbemp tbemp = allTbempMap.get(employeeCode);
                log.info("queryMyAgentList-TbEmp {}", JSONObject.toJSONString(tbemp));
                List<AppEmployeeRecruitmentInfoListVO> resultList = tbempMapper.queryMyAgentListByEmpInCodeAndInstCode(tbemp.getEmpincode(), employeeName);
                dataProcessing(resultList);
                return resultList;
            }else {
                return null;
            }
        }
    }

    @Override
    public AppEmployeeInfoVO queryAppRecruitmentEmployeeInfo(String employeeCode, String visitorId) {
        if(StrUtil.isBlank(employeeCode) && StrUtil.isBlank(visitorId)){
            return null;
        }
        List<AppEmployeeInfoVO> appEmployeeInfoVOList = tbempMapper.queryAppRecruitmentEmployeeInfo(employeeCode, visitorId);
        if(CollUtil.isNotEmpty(appEmployeeInfoVOList)){
            AppEmployeeInfoVO appEmployeeInfoVO = appEmployeeInfoVOList.get(0);
            if(appEmployeeInfoVO == null){
                return null;
            }else {
                List<AppEmployeePersonalExperienceVO> personalExperiences = tbempMapper.queryEmployeePersonalExperience(appEmployeeInfoVO.empCode);
                if(CollUtil.isNotEmpty(personalExperiences)){
                    appEmployeeInfoVO.employeePersonalExperiences = personalExperiences.stream()
                            .sorted(Comparator.comparing(AppEmployeePersonalExperienceVO::getStartDate))
                            .collect(Collectors.toList());
                }
                if(AppEmployeeStatusEnum.PENDING_INTERVIEW.getCode().equals(appEmployeeInfoVO.empStatus) ||
                        AppEmployeeStatusEnum.FIRST_INTERVIEW_PENDING.getCode().equals(appEmployeeInfoVO.empStatus) ||
                        AppEmployeeStatusEnum.FIRST_INTERVIEW_FAILED.getCode().equals(appEmployeeInfoVO.empStatus) ||
                        AppEmployeeStatusEnum.PENDING_SECOND_INTERVIEW.getCode().equals(appEmployeeInfoVO.empStatus) ||
                        AppEmployeeStatusEnum.SECOND_INTERVIEW_FAILED.getCode().equals(appEmployeeInfoVO.empStatus) ){
                    appEmployeeInfoVO.entryDocuments = null;
                }
                return appEmployeeInfoVO;
            }
        }else {
            return null;
        }
    }

    @Override
    public List<RankSequ> queryAppRankSequence(String companyCode) {
        List<RankSequ> rankSequList = rankSequMapper.getRankSequenceByCompanyCode(companyCode);
        log.info("queryAppRankSequence-rankSequList {}", JSONObject.toJSONString(rankSequList));
        if (CollectionUtils.isNotEmpty(rankSequList)){
            List<String> list = Arrays.asList("RLP", "OM", "FHWC", "SWS");
            rankSequList = rankSequList.stream().filter(o->list.contains(o.getRankSequCode())).collect(Collectors.toList());
        }
        return rankSequList;
    }

    @Override
    public List<RankDef> queryAppRankCode(String rankSequenceCode, String companyCode) {
        List<RankDef> rankDefs = rankDefMapper.queryRankCodeByRankSequenceCode(rankSequenceCode, companyCode);
        log.info("queryAppRankCode-rankDefs {}", JSONObject.toJSONString(rankDefs));
        return rankDefs;
    }

    @Override
    public Boolean isAuditFlag(String employeeCode) {
        List<String> codeList = tbempMapper.getEmployByEmployeeCode(employeeCode);
        return CollUtil.isNotEmpty(codeList);
    }

    /**
     * 将字符串转化为List<String>
     * @param str   字符串
     * @return      集合
     */
    public List<String> convertToList(String str) {
        return Arrays.stream(str.replaceAll("[\\[\\]\"]", "").split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 特殊处理数据
     * @param resultList 集合
     */
    private void dataProcessing(List<AppEmployeeRecruitmentInfoListVO> resultList) {
        if(CollUtil.isNotEmpty(resultList)){
            //计算年龄
            resultList.forEach(each -> {
                Date birthday = each.getBirthday();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String format = sdf.format(birthday);
                Integer age = calculateAgeWithMultipleFormats(format);
                each.setAge(age);
            });
        }
    }

    /**
     * 根据字符串类型的出生日期计算年龄，支持多种日期格式
     *
     * @param birthDateStr 出生日期字符串，支持多种格式
     * @return 计算出的年龄，如果日期格式错误或为空则返回null
     */
    public Integer calculateAgeWithMultipleFormats(String birthDateStr) {
        if (birthDateStr == null || birthDateStr.trim().isEmpty()) {
            return null;
        }

        LocalDate birthDate = null;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        birthDate = LocalDate.parse(birthDateStr.trim(), formatter);

        // 获取当前日期并计算年龄
        LocalDate currentDate = LocalDate.now();

        // 检查出生日期是否在未来
        if (birthDate.isAfter(currentDate)) {
            return null;
        }

        // 计算年龄
        Period period = Period.between(birthDate, currentDate);
        return period.getYears();
    }
}
