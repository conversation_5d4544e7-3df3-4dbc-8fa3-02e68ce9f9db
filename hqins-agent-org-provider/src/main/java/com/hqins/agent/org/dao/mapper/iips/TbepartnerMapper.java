package com.hqins.agent.org.dao.mapper.iips;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @date 2021-05-07
* @Description 公司信息表
*/
@Mapper
@DS("iips")
@Repository
public interface TbepartnerMapper extends BaseMapper<Tbepartner> {

    List<String> selectInstListByCompanyCode(Map map);
}

