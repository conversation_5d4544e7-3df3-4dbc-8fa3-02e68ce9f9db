package com.hqins.agent.org.utils;

import lombok.experimental.UtilityClass;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES加密工具
 *
 * <AUTHOR>
 * @since 2023/2/2
 */
@UtilityClass
public class AESUtil {

    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_CHARSET = "utf-8";

    /**
     * AES 加密操作
     *
     * @param content  待加密内容
     * @param password 加密密码
     * @param iv       使用 CBC 模式，需要一个向量iv，可增加加密算法的强度
     * @return 加密数据
     */
    public String encrypt(String content, String password, String iv) throws Exception {
        //创建密码器
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        //密码key(超过16字节即128bit的key，需要替换jre中的local_policy.jar和US_export_policy.jar，否则报错：Illegal key size)
        SecretKeySpec keySpec = new SecretKeySpec(password.getBytes(KEY_CHARSET), KEY_ALGORITHM);
        //向量iv
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(KEY_CHARSET));
        //初始化为加密模式的密码器
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec);
        //加密
        byte[] byteContent = content.getBytes(KEY_CHARSET);
        byte[] result = cipher.doFinal(byteContent);
        return new String(new Base64().encode(result), KEY_CHARSET);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    /**
     * AES 解密操作
     *
     * @param contentBytes 密文
     * @param password     密码
     * @param iv           使用CBC模式，需要一个向量iv，可增加加密算法的强度
     * @return 明文
     */
    public String decrypt(String contentBytes, String password, String iv) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(password.getBytes(KEY_CHARSET), KEY_ALGORITHM);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(KEY_CHARSET));
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec);
        byte[] encrypted1 = Base64.decodeBase64(contentBytes);//先用base64解码
        byte[] result = cipher.doFinal(encrypted1);
        return new String(result, KEY_CHARSET);
    }

    public static void main(String[] args)throws Exception {
        String password = "jBRCQd6p#hurZDhO8n6t4@pKrVa8JCKG";
        String iv = "P8MxE59mFRORPVUf";
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(password.getBytes(KEY_CHARSET), KEY_ALGORITHM);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(KEY_CHARSET));
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec);
        byte[] encrypted1 = Base64.decodeBase64("QyUlxuJfNYNACmdTPKxP4A==");//先用base64解码
        byte[] result = cipher.doFinal(encrypted1);
        String a = new String(result, KEY_CHARSET);
        System.out.println(a);
    }

    /**
     * 将16进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    public byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

}
