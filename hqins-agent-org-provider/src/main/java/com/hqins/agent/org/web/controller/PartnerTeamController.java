package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.request.PartnerTeamQueryRequest;
import com.hqins.agent.org.model.vo.PartnerTeamTreeNodeVO;
import com.hqins.agent.org.model.vo.SaleTeamVO;
import com.hqins.agent.org.model.vo.SimpleTeamTreeNodeVO;
import com.hqins.agent.org.model.vo.TeamVO;
import com.hqins.agent.org.service.PartnerEmployeeService;
import com.hqins.agent.org.service.PartnerTeamService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "合伙人-销售团队管理")
@RestController
@RequestMapping("/partner/teams")
@RefreshScope
@Slf4j
public class PartnerTeamController {

    @Autowired
    private PartnerTeamService partnerTeamService;

    @Autowired
    private PartnerEmployeeService partnerEmployeeService;

    @ApiOperation("查询合伙人机构下顶级销售组信息")
    @GetMapping("/list-top-sale-team")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SaleTeamVO>> listTopSaleTeam(
            @ApiParam("机构代码") @RequestParam(value = "instCode", required = false) String instCode
    ) {
        return ApiResult.ok(partnerTeamService.listTopSaleTeam(instCode));
    }

    @ApiOperation("查询合伙人机构下销售组信息")
    @GetMapping("/list-sale-team")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SaleTeamVO>> listSaleTeam(
            @ApiParam("机构代码") @RequestParam(value = "instCode", required = false) String instCode,
            @ApiParam("机构层级 AREA(\"营业区\"),\n" +
                    "    DEPT(\"营业部\"),\n" +
                    "    TEAM(\"营业组\");") @RequestParam(value = "teamLevel", required = false) TeamLevel teamLevel
    ) {
        return ApiResult.ok(partnerTeamService.listSaleTeam(instCode, teamLevel));
    }

    @ApiOperation("查询合伙人机构下销售组信息")
    @GetMapping("/orgCode/list-sale-team")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SaleTeamVO>> listSaleTeamByOrgCode(
            @ApiParam("合伙人机构") @RequestParam(value = "orgCode") String orgCode,
            @ApiParam("是否ifp 空-全量 true-IFP机构 false-非IFP机构") @RequestParam(value = "ifpFlag",required = false) Boolean ifpFlag) {
        return ApiResult.ok(partnerEmployeeService.listSaleTeamByOrgCode(orgCode,ifpFlag));
    }

    @ApiOperation("分页查询合伙人团队")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PartnerTeamTreeNodeVO>> listMyTree(
            @ApiParam("归属合伙人名称") @RequestParam(value = "partnerName", required = false) String partnerName,
            @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
            @ApiParam("团队代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("团队名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("团队级别") @RequestParam(value = "level",required = false) TeamLevel level,
            @ApiParam("团队级别,支持多选") @RequestParam(value = "teamLevel",required = false) String teamLevel,
            @ApiParam("团队状态,团队状态,ENABLED-有效；DISABLED-失效") @RequestParam(value = "status",required = false) String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        PartnerTeamQueryRequest queryRequest = PartnerTeamQueryRequest.builder()
                .partnerName(partnerName).orgName(orgName).code(code).name(name).level(level)
                .teamLevel(teamLevel).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(partnerTeamService.listMyTree(queryRequest));
    }

    /**
     * 获取合伙人销售团队简化版，只展示名称编码
     * 有管理权限的且生效状态的合伙人销售团队
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取有管理权限的合伙人销售团队，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleTeamTreeNodeVO>> listMyTreeSimple(
            @ApiParam("销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("营销团队代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("营销团队名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("团队状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "50") long size) {

        PartnerTeamQueryRequest queryRequest = PartnerTeamQueryRequest.builder()
                .orgCode(orgCode).code(code).name(name).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(partnerTeamService.listMyTreeSimple(queryRequest));
    }

    @ApiOperation("获取有管理权限的合伙人销售团队，督导账号H5使用")
    @GetMapping("/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleTeamTreeNodeVO>> frontListMySimple(
            @ApiParam("销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("营销团队代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("营销团队名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("团队状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "50") long size) {

        PartnerTeamQueryRequest queryRequest = PartnerTeamQueryRequest.builder()
                .orgCode(orgCode).code(code).name(name).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();
        RequestContextHolder.setAdminAppIdLocal(1L);
        RequestContextHolder.setStaffIdLocal(1L);
        return ApiResult.ok(partnerTeamService.listMyTreeSimple(queryRequest));
    }

    @ApiOperation("获取销售员管理的团队")
    @GetMapping("/managed")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TeamVO>> listManagePartnerTeams(
            @ApiParam("销售员代码") @RequestParam(value = "employeeCode", required = false) String employeeCode) {
        return ApiResult.ok(partnerTeamService.listManagePartnerTeams(employeeCode));
    }

}
