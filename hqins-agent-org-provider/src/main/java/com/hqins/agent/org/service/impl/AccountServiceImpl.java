package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.mapper.iips.TbepartnerMapper;
import com.hqins.agent.org.model.enums.AccountStatus;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.request.AccountQueryRequest;
import com.hqins.agent.org.model.request.ChannelEmployeeRequest;
import com.hqins.agent.org.model.request.PartnerEmployeeRequest;
import com.hqins.agent.org.model.vo.AccountVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.rpc.client.GoodsClient;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.*;
import com.hqins.agent.org.utils.PasswordUtil;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.um.model.dto.*;
import com.hqins.um.model.request.AgentCreateByAdminRequest;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class AccountServiceImpl implements AccountService {

    @Autowired
    private ChannelEmployeeService channelEmployeeService;
    @Autowired
    private PartnerEmployeeService partnerEmployeeService;
    @Autowired
    private TbepartnerMapper tbepartnerMapper;
    @Autowired
    private SupervisorEmployeeService supervisorEmployeeService;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private UmClient umClient;

    @Autowired
    private GoodsClient goodsClient;



    @Override
    public PageInfo<AccountVO> listMy(AccountQueryRequest req) {
        PageInfo<AccountVO> result = null;
        if (AgentOrgType.CHANNEL.equals(req.getOrgType())) {
            ChannelEmployeeRequest queryRequest = ChannelEmployeeRequest.builder()
                    .code(req.getCode()).name(req.getName()).channelName(req.getTopName())
                    .orgName(req.getOrgName()).teamName(req.getTeamName()).mobile(req.getMobile())
                    .current(req.getCurrent()).size(req.getSize()).build();
            queryRequest.correctPageQueryParameters();
            result = channelEmployeeService.listMyAccountVO(queryRequest);
        } else {
            PartnerEmployeeRequest queryRequest = PartnerEmployeeRequest.builder()
                    .code(req.getCode()).name(req.getName()).partnerName(req.getTopName())
                    .orgName(req.getOrgName()).teamName(req.getTeamName()).mobile(req.getMobile())
                    .current(req.getCurrent()).size(req.getSize()).build();
            //增加督导账号查询 isSupervisor == true 查询督导账号表
            if (Boolean.TRUE .equals(req.getIsSupervisor())){
                result = supervisorEmployeeService.listMyAccountVO(queryRequest);
            }else {
                result = partnerEmployeeService.listMyAccountVO(queryRequest);
            }
            //从销管库中拿到的数据没有topName.需要批量获取一下。
            Set<String> topCodes = StreamEx.of(result.getRecords()).map(AccountVO::getTopCode).toSet();
            //存放编码和名称map
            Map<String, String> tbepartnersMap = new HashMap<>(topCodes.size());
            if (!topCodes.isEmpty()) {
                LambdaQueryWrapper<Tbepartner> w = new LambdaQueryWrapper<Tbepartner>()
                        .in(Tbepartner::getCompanycode, topCodes);
                List<Tbepartner> tbepartners = tbepartnerMapper.selectList(w);
                for (Tbepartner t : tbepartners) {
                    tbepartnersMap.put(t.getCompanycode(), t.getCompanyname());
                }
            }
            //遍历page设置topName
            for (AccountVO vo : result.getRecords()) {
                vo.setTopName(tbepartnersMap.get(vo.getTopCode()));
            }
        }
        if (result.getRecords().isEmpty()) {
            return result;
        }
        //跨服务获取账号信息设置
        List<String> codes = StreamEx.of(result.getRecords()).map(AccountVO::getCode).toList();
        List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(codes);
        //存放用户编码和agentUserInfo
        if (!CollectionUtils.isEmpty(accountInfoDTOList)) {
            Map<String, AgentDTO> agentDtoMap = convertData(accountInfoDTOList);
            //遍历page设置AccountVO属性
            for (AccountVO vo : result.getRecords()) {
                AgentDTO u = agentDtoMap.get(vo.getCode());
                if (u == null) {
                    vo.setAccountStatus(AccountStatus.UNBIND);
                } else {
                    vo.setAgentId(u.getAgentId());
                    vo.setWechatBind(u.getBindWeChat());
                    if (AppConsts.INVALID.equals(u.getState())) {
                        if (EmployeeStatus.SERVING.equals(vo.getEmployeeStatus())) {
                            vo.setAccountStatus(AccountStatus.BLOCKED);
                        } else {
                            vo.setAccountStatus(AccountStatus.DISABLED);
                        }
                    } else {
                        vo.setAccountStatus(AccountStatus.ENABLED);
                    }
                }
            }
        } else {
            for (AccountVO vo : result.getRecords()) {
                if (EmployeeStatus.SERVING.equals(vo.getEmployeeStatus())) {
                    vo.setAccountStatus(AccountStatus.UNBIND);
                }
            }
        }

        return result;
    }


    public Map<String, AgentDTO> convertData(List<AccountInfoDTO> accountInfoDTOList) {
        Map<String, AgentDTO> agentDtoMap = Maps.newHashMap();
        for (AccountInfoDTO accountInfoDTO : accountInfoDTOList) {
            if (null == accountInfoDTO.getAgentInfo()) {
                continue;
            }
            agentDtoMap.put(accountInfoDTO.getAgentInfo().getEmployeeCode(), accountInfoDTO.getAgentInfo());
        }
        return agentDtoMap;
    }

    /**
     * 锁定账号
     *
     * @param orgType
     * @param employeeCode
     * @param agentId
     */
    @Override
    public void blocked(AgentOrgType orgType, String employeeCode, long agentId) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        } else {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getPartnerOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        }
        //跨服务把销售员账号停用
        updateAgentState(agentId, AppConsts.INVALID);
        //调用B端黑名单接口
        goodsClient.addEmployees(employeeCode,orgType);
    }


    /**
     * 更新代理人状态
     *
     * @param agentId 代理人id
     * @param state   状态
     */
    public void updateAgentState(long agentId, Integer state) {
        AccountInfoDTO accountInfoDTO = umClient.getAgentInfo(agentId);
        if (null != accountInfoDTO) {
            AgentStateDTO agentStateDTO = new AgentStateDTO();
            agentStateDTO.setUserId(accountInfoDTO.getUserId());
            agentStateDTO.setState(state);
            agentStateDTO.setStaffId(RequestContextHolder.getStaffId());
            umClient.updateEmployeeStateByAdmin(agentStateDTO);
        }
    }

    /**
     * 解锁账号
     *
     * @param orgType
     * @param employeeCode
     * @param agentId
     */
    @Override
    public void unblocked(AgentOrgType orgType, String employeeCode, long agentId) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        } else {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getPartnerOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        }
        AssertUtil.isTrue(EmployeeStatus.SERVING.equals(employeeVO.getStatus()), new ApiException("销售员是离职状态不可以单独启用账号"));
        //跨服务把销售员账号启用
        updateAgentState(agentId, AppConsts.VALID);
        //调用B端黑名单接口
        goodsClient.deleteEmployees(employeeCode,orgType);
    }

    /**
     * 解锁账号
     *
     * @param orgType
     * @param employeeCode
     * @param agentId
     */
    @Override
    public void unbindWx(AgentOrgType orgType, String employeeCode, long agentId) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        } else {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getPartnerOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        }
        //跨服务解绑微信
        AccountInfoDTO accountInfoDTO = umClient.getAgentInfo(agentId);
        if (null != accountInfoDTO) {
            WeChatUpdateDTO weChatUpdateDTO = new WeChatUpdateDTO();
            weChatUpdateDTO.setUserId(accountInfoDTO.getUserId());
            weChatUpdateDTO.setStaffId(RequestContextHolder.getStaffId());
            umClient.updateWeChatByAdmin(weChatUpdateDTO);
        }

    }

    @Override
    public void resetPwd(AgentOrgType orgType, String employeeCode, long agentId) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        } else {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getPartnerOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        }
        if (EmployeeStatus.SERVING.equals(employeeVO.getStatus())) {
            //跨服务重置密码
            AccountInfoDTO accountInfoDTO = umClient.getAgentInfo(agentId);
            if (null != accountInfoDTO) {
                CredentialResetDTO credentialResetDTO = new CredentialResetDTO();
                credentialResetDTO.setUserId(accountInfoDTO.getUserId());
                credentialResetDTO.setPassword(PasswordUtil.generatePwd(employeeVO.getIdCode()));
                credentialResetDTO.setStaffId(RequestContextHolder.getStaffId());
                umClient.resetCredentialByAdmin(credentialResetDTO);
            }
        }
    }

    @Override
    public void synchronizationUm(AgentOrgType orgType, String employeeCode) {
        EmployeeVO employeeVO = null;
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            employeeVO = channelEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        } else {
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
                AssertUtil.isTrue(myDataAccess.getPartnerOrgCodes().contains(employeeVO.getOrgCode()), new ApiException("编码错误，或者没有权限"));
            }
        }

        //同步um,创建账号
        AgentCreateByAdminRequest request = new AgentCreateByAdminRequest();
        request.setOrgType(orgType.name());
        request.setEmployeeCode(employeeVO.getCode());
        request.setState(AppConsts.VALID);
        request.setPhone(employeeVO.getMobile());
        request.setPassword(PasswordUtil.generatePwd(employeeVO.getIdCode()));
        request.setStaffId(RequestContextHolder.getStaffId());
        request.setName(employeeVO.getName());
        umClient.createAgentUser(request);
    }
}
