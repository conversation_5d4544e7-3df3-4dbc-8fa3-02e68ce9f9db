package com.hqins.agent.org.dao;

import com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory;
import com.hqins.agent.org.dao.entity.settle.HonorCurrentInfo;
import com.hqins.agent.org.model.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/21
 */
public interface HonorSystemDao {


    /**
     *  荣誉列表查询
     * @param empCode
     * @return
     */
    List<HonorQueryListVO> queryHonorList(String empCode);


    List<HonorPeriodMetricsVO> queryGuinnessMetrics(String agentCode, String checkPeriod, String honorCode, String value);

    HonorCurrentInfo queryQXJYHCurrentDetail(String empCode, String honorType);

    List<HonorAssessmentHistory> queryQXJYHHistoryDetail(String empCode, String honorType);

    List<HonorAssessmentHistory> queryQYBSXDetail(String empCode, String honorType, List<String> assessmentPeriods);

    List<HonorPeriodMetrics> queryQYBSXPeriodMetrics(String empCode, String honorType, List<String> assessmentPeriods);

    List<HonorPeriodMetrics> queryGRRYPeriodMetrics(String empCode, String honorType, List<String> honorCodeList, String period, String teamCode);

    List<HonorPeriodMetrics> queryImmediateHigherMetrics(String honorType, String honorCode, String period, String lastPosition);

    List<HonorPeriodMetrics> queryMDRTDetail(String empCode, String honorType, String period);

    List<HonorPeriodMetrics> queryQHJNXDetail(String empCode, String honorType, String period);

    List<HonorAssessmentHistory> queryHonorHistory(String empCode);

    List<HonorCongratulationVO> queryHonorCongratulationList(List<String> employeeCodeList, String honorType, String year);

    List<HonoraryTitleInfoVO> queryEmpHonoraryDetail(String honorId);

}
