package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelDealer;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.dao.mapper.exms.TbpartassignmanagerMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelDealerMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelTeamMapper;
import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.OrgService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class OrgServiceImpl implements OrgService {

    @Autowired
    private TbsaleteamMapper tbsaleteamMapper;
    @Autowired
    private ChannelTeamMapper channelTeamMapper;
    @Autowired
    private ChannelDealerMapper channelDealerMapper;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private BaseInstMapper baseInstMapper;
    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;

    @Override
    public List<OrgVO> getOrgsByTop(AgentOrgType orgType, String topCode, boolean levels) {
        AssertUtil.isTrue(!StringUtil.isBlank(topCode) && orgType != null, new ApiException("参数错误"));

        Tbepartner top = cacheService.getAllTbepartnersMap().get(topCode);
        String cpType = AppConsts.CPTYPE_CHANNEL;
        if (AgentOrgType.PARTNER.equals(orgType)) {
            cpType = AppConsts.CPTYPE_PARTNER;
        }
        AssertUtil.isTrue(top != null && cpType.equals(top.getCptype()), new ApiException("顶层编码错误"));

        List<BaseInst> list = StreamEx.of(cacheService.getAllBaseInsts()).filter(t -> "1".equals(t.getInstStatus())
                && top.getCompanyid().equals(t.getCompanyid())
                && (levels == true || t.getParentInstCode() == null)
        ).toList();

        return BeanCopier.copyList(list, inst -> PartnerConverter.instToOrgVO(inst, orgType));
    }

    @Override
    public List<OrgVO> getOrgs(AgentOrgType orgType, String orgCode, boolean levels) {
        AssertUtil.isTrue(!StringUtil.isBlank(orgCode) && orgType != null, new ApiException("参数错误"));

        BaseInst org = cacheService.getAllBaseInstsMap().get(orgCode);
        AssertUtil.isTrue(org != null && AppConsts.INST_STATUS_ENABLED.equals(org.getInstStatus()), new ApiException("机构编码错误"));

        Tbepartner top = cacheService.getAllIdTbepartnersMap().get(org.getCompanyid());
        String cpType = AppConsts.CPTYPE_CHANNEL;
        if (AgentOrgType.PARTNER.equals(orgType)) {
            cpType = AppConsts.CPTYPE_PARTNER;
        }
        AssertUtil.isTrue(top != null && cpType.equals(top.getCptype()), new ApiException("机构类型和机构不匹配"));

        List<BaseInst> list = new ArrayList<>();
        for (BaseInst t : cacheService.getAllBaseInsts()) {
            if (!AppConsts.INST_STATUS_ENABLED.equals(t.getInstStatus())) {
                continue;
            }
            if (!levels && orgCode.equals(t.getParentInstCode())) {
                list.add(t);
            }
            if (levels && t.getParentInstCode() != null && t.getParentInstCode().indexOf(orgCode) == 0) {
                list.add(t);
            }
        }

        return BeanCopier.copyList(list, inst -> PartnerConverter.instToOrgVO(inst, orgType));
    }

    /**
     * 根据机构代码查其上的所有父级机构
     * @param orgType
     * @param orgCode
     * @param levels
     * @return
     */
    @Override
    public List<OrgVO> getParentsOrgs(AgentOrgType orgType, String orgCode, boolean levels) {
        AssertUtil.isTrue(!StringUtil.isBlank(orgCode) && orgType != null, new ApiException("参数错误"));

        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(orgCode);
        AssertUtil.isTrue(baseInst != null && AppConsts.INST_STATUS_ENABLED.equals(baseInst.getInstStatus()), new ApiException("机构编码错误"));

        Tbepartner top = cacheService.getAllIdTbepartnersMap().get(baseInst.getCompanyid());
        String cpType = AppConsts.CPTYPE_CHANNEL;
        if (AgentOrgType.PARTNER.equals(orgType)) {
            cpType = AppConsts.CPTYPE_PARTNER;
        }
        AssertUtil.isTrue(top != null && cpType.equals(top.getCptype()), new ApiException("机构类型和机构不匹配"));

        List<BaseInst> list = new ArrayList<>();
        for (int i = 0 ;i < 15; i++){
            orgCode = baseInst.getParentInstCode();
            baseInst = cacheService.getAllBaseInstsMap().get(orgCode);
            if (baseInst.getParentInstCode() == null || orgCode.equals(baseInst.getParentInstCode())){
                //到顶级，退出
                list.add(baseInst);
                break;
            }
            if(levels){
                list.add(baseInst);
            }
        }
        return BeanCopier.copyList(list, inst -> PartnerConverter.instToOrgVO(inst, orgType));
    }

    @Override
    public List<TeamVO> getTeamsByOrg(AgentOrgType orgType, String orgCode, boolean levels) {
        AssertUtil.isTrue(!StringUtil.isBlank(orgCode) && orgType != null, new ApiException("参数错误"));

        if (AgentOrgType.CHANNEL.equals(orgType)) {
            LambdaQueryWrapper<ChannelTeam> w = new LambdaQueryWrapper<ChannelTeam>()
                    .eq(ChannelTeam::getStatus, TeamStatus.ENABLED.name())
                    .eq(ChannelTeam::getOrgCode, orgCode).orderByAsc(ChannelTeam::getCode);
            if (!levels) {
                //只获取一层
                w.eq(ChannelTeam::getLevel, TeamLevel.AREA.name());
            }
            List<ChannelTeam> channelTeams = channelTeamMapper.selectList(w);
            return BeanCopier.copyList(channelTeams, team -> PartnerConverter.channelTeamToTeamVO(team, orgType));
        }
        LambdaQueryWrapper<Tbsaleteam> w = new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, "00")
                .eq(Tbsaleteam::getInstCode, orgCode).orderByAsc(Tbsaleteam::getSaleteamincode);
        if (!levels) {
            //只获取一层
            w.eq(Tbsaleteam::getTeamlevel, "03");
        }
        List<Tbsaleteam> tbsaleteams = tbsaleteamMapper.selectList(w);
        return BeanCopier.copyList(tbsaleteams, team -> PartnerConverter.tbsaleteamToTeamVO(team, orgType));

    }

    @Override
    public List<TeamVO> getTeams(AgentOrgType orgType, String teamCode, boolean levels) {
        AssertUtil.isTrue(!StringUtil.isBlank(teamCode) && orgType != null, new ApiException("参数错误"));

        if (AgentOrgType.CHANNEL.equals(orgType)) {
            if (!levels) {
                List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                        .eq(ChannelTeam::getStatus, TeamStatus.ENABLED.name())
                        .eq(ChannelTeam::getParentCode, teamCode).orderByAsc(ChannelTeam::getCode));
                return BeanCopier.copyList(channelTeams, team -> PartnerConverter.channelTeamToTeamVO(team, orgType));
            } else {
                List<TeamVO> channelTeams = getChannelTeams(teamCode, new ArrayList<>());
                Collections.sort(channelTeams, (TeamVO o1, TeamVO o2) -> {
                    if (o1 == null || o2 == null) {
                        return 1;
                    }
                    return o1.getCode().compareTo(o2.getCode());
                });
                return channelTeams;
            }
        } else {
            if (!levels) {
                List<Tbsaleteam> tbsaleteams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                        .eq(Tbsaleteam::getSupersaleteamcode, teamCode).orderByAsc(Tbsaleteam::getSaleteamincode));
                return BeanCopier.copyList(tbsaleteams, team -> PartnerConverter.tbsaleteamToTeamVO(team, orgType));
            } else {
                List<TeamVO> partnerTeams = getPartnerTeams(teamCode, new ArrayList<>());
                Collections.sort(partnerTeams, (TeamVO o1, TeamVO o2) -> {
                    if (o1 == null || o2 == null) {
                        return 1;
                    }
                    return o1.getCode().compareTo(o2.getCode());
                });
                return partnerTeams;
            }
        }
    }

    /**
     * 递归查到子层，放到队列里
     *
     * @param parentCode
     * @param teams
     * @return
     */
    private List<TeamVO> getChannelTeams(String parentCode, List<TeamVO> teams) {
        List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .eq(ChannelTeam::getStatus, TeamStatus.ENABLED.name())
                .eq(ChannelTeam::getParentCode, parentCode).orderByAsc(ChannelTeam::getCode));
        List<TeamVO> childs = BeanCopier.copyList(channelTeams, team -> PartnerConverter.channelTeamToTeamVO(team, AgentOrgType.CHANNEL));
        for (TeamVO vo : childs) {
            if (!TeamLevel.TEAM.equals(vo.getOrgType())) {
                getChannelTeams(vo.getCode(), teams);
            }
        }
        teams.addAll(childs);
        return teams;
    }

    /**
     * 递归查到子层，放到队列里
     *
     * @param parentCode
     * @param teams
     * @return
     */
    private List<TeamVO> getPartnerTeams(String parentCode, List<TeamVO> teams) {
        List<Tbsaleteam> tbsaleteams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, "00")
                .eq(Tbsaleteam::getSupersaleteamcode, parentCode).orderByAsc(Tbsaleteam::getSaleteamincode));
        List<TeamVO> childs = BeanCopier.copyList(tbsaleteams, team -> PartnerConverter.tbsaleteamToTeamVO(team, AgentOrgType.PARTNER));
        for (TeamVO vo : childs) {
            if (!TeamLevel.TEAM.equals(vo.getLevel())) {
                getPartnerTeams(vo.getCode(), teams);
            }
        }
        teams.addAll(childs);
        return teams;
    }

    @Override
    public DealerVO getDealer(String orgCode) {
        BaseInst org = cacheService.getAllBaseInstsMap().get(orgCode);
        AssertUtil.isTrue(org != null && AppConsts.INST_STATUS_ENABLED.equals(org.getInstStatus()), new ApiException("机构编码错误"));

        DealerVO vo = new DealerVO();
        vo.setCode(org.getInstCode());
        vo.setName(org.getInstName());
        List<ChannelDealer> channelDealers = channelDealerMapper.selectList(new LambdaQueryWrapper<ChannelDealer>()
                .eq(ChannelDealer::getOwnerOrgCode, orgCode));
        if (!channelDealers.isEmpty()) {
            vo.setDealerCode(channelDealers.get(0).getOrgCode());
            vo.setDealerName(channelDealers.get(0).getOrgName());
        }
        return vo;
    }

    @Override
    public List<OrgVO> topOrg() {
        List<OrgVO> result;
        //查询渠道商
        TopQueryRequest request = TopQueryRequest.builder()
                .size(-1L)
                .build();
        Page<Tbepartner> p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_CHANNEL, request, null);
        result = StreamEx.of(p.getRecords()).map(baseInst -> {
            OrgVO orgVO = OrgVO.builder()
                    .code(baseInst.getCompanycode())
                    .name(baseInst.getCompanyname())
                    .orgType(AgentOrgType.CHANNEL)
                    .build();
            return orgVO;
        }).toList();

        //查询合伙人
        p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, request, null);
        result.addAll(StreamEx.of(p.getRecords()).map(baseInst -> {
            OrgVO orgVO = OrgVO.builder()
                    .code(baseInst.getCompanycode())
                    .name(baseInst.getCompanyname())
                    .orgType(AgentOrgType.PARTNER)
                    .build();
            return orgVO;
        }).toList());
        return result;
    }

    /**
     * 根据机构代码查公司信息
     * @param orgType
     * @param orgCode
     * @return
     */
    @Override
    public CompanyVO getCompany(AgentOrgType orgType, String orgCode) {
        AssertUtil.isTrue(!StringUtil.isBlank(orgCode) && orgType != null, new ApiException("参数错误"));
        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(orgCode);
        if (baseInst == null) {
            return null;
        }
        Tbepartner top = cacheService.getAllIdTbepartnersMap().get(baseInst.getCompanyid());
        String cpType = AppConsts.CPTYPE_CHANNEL;
        if (AgentOrgType.PARTNER.equals(orgType)) {
            cpType = AppConsts.CPTYPE_PARTNER;
        }
        AssertUtil.isTrue(top != null && cpType.equals(top.getCptype()), new ApiException("机构类型和机构不匹配"));
        CompanyVO companyVO = new CompanyVO();
        companyVO.setCode(orgCode);
        companyVO.setName(baseInst.getInstName());
        companyVO.setOrgType(orgType);
        companyVO.setCompanyId(top.getCompanyid());
        companyVO.setCompanyCode(top.getCompanycode());
        companyVO.setCompanyName(top.getCompanyname());
        return companyVO;
    }

    @Override
    public InstResultVo queryInstByOrgCode(String orgCode) {
        if (StringUtil.isEmpty(orgCode)){
            return null;
        }
        BaseInst baseInst = baseInstMapper.queryInstByOrgCode(orgCode);
        if (baseInst == null){
            return null;
        }
        InstResultVo instResultVo = BeanCopier.copyObject(baseInst,InstResultVo.class);
        return instResultVo;
    }

    /**
     * 根据机构代码列表，批量查公司信息
     * @param orgType
     * @param orgCodes
     * @return
     */
    @Override
    public List<CompanyVO> getCompanyList(AgentOrgType orgType, String[] orgCodes) {
        if (orgType == null || orgCodes == null || orgCodes.length == 0){
            return new ArrayList<>(0);
        }
        List<CompanyVO> resList = new ArrayList<>();
        for (String orgCode : orgCodes){
            try{
                CompanyVO companyVO = getCompany(orgType,orgCode);
                if (companyVO != null){
                    resList.add(companyVO);
                }
            }catch (Exception e){
                log.error("getCompanyList_error,orgType:{},orgCodes:{}",orgType,orgCodes,e);
            }
        }
        return resList;
    }

    @Override
    public List<PartassignmanagerVO> queryCompanyInst(String companyInstCode) {
        if (StringUtil.isEmpty(companyInstCode)){
            return new ArrayList<>(0);
        }
        List<PartassignmanagerVO> resList = new ArrayList<>();
        List<Tbpartassignmanager> tbpartassignmanagerList = tbpartassignmanagerMapper.queryCompanyInstByCompanyInstCode(companyInstCode);
        for(Tbpartassignmanager tbpartassignmanager : tbpartassignmanagerList){
            PartassignmanagerVO vo = new PartassignmanagerVO();
            fillMerchantOrgInfo(vo, tbpartassignmanager);
            resList.add(vo);
        }
        return resList;
    }

    public void fillMerchantOrgInfo(PartassignmanagerVO vo, Tbpartassignmanager tbpartassignmanager) {
        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(tbpartassignmanager.getMerchantOrgCode());
        if (baseInst != null) {
            vo.setMerchantOrgCode(baseInst.getInstCode());
            vo.setMerchantOrgName(baseInst.getInstName());
        } else {
            vo.setMerchantOrgCode(tbpartassignmanager.getMerchantOrgCode());
            vo.setMerchantOrgName(tbpartassignmanager.getMerchantOrgName());
        }
    }

    @Override
    public List<QueryAllVO>  queryAll() {
        return baseInstMapper.queryAll();
    }

    public TbepartnerVO getTbepartnerByOrgCode(String orgCode) {
        AssertUtil.isTrue(StringUtil.isNotBlank(orgCode), new ApiException("参数错误"));
        //查询机构
        BaseInst baseInst = cacheService.getAllBaseInstsMap().get(orgCode);
        //机构信息校验,未查询到返回null,不能直接抛异常,调用放自已处理
        if(baseInst == null || StringUtils.isEmpty(baseInst.getCompanyid())){
            log.info("未查到相应的机构信息或所查到的机构未归属到任何公司下!!");
            return null;
        }
        //AssertUtil.isTrue(baseInst != null , new ApiException("未查到相应的机构信息!"));
        //AssertUtil.isTrue(StringUtils.isNotEmpty(baseInst.getCompanyid()), new ApiException("所查到的机构未归属到任何公司下!"));
        //查询公司信息 tbepartner 表
        return  BeanCopier.copyObject(cacheService.getAllIdTbepartnersMap().get(baseInst.getCompanyid()),TbepartnerVO.class);
    }
}
