package com.hqins.agent.org.enums;

public enum MqOnsTagEnum {

    //销管Topic：U-HQINS-NCBSMS，Tag列表
    tagDPartnerEmpInter("TAG-D-PARTNEREMPINTER", "销管人员新增和变更"),
    ;


    private String tag;
    private String mag;

    MqOnsTagEnum(String tag, String mag) {
        this.tag = tag;
        this.mag = mag;
    }

    public static MqOnsTagEnum getMethodByTag(String tag) {
        for (MqOnsTagEnum value : MqOnsTagEnum.values()) {
            if (value.tag.equals(tag)) {
                return value;
            }
        }
        return null;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getMag() {
        return mag;
    }

    public void setMag(String mag) {
        this.mag = mag;
    }
}
