package com.hqins.agent.org.web.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.hqins.agent.org.model.request.DataAccessSaveRequest;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.model.vo.RoleAccessesVO;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.utils.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13
 * @Description
 */
@Api(tags = "数据访问权限控制")
@RestController
@RequestMapping("/data/accesses")
@RefreshScope
@Slf4j
public class DataAccessController {

    @Autowired
    private DataAccessService dataAccessService;

    @ApiOperation("更新角色下数据权限")
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> save(@Valid @RequestBody DataAccessSaveRequest request) {
        log.info("更新角色数据权限请求参数："+ JsonUtil.toJSON(request));
        dataAccessService.updateDataAccess(request);
        return ApiResult.ok();
    }

    @ApiOperation("查询指定角色的数据权限")
    @GetMapping("/roles/{roleId:" + Strings.REGEX_ID + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<RoleAccessesVO> query(@ApiParam("roleId") @PathVariable("roleId") Long roleId) {
        MyDataAccessVO myDataAccessVO = dataAccessService.getDataAccessByRoleId(roleId);
        RoleAccessesVO roleAccessesVO = new RoleAccessesVO();
        roleAccessesVO.setRoleId(roleId);
        List<String> partners = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(myDataAccessVO.getPartnerCodes())) {
            partners.addAll(myDataAccessVO.getPartnerCodes());
        }
        if (CollectionUtils.isNotEmpty(myDataAccessVO.getPartnerOrgCodes())) {
            partners.addAll(myDataAccessVO.getPartnerOrgCodes());
        }
        roleAccessesVO.setPartners(partners);
        List<String> channels = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(myDataAccessVO.getChannelCodes())) {
            channels.addAll(myDataAccessVO.getChannelCodes());
        }
        if (CollectionUtils.isNotEmpty(myDataAccessVO.getChannelOrgCodes())) {
            channels.addAll(myDataAccessVO.getChannelOrgCodes());
        }
        roleAccessesVO.setChannels(channels);
        return ApiResult.ok(roleAccessesVO);
    }

    @ApiOperation("当前后台某个用户拥有数据权限的组织机构code列表")
    @GetMapping("/current-staff/data-access")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<MyDataAccessVO> getMyDataAccess(@ApiParam("应用id") @RequestParam("adminAppId") Long adminAppId,
                                                     @ApiParam("员工id") @RequestParam("staffId") Long staffId) {

        return ApiResult.ok(dataAccessService.getMyDataAccessByAppIdStaffId(adminAppId, staffId));
    }
}
