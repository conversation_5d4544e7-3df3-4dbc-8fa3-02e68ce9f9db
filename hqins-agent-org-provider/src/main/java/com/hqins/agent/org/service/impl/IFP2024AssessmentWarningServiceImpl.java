package com.hqins.agent.org.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.cache.RedisService;
import com.hqins.agent.org.dao.IFP2024AssessmentDao;
import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.IFP2024AssessmentWarningService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class IFP2024AssessmentWarningServiceImpl implements IFP2024AssessmentWarningService {

    private final IFP2024AssessmentDao assessmentDao;

    private final RedisService redisService;

    private final CacheService cacheService;

    /**
     * 当期类型
     */
    private static final String CURRENT = "1";

    /**
     * 维持类型
     */
    private static final String KEEP = "MAINTAIN";

    /**
     * 晋升类型
     */
    private static final String PROMOTION = "PROMOTION";

    /**
     * 异常类型
     */
    private static final String ERROR_CHECK_TYPE = "ERROR";

    /**
     * 扣减月份
     */
    private static final int MONTHS_TO_SUBTRACT_THREE = 3;

    /**
     * 扣减月份
     */
    private static final int MONTHS_TO_SUBTRACT_SIX= 6;

    /**
     * 日期格式:年月日
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public IFP2024GroupVO queryGroupInfo(String paramType, String teamCode) {
        IFP2024GroupVO groupVO = new IFP2024GroupVO();
        LocalDate now = LocalDate.now();
        String quarterMonth = getQuarterMonth(Quarter.fromMonth(now.getMonthValue()), now);
        if(!CURRENT.equals(paramType)){
            quarterMonth = subtractMonths(quarterMonth, MONTHS_TO_SUBTRACT_THREE);
        }
        List<String> monthList = generateMonthList(paramType, quarterMonth);
        //获取销售团队
        Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
        if(!allTbsaleteamsMap.containsKey(teamCode)){
            log.info("团队为[{}],未在缓存中获取到相应的团队信息", teamCode);
            return groupVO;
        }
        Tbsaleteam tbsaleteam = allTbsaleteamsMap.get(teamCode);
        List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckGroupInfo(monthList, tbsaleteam.getSaleteamincode());
        if(CollUtil.isEmpty(personInfos)){
            log.info("团队为[{}],未查询到相应的参与考核人员", teamCode);
            return groupVO;
        }
        //获取各批次对应的状态
        List<CheckBatch> batchList = assessmentDao.queryBatchList(monthList, tbsaleteam.getInstCode());
        Map<String, CheckBatch> sortedBatchMap = batchList.stream()
                .sorted(Comparator.comparing(CheckBatch::getCheckMonth))
                .collect(Collectors.toMap(
                        CheckBatch::getCheckMonth,
                        checkBatch -> checkBatch,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
        //获取维持考核数据
        getKeepGroupInfo(personInfos, sortedBatchMap, groupVO, quarterMonth);
        //获取晋升考核数据
        getPromotionGroupInfo(personInfos, sortedBatchMap, groupVO, monthList, quarterMonth);
        return groupVO;
    }

    private void getPromotionGroupInfo(List<CheckBatchPersonInfoTmp> personInfos, Map<String, CheckBatch> sortedBatchMap, IFP2024GroupVO groupVO, List<String> monthList, String quarterMonth) {
        List<IFP2024GroupInfoVO> groupInfoVOS = new ArrayList<>();
        LocalDate now = LocalDate.now();
        int value = now.getMonthValue();
        monthList.forEach(each -> {
            LocalDate date = LocalDate.parse(each + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            int checkMonthValue = date.getMonthValue();
            IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
            if(value >= checkMonthValue){
                if(sortedBatchMap.containsKey(each)){
                    List<CheckBatchPersonInfoTmp> personList = personInfos.stream()
                            .filter(personInfo -> Objects.equals(PROMOTION, personInfo.getCheckType())
                                    && Objects.equals(personInfo.getCheckMonth(), String.format("%d-%02d", date.getYear(), date.getMonthValue()))
                                    && Objects.equals(personInfo.getRankSeqCode(), personInfo.getTargetRankSeqCode())
                                    && Objects.equals(personInfo.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL1.getLevel())))
                            .collect(Collectors.toList());
                    groupInfoVO.setStatus(Objects.equals(sortedBatchMap.get(each).getCheckStatus(), "0") ? "试算" : "已确认");
                    if(CollUtil.isNotEmpty(personList)){
                        groupInfoVO.setValue(String.valueOf(personList.size()));
                        List<IFP2024EmployeeVO> employeeVOS = new ArrayList<>();
                        personList.forEach(personInfo -> {
                            IFP2024EmployeeVO employeeVO = new IFP2024EmployeeVO();
                            employeeVO.setEmployeeCode(personInfo.getAgentCode());
                            employeeVO.setEmployeeName(personInfo.getAgentName());
                            employeeVOS.add(employeeVO);
                        });
                        groupInfoVO.setEmployeeList(employeeVOS);
                    }else {
                        groupInfoVO.setValue("0");
                        groupInfoVO.setEmployeeList(new ArrayList<>());
                    }
                }else {
                    groupInfoVO.setValue("0");
                    groupInfoVO.setStatus("已确认");
                    groupInfoVO.setEmployeeList(new ArrayList<>());
                }
            }else {
                groupInfoVO.setValue("");
                groupInfoVO.setEmployeeList(new ArrayList<>());
            }
            groupInfoVO.setName(checkMonthValue + "月晋升");
            groupInfoVOS.add(groupInfoVO);
        });
        //获取晋升合伙人标签
        List<CheckBatchPersonInfoTmp> personList = personInfos.stream()
                .filter(personInfo -> Objects.equals(PROMOTION, personInfo.getCheckType())
                        && Objects.equals(personInfo.getCheckMonth(), quarterMonth)
                        && !Objects.equals(personInfo.getRankSeqCode(), personInfo.getTargetRankSeqCode()))
                .collect(Collectors.toList());
        IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
        groupInfoVO.setName("晋升合伙人");
        groupInfoVO.setStatus(Objects.equals(sortedBatchMap.get(quarterMonth).getCheckStatus(), "0") ? "试算" : "已确认");
        if(CollUtil.isNotEmpty(personList)){
            List<CheckBatchPersonInfoTmp> collect = personList.stream()
                    .filter(personInfo -> Objects.equals(personInfo.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL1.getLevel())) || Objects.equals(personInfo.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL2.getLevel())))
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(collect)){
                groupInfoVO.setValue(String.valueOf(collect.size()));
                List<IFP2024EmployeeVO> employeeVOS = new ArrayList<>();
                collect.forEach(personInfo -> {
                    IFP2024EmployeeVO employeeVO = new IFP2024EmployeeVO();
                    employeeVO.setEmployeeCode(personInfo.getAgentCode());
                    employeeVO.setEmployeeName(personInfo.getAgentName());
                    employeeVOS.add(employeeVO);
                });
                groupInfoVO.setEmployeeList(employeeVOS);
                groupInfoVOS.add(groupInfoVO);
            }else {
                groupInfoVO.setValue("0");
                groupInfoVO.setEmployeeList(new ArrayList<>());
                groupInfoVOS.add(groupInfoVO);
            }
        }else {
            groupInfoVO.setValue("0");
            groupInfoVO.setEmployeeList(new ArrayList<>());
            groupInfoVOS.add(groupInfoVO);
        }
        //填充最终结果
        groupVO.setPromotion(groupInfoVOS);
    }

    private void getKeepGroupInfo(List<CheckBatchPersonInfoTmp> personInfos, Map<String, CheckBatch> sortedBatchMap, IFP2024GroupVO groupVO, String quarterMonth) {
        List<IFP2024GroupInfoVO> groupInfoVOS = new ArrayList<>();
        CheckBatch checkBatch = sortedBatchMap.get(quarterMonth);
        //过滤参与维持考核类型
        List<CheckBatchPersonInfoTmp> keepPersonList = personInfos.stream()
                .filter(each -> Objects.equals(KEEP, each.getCheckType()) && Objects.equals(each.getCheckMonth(), quarterMonth))
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(keepPersonList)){
            //过滤已达标人员
            List<CheckBatchPersonInfoTmp> collect = keepPersonList.stream()
                    .filter(each -> Objects.equals(each.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.KEEP_LEVEL.getLevel())))
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(collect)){
                IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
                groupInfoVO.setName("已达标");
                groupInfoVO.setValue(String.valueOf(collect.size()));
                groupInfoVO.setStatus(Objects.equals(checkBatch.getCheckStatus(),"0") ? "试算" : "已确认");
                List<IFP2024EmployeeVO> employeeVOS = new ArrayList<>();
                collect.forEach(each -> {
                    IFP2024EmployeeVO employeeVO =new IFP2024EmployeeVO();
                    employeeVO.setEmployeeCode(each.getAgentCode());
                    employeeVO.setEmployeeName(each.getAgentName());
                    employeeVOS.add(employeeVO);
                });
                groupInfoVO.setEmployeeList(employeeVOS);
                groupInfoVOS.add(groupInfoVO);
            }
            else {
                IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
                groupInfoVO.setName("已达标");
                groupInfoVO.setValue("0");
                groupInfoVO.setStatus(Objects.equals(checkBatch.getCheckStatus(),"0") ? "试算" : "已确认");
                groupInfoVO.setEmployeeList(new ArrayList<>());
                groupInfoVOS.add(groupInfoVO);
            }
            //过滤未达标
            List<CheckBatchPersonInfoTmp> collect1 = keepPersonList.stream()
                    .filter(each -> Objects.equals(each.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.REDUCE_LEVEL1.getLevel())) || Objects.equals(each.getCheckSettleResult(), String.valueOf(CheckManageIFPEnum.QUIT.getLevel())))
                    .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(collect1)){
                IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
                groupInfoVO.setName("未达标");
                groupInfoVO.setValue(String.valueOf(collect1.size()));
                groupInfoVO.setStatus(Objects.equals(checkBatch.getCheckStatus(),"0") ? "试算" : "已确认");
                List<IFP2024EmployeeVO> employeeVOS = new ArrayList<>();
                collect1.forEach(each -> {
                    IFP2024EmployeeVO employeeVO =new IFP2024EmployeeVO();
                    employeeVO.setEmployeeCode(each.getAgentCode());
                    employeeVO.setEmployeeName(each.getAgentName());
                    employeeVOS.add(employeeVO);
                });
                groupInfoVO.setEmployeeList(employeeVOS);
                groupInfoVOS.add(groupInfoVO);
            }else {
                IFP2024GroupInfoVO groupInfoVO = new IFP2024GroupInfoVO();
                groupInfoVO.setName("未达标");
                groupInfoVO.setValue("0");
                groupInfoVO.setStatus(Objects.equals(checkBatch.getCheckStatus(),"0") ? "试算" : "已确认");
                groupInfoVO.setEmployeeList(new ArrayList<>());
                groupInfoVOS.add(groupInfoVO);
            }
        }
        groupVO.setKeep(groupInfoVOS);
    }

    private static List<String> generateMonthList(String paramType, String quarterMonth) {
        List<String> monthList = new ArrayList<>();
        LocalDate date = LocalDate.parse(quarterMonth + "-01", DATE_FORMATTER);
        for (int i = 2; i >= 0; i--) {
            monthList.add(date.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        return monthList;
    }

    private static String getQuarterMonth(Quarter quarter, LocalDate now) {
        return String.format("%d-%02d", now.getYear(), Quarter.fromMonth(now.getMonthValue()).getEndMonth());
    }


    @Override
    public List<IFP2024TabVO> queryEmployeeInfo(String paramType, String employeeCode) {
        String redisKey = generateRedisKey(paramType, employeeCode);
        List<IFP2024TabVO> result;
        result = getCachedEmployeeInfo(redisKey);
        if(CollUtil.isNotEmpty(result)){
            return result;
        }
        RankDef rankInfo = assessmentDao.getRankInfo(employeeCode);
        if(Objects.isNull(rankInfo)) {
            log.info("工号为:[{}],未获取到考核人员的职级信息", employeeCode);
            return result;
        }
        log.info("工号为:[{}]的考核人员职级信息是:[{}]", employeeCode,rankInfo);
        if(!IFP2024RankEnum.exists(rankInfo.getRankCode())) {
            log.info("工号为:[{}]的职级不在考核范围内", employeeCode);
            return result;
        }
        if(Objects.equals(CURRENT, paramType)) {
            result = getCurrentEmployeeInfo(result, employeeCode, rankInfo);
        } else {
            result = getPreviousEmployeeInfo(result, employeeCode, rankInfo);
        }
        if(CollUtil.isEmpty(result)){
            log.info("工号为:[{}],未查询到该人员的考核信息", employeeCode);
            return result;
        }
        cacheEmployeeInfo(redisKey, result);
        return result;
    }

    private void cacheEmployeeInfo(String redisKey, List<IFP2024TabVO> assessmentResults) {
        Gson gson = new Gson();
        if (redisService.setExpire(redisKey, gson.toJson(assessmentResults), 10, TimeUnit.MINUTES)) {
            log.info("缓存个人考核信息成功,缓存的键为:[{}]", redisKey);
        }
    }

    private List<IFP2024TabVO> getCachedEmployeeInfo(String redisKey) {
        Gson gson = new Gson();
        if (Objects.nonNull(redisService.get(redisKey))) {
            log.info("获取当前考核人员的缓存信息成功,查询的键为:[{}]", redisKey);
            Type listType = new TypeToken<List<IFP2024TabVO>>() {}.getType();
            return gson.fromJson(redisService.get(redisKey).toString(), listType);
        }
        return new ArrayList<>();
    }

    private String generateRedisKey(String paramType, String employeeCode) {
        return "employeeInfo:" + paramType + "-" + employeeCode;
    }

    /**
     *
     * 获取员工上期考核预警结果
     * @param result            考核结果页签集合
     * @param employeeCode      员工代码
     * @param rankInfo          职级信息
     * @return                  考核结果页签集合
     */
    private List<IFP2024TabVO> getPreviousEmployeeInfo(List<IFP2024TabVO> result, String employeeCode, RankDef rankInfo) {
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        if(Objects.equals(IFP2024RankEnum.FHWC.getValue(), rankInfo.getRankSequCode())){
            //获取维持页签
            getKeepResult(year, month, employeeCode, result, Objects.equals(IFP2024RankEnum.FHWC01.getValue(), rankInfo.getRankCode()));
            //获取晋升页签
            getPromotionResult(year, month, employeeCode, result);
            //获取晋升合伙人类型
            if(Objects.equals(IFP2024RankEnum.FHWC03.getValue(), rankInfo.getRankCode()) || Objects.equals(IFP2024RankEnum.FHWC04.getValue(), rankInfo.getRankCode())){
                //一、三季度上期
                String format = String.format("%d-%02d", year, Quarter.fromMonth(month).getEndMonth());
                if(month <= Quarter.FIRST_QUARTER.getEndMonth() || (month > Quarter.SECOND_QUARTER.getEndMonth() && month <= Quarter.THIRD_QUARTER.getEndMonth())){
                    String oneMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_THREE);
                    List<String> partnerList = new ArrayList<>();
                    partnerList.add(oneMonth);
                    //查询该考核人员
                    List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckPersonInfo(partnerList, employeeCode);
                    if(CollUtil.isEmpty(personInfos)){
                        log.info("工号为:[{}]的考核人员未查询到一三季度上期晋升合伙人类型的考核信息", employeeCode);
                        return result;
                    }
                    Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(personInfos, "PARTNER");
                    getPartnerEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);
                }
                //二、四季度上期
                if((month > Quarter.FIRST_QUARTER.getEndMonth() && month <= Quarter.SECOND_QUARTER.getEndMonth()) || (month > Quarter.THIRD_QUARTER.getEndMonth() && month <= Quarter.FOURTH_QUARTER.getEndMonth())){
                    String oneMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_THREE);
                    List<String> partnerList = new ArrayList<>();
                    partnerList.add(oneMonth);
                    //查询该考核人员
                    List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckPersonInfo(partnerList, employeeCode);
                    if(CollUtil.isEmpty(personInfos)){
                        String twoMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_SIX);
                        partnerList.add(twoMonth);
                        //查询该考核人员
                        List<CheckBatchPersonInfoTmp> newPersonInfos = assessmentDao.queryCheckPersonInfo(partnerList, employeeCode);
                        if(CollUtil.isEmpty(personInfos)){
                            log.info("工号为:[{}]的考核人员未查询到二四季度上期晋升合伙人类型的考核信息", employeeCode);
                            return result;
                        }
                        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(newPersonInfos, "PARTNER");
                        getPartnerEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);
                    }else {
                        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(personInfos, "PARTNER");
                        getPartnerEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);
                    }
                }
            }
        }else {
            //获取维持页签
            String format;
            if(month <= Quarter.SECOND_QUARTER.getEndMonth()){
                format = String.format("%d-%02d", year, Quarter.SECOND_QUARTER.getEndMonth());
            }else {
                format = String.format("%d-%02d", year, Quarter.FOURTH_QUARTER.getEndMonth());
            }
            String tmpMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_SIX);
            List<String> monthList = new ArrayList<>();
            monthList.add(tmpMonth);
            //查询该考核人员
            List<CheckBatchPersonInfoTmp> newPersonInfos = assessmentDao.queryCheckPersonInfo(monthList, employeeCode);
            if(CollUtil.isEmpty(newPersonInfos)){
                log.info("工号为:[{}]的考核人员未查询到上期的考核信息", employeeCode);
                return result;
            }
            //获取维持页签
            getSWSKeepResult(newPersonInfos, result);
            //获取晋升页签
            getSWSPromotionResult(newPersonInfos, result);
            //获取晋升合伙人页签
            if(Objects.equals(Quarter.fromMonth(month).getEndMonth(), Quarter.SECOND_QUARTER.getEndMonth()) || Objects.equals(Quarter.fromMonth(month).getEndMonth(), Quarter.FOURTH_QUARTER.getEndMonth())){
                //晋升合伙人考核期
                String partnerMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_THREE);
                monthList.clear();
                monthList.add(partnerMonth);
                //查询该考核人员
                List<CheckBatchPersonInfoTmp> partnerPersonInfos = assessmentDao.queryCheckPersonInfo(monthList, employeeCode);
                if(CollUtil.isEmpty(partnerPersonInfos)){
                    //获取晋升合伙人页签
                    Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(newPersonInfos, "PARTNER");
                    getPartnerEmployeeCheckInfo(newPersonInfos, groupByTmpIdMap, result);
                }else {
                    //获取晋升合伙人页签
                    Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(partnerPersonInfos, "PARTNER");
                    getPartnerEmployeeCheckInfo(partnerPersonInfos, groupByTmpIdMap, result);
                }
            }else {
                //获取晋升合伙人页签
                Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(newPersonInfos, "PARTNER");
                getPartnerEmployeeCheckInfo(newPersonInfos, groupByTmpIdMap, result);
            }
        }
        if(CollUtil.isEmpty(result)){
            return null;
        }
        return result;
    }

    private void getSWSPromotionResult(List<CheckBatchPersonInfoTmp> newPersonInfos, List<IFP2024TabVO> result) {
        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(newPersonInfos, "PROMOTION");
        if(!CollUtil.isEmpty(groupByTmpIdMap)){
            getPromotionEmployeeCheckInfo(newPersonInfos, groupByTmpIdMap, result);
        }
    }

    private void getSWSKeepResult(List<CheckBatchPersonInfoTmp> newPersonInfos, List<IFP2024TabVO> result) {
        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(newPersonInfos, "KEEP");
        if(!CollUtil.isEmpty(groupByTmpIdMap)){
            getKeepEmployeeCheckInfo(newPersonInfos, groupByTmpIdMap, result, null);
        }
    }

    private void getPromotionResult(int year, int month, String employeeCode, List<IFP2024TabVO> result) {
        String format = String.format("%d-%02d", year, month);
        String promotionMonth = subtractMonths(format, 1);
        if (promotionMonth.isEmpty()) {
            log.info("未查询到工号为[{}]的考核人员上期晋升类型的考核周期信息", employeeCode);
            return;
        }
        List<String> promotionList = new ArrayList<>();
        promotionList.add(promotionMonth);
        List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckPersonInfo(promotionList, employeeCode);
        if (CollUtil.isEmpty(personInfos)) {
            log.info("未查询到工号为[{}]的考核人员上期晋升类型的考核信息", employeeCode);
            return;
        }
        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(personInfos, "PROMOTION");
        if(CollUtil.isEmpty(groupByTmpIdMap)){
            return ;
        }
        getPromotionEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);
    }

    private void getKeepResult(int year, int month, String employeeCode, List<IFP2024TabVO> result, boolean flag) {
        String keepMonth;
        if(flag){
            String format = String.format("%d-%02d", year, month);
            keepMonth = subtractMonths(format, 1);
        }else {
            String format = String.format("%d-%02d", year, Quarter.fromMonth(month).getEndMonth());
            keepMonth = subtractMonths(format, MONTHS_TO_SUBTRACT_THREE);
        }
        if (StrUtil.isEmpty(keepMonth)) {
            log.info("工号为:[{}]的考核人员未查询到上期维持类型的考核周期信息", employeeCode);
            return;
        }
        List<String> keepMonthList = Collections.singletonList(keepMonth);
        List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckPersonInfo(keepMonthList, employeeCode);
        if (CollUtil.isEmpty(personInfos)) {
            log.info("工号为:[{}]的考核人员未查询到上期维持类型的考核信息", employeeCode);
            return;
        }
        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = getGroupByTmpIdMap(personInfos, "KEEP");
        if (CollUtil.isEmpty(groupByTmpIdMap)) {
            return;
        }
        if(flag){
            getKeepEmployeeCheckInfo(personInfos, groupByTmpIdMap, result, keepMonth);
        }else {
            getKeepEmployeeCheckInfo(personInfos, groupByTmpIdMap, result, null);
        }

    }

    private Map<String, List<CheckBatchPersonConfig>> getGroupByTmpIdMap(List<CheckBatchPersonInfoTmp> personInfos, String name) {
        //获取该考核人员在考核期内所有的考核项
        List<String> tmpIdList = personInfos.stream()
                .filter(each -> StrUtil.isNotBlank(each.getId()))
                .map(CheckBatchPersonInfoTmp::getId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(tmpIdList)) {
            String agentCode = personInfos.get(0).getAgentCode();
            switch (name) {
                case "KEEP":
                    log.info("工号为:[{}]的考核人员未查询到上期维持类型的考核指标信息", agentCode);
                    break;
                case "PROMOTION":
                    log.info("工号为:[{}]的考核人员未查询到上期晋升类型的考核指标信息", agentCode);
                    break;
                case "PARTNER":
                    log.info("工号为:[{}]的考核人员未查询到上期晋升合伙人类型的考核指标信息", agentCode);
                    break;
                default:
                    log.warn("Unknown name parameter: {}", name);
                    break;
            }
            return Collections.emptyMap();
        }
        List<CheckBatchPersonConfig> personConfigs = assessmentDao.queryAllPersonConfig(tmpIdList);
        return personConfigs.stream()
                .filter(each -> !ERROR_CHECK_TYPE.equals(each.getCheckType()))
                .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckPersonTmpId));
    }

    public static String subtractMonths(String inputDate, int monthsToSubtract) {
        // 解析输入的日期字符串
        LocalDate date = LocalDate.parse(inputDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 减去指定的月份数
        LocalDate resultDate = date.minusMonths(monthsToSubtract);
        // 格式化结果日期并返回
        return resultDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    public static String plusMonths(String inputDate, int monthsToPlus) {
        // 解析输入的日期字符串
        LocalDate date = LocalDate.parse(inputDate + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 减去指定的月份数
        LocalDate resultDate = date.plusMonths(monthsToPlus);
        // 格式化结果日期并返回
        return resultDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     *
     * 获取员工当期考核预警结果
     * @param result            考核结果页签集合
     * @param employeeCode      员工代码
     * @param rankInfo          职级信息
     * @return                  考核结果页签集合
     */
    private List<IFP2024TabVO> getCurrentEmployeeInfo(List<IFP2024TabVO> result, String employeeCode, RankDef rankInfo) {
        // 需要查询的考核期集合
        List<String> checkMonthList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        int month = now.getMonthValue();

        // 获取考核期数据
        if (Objects.equals(IFP2024RankEnum.FHWC.getValue(), rankInfo.getRankSequCode())) {
            //补充逻辑:见习顾问每月都跑维持与晋升
            if(Objects.equals(IFP2024RankEnum.FHWC01.getValue(), rankInfo.getRankCode())){
                checkMonthList.add(String.format("%d-%02d", year, month));
            }else {
                // 获取顾问序列维持或晋级考核的考核期
                getCurrentCheckMonthForKeepOrPromotion(checkMonthList, true, year, month);
            }
            //晋升合伙人
            if(Objects.equals(Quarter.FIRST_QUARTER.getEndMonth(),Quarter.fromMonth(month).getEndMonth())){
                checkMonthList.add(String.format("%d-%02d", year, Quarter.SECOND_QUARTER.getEndMonth()));
            }
            if(Objects.equals(Quarter.THIRD_QUARTER.getEndMonth(),Quarter.fromMonth(month).getEndMonth())){
                checkMonthList.add(String.format("%d-%02d", year, Quarter.FOURTH_QUARTER.getEndMonth()));
            }
        } else {
            // 获取合伙人序列维持或晋级考核的考核期
            getCurrentCheckMonthForKeepOrPromotion(checkMonthList, false, year, month);
        }

        if (CollUtil.isEmpty(checkMonthList)) {
            log.error("{}：未查询到该员工当期对应的考核周期信息", employeeCode);
            return null;
        }

        // 使用 Stream 流去重
        List<String> distinctList = checkMonthList.stream()
                .distinct()
                .collect(Collectors.toList());

        // 查询该考核人员
        List<CheckBatchPersonInfoTmp> personInfos = assessmentDao.queryCheckPersonInfo(distinctList, employeeCode);
        if (CollUtil.isEmpty(personInfos)) {
            log.error("{}：未查询到该员工当期对应的考核信息", employeeCode);
            return null;
        }

        // 获取该考核人员在考核期内所有的考核项
        List<String> tmpIdList = personInfos.stream()
                .filter(each -> StrUtil.isNotBlank(each.getId()))
                .map(CheckBatchPersonInfoTmp::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(tmpIdList)) {
            log.error("{}：未查询到该员工当期对应的考核指标信息", employeeCode);
            return null;
        }

        List<CheckBatchPersonConfig> personConfigs = assessmentDao.queryAllPersonConfig(tmpIdList);

        // 过滤check_type为ERROR的无效数据 并 根据tmpId进行分组
        Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap = personConfigs.stream()
                .filter(each -> !Objects.equals(each.getCheckType(), ERROR_CHECK_TYPE))
                .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckPersonTmpId));

        // 获取维持页签
        if(Objects.equals(IFP2024RankEnum.FHWC01.getValue(), rankInfo.getRankCode())){
            getKeepEmployeeCheckInfo(personInfos, groupByTmpIdMap, result, String.format("%d-%02d", year, month));
        }else {
            getKeepEmployeeCheckInfo(personInfos, groupByTmpIdMap, result, null);
        }


        // 获取晋升页签
        getPromotionEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);

        if (Objects.equals(IFP2024RankEnum.FHWC03.getValue(), rankInfo.getRankCode())
                || Objects.equals(IFP2024RankEnum.FHWC04.getValue(), rankInfo.getRankCode())) {

            //一、三季度上期
            String format = String.format("%d-%02d", year, Quarter.fromMonth(month).getEndMonth());
            if(month <= Quarter.FIRST_QUARTER.getEndMonth() || (month > Quarter.SECOND_QUARTER.getEndMonth() && month <= Quarter.THIRD_QUARTER.getEndMonth())){
                //首先过滤当前季度的考核期判断是否有人
                List<CheckBatchPersonInfoTmp> onePersonInfos = personInfos.stream()
                        .filter(each -> Objects.equals(each.getCheckMonth(), format) && !Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode())).collect(Collectors.toList());
                if(CollUtil.isEmpty(onePersonInfos)){
                    String twoMonth = plusMonths(format, MONTHS_TO_SUBTRACT_THREE);
                    List<CheckBatchPersonInfoTmp> twoPersonInfos = personInfos.stream()
                            .filter(each -> Objects.equals(each.getCheckMonth(), twoMonth) && !Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode())).collect(Collectors.toList());
                    if(CollUtil.isEmpty(twoPersonInfos)){
                        log.info("工号为:[{}]的考核人员未查询到一三季度当期晋升合伙人类型的考核信息", employeeCode);
                        return result;
                    }
                    // 获取晋升合伙人页签
                    getPartnerEmployeeCheckInfo(twoPersonInfos, groupByTmpIdMap, result);
                }else {
                    // 获取晋升合伙人页签
                    getPartnerEmployeeCheckInfo(onePersonInfos, groupByTmpIdMap, result);
                }
            }
            //二、四季度
            if((month > Quarter.FIRST_QUARTER.getEndMonth() && month <= Quarter.SECOND_QUARTER.getEndMonth()) || (month > Quarter.THIRD_QUARTER.getEndMonth() && month <= Quarter.FOURTH_QUARTER.getEndMonth())){
                // 获取晋升合伙人页签
                getPartnerEmployeeCheckInfo(personInfos, groupByTmpIdMap, result);
            }
        }

        return result;
    }

    private String getQuarterEndMonth(int year, int month) {
        if (month <= Quarter.FIRST_QUARTER.getEndMonth()) {
            return String.format("%d-%02d", year, Quarter.FIRST_QUARTER.getEndMonth());
        } else if (month <= Quarter.SECOND_QUARTER.getEndMonth()) {
            return String.format("%d-%02d", year, Quarter.SECOND_QUARTER.getEndMonth());
        } else if (month <= Quarter.THIRD_QUARTER.getEndMonth()) {
            return String.format("%d-%02d", year, Quarter.THIRD_QUARTER.getEndMonth());
        } else {
            return String.format("%d-%02d", year, Quarter.FOURTH_QUARTER.getEndMonth());
        }
    }

    private boolean shouldParticipateInEvaluation(String employeeCode, int month, int year) {
        int endMonth = Quarter.fromMonth(month).getEndMonth();
        if ((month <= Quarter.FIRST_QUARTER.getEndMonth()) ||
                (month > Quarter.SECOND_QUARTER.getEndMonth() && month <= Quarter.THIRD_QUARTER.getEndMonth())) {
            return isParticipatingInEvaluation(employeeCode, String.format("%d-%02d", year, endMonth));
        }
        return false;
    }

    /**
     *
     * 获取晋升合伙人页签
     * @param personInfos       考核员工
     * @param groupByTmpIdMap   指标项Map
     * @param result            晋升合伙人页签
     */
    private void getPartnerEmployeeCheckInfo(List<CheckBatchPersonInfoTmp> personInfos,
                                             Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap,
                                             List<IFP2024TabVO> result) {
        CheckBatchPersonInfoTmp partnerPersonInfo = getPartnerPersonInfo(personInfos);
        if (partnerPersonInfo == null) {
            return;
        }

        List<CheckBatchPersonConfig> checkBatchPersonConfigs = groupByTmpIdMap.get(partnerPersonInfo.getId());
        if (checkBatchPersonConfigs == null) {
            return;
        }

        Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap = checkBatchPersonConfigs.stream()
                .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckItem));

        IFP2024TabVO tabVO = new IFP2024TabVO();
        tabVO.setTabCode("PARTNER");
        tabVO.setTabName("晋升(合伙人)");
        tabVO.setTabStatus(Integer.parseInt(partnerPersonInfo.getCheckSettleResult()) > 0 ? "已达标" : "未达标");
        tabVO.setCheckMonth(getCheckMonthStr(partnerPersonInfo));

        handleMiddleSeniorAdvisorPartner(groupByCheckItemMap, tabVO, partnerPersonInfo);

        result.add(tabVO);
    }

    private void handleMiddleSeniorAdvisorPartner(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap,
                                                  IFP2024TabVO tabVO,
                                                  CheckBatchPersonInfoTmp partnerPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        //进度条
        getOfficeProgressBarVOForPromotion(groupByCheckItemMap, barVO, partnerPersonInfo);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //入职时长
        getAcquiesceCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.joining_duration.getValue(), checkConfigList, "入职时长");
        //考核期内个人FYC
        getPartnerPromotion(groupByCheckItemMap, IFP2024CheckItemEnum.partner_promotion_monthly_fyc.getValue(), partnerPersonInfo, checkConfigList);
        //筹备所月均标准人力
        getPublicCheckItem(groupByCheckItemMap,  IFP2024CheckItemEnum.monthly_average_manpower_achieved.getValue(), IFP2024CheckItemEnum.agency_promotion_monthly_manpower_standard.getValue(), checkConfigList, "筹备所月均标准人力", "人");
        //筹备所13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap,IFP2024CheckItemEnum.preparation_office_continuation_rate.getValue(), IFP2024CheckItemEnum.preparation_office_continuation_rate_standard.getValue(), checkConfigList, "筹备所13个月继续率");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private CheckBatchPersonInfoTmp getPartnerPersonInfo(List<CheckBatchPersonInfoTmp> personInfos) {
        List<CheckBatchPersonInfoTmp> partnerPersonList = personInfos.stream()
                .filter(each -> Objects.equals(PROMOTION, each.getCheckType())
                        && !Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(partnerPersonList)){
            return null;
        }
        if(partnerPersonList.size() > 1){
            throw new RuntimeException("获取晋升合伙人考核类型页签异常");
        }
        return partnerPersonList.get(0);
    }


    /**
     *
     * 获取晋升页签
     * @param personInfos       考核员工
     * @param groupByTmpIdMap   指标项
     * @param result            晋升页签
     */
    private void getPromotionEmployeeCheckInfo(List<CheckBatchPersonInfoTmp> personInfos, Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap, List<IFP2024TabVO> result) {
        CheckBatchPersonInfoTmp promotionPersonInfo = getPromotionPersonInfo(personInfos);
        if (promotionPersonInfo == null) {
            return;
        }

        List<CheckBatchPersonConfig> checkBatchPersonConfigs = groupByTmpIdMap.get(promotionPersonInfo.getId());
        Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap = checkBatchPersonConfigs.stream()
                .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckItem));

        IFP2024TabVO tabVO = new IFP2024TabVO();
        tabVO.setTabCode("PROMOTION");
        tabVO.setCheckMonth(getCheckMonthStr(promotionPersonInfo));
        tabVO.setTabStatus(Objects.equals(String.valueOf(CheckManageIFPEnum.PROMOTED_LEVEL1.getLevel()),promotionPersonInfo.getCheckSettleResult()) ? "已达标" : "未达标");
        setTabNameByRankCodeForPromotion(tabVO, promotionPersonInfo);

        String targetRankCode = promotionPersonInfo.getTargetRankCode();

        if (IFP2024RankEnum.FHWC02.getValue().equals(targetRankCode)) {
            handleJuniorAdvisorPromotion(groupByCheckItemMap, tabVO, promotionPersonInfo);
        } else if (IFP2024RankEnum.FHWC03.getValue().equals(targetRankCode) || IFP2024RankEnum.FHWC04.getValue().equals(targetRankCode)) {
            handleMiddleSeniorAdvisorPromotion(groupByCheckItemMap, tabVO, promotionPersonInfo);
        } else if (IFP2024RankEnum.SWS02.getValue().equals(targetRankCode)) {
            handleSeniorPartnerPromotion(groupByCheckItemMap, tabVO, promotionPersonInfo);
        } else {
            throw new IllegalArgumentException("Unknown rank code: " + targetRankCode);
        }

        result.add(tabVO);
    }

    private void handleSeniorPartnerPromotion(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp promotionPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getOfficeProgressBarVOForPromotion(groupByCheckItemMap, barVO, promotionPersonInfo);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //任职合伙人时长
        getAcquiesceCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.partner_tenure.getValue(), checkConfigList, "任职合伙人时长");
        //考核期内个人FYC
        getPartnerPromotion(groupByCheckItemMap, IFP2024CheckItemEnum.partner_promotion_fyc_standard.getValue(), promotionPersonInfo, checkConfigList);
        //事务所月均标准人力
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.monthly_average_manpower_achieved.getValue(), IFP2024CheckItemEnum.agency_promotion_monthly_manpower_standard.getValue(), checkConfigList, "事务所月均标准人力", "人");
        //事务所13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.preparation_office_continuation_rate.getValue(), IFP2024CheckItemEnum.preparation_office_continuation_rate_standard.getValue(), checkConfigList, "事务所13个月继续率");
        //直接育成事务所
        getDirectlyDevelopedAgency(groupByCheckItemMap, checkConfigList);
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handleMiddleSeniorAdvisorPromotion(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp promotionPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getPromoteBar(groupByCheckItemMap, promotionPersonInfo, barVO);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //有无挂零月
        if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.zero_month_indicator.getValue())) {
            CheckBatchPersonConfig zero_month_indicator = groupByCheckItemMap.get(IFP2024CheckItemEnum.zero_month_indicator.getValue()).get(0);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(IFP2024CheckItemEnum.zero_month_indicator.getLabel());
            configVO.setConfigValue(zero_month_indicator.getContentValue());
            configVO.setWhetherCompleted(!Objects.equals(zero_month_indicator.getContentValue(), "是"));
            getDetailValue(groupByCheckItemMap, checkConfigList, configVO);
        }
        //销售件数
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.personal_accumulated_count.getValue(), IFP2024CheckItemEnum.personal_accumulated_count_standard.getValue(), checkConfigList, "考核期内销售件数", "件");
        //13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.personal_continuation_rate.getValue(), IFP2024CheckItemEnum.personal_continuation_rate_standard.getValue(), checkConfigList, "13个月继续率");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handleJuniorAdvisorPromotion(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp promotionPersonInfo) {
        //个人晋升进度条
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        BigDecimal standardValue = new BigDecimal(3000).setScale(2, RoundingMode.HALF_UP);
        if(promotionPersonInfo.getPromoteActualSTP() != null){
            BigDecimal actualValue = promotionPersonInfo.getPromoteActualSTP().setScale(2, RoundingMode.HALF_UP);
            barVO.setPersonalAccumulatedFYC(actualValue.toString());
            BigDecimal subtract = standardValue.subtract(actualValue);
            //超量完成目标
            if(subtract.compareTo(BigDecimal.ZERO) <= 0){
                barVO.setDistanceToTheTarget(BigDecimal.ZERO.toString());
                barVO.setProgressBar("100");
            }else {
                barVO.setDistanceToTheTarget(subtract.toString());
                BigDecimal divideValue = actualValue.divide(standardValue, 2, RoundingMode.HALF_UP);
                BigDecimal progressBar = divideValue.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                barVO.setProgressBar(progressBar.toString());
            }
        }else {
            barVO.setPersonalAccumulatedFYC("0");
            barVO.setDistanceToTheTarget(standardValue.toString());
            barVO.setProgressBar("0");
        }
        tabVO.setProgressBarVO(barVO);
        //考核期内销售件数
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.personal_accumulated_count.getValue(), IFP2024CheckItemEnum.personal_accumulated_count_standard.getValue(), checkConfigList, "考核期内销售件数", "件");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void setTabNameByRankCodeForPromotion(IFP2024TabVO tabVO, CheckBatchPersonInfoTmp promotionPersonInfo) {
        String label = "";
        String targetRankCode = promotionPersonInfo.getTargetRankCode();

        if (IFP2024RankEnum.FHWC02.getValue().equals(targetRankCode)) {
            label = IFP2024RankEnum.FHWC02.getLabel();
        } else if (IFP2024RankEnum.FHWC03.getValue().equals(targetRankCode)) {
            label = IFP2024RankEnum.FHWC03.getLabel();
        } else if (IFP2024RankEnum.FHWC04.getValue().equals(targetRankCode)) {
            label = IFP2024RankEnum.FHWC04.getLabel();
        } else if (IFP2024RankEnum.SWS02.getValue().equals(targetRankCode)) {
            label = IFP2024RankEnum.SWS02.getLabel();
        }

        tabVO.setTabName("晋升(" + label + ")");
    }

    private CheckBatchPersonInfoTmp getPromotionPersonInfo(List<CheckBatchPersonInfoTmp> personInfos) {
        List<CheckBatchPersonInfoTmp> promotionPersonList = personInfos.stream()
                .filter(each -> Objects.equals(PROMOTION, each.getCheckType())
                        && !Objects.equals(each.getRankCode(), IFP2024RankEnum.FHWC04.getValue())
                        && Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(promotionPersonList)){
            return null;
        }
        if(promotionPersonList.size() > 1){
            throw new RuntimeException("获取晋升考核类型页签异常");
        }
        return promotionPersonList.get(0);
    }


    /**
     * 获取维持页签
     *
     * @param personInfos     考核员工
     * @param groupByTmpIdMap 指标项
     * @param result          维持页签
     * @param format
     */
    private void getKeepEmployeeCheckInfo(List<CheckBatchPersonInfoTmp> personInfos, Map<String, List<CheckBatchPersonConfig>> groupByTmpIdMap, List<IFP2024TabVO> result, String format) {
        CheckBatchPersonInfoTmp keepPersonInfo = getKeepPersonInfo(personInfos, format);
        if (keepPersonInfo == null) {
            return;
        }

        List<CheckBatchPersonConfig> checkBatchPersonConfigs = groupByTmpIdMap.get(keepPersonInfo.getId());
        Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap = checkBatchPersonConfigs.stream()
                .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckItem));

        IFP2024TabVO tabVO = new IFP2024TabVO();
        tabVO.setTabCode("KEEP");
        tabVO.setCheckMonth(getCheckMonthStr(keepPersonInfo));
        tabVO.setTabStatus(Objects.equals(String.valueOf(CheckManageIFPEnum.KEEP_LEVEL.getLevel()),keepPersonInfo.getCheckSettleResult()) ? "已达标" : "未达标");
        setTabNameByRankCode(tabVO, keepPersonInfo);

        if (IFP2024RankEnum.FHWC01.getValue().equals(keepPersonInfo.getTargetRankCode())) {
            handleApprenticeAdvisor(groupByCheckItemMap, tabVO, keepPersonInfo);
        } else if (IFP2024RankEnum.FHWC02.getValue().equals(keepPersonInfo.getTargetRankCode())) {
            handleJuniorAdvisor(groupByCheckItemMap, tabVO, keepPersonInfo);
        } else if (IFP2024RankEnum.FHWC03.getValue().equals(keepPersonInfo.getTargetRankCode()) || IFP2024RankEnum.FHWC04.getValue().equals(keepPersonInfo.getTargetRankCode())) {
            handleMiddleSeniorAdvisor(groupByCheckItemMap, tabVO, keepPersonInfo);
        } else if (IFP2024RankEnum.SWS01.getValue().equals(keepPersonInfo.getTargetRankCode())) {
            handlePartner(groupByCheckItemMap, tabVO, keepPersonInfo);
        } else if (IFP2024RankEnum.SWS02.getValue().equals(keepPersonInfo.getTargetRankCode())) {
            handleSeniorPartner(groupByCheckItemMap, tabVO, keepPersonInfo);
        } else {
            throw new IllegalArgumentException("Unknown rank code: " + keepPersonInfo.getTargetRankCode());
        }

        result.add(tabVO);
    }

    private void handleSeniorPartner(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getOfficeProgressBarVO(groupByCheckItemMap, barVO, keepPersonInfo);
        tabVO.setProgressBarVO(barVO);

        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //达成活动人力月数
        if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.monthly_activity_manpower_achieved.getValue())) {
            handleMonthlyActivityManpowerAchieved(groupByCheckItemMap, checkConfigList, keepPersonInfo);
        }
        //事务所月标准人力
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.monthly_average_manpower_achieved.getValue(), IFP2024CheckItemEnum.agency_maintain_monthly_manpower_standard.getValue(), checkConfigList, "事务所月标准人力", "人");
        //事务所13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.preparation_office_continuation_rate.getValue(), IFP2024CheckItemEnum.preparation_office_continuation_rate_meet_standard.getValue(), checkConfigList, "事务所13个月继续率");
        //直接育成事务所
        getDirectlyDevelopedAgency(groupByCheckItemMap, checkConfigList);
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handlePartner(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getOfficeProgressBarVO(groupByCheckItemMap, barVO, keepPersonInfo);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //达成活动人力月数
        if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.monthly_activity_manpower_achieved.getValue())) {
            handleMonthlyActivityManpowerAchieved(groupByCheckItemMap, checkConfigList, keepPersonInfo);
        }
        //事务所月标准人力
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.monthly_average_manpower_achieved.getValue(), IFP2024CheckItemEnum.agency_maintain_monthly_manpower_standard.getValue(), checkConfigList, "事务所月标准人力", "人");
        //事务所13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.preparation_office_continuation_rate.getValue(), IFP2024CheckItemEnum.preparation_office_continuation_rate_meet_standard.getValue(), checkConfigList, "事务所13个月继续率");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handleMonthlyActivityManpowerAchieved(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, List<IFP2024ConfigVO> checkConfigList, CheckBatchPersonInfoTmp keepPersonInfo) {
        //达成活动人力月数
        if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.yj_monthly_activity_manpower_achieved.getValue())) {
            CheckBatchPersonConfig isTrue = groupByCheckItemMap.get(IFP2024CheckItemEnum.monthly_activity_manpower_achieved.getValue()).get(0);
            List<CheckBatchPersonConfig> personConfigs = groupByCheckItemMap.get(IFP2024CheckItemEnum.yj_monthly_activity_manpower_achieved.getValue());
            List<CheckBatchPersonConfig> collect = personConfigs.stream().filter(each -> !Objects.equals(each.getContentValue(), "0")).collect(Collectors.toList());
            LinkedHashMap<String, String> detailVale = personConfigs.stream()
                    .sorted(Comparator.comparingInt(config -> config.month))
                    .collect(Collectors.toMap(
                            config -> config.month + "月",
                            config -> Objects.equals(config.getContentValue(), "0") ? "未达成" : "达成",
                            (existing, replacement) -> existing,
                            LinkedHashMap::new));
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(IFP2024CheckItemEnum.yj_monthly_activity_manpower_achieved.getLabel());
            configVO.setConfigValue(collect.size() + "/" +keepPersonInfo.getCheckMonthNums().toString());
            configVO.setWhetherCompleted(Objects.equals(isTrue.getContentValue(), "1"));
            configVO.setDetailVale(detailVale);
            checkConfigList.add(configVO);
        }
    }

    private void handleMiddleSeniorAdvisor(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getPersonProgressBarVO(groupByCheckItemMap, barVO, keepPersonInfo);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //销售件数
        getPublicCheckItem(groupByCheckItemMap, IFP2024CheckItemEnum.personal_accumulated_count.getValue(), IFP2024CheckItemEnum.personal_accumulated_count_standard.getValue(), checkConfigList, "考核期内销售件数", "件");
        //13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.personal_continuation_rate.getValue(), IFP2024CheckItemEnum.personal_continuation_rate_meet_standard.getValue(), checkConfigList, "13个月继续率");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handleJuniorAdvisor(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        IFP2024ProgressBarVO barVO = new IFP2024ProgressBarVO();
        getPersonProgressBarVO(groupByCheckItemMap, barVO, keepPersonInfo);
        tabVO.setProgressBarVO(barVO);
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //13个月继续率
        getPersonalContinuationRate(groupByCheckItemMap, IFP2024CheckItemEnum.personal_continuation_rate.getValue(), IFP2024CheckItemEnum.personal_continuation_rate_meet_standard.getValue(), checkConfigList, "13个月继续率");
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void handleApprenticeAdvisor(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        //考核项集合
        List<IFP2024ConfigVO> checkConfigList = new ArrayList<>();
        //连续九个月业绩挂零
        if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.consecutive_zero_9months.getValue())) {
            CheckBatchPersonConfig consecutive_zero_9months = groupByCheckItemMap.get(IFP2024CheckItemEnum.consecutive_zero_9months.getValue()).get(0);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(IFP2024CheckItemEnum.consecutive_zero_9months.getLabel());
            configVO.setConfigValue(consecutive_zero_9months.getContentValue());
            configVO.setWhetherCompleted(!Objects.equals(consecutive_zero_9months.getContentValue(), "是"));
            getDetailValue(groupByCheckItemMap, checkConfigList, configVO);
        }
        tabVO.setCheckConfigList(checkConfigList);
    }

    private void setTabNameByRankCode(IFP2024TabVO tabVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        String label = "";
        if (Objects.equals(IFP2024RankEnum.FHWC01.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.FHWC01.getLabel();
        } else if (Objects.equals(IFP2024RankEnum.FHWC02.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.FHWC02.getLabel();
        } else if (Objects.equals(IFP2024RankEnum.FHWC03.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.FHWC03.getLabel();
        } else if (Objects.equals(IFP2024RankEnum.FHWC04.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.FHWC04.getLabel();
        } else if (Objects.equals(IFP2024RankEnum.SWS01.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.SWS01.getLabel();
        } else if (Objects.equals(IFP2024RankEnum.SWS02.getValue(), keepPersonInfo.getTargetRankCode())) {
            label = IFP2024RankEnum.SWS02.getLabel();
        }
        tabVO.setTabName("维持(" + label + ")");
    }

    private CheckBatchPersonInfoTmp getKeepPersonInfo(List<CheckBatchPersonInfoTmp> personInfos, String format) {
        List<CheckBatchPersonInfoTmp> keepPersonList;
        if(StrUtil.isNotEmpty(format)){
            keepPersonList = personInfos.stream()
                    .filter(each -> Objects.equals(KEEP, each.getCheckType()) && Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode()) && Objects.equals(each.getCheckMonth(), format))
                    .collect(Collectors.toList());
        }else {
            keepPersonList = personInfos.stream()
                    .filter(each -> Objects.equals(KEEP, each.getCheckType()) && Objects.equals(each.getRankSeqCode(), each.getTargetRankSeqCode()))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(keepPersonList)) {
            return null;
        }
        if (keepPersonList.size() > 1) {
            throw new RuntimeException("获取维持考核类型页签异常");
        }
        return keepPersonList.get(0);
    }

    private void getOfficeProgressBarVOForPromotion(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024ProgressBarVO barVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_promotion_fyc_standard.getValue())){
            CheckBatchPersonConfig standard = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_promotion_fyc_standard.getValue()).get(0);
            BigDecimal standardValue = new BigDecimal(standard.getContentValue()).setScale(2, RoundingMode.HALF_UP);
            if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_promotion_actual_fyc.getValue())) {
                CheckBatchPersonConfig actual = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_promotion_actual_fyc.getValue()).get(0);
                BigDecimal actualValue = new BigDecimal(actual.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                BigDecimal subtract = standardValue.subtract(actualValue);
                //超量完成目标
                if(subtract.compareTo(BigDecimal.ZERO) <= 0){
                    barVO.setDistanceToTheTarget(BigDecimal.ZERO.toString());
                    barVO.setProgressBar("100");
                }else {
                    barVO.setDistanceToTheTarget(subtract.toString());
                    BigDecimal divideValue = actualValue.divide(standardValue, 2, RoundingMode.HALF_UP);
                    BigDecimal progressBar = divideValue.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                    barVO.setProgressBar(progressBar.toString());
                }
            }else {
                barVO.setPersonalAccumulatedFYC("0");
                barVO.setDistanceToTheTarget(standardValue.toString());
                barVO.setProgressBar("0");
            }
        }else {
            if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_promotion_actual_fyc.getValue())) {
                CheckBatchPersonConfig actual = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_promotion_actual_fyc.getValue()).get(0);
                BigDecimal actualValue = new BigDecimal(actual.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                barVO.setDistanceToTheTarget("0");
                barVO.setProgressBar("100");
            }
        }
    }

    private void getPartnerPromotion(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, String value, CheckBatchPersonInfoTmp personInfoTmp, List<IFP2024ConfigVO> checkConfigList) {
        if(groupByCheckItemMap.containsKey(value)){
            CheckBatchPersonConfig standard = groupByCheckItemMap.get(value).get(0);
            BigDecimal standardValue = new BigDecimal(standard.getContentValue()).multiply(new BigDecimal(personInfoTmp.getCheckMonthNums())).setScale(0, RoundingMode.HALF_UP);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName("考核期内个人FYC");
            if(personInfoTmp.getPromoteActualSTP() != null){
                BigDecimal bigDecimal = personInfoTmp.getPromoteActualSTP().setScale(2, RoundingMode.HALF_UP);
                configVO.setConfigValue(bigDecimal + "元" + "/" + standardValue + "元");
                configVO.setWhetherCompleted(bigDecimal.compareTo(standardValue) >= 0);
            }else {
                configVO.setConfigValue("0.00元/" + standardValue + "元");
                configVO.setWhetherCompleted(false);
            }
            checkConfigList.add(configVO);
        }else {
            if(personInfoTmp.getPromoteActualSTP() != null){
                IFP2024ConfigVO configVO = new IFP2024ConfigVO();
                configVO.setConfigName("考核期内个人FYC");
                configVO.setConfigValue(personInfoTmp.getPromoteActualSTP().setScale(2, RoundingMode.HALF_UP) + "元" + "/0元");
                configVO.setWhetherCompleted(false);
                checkConfigList.add(configVO);
            }
        }
    }

    private void getAcquiesceCheckItem(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, String value, List<IFP2024ConfigVO> checkConfigList, String configName) {
        if (groupByCheckItemMap.containsKey(value)) {
            CheckBatchPersonConfig actual = groupByCheckItemMap.get(value).get(0);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(configName);
            configVO.setConfigValue(actual.getContentValue() + "个月/6个月");
            configVO.setWhetherCompleted(new BigDecimal(actual.getContentValue()).compareTo(new BigDecimal(6)) >= 0);
            checkConfigList.add(configVO);
        } else {
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(configName);
            configVO.setConfigValue("0个月/6个月");
            configVO.setWhetherCompleted(false);
            checkConfigList.add(configVO);
        }
    }

    private void getPromoteBar(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, CheckBatchPersonInfoTmp promotionPerson, IFP2024ProgressBarVO barVO) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.advisor_promotion_fyc.getValue())){
            CheckBatchPersonConfig personConfig = groupByCheckItemMap.get(IFP2024CheckItemEnum.advisor_promotion_fyc.getValue()).get(0);
            BigDecimal standardValue = new BigDecimal(personConfig.getContentValue()).setScale(2, RoundingMode.HALF_UP);
            if(promotionPerson.getPromoteActualSTP() != null){
                BigDecimal actualValue = promotionPerson.getPromoteActualSTP().setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                BigDecimal subtract = standardValue.subtract(actualValue);
                //超量完成目标
                if(subtract.compareTo(BigDecimal.ZERO) <= 0){
                    barVO.setDistanceToTheTarget(BigDecimal.ZERO.toString());
                    barVO.setProgressBar("100");
                }else {
                    barVO.setDistanceToTheTarget(subtract.toString());
                    BigDecimal divideValue = actualValue.divide(standardValue, 2, RoundingMode.HALF_UP);
                    BigDecimal progressBar = divideValue.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                    barVO.setProgressBar(progressBar.toString());
                }
            }else {
                barVO.setPersonalAccumulatedFYC("0");
                barVO.setDistanceToTheTarget(standardValue.toString());
                barVO.setProgressBar("0");
            }
        }else {
            if(promotionPerson.getPromoteActualSTP() != null){
                barVO.setPersonalAccumulatedFYC(promotionPerson.getPromoteActualSTP().setScale(2, RoundingMode.HALF_UP).toString());
                barVO.setDistanceToTheTarget("0");
                barVO.setProgressBar("100");
            }
        }
    }

    private void getDirectlyDevelopedAgency(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, List<IFP2024ConfigVO> checkConfigList) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.directly_developed_agency.getValue())){
            CheckBatchPersonConfig rate = groupByCheckItemMap.get(IFP2024CheckItemEnum.directly_developed_agency.getValue()).get(0);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName("直接育成事务所");
            configVO.setConfigValue(rate.getContentValue() + "个/1个");
            configVO.setWhetherCompleted(new BigDecimal(rate.getContentValue()).compareTo(new BigDecimal(1)) >= 0);
            checkConfigList.add(configVO);
        }
    }

    private void getOfficeProgressBarVO(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024ProgressBarVO barVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_maintain_fyc_standard.getValue())){
            CheckBatchPersonConfig standard = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_maintain_fyc_standard.getValue()).get(0);
            BigDecimal standardValue = new BigDecimal(standard.getContentValue()).setScale(2, RoundingMode.HALF_UP);
            if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_average_actual_fyc.getValue())) {
                CheckBatchPersonConfig actual = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_average_actual_fyc.getValue()).get(0);
                BigDecimal actualValue = new BigDecimal(actual.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                BigDecimal subtract = standardValue.subtract(actualValue);
                //超量完成目标
                if(subtract.compareTo(BigDecimal.ZERO) <= 0){
                    barVO.setDistanceToTheTarget(BigDecimal.ZERO.toString());
                    barVO.setProgressBar("100");
                }else {
                    barVO.setDistanceToTheTarget(subtract.toString());
                    BigDecimal divideValue = actualValue.divide(standardValue, 2, RoundingMode.HALF_UP);
                    BigDecimal progressBar = divideValue.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                    barVO.setProgressBar(progressBar.toString());
                }
            }else {
                barVO.setPersonalAccumulatedFYC("0");
                barVO.setDistanceToTheTarget(standardValue.toString());
                barVO.setProgressBar("0");
            }
        }else {
            if (groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.agency_average_actual_fyc.getValue())) {
                CheckBatchPersonConfig actual = groupByCheckItemMap.get(IFP2024CheckItemEnum.agency_average_actual_fyc.getValue()).get(0);
                BigDecimal actualValue = new BigDecimal(actual.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                barVO.setDistanceToTheTarget("0");
                barVO.setProgressBar("100");
            }
        }
    }

    private void getPersonProgressBarVO(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, IFP2024ProgressBarVO barVO, CheckBatchPersonInfoTmp keepPersonInfo) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.advisor_maintenance_monthly_fyc.getValue())){
            CheckBatchPersonConfig personConfig = groupByCheckItemMap.get(IFP2024CheckItemEnum.advisor_maintenance_monthly_fyc.getValue()).get(0);
            BigDecimal standardValue = new BigDecimal(personConfig.getContentValue()).multiply(new BigDecimal(keepPersonInfo.getCheckMonthNums())).setScale(2, RoundingMode.HALF_UP);
            if(keepPersonInfo.getKeepActualSTP() != null){
                BigDecimal actualValue = keepPersonInfo.getKeepActualSTP().setScale(2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                BigDecimal subtract =standardValue.subtract(actualValue);
                //超量完成目标
                if(subtract.compareTo(BigDecimal.ZERO) <= 0){
                    barVO.setDistanceToTheTarget(BigDecimal.ZERO.toString());
                    barVO.setProgressBar("100");
                }else {
                    barVO.setDistanceToTheTarget(subtract.toString());
                    BigDecimal divideValue = actualValue.divide(standardValue, 2, RoundingMode.HALF_UP);
                    BigDecimal progressBar = divideValue.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
                    barVO.setProgressBar(progressBar.toString());
                }
            }else {
                barVO.setPersonalAccumulatedFYC("0");
                barVO.setDistanceToTheTarget(standardValue.toString());
                barVO.setProgressBar("0");
            }
        }else {
            if(keepPersonInfo.getKeepActualSTP() != null){
                BigDecimal actualValue = keepPersonInfo.getKeepActualSTP().setScale(2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                barVO.setPersonalAccumulatedFYC(actualValue.toString());
                barVO.setDistanceToTheTarget("0");
                barVO.setProgressBar("100");
            }
        }
    }

    private void getPublicCheckItem(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, String actualName, String standardName, List<IFP2024ConfigVO> checkConfigList, String configName, String unit) {
        String str = unit == null ? "" : unit;
        if(groupByCheckItemMap.containsKey(standardName)){
            CheckBatchPersonConfig rateStandard = groupByCheckItemMap.get(standardName).get(0);
            BigDecimal rateStandardValue = new BigDecimal(rateStandard.getContentValue()).setScale(0, RoundingMode.HALF_UP);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(configName);
            if(groupByCheckItemMap.containsKey(actualName)){
                CheckBatchPersonConfig rate = groupByCheckItemMap.get(actualName).get(0);
                BigDecimal actualValue;
                if(Objects.equals(unit,"件")){
                    actualValue = new BigDecimal(rate.getContentValue()).setScale(0, RoundingMode.HALF_UP);
                }else {
                    actualValue = new BigDecimal(rate.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                }
                configVO.setConfigValue(actualValue + str + "/" +  rateStandardValue + str);
                configVO.setWhetherCompleted(new BigDecimal(rate.getContentValue()).compareTo(new BigDecimal(rateStandard.getContentValue())) >= 0);
                checkConfigList.add(configVO);
            }else {
                if(Objects.equals(unit,"件")){
                    configVO.setConfigValue("0" + str + "/" +  rateStandardValue + str);
                }else {
                    configVO.setConfigValue("0.00" + str + "/" +  rateStandardValue + str);
                }
                configVO.setWhetherCompleted(false);
                checkConfigList.add(configVO);
            }
        }else {
            if(groupByCheckItemMap.containsKey(actualName)){
                CheckBatchPersonConfig rate = groupByCheckItemMap.get(actualName).get(0);
                IFP2024ConfigVO configVO = new IFP2024ConfigVO();
                configVO.setConfigName(configName);
                if(Objects.equals(unit,"件")){
                    configVO.setConfigValue(new BigDecimal(rate.getContentValue()).setScale(0, RoundingMode.HALF_UP) + str + "/" +  "0" + str);
                }else {
                    configVO.setConfigValue(new BigDecimal(rate.getContentValue()).setScale(2, RoundingMode.HALF_UP) + str + "/" +  "0" + str);
                }
                configVO.setWhetherCompleted(true);
                checkConfigList.add(configVO);
            }
        }
    }

    private void getPersonalContinuationRate(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, String actualName, String standardName, List<IFP2024ConfigVO> checkConfigList, String configName) {
        if(groupByCheckItemMap.containsKey(standardName)){
            CheckBatchPersonConfig rateStandard = groupByCheckItemMap.get(standardName).get(0);
            IFP2024ConfigVO configVO = new IFP2024ConfigVO();
            configVO.setConfigName(configName);
            BigDecimal rateStandardValue = new BigDecimal(rateStandard.getContentValue()).multiply(new BigDecimal(100)).setScale(0,RoundingMode.HALF_UP);
            if(groupByCheckItemMap.containsKey(actualName)){
                CheckBatchPersonConfig rate = groupByCheckItemMap.get(actualName).get(0);
                BigDecimal actualValue = new BigDecimal(rate.getContentValue()).setScale(2, RoundingMode.HALF_UP);
                configVO.setConfigValue(actualValue + "%/" + rateStandardValue + "%");
                configVO.setWhetherCompleted(actualValue.compareTo(rateStandardValue) >= 0);
                checkConfigList.add(configVO);
            }else {
                configVO.setConfigValue("0%/" +  rateStandardValue + "%");
                configVO.setWhetherCompleted(false);
                checkConfigList.add(configVO);
            }
        }else {
            if(groupByCheckItemMap.containsKey(actualName)){
                IFP2024ConfigVO configVO = new IFP2024ConfigVO();
                CheckBatchPersonConfig rate = groupByCheckItemMap.get(actualName).get(0);
                configVO.setConfigName(configName);
                configVO.setConfigValue(new BigDecimal(rate.getContentValue()).setScale(2, RoundingMode.HALF_UP) + "%/" + "0%");
                configVO.setWhetherCompleted(true);
                checkConfigList.add(configVO);
            }
        }
    }

    private void getDetailValue(Map<String, List<CheckBatchPersonConfig>> groupByCheckItemMap, List<IFP2024ConfigVO> checkConfigList, IFP2024ConfigVO configVO) {
        if(groupByCheckItemMap.containsKey(IFP2024CheckItemEnum.monthly_actual_achievement.getValue())){
            List<CheckBatchPersonConfig> monthList = groupByCheckItemMap.get(IFP2024CheckItemEnum.monthly_actual_achievement.getValue());
            LinkedHashMap<String, String> detailVale = monthList.stream()
                    .sorted(Comparator.comparingInt(config -> config.month))
                    .collect(Collectors.toMap(
                            config -> config.month + "月FYC",
                            config -> (StrUtil.isNotBlank(config.contentValue) ? new BigDecimal(config.contentValue).setScale(2, RoundingMode.HALF_UP).toString() : "0.00") + "元" ,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new));
            configVO.setDetailVale(detailVale);
        }
        checkConfigList.add(configVO);
    }

    private String getCheckMonthStr(CheckBatchPersonInfoTmp personInfo) {
        String checkStartMonth = personInfo.getCheckStartMonth();
        String checkEndMonth = personInfo.getCheckEndMonth();
        // 将输入的月份字符串转换为日期对象，指定为该月的第一天
        LocalDate startDate = LocalDate.parse(checkStartMonth + "-01");
        // 获取结束月的最后一天，利用月底自动校正
        LocalDate endDate = LocalDate.parse(checkEndMonth + "-01").withDayOfMonth(
                LocalDate.parse(checkEndMonth + "-01").lengthOfMonth()
        );

        // 设置输出的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.M.d");
        // 格式化开始和结束日期
        String formattedStartDate = startDate.format(formatter);
        String formattedEndDate = endDate.format(formatter);

        // 拼接并返回结果
        return formattedStartDate + "-" + formattedEndDate;
    }

    public boolean isParticipatingInEvaluation(String employeeCode, String checkMonth){
        List<TbEmpFlow> flowInfoList = assessmentDao.getFlowInfo(employeeCode);
        // 排序
        flowInfoList.sort(Comparator.comparing(TbEmpFlow::getRankTime));

        if (flowInfoList.size() == 1) {
            TbEmpFlow empFlow1 = flowInfoList.get(0);
            if (IFP2024RankEnum.FHWC04.getValue().equals(empFlow1.getRankCode())) {
                // 取到高级顾问职级的变更时间  判断到变更时间 —— 当前考核截止月 = 3 满足条件
                LocalDate rankTime = empFlow1.getRankTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                if (isEvaluationConditionMet(rankTime, checkMonth)) {
                    return true;
                }
            }
        }

        for (int i = 0; i < flowInfoList.size() - 1; i++) {
            TbEmpFlow current = flowInfoList.get(i);
            TbEmpFlow next = flowInfoList.get(i + 1);
            // 当前记录为合伙人且上一条记录是高级顾问 才满足条件
            if (IFP2024RankEnum.SWS01.getValue().equals(current.getRankCode()) && IFP2024RankEnum.FHWC04.getValue().equals(next.getRankCode())) {
                // 取到高级顾问职级的变更时间  判断到变更时间 —— 当前考核截止月 = 3 满足条件
                LocalDate rankTime = next.getRankTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                if (isEvaluationConditionMet(rankTime, checkMonth)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static boolean isEvaluationConditionMet(LocalDate rankTime, String checkMonth) {
        LocalDate checkLocalDate = LocalDate.parse(checkMonth + "-01");
        // 计算两个日期之间的月份数
        long monthsBetween = ChronoUnit.MONTHS.between(rankTime, checkLocalDate);
        // 如果时间差恰好为3个月，则返回true
        return 3 == monthsBetween;
    }

    private void getCurrentCheckMonthForKeepOrPromotion(List<String> checkMonthList, boolean value, int year, int month) {
        if(value){
            //顾问序列维持或晋升类型考核期
            checkMonthList.add(String.format("%d-%02d", year, month));
            checkMonthList.add(String.format("%d-%02d", year, Quarter.fromMonth(month).getEndMonth()));
        }else {
            //合伙人序列维持或晋升类型考核期
            if (month <= Quarter.SECOND_QUARTER.getEndMonth()) {
                checkMonthList.add(String.format("%d-%02d", year, Quarter.SECOND_QUARTER.getEndMonth()));
            }else {
                checkMonthList.add(String.format("%d-%02d", year, Quarter.FOURTH_QUARTER.getEndMonth()));
            }
        }
    }
}
