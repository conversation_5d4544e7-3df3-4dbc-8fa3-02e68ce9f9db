package com.hqins.agent.org.dao.entity.settle;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 荣誉奖项标准指标定义表
* </p>
*
* <AUTHOR>
* @since 2025-03-25
*/
@TableName("honor_standard_metrics")
@Data
@ApiModel(value = "荣誉奖项标准指标定义表", description = "荣誉奖项标准指标定义表")
public class HonorStandardMetrics implements Serializable {
    @ApiModelProperty(value = "主键ID", required = true, example = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "荣誉类型代码", example = "荣誉类型代码")
    private String honorClass;

    @ApiModelProperty(value = "荣誉类型名称", example = "荣誉类型名称")
    private String honorClassName;

    @ApiModelProperty(value = "荣誉项代码", example = "荣誉项代码")
    private String honorCode;

    @ApiModelProperty(value = "荣誉项名称", example = "荣誉项名称")
    private String honorName;

    @ApiModelProperty(value = "年度", example = "年度")
    private String metricsYear;

    @ApiModelProperty(value = "指标项代码", example = "指标项代码")
    private String metricsType;

    @ApiModelProperty(value = "指标项名称", example = "指标项名称")
    private String metricsTypeName;

    @ApiModelProperty(value = "指标值", example = "指标值")
    private String metricsValue;

    @ApiModelProperty(value = "指标单位", example = "指标单位")
    private String metricsUnit;

    @ApiModelProperty(value = "指标状态(0,未生效；1，已生效)", example = "指标状态(0,未生效；1，已生效)")
    private String metricsStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人", example = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间", example = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "修改人", example = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "修改时间", example = "修改时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
