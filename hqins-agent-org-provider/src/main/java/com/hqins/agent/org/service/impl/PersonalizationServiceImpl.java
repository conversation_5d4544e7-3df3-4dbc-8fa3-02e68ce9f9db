package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.ChannelEmployeeDao;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.CustomMenu;
import com.hqins.agent.org.dao.entity.org.Personalization;
import com.hqins.agent.org.dao.mapper.org.CustomMenuMapper;
import com.hqins.agent.org.dao.mapper.org.PersonalizationMapper;
import com.hqins.agent.org.model.request.PersonalizationSaveByFeatureRequest;
import com.hqins.agent.org.model.request.PersonalizationSaveRequest;
import com.hqins.agent.org.model.request.PersonalizationrQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.PartnerEmployeeService;
import com.hqins.agent.org.service.PersonalizationService;
import com.hqins.agent.org.service.SupervisorEmployeeService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.web.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class PersonalizationServiceImpl implements PersonalizationService {

    @Autowired
    private CustomMenuMapper customMenuMapper;
    @Autowired
    private PersonalizationMapper personalizationMapper;
    @Autowired
    private PartnerEmployeeService partnerEmployeeService;
    @Autowired
    private ChannelEmployeeDao channelEmployeeDao;
    @Autowired
    private SupervisorEmployeeService supervisorEmployeeService;

    @Override
    public List<CustomMenuVO> getMyCustomMenu(PersonalizationrQueryRequest queryRequest) {
        log.debug("EmployeeCode：{} ，OrgType：{}",RequestContextHolder.getEmployeeCode(),RequestContextHolder.getOrgType());
        String employeeCode = RequestContextHolder.getEmployeeCode();
        String code = null;
        EmployeeVO employeeVO = null;
        //获取顶层机构编码
        if (AgentOrgType.CHANNEL.name().equals(RequestContextHolder.getOrgType())) {
            ChannelEmployee emp = channelEmployeeDao.getByCode(employeeCode);
            code = emp.getChannelCode();
        } else {
            //从数据库抓取
            employeeVO = partnerEmployeeService.getEmployeeVO(employeeCode);
            code = employeeVO.getOrgCode();
        }
        //获取所有的授权菜单id
        Set<Long> menuIds = StreamEx.of(personalizationMapper.selectList(new LambdaQueryWrapper<Personalization>()
                .eq(Personalization::getCode, code))).map(Personalization::getCustomMenuId).toSet();

        if (menuIds.isEmpty()) {
            //如果没有授权任何菜单，就直接返回，不用继续往下处理了
            return new ArrayList<>();
        }
        //根据索授权的id获取自定义菜单
        List<CustomMenu> customMenus = customMenuMapper.selectList(new LambdaQueryWrapper<CustomMenu>()
                .eq(CustomMenu::getAppType, queryRequest.getAppType().name()).in(CustomMenu::getId, menuIds)
                .orderByAsc(CustomMenu::getId));

        return BeanCopier.copyList(customMenus, CustomMenuVO.class);
    }

    @Override
    public List<MenuGroupVO> getAllCustomMenu(PersonalizationrQueryRequest queryRequest) {
        //获取所有的授权记录
        List<Personalization> cfgs = personalizationMapper.selectList(new LambdaQueryWrapper<Personalization>()
                .eq(Personalization::getCode, queryRequest.getCode()).orderByAsc(Personalization::getId));
        //获取所有的自定义菜单
        List<CustomMenu> customMenus = customMenuMapper.selectList(new LambdaQueryWrapper<CustomMenu>()
                .eq(CustomMenu::getAppType, queryRequest.getAppType().name()).orderByAsc(CustomMenu::getId));
        List<CustomMenuVO> customMenuVOs = BeanCopier.copyList(customMenus, CustomMenuVO.class);
        customMenuVOs.forEach(cm -> cfgs.forEach(c -> {
            if (cm.getId().equals(c.getCustomMenuId())) {
                cm.setSelected(true);
            }
        }));
        return StreamEx.of(StreamEx.of(customMenuVOs).groupingBy(CustomMenuVO::getModule).entrySet())
                .map(entry -> MenuGroupVO.builder()
                        .sort(entry.getKey().getSort())
                        .module(entry.getKey().name())
                        .moduleDesc(entry.getKey().getLabel())
                        .menus(entry.getValue())
                        .build())
                .sortedBy(MenuGroupVO::getSort)
                .toList();
    }

    @Override
    public void updateCustomMenu(PersonalizationSaveRequest request) {
        //1.删除机构对应的所有菜单授权
        personalizationMapper.delete(new LambdaQueryWrapper<Personalization>()
                .eq(Personalization::getCode, request.getCode()));
        //2.重新授权
        request.getCustomMenuIds().forEach(menuId -> {
            //检测菜单id是否存在,存在才会继续插入
            List<CustomMenu> customMenus = customMenuMapper.selectList(new LambdaQueryWrapper<CustomMenu>()
                    .eq(CustomMenu::getId, menuId));
            if (!customMenus.isEmpty()) {
                personalizationMapper.insert(Personalization.builder()
                        .code(request.getCode())
                        .customMenuId(menuId)
                        .orgType(request.getOrgType().name())
                        .type("O")
                        .build());
            }
        });
    }

    @Override
    public void updateCustomMenuByFeature(PersonalizationSaveByFeatureRequest request) {
        List<PersonalizationSaveByFeatureRequest.OrgDataByPersonalizationSaveRequest> orgByReq = request.getOrgByReq();

        //1.删除机构对应的所有菜单授权
        personalizationMapper.delete(new LambdaQueryWrapper<Personalization>()
                .eq(Personalization::getCustomMenuId, request.getCustomMenuId()));

        if (CollectionUtils.isNotEmpty(request.getOrgByReq())) {
            //2.重新授权
            orgByReq.forEach(orgCod -> personalizationMapper.insert(Personalization.builder()
                    .code(orgCod.getCode())
                    .customMenuId(request.getCustomMenuId())
                    .orgType(orgCod.getOrgType().name())
                    .type("F")
                    .build()));
        }
    }

    @Override
    public PageInfo<CustomMenu> getCustomMenuNode(String platform, String node, String name,Long current, Long size) {
        //功能所属模块：PRE_SALES-售前；ON_SALE-售中；POST_SALE-售后
        Page<CustomMenu> page = customMenuMapper.selectPage(new Page<>(current,size), Wrappers.lambdaQuery(CustomMenu.class)
                .like(StringUtil.isNotEmpty(name),CustomMenu::getName,name)
                .eq(StringUtil.isNotEmpty(node),CustomMenu::getModule, node)
                .eq(StringUtil.isNotEmpty(platform),CustomMenu::getAppType, platform)
                .orderByDesc(CustomMenu::getId)
        );

        return PageUtil.convert(page, p -> BeanCopier.copyObject(p, CustomMenu.class));
    }

    @Override
    public List<Personalization> getOrg(String code) {
        List<Personalization> personalizationList = personalizationMapper.selectList(new LambdaQueryWrapper<Personalization>()
                .eq(Personalization::getCustomMenuId, code));
        personalizationList.sort(Comparator.comparing(Personalization::getUpdateTime));
        return personalizationList;
    }

    @Override
    public String createMenu (CustomMenu customMenu){
        log.info("createMenu,创建,开始,customMenu:{}", JsonUtil.toJSON(customMenu));
        String code = customMenu.getCode();
        String appType = customMenu.getAppType();
        int count = customMenuMapper.selectCount(new LambdaQueryWrapper<CustomMenu>()
                .eq(CustomMenu::getCode, code)
                .eq(CustomMenu::getAppType, appType));
        AssertUtil.isTrue(count == 0, new ApiException("Code码重复"));
        customMenu.setCreatorId(RequestContextHolder.getStaffId());
        customMenu.setCreator(RequestContextHolder.getStaffUsername());
        customMenu.setCreateTime(LocalDateTime.now());
        customMenu.setUpdateTime(LocalDateTime.now());
        customMenuMapper.insert(customMenu);
        return "Success";
    }

    @Override
    public String updateMenu (CustomMenu customMenu){
        log.info("updateMenu,开始,customMenu:{}", JsonUtil.toJSON(customMenu));
        CustomMenu customMenuDb = customMenuMapper.selectById(customMenu.getId());
        customMenuDb.setName(customMenu.getName());
        customMenuDb.setAppType(customMenu.getAppType());
        customMenuDb.setModule(customMenu.getModule());
        customMenuDb.setRemark(customMenu.getRemark());
        customMenuDb.setUpdateTime(LocalDateTime.now());
        customMenuDb.setModifierId(RequestContextHolder.getStaffId());
        customMenuDb.setModifier(RequestContextHolder.getStaffUsername());
        customMenuMapper.updateById(customMenuDb);
        return "Success";
    }

    public String deleteMenu(Long id){
        log.info("deleteMenu,开始,id:{},操作人ID:{},操作人姓名:{}", id,RequestContextHolder.getStaffId(),RequestContextHolder.getStaffUsername());
        CustomMenu customMenu = customMenuMapper.selectById(id);
        customMenuMapper.deleteById(id);
        personalizationMapper.delete(new LambdaQueryWrapper<Personalization>().eq(Personalization::getCustomMenuId, id));
        return "Success";
    }
}
