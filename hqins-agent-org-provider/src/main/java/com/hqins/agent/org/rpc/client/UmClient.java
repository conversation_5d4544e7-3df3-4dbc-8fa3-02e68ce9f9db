package com.hqins.agent.org.rpc.client;

import com.hqins.agent.org.constants.AppConsts;
import com.hqins.common.utils.JsonUtil;
import com.hqins.um.api.AccountApi;
import com.hqins.um.api.AdminApi;
import com.hqins.um.api.AgentApi;
import com.hqins.um.model.dto.*;
import com.hqins.um.model.request.AgentCreateByAdminRequest;
import com.hqins.um.model.request.VisitorCreateByAdminRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11-05
 * @Description
 */
@Service
@Slf4j
public class UmClient {

    private final AdminApi adminApi;
    private final AccountApi accountApi;
    private final AgentApi agentApi;


    public UmClient(AdminApi adminApi, AccountApi accountApi, AgentApi agentApi) {
        this.adminApi = adminApi;
        this.accountApi = accountApi;
        this.agentApi = agentApi;
    }

    /**
     * 根据agentId查询账号信息
     *
     * @param agentId
     * @return
     */
    public AccountInfoDTO getAgentInfo(Long agentId) {
        log.debug("UmClient #getAgentInfo  agentId:{}", agentId);
        AccountInfoDTO result = agentApi.getAgentInfo(agentId);
        log.debug("UmClient #getAgentInfo  response:{}", JsonUtil.toJSON(result));
        return result;
    }

    /**
     * 创建代理人
     *
     * @param request
     * @return
     */
    public Long createAgentUser(AgentCreateByAdminRequest request) {
        log.debug("UmClient #createAgentUser  request:{}", JsonUtil.toJSON(request));
        request.setAppId(AppConsts.APP_ID);
        Long result = adminApi.createAgentByAdmin(request);
        log.debug("UmClient #createAgentUser  response:{}", JsonUtil.toJSON(result));
        return result;
    }

    /**
     * 根据代理人编码查询账号信息
     *
     * @param employCode
     * @return
     */
    public List<AccountInfoDTO> getAgentsBatch(String employCode) {
        AccountInfoListDTO accountInfoListDTO = new AccountInfoListDTO();
        accountInfoListDTO.setEmployeeCodeList(Collections.singletonList(employCode));
        log.debug("UmClient #getAgentsBatch  request:{}", JsonUtil.toJSON(accountInfoListDTO));
        List<AccountInfoDTO> listAccountInfo = accountApi.listAccountInfo(accountInfoListDTO);
        log.debug("UmClient #getAgentsBatch  response:{}", JsonUtil.toJSON(accountInfoListDTO));
        return listAccountInfo;
    }

    /**
     * 根据代理人编码集合查询账号信息
     *
     * @param employCodeList
     * @return
     */
    public List<AccountInfoDTO> getAgentsBatch(List<String> employCodeList) {
        AccountInfoListDTO accountInfoListDTO = new AccountInfoListDTO();
        accountInfoListDTO.setEmployeeCodeList(employCodeList);
        log.debug("UmClient #getAgentsBatch  request:{}", JsonUtil.toJSON(accountInfoListDTO));
        List<AccountInfoDTO> listAccountInfo = accountApi.listAccountInfo(accountInfoListDTO);
        log.debug("UmClient #getAgentsBatch  response:{}", JsonUtil.toJSON(accountInfoListDTO));
        return listAccountInfo;
    }

    /**
     * 代理人修改手机号
     *
     * @param userId
     * @param phone
     * @param newPhone
     */
    public void agentUserPhoneUpdate(Long userId, String phone, String newPhone) {
        PhoneUpdateDTO phoneUpdateDTO = new PhoneUpdateDTO();
        phoneUpdateDTO.setPhone(phone);
        phoneUpdateDTO.setNewPhone(newPhone);
        phoneUpdateDTO.setStaffId(-1L);
        phoneUpdateDTO.setUserId(userId);
        phoneUpdateDTO.setAppId(AppConsts.APP_ID);
        log.debug("UmClient #agentUserPhoneUpdateRequest  request:{}", JsonUtil.toJSON(phoneUpdateDTO));
        adminApi.updatePhoneByAdmin(phoneUpdateDTO);
        log.debug("UmClient #agentUserPhoneUpdateRequest  response:{}", JsonUtil.toJSON(phoneUpdateDTO));
    }

    /**
     * 创建访客信息
     *
     * @param request
     */
    public void createVisitorByAdmin(VisitorCreateByAdminRequest request) {
        log.debug("UmClient #createCustomerUser  request:{}", JsonUtil.toJSON(request));
        request.setAppId(AppConsts.APP_ID);
        Long result = adminApi.createVisitorByAdmin(request);
        log.debug("UmClient #createCustomerUser  response:{}", JsonUtil.toJSON(result));
    }

    /**
     * 更改代理人状态
     *
     * @param agentStateDTO
     */
    public void updateEmployeeStateByAdmin(AgentStateDTO agentStateDTO) {
        log.debug("UmClient #updateEmployeeStateByAdmin  request:{}", JsonUtil.toJSON(agentStateDTO));
        agentStateDTO.setAppId(AppConsts.APP_ID);
        adminApi.updateEmployeeStateByAdmin(agentStateDTO);
    }

    /**
     * 微信解绑
     *
     * @param weChatUpdateDTO
     */
    public void updateWeChatByAdmin(WeChatUpdateDTO weChatUpdateDTO) {
        log.debug("UmClient #updateWeChatByAdmin  request:{}", JsonUtil.toJSON(weChatUpdateDTO));
        weChatUpdateDTO.setAppId(AppConsts.APP_ID);
        adminApi.updateWeChatByAdmin(weChatUpdateDTO);
    }

    /**
     * 重置密码
     *
     * @param credentialResetDTO
     */
    public void resetCredentialByAdmin(CredentialResetDTO credentialResetDTO) {
        log.debug("UmClient #resetCredentialByAdmin  request:{}", JsonUtil.toJSON(credentialResetDTO));
        credentialResetDTO.setAppId(AppConsts.APP_ID);
        adminApi.resetCredentialByAdmin(credentialResetDTO);
    }

    /**
     * 校验手机号是否存在
     *
     * @param phoneList
     * @return
     */
    public List<String> checkPhoneList(List<String> phoneList) {
        log.debug("UmClient #checkPhoneList  request:{}", JsonUtil.toJSON(phoneList));
        List<String> resultList = accountApi.checkPhoneList(phoneList);
        log.debug("UmClient #checkPhoneList  response:{}", JsonUtil.toJSON(resultList));
        return resultList;
    }

}
