package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.InsureOrgQueryRequest;
import com.hqins.agent.org.model.vo.InsureOrgMgrVO;
import com.hqins.agent.org.model.vo.InsureOrgVO;
import com.hqins.agent.org.service.InsureOrgService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "销售机构-投保专用")
@RestController
@RequestMapping("/insure")
@Slf4j
public class InsureOrgController {

    @Autowired
    private InsureOrgService insureOrgService;

    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCodes
     * @return
     */
    @ApiOperation("根据机构编码获取投保专用机构信息")
    @GetMapping("/org")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<InsureOrgVO> getInsureOrg(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                      @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {
        AssertUtil.notEmpty(orgCode,new ApiException("机构代码信息不存在，请确认后重试！"));
        return ApiResult.ok(insureOrgService.selectInsureOrgsByCode(orgType, orgCode));
    }
    /**
     * 根据机构编码获取投保专用机构信息
     *
     * @param orgType
     * @param orgCodes
     * @return
     */
    @ApiOperation("根据机构编码获取投保专用机构信息")
    @GetMapping("/orgs")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<InsureOrgVO>> getInsureOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                      @ApiParam("机构代码") @RequestParam(value = "orgCodes") List<String> orgCodes) {
        AssertUtil.notEmpty(orgCodes,new ApiException("机构代码信息不存在，请确认后重试！"));
        return ApiResult.ok(insureOrgService.selectInsureOrgsByCodes(orgType, orgCodes));
    }

    @ApiOperation("根据机构编码获取投保专用机构信息")
    @PostMapping(path = "/orgs")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<InsureOrgVO>> getInsureOrgs2(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                      @ApiParam("机构代码") @RequestBody List<String> orgCodes) {
        AssertUtil.notEmpty(orgCodes,new ApiException("机构代码信息不存在，请确认后重试！"));
        return ApiResult.ok(insureOrgService.selectInsureOrgsByCodes(orgType, orgCodes));
    }

    /**
     * 根据条件获取投保专用机构信息
     *
     * @param orgType
     * @param topCode
     * @param topName
     * @param code
     * @param name
     * @param current
     * @param size
     * @return
     */
    @ApiOperation("根据条件获取投保专用机构信息")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<InsureOrgMgrVO>> getInsureOrgs(
            @ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
            @ApiParam("合伙人、渠道商代码") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("合伙人、渠道商名称") @RequestParam(value = "topName", required = false) String topName,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构层级：00-总行/总公司 01-省分/分公司 02-市分 03-支行 04-网点") @RequestParam(value = "orgLevels", required = false) List<String> orgLevels,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        InsureOrgQueryRequest queryRequest = InsureOrgQueryRequest.builder().code(code).name(name)
                .orgType(orgType).topCode(topCode).topName(topName).orgLevels(orgLevels)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(insureOrgService.selectInsureOrgsAll(queryRequest));
    }

    /**
     * a端商品配置导出查询机构
     *
     * @param orgType
     * @param topCode
     * @param topName
     * @param code
     * @param name
     * @param current
     * @param size
     * @return
     */
    @ApiOperation("a端商品配置导出查询机构")
    @GetMapping("/all2")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<InsureOrgMgrVO>> getInsureOrgs2(
            @ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
            @ApiParam("合伙人、渠道商代码") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("合伙人、渠道商名称") @RequestParam(value = "topName", required = false) String topName,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构层级：00-总行/总公司 01-省分/分公司 02-市分 03-支行 04-网点") @RequestParam(value = "orgLevels", required = false) List<String> orgLevels,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        InsureOrgQueryRequest queryRequest = InsureOrgQueryRequest.builder().code(code).name(name)
                .orgType(orgType).topCode(topCode).topName(topName).orgLevels(orgLevels)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();
        PageInfo<InsureOrgMgrVO> pageInfo = insureOrgService.selectInsureOrgsAll(queryRequest);
        return ApiResult.ok(pageInfo.getRecords());
    }

    @ApiOperation("给机构设置经销商")
    @PutMapping("/dealer")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> updateDealer(
            @ApiParam("拥有者") @RequestParam(value = "ownerOrgCodes") List<String> ownerOrgCodes,
            @ApiParam("经销商机构") @RequestParam(value = "orgCode") String orgCode) {

        insureOrgService.updateDealer(ownerOrgCodes, orgCode);

        return ApiResult.ok();
    }

}
