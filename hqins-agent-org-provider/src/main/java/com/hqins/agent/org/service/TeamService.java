package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.TeamHonorRequest;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.HonorVO;

import java.util.Date;
import java.util.List;


public interface TeamService {

    List<EmployeeVO> getBirthDayEmpList(String saleTeamCode, Date startDate, Date endDate) throws Exception;

    EmployeeVO getEmpInfo(String employeeCode);
}
