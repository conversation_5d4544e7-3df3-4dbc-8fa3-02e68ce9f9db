package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.InsureOrgQueryRequest;
import com.hqins.agent.org.model.vo.InsureOrgMgrVO;
import com.hqins.agent.org.model.vo.InsureOrgVO;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/525
 * @Description
 */
public interface InsureOrgService {

    InsureOrgVO selectInsureOrgsByCode(AgentOrgType orgType, String orgCode);
    List<InsureOrgVO> selectInsureOrgsByCodes(AgentOrgType orgType, List<String> orgCodes);

    PageInfo<InsureOrgMgrVO> selectInsureOrgsAll(InsureOrgQueryRequest queryRequest);

    void updateDealer(List<String> ownerOrgCodes, String orgCode);
}
