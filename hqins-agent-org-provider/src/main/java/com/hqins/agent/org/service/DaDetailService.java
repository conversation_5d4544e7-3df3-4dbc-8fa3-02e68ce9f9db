package com.hqins.agent.org.service;

import com.hqins.agent.org.model.vo.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface DaDetailService {

    List<HoloDaDetailUwVo> selectDaDetailUwList(Map requestMap);

    PerformanceVO selectPerformanceAcc(List<String> instCodeList, String teamCode);

    List<SupervisorPerformanceDetailNumberVO> selectPerformanceList(Map map);

    List<MarkDetailVO> getHologresSelfPolicyList(List<String> codeList, String teamCode, LocalDate startDate, LocalDate endDate);

    List<MarkDetailVO> getSupervisorCtList(List<String> instCodeList, String teamCode, LocalDate startDate, LocalDate endDate);

    List<SupervisorPerformanceDetailNumberVO> selectAppNumList(Map map);
}