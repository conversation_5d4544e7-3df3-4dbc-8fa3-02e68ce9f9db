package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* 内勤看板-家庭风险管理师导出VO类.
* <AUTHOR> MXH
* @create 2025/3/25 18:20
*/
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class PerformanceCheckRiskMangerExcel implements Serializable {

    @ExcelProperty("机构")
    @ApiModelProperty("机构")
    private String  instName;

    @ExcelProperty("团队")
    @ApiModelProperty("团队")
    private String  teamName;

    @ExcelProperty("代理人")
    @ApiModelProperty("代理人")
    private String  agentName;

    @ExcelProperty("职级")
    @ApiModelProperty(value = "职级")
    private String rankName;

    @ExcelProperty("是否连续9个月挂0")
    @ApiModelProperty(value = "是否连续9个月挂0")
    private String  consecutiveZeroNineMonths;

    @ExcelProperty("FYC维持考核标准(元)")
    @ApiModelProperty(value = "FYC维持考核标准(元)")
    private String maintenanceMonthlyFyc;

    @ExcelProperty("FYC实际达成(元)")
    @ApiModelProperty(value = "FYC实际达成(元)")
    private String keepActualStp;

    @ExcelProperty("距离达标差距(元)")
    @ApiModelProperty(value = "距离达标差距(元)")
    private String distanceGap;
}
