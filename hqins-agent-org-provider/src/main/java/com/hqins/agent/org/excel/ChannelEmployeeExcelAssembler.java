package com.hqins.agent.org.excel;

import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.model.enums.RoleType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 类型转换
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Service
public class ChannelEmployeeExcelAssembler {

    public ChannelEmployeeExcel assembler(ChannelEmployee channelEmployee) {
        ChannelEmployeeExcel channelEmployeeExcel = new ChannelEmployeeExcel();
        channelEmployeeExcel.setCode(StringUtils.isNotEmpty(channelEmployee.getCode()) ? channelEmployee.getCode() : "");
        channelEmployeeExcel.setName(StringUtils.isNotEmpty(channelEmployee.getName()) ? channelEmployee.getName() : "");
        channelEmployeeExcel.setOrgCode(StringUtils.isNotEmpty(channelEmployee.getOrgCode()) ? channelEmployee.getOrgCode() : "");
        channelEmployeeExcel.setOrgName(StringUtils.isNotEmpty(channelEmployee.getOrgName()) ? channelEmployee.getOrgName() : "");
        channelEmployeeExcel.setTeamCode(StringUtils.isNotEmpty(channelEmployee.getTeamCode()) ? channelEmployee.getTeamCode() : "");
        channelEmployeeExcel.setTeamName(StringUtils.isNotEmpty(channelEmployee.getTeamName()) ? channelEmployee.getTeamName() : "");
        channelEmployeeExcel.setJobNumber(StringUtils.isNotEmpty(channelEmployee.getJobNumber()) ? channelEmployee.getJobNumber() : "");
        channelEmployeeExcel.setMobile(StringUtils.isNotEmpty(channelEmployee.getMobile()) ? channelEmployee.getMobile() : "");
        channelEmployeeExcel.setIdType(null != channelEmployee.getIdType() ? null != IdType.get(channelEmployee.getIdType()) ? IdType.get(channelEmployee.getIdType()).getLabel() : null : null);
        channelEmployeeExcel.setIdCode(StringUtils.isNotEmpty(channelEmployee.getIdCode()) ? channelEmployee.getIdCode() : "");
        channelEmployeeExcel.setGender(Gender.get(channelEmployee.getGender()).getLabel());
        if (channelEmployee.getUniversalQualification()) {
            channelEmployeeExcel.setUniversalQualification("是");
        } else {
            channelEmployeeExcel.setUniversalQualification("否");
        }

        channelEmployeeExcel.setLicenseNo(StringUtils.isNotEmpty(channelEmployee.getLicenseNo()) ? channelEmployee.getLicenseNo() : "");
        channelEmployeeExcel.setLicenseStartDate(channelEmployee.getLicenseStartDate() != null ? channelEmployee.getLicenseStartDate().toString() : null);
        channelEmployeeExcel.setLicenseEndDate(channelEmployee.getLicenseEndDate() != null ? channelEmployee.getLicenseEndDate().toString() : null);
        channelEmployeeExcel.setRoleType(null != channelEmployee.getRoleType() ? Objects.requireNonNull(RoleType.get(channelEmployee.getRoleType())).getLabel() : null);
        channelEmployeeExcel.setEntryTime(channelEmployee.getEntryTime() != null ? channelEmployee.getEntryTime().toString() : null);
        channelEmployeeExcel.setQuitTime(channelEmployee.getQuitTime() != null ? channelEmployee.getQuitTime().toString() : null);
        return channelEmployeeExcel;
    }


    public static void main(String[] args) {
        System.out.println(Objects.requireNonNull(RoleType.get("FORMAL")).getLabel());

        System.out.println(IdType.get("ID").getLabel());

    }

}
