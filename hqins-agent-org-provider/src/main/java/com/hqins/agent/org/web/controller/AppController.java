package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.LoginType;
import com.hqins.agent.org.service.AppService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * APP
 * <AUTHOR>
 * @since 2024-06-13
 */
@Api(tags = "APP")
@RestController
@RequestMapping("/app")
@Slf4j
public class AppController {

    private final AppService appService;

    public AppController(AppService appService) {
        this.appService = appService;
    }

    @ApiOperation("校验是否能登录")
    @GetMapping ("/check-login")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> checkLogin(@ApiParam("销售员代码/手机号") @RequestParam(value = "code") String code,
                                         @ApiParam("登录类型") @RequestParam(value = "loginType") LoginType loginType) {
        return ApiResult.ok(appService.checkLogin(code,loginType));
    }

    
}
