package com.hqins.agent.org.dao.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hqins.common.web.RequestContextHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2021/4/5
 * @Description
 */
@Component
public class AutoFillObjectHandler implements MetaObjectHandler {

    private Long getUserId() {
        if (RequestContextHolder.getStaffId() != null) {
            return RequestContextHolder.getStaffId();
        }
        return 0L;
    }

    private String getUsername() {
        if (RequestContextHolder.getStaffId() != null) {
            return RequestContextHolder.getStaffUsername();
        }
        return "";
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "creator", String.class, getUsername());
        this.strictInsertFill(metaObject, "creatorId", Long.class, getUserId());
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "deleted", Boolean.class, Boolean.FALSE);
        this.updateFill(metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "modifier", String.class, getUsername());
        this.strictUpdateFill(metaObject, "modifierId", Long.class, getUserId());
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        Object obj = fieldVal.get();
        if (Objects.nonNull(obj)) {
            metaObject.setValue(fieldName, obj);
        }
        return this;
    }
}
