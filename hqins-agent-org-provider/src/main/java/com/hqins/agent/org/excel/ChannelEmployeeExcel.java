package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合伙人下代理人模型
 * <AUTHOR>
 * @date 2022-09-06
 */
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class ChannelEmployeeExcel implements Serializable {
    @ExcelProperty("人员代码")
    @ApiModelProperty("人员代码")
    private String code;

    @ExcelProperty("人员名称")
    @ApiModelProperty("人员名称")
    private String name;

    @ExcelProperty("归属销售机构代码")
    @ApiModelProperty("归属销售机构代码")
    private String orgCode;

    @ExcelProperty("归属销售机构名称")
    @ApiModelProperty("归属销售机构名称")
    private String orgName;

    @ExcelProperty("归属团队代码")
    @ApiModelProperty("归属团队代码")
    private String teamCode;

    @ExcelProperty("归属团队名称")
    @ApiModelProperty("归属团队名称")
    private String teamName;

    @ExcelProperty("内部工号")
    @ApiModelProperty("内部工号")
    private String jobNumber;

    @ExcelProperty("手机号")
    @ApiModelProperty("手机号")
    private String mobile;

    @ExcelProperty("证件类型")
    @ApiModelProperty("证件类型")
    private String idType;

    @ExcelProperty("证件号码")
    @ApiModelProperty("证件号码")
    private String idCode;

    @ExcelProperty("性别")
    @ApiModelProperty("性别")
    private String gender;

    @ExcelProperty("万能险资格")
    @ApiModelProperty("万能险资格")
    private String universalQualification;

    @ExcelProperty("执业证号")
    @ApiModelProperty("执业证号")
    private String licenseNo;

    @ExcelProperty("证书起期")
    @ApiModelProperty("证书起期")
    @JSONField(format = "yyyy-MM-dd")
    private String licenseStartDate;

    @ExcelProperty("证书止期")
    @ApiModelProperty("证书止期")
    @JSONField(format = "yyyy-MM-dd")
    private String licenseEndDate;

    @ExcelProperty("是否体验账号")
    @ApiModelProperty("是否体验账号")
    private String roleType;

    @ExcelProperty("入职日期")
    @ApiModelProperty("入职日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private String entryTime;

    @ExcelProperty("离职日期")
    @ApiModelProperty("离职日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private String quitTime;
}
