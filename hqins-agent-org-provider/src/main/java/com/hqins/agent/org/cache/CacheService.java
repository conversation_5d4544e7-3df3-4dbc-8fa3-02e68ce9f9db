package com.hqins.agent.org.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.model.request.InsureOrgQueryRequest;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.DigitalAllDataVO;
import com.hqins.agent.org.model.vo.DigitalDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreAllDetailVO;
import com.hqins.agent.org.model.vo.DigitalScoreDetailVO;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import org.springframework.core.env.Environment;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @since 2021/6/11
 */
@Component
@Getter
@Slf4j
public class CacheService {
    private List<Tbepartner> allTbepartners;
    private Map<String, Tbepartner> allTbepartnersMap;
    private Map<String, Tbepartner> allIdTbepartnersMap;
    private List<Tbsaleteam> allTbsaleteams;
    private Map<String, Tbsaleteam> allTbsaleteamsMap;
    private List<BaseInst> allBaseInsts;
    private Map<String, BaseInst> allBaseInstsMap;
    private List<ChannelTeam> allChannelTeams;
    private Map<String, ChannelTeam> allChannelTeamsMap;
    private Map<String, Tbemp> allTbempMap;
    private Map<String, Tbemp> allTbempInCodeMap;
    private Map<String, RankDef> allRankDefMap;
    private Map<String, RankSequ> allRankSequMap;
    private List<Tbpartassignmanager> allAssignManagers;

    private Map<String,List<DigitalDataVO>> allDigitalScoreMap;

    private Map<String, Map<String, DigitalScoreAllDetailVO>> allDigitalScoreDetailMap;

    private boolean turnOn;

    @Autowired
    private Environment environment;

    public boolean isTurnOn() {
        return turnOn;
    }

    public void resetTurnOn() {
        String turnOn = environment.getProperty("zybx.turnOn");
        this.turnOn = Boolean.valueOf(turnOn);
    }

    public void setAllRankDefMap(List<RankDef> rankDefList) {
        this.allRankDefMap = rankDefList.stream().collect(Collectors.toMap(RankDef :: getRankCode, Function.identity(), (key1, key2) -> key1));
    }

    public void setAllRankSequMap(List<RankSequ> rankSequList) {
        this.allRankSequMap = rankSequList.stream().collect(Collectors.toMap(RankSequ :: getRankSequCode, Function.identity(), (key1, key2) -> key1));
    }

    public void setAllTbempMap(List<Tbemp> tbemps) {
        this.allTbempMap = tbemps.stream().collect(Collectors.toMap(Tbemp :: getEmpcode, Function.identity(), (key1, key2) -> key1));
        this.allTbempInCodeMap = tbemps.stream().collect(Collectors.toMap(Tbemp :: getEmpincode, Function.identity(), (key1, key2) -> key1));
    }

    public void setAllTbepartners(List<Tbepartner> allTbepartners) {
        if (CollectionUtils.isNotEmpty(allTbepartners)) {
            Collections.sort(allTbepartners, (Tbepartner o1, Tbepartner o2) -> {
                if (o1 == null || o2 == null) {
                    return 1;
                }
                return o1.getCompanycode().compareTo(o2.getCompanycode());
            });
            this.allTbepartnersMap = allTbepartners.stream().collect(Collectors.toMap(Tbepartner::getCompanycode, Function.identity(), (key1, key2) -> key1));
            this.allIdTbepartnersMap = allTbepartners.stream().collect(Collectors.toMap(Tbepartner::getCompanyid, Function.identity(), (key1, key2) -> key1));
            this.allTbepartners = allTbepartners;
        } else {
            this.allTbepartnersMap = this.allTbepartnersMap != null ? this.allTbepartnersMap : new HashMap<>();
            this.allIdTbepartnersMap = this.allIdTbepartnersMap != null ? this.allIdTbepartnersMap : new HashMap<>();
            this.allTbepartners = CollectionUtils.isNotEmpty(this.allTbepartners) ? this.allTbepartners : new ArrayList<>();
        }
    }

    public void setAllTbsaleteams(List<Tbsaleteam> allTbsaleteams) {
        if (CollectionUtils.isNotEmpty(allTbsaleteams)) {
            Collections.sort(allTbsaleteams, (Tbsaleteam o1, Tbsaleteam o2) -> {
                if (o1 == null || o2 == null) {
                    return 1;
                }
                return o1.getSaleteamcode().compareTo(o2.getSaleteamcode());
            });
            this.allTbsaleteamsMap = allTbsaleteams.stream().collect(Collectors.toMap(Tbsaleteam::getSaleteamcode, Function.identity(), (key1, key2) -> key1));
            if(CollUtil.isNotEmpty(allTbsaleteamsMap)){
                if(allTbsaleteamsMap.containsKey("000000001172")){
                    log.info("有问题的key对应的value:{}", JSONObject.toJSONString(allTbsaleteamsMap.get("000000001172")));
                }
                List<String> collect = allTbsaleteamsMap.keySet().stream().filter(t -> StrUtil.isNotBlank(t)).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(collect)){
                    log.info("所有key:{}", JSONObject.toJSONString(collect));
                }
            }
            this.allTbsaleteams = allTbsaleteams;
        } else {
            this.allTbsaleteamsMap = this.allTbsaleteamsMap != null ? this.allTbsaleteamsMap : new HashMap<>();
            this.allTbsaleteams = CollectionUtils.isNotEmpty(this.allTbsaleteams) ? this.allTbsaleteams : new ArrayList<>();
        }
    }

    public void setAllAssignManager(List<Tbpartassignmanager> allAssignManagers) {
        this.allAssignManagers = allAssignManagers;
    }

    public void setAllBaseInsts(List<BaseInst> allBaseInsts) {
        if (CollectionUtils.isNotEmpty(allTbsaleteams)) {Collections.sort(allBaseInsts, (BaseInst o1, BaseInst o2) -> {
            try{
                if (o1 == null || o2 == null) {
                    return 1;
                }
                if(null !=o1.getInstStatus() && null !=o2.getInstStatus()){
                    if (o1.getInstStatus().equals(o2.getInstStatus())) {
                        return o1.getInstCode().compareTo(o2.getInstCode());
                    } else {
                        if (AppConsts.INST_STATUS_ENABLED.equals(o1.getInstStatus())) {
                            return -1;
                        }
                        if (AppConsts.INST_STATUS_ENABLED.equals(o2.getInstStatus())) {
                            return 1;
                        }
                        return o1.getInstCode().compareTo(o2.getInstCode());
                    }
                }else{
                    return 1;
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            return  1;

            });
        this.allBaseInstsMap = allBaseInsts.stream().collect(Collectors.toMap(BaseInst::getInstCode, Function.identity(), (key1, key2) -> key1));
            this.allBaseInsts = allBaseInsts;
        } else {
            this.allBaseInstsMap = this.allBaseInstsMap != null ? this.allBaseInstsMap : new HashMap<>();
            this.allBaseInsts = CollectionUtils.isNotEmpty(this.allBaseInsts) ? this.allBaseInsts : new ArrayList<>();
        }
    }

    public void setAllChannelTeams(List<ChannelTeam> allChannelTeams) {
        if (CollectionUtils.isNotEmpty(allChannelTeams)) {
            Collections.sort(allChannelTeams, (ChannelTeam o1, ChannelTeam o2) -> {
                if (o1 == null || o2 == null) {
                    return 1;
                }
                return o1.getCode().compareTo(o2.getCode());
            });
            this.allChannelTeamsMap = allChannelTeams.stream().collect(Collectors.toMap(ChannelTeam::getCode, Function.identity()));
            this.allChannelTeams = allChannelTeams;
        } else {

            this.allChannelTeamsMap = this.allChannelTeamsMap != null ? this.allChannelTeamsMap : new HashMap<>();
            this.allChannelTeams = CollectionUtils.isNotEmpty(this.allChannelTeams) ? this.allChannelTeams : new ArrayList<>();
        }
    }

    /**
     * 根据条件获取有权限的顶层机构分页
     * topCodes为null则获取所有的
     *
     * @param cptypeChannel
     * @param queryRequest
     * @param topCodes
     * @return
     */
    public Page<Tbepartner> selectTbepartnerPage(String cptypeChannel, TopQueryRequest queryRequest, Set<String> topCodes) {
        List<Tbepartner> list = selectTbepartners(cptypeChannel, queryRequest, topCodes);
        if (queryRequest.getSize() < 0) {
            //如果过size小于0，则返回全量数据
            Page<Tbepartner> page = new Page(1L, list.size(), list.size());
            page.setRecords(list);
            return page;
        }
        return PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());
    }

    /**
     * 根据条件获取有权限的顶层机构列表
     * topCodes为null则获取所有的
     *
     * @param cptype
     * @param queryRequest
     * @param topCodes
     * @return
     */
    public List<Tbepartner> selectTbepartners(String cptype, TopQueryRequest queryRequest, Set<String> topCodes) {
        List<Tbepartner> list = this.allTbepartners.stream().filter(t -> cptype.equals(t.getCptype())
                && t.getComanystatus() == 1
                && (StringUtil.isBlank(queryRequest.getCode()) || queryRequest.getCode().equals(t.getCompanycode()))
                && (StringUtil.isBlank(queryRequest.getName()) || t.getCompanyname().contains(queryRequest.getName()))
                && (topCodes == null || topCodes.contains(t.getCompanycode()))
        ).collect(Collectors.toList());
        return list;
    }

    /**
     * 根据条件获取有权限的顶层机构分页
     * topCodes为null则获取所有的
     *
     * @param cptype
     * @param queryRequest
     * @param orgCodes
     * @return
     */
    public Page<BaseInst> selectBaseInstPage(String cptype, OrgQueryRequest queryRequest, Set<String> orgCodes) {
        List<BaseInst> list = selectBaseInsts(cptype, queryRequest, orgCodes);
        if (queryRequest.getSize() < 0) {
            //如果过size小于0，则返回全量数据
            Page<BaseInst> page = new Page(1L, list.size(), list.size());
            page.setRecords(list);
            return page;
        }
        return PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());
    }

    /**
     * 根据条件获取有权限的顶层机构分页
     * topCodes为null则获取所有的
     *
     * @param cptype
     * @param queryRequest
     * @return
     */
    public Page<BaseInst> selectBaseInstPage(String cptype, InsureOrgQueryRequest queryRequest, Set<String> orgCodes) {
        List<BaseInst> list = selectBaseInsts(cptype, queryRequest, orgCodes);
        if (queryRequest.getSize() < 0) {
            //如果过size小于0，则返回全量数据
            Page<BaseInst> page = new Page(1L, list.size(), list.size());
            page.setRecords(list);
            return page;
        }
        return PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());
    }

    private List<BaseInst> selectBaseInsts(String cptype, InsureOrgQueryRequest queryRequest, Set<String> orgCodes) {
        Set<String> topIds = this.allTbepartners.stream().filter(t -> cptype.equals(t.getCptype())
                && t.getComanystatus() == 1
                && (StringUtil.isBlank(queryRequest.getTopCode()) || queryRequest.getTopCode().equals(t.getCompanycode()))
                && (StringUtil.isBlank(queryRequest.getTopName()) || queryRequest.getTopName().contains(t.getCompanyname()))
        ).map(Tbepartner::getCompanyid).collect(Collectors.toSet());
        List<BaseInst> list = this.allBaseInsts.stream().filter(t -> topIds.contains(t.getCompanyid())
                && "1".equals(t.getInstStatus())
                && (StringUtil.isBlank(queryRequest.getCode()) || queryRequest.getCode().equals(t.getInstCode()))
                && (StringUtil.isBlank(queryRequest.getName()) || t.getInstName().contains(queryRequest.getName()))
                && (queryRequest.getOrgLevels() == null || queryRequest.getOrgLevels().isEmpty() || queryRequest.getOrgLevels().contains(t.getInstLevel()))
        ).collect(Collectors.toList());

        //增加过滤条件
        List<BaseInst> filterList = list.stream().filter(t -> orgCodes == null || orgCodes.contains(t.getInstCode())).collect(Collectors.toList());
        return filterList;
    }

    /**
     * 根据条件获取有权限的销售机构列表
     * topCodes为null则获取所有的
     *
     * @param cptype
     * @param queryRequest
     * @param orgCodes
     * @return
     */
    public List<BaseInst> selectBaseInsts(String cptype, OrgQueryRequest queryRequest, Set<String> orgCodes) {
        Set<String> topIds = this.allTbepartners.stream().filter(t -> cptype.equals(t.getCptype())
                && t.getComanystatus() == 1
                && (StringUtil.isBlank(queryRequest.getTopCode()) || queryRequest.getTopCode().equals(t.getCompanycode()))
                && (StringUtil.isBlank(queryRequest.getTopName()) || queryRequest.getTopName().contains(t.getCompanyname()))
        ).map(Tbepartner::getCompanyid).collect(Collectors.toSet());
        List<BaseInst> list = this.allBaseInsts.stream().filter(t -> (!"ENABLED".equals(queryRequest.getStatus()) || "1".equals(t.getInstStatus()))
                && (StringUtil.isBlank(queryRequest.getCode()) || queryRequest.getCode().equals(t.getInstCode()))
                && (StringUtil.isBlank(queryRequest.getName()) || t.getInstName().contains(queryRequest.getName()))
                && topIds.contains(t.getCompanyid())
                && (orgCodes == null || orgCodes.contains(t.getInstCode()))
        ).collect(Collectors.toList());
        return list;
    }

    /**
     * 根据机构编码、名字获取销售机构列表
     *
     * @param orgCode 精确搜索
     * @param orgName 模糊搜索
     * @return
     */
    public List<BaseInst> selectBaseInsts(String orgCode, String orgName, boolean superAdmin, Set<String> orgCodes) {
        List<BaseInst> list = this.allBaseInsts.stream().filter(t -> "1".equals(t.getInstStatus())
                && (StringUtil.isBlank(orgCode) || orgCode.equals(t.getInstCode()))
                && (StringUtil.isBlank(orgName) || t.getInstName().contains(orgName))
                && (superAdmin || orgCodes.contains(t.getInstCode()))
        ).collect(Collectors.toList());
        return list;
    }

    /**
     * 根据机构编码获取获取子机构编码，包含自己
     *
     * @param orgCode
     * @param orgName
     * @return
     */
    public List<String> selectAllChildCodes(String orgCode, String orgName, boolean superAdmin, Set<String> orgCodes) {
        List<String> list = new ArrayList<>();
        List<BaseInst> insts = selectBaseInsts(orgCode, orgName, superAdmin, orgCodes);
        if (insts.isEmpty()) {
            return list;
        }
        List<String> instCodes = insts.stream().map(BaseInst::getInstCode).collect(Collectors.toList());
        if (instCodes.isEmpty()) {
            return list;
        }
        for (BaseInst t : this.getAllBaseInsts()) {
            if (!AppConsts.INST_STATUS_ENABLED.equals(t.getInstStatus())) {
                continue;
            }
            if (!superAdmin && !orgCodes.contains(t.getInstCode())) {
                continue;
            }
            for (String code : instCodes) {
                if (!StringUtil.isBlank(t.getParentInstCode()) && t.getParentInstCode().indexOf(code) == 0) {
                    list.add(t.getInstCode());
                }
            }
        }
        list.addAll(instCodes);
        return list;
    }

    public void setAllDigitalDataMap(List<DigitalAllDataVO> digitalAllDataVOList) {
        if (CollectionUtils.isEmpty(digitalAllDataVOList)){
            return;
        }
        if (CollectionUtils.isNotEmpty(digitalAllDataVOList)){
            //按照代理人分组，组内为该代理人每个月份的积分数据
            this.allDigitalScoreMap = digitalAllDataVOList
                    .stream()
                    .collect(Collectors.groupingBy(DigitalAllDataVO::getAgentCode, Collectors.mapping(item -> {
                                DigitalDataVO digitalDataVO = new DigitalDataVO();
                                digitalDataVO.setMonthDate(item.getMonthDate());
                                digitalDataVO.setScore(item.getScore());
                                return digitalDataVO;
                            }, Collectors.toList()))
                    );
            //将组内数据按照时间正序排序
            this.allDigitalScoreMap.values().forEach(item -> item.sort((Comparator.comparing(DigitalDataVO::getMonthDate))));
        }
    }

    public void setDigitalDetailMap(List<DigitalScoreDetailVO> digitalDetailList) {
        if (CollectionUtils.isEmpty(digitalDetailList)){
            return;
        }
        Map<String, Map<String, DigitalScoreAllDetailVO>> result = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        Map<String, Map<String, List<DigitalScoreDetailVO>>> map = digitalDetailList
                .stream()
                .filter(item -> null != item.getActionTime())
                .collect(Collectors.groupingBy(DigitalScoreDetailVO::getAgentCode,
                        Collectors.groupingBy(item -> formatter.format(item.getActionTime()))));
        map.forEach((k,v) -> {
            Map<String, DigitalScoreAllDetailVO> stringListHashMap = new HashMap<>();
            v.forEach((x,y) -> {
                List<DigitalScoreAllDetailVO.DetailDataVO> detailDataVOS = new ArrayList<>();
                //封装明细列表集合
                for (DigitalScoreDetailVO digitalScoreDetailVO : y) {
                    DigitalScoreAllDetailVO.DetailDataVO detailDataVO = new DigitalScoreAllDetailVO.DetailDataVO();
                    detailDataVO.setActionTime(digitalScoreDetailVO.getActionTime());
                    detailDataVO.setActionType(digitalScoreDetailVO.getActionType());
                    detailDataVO.setRecordPointsFlag(digitalScoreDetailVO.getRecordPointsFlag());
                    detailDataVOS.add(detailDataVO);
                }
                //按完成时间倒序
                List<DigitalScoreAllDetailVO.DetailDataVO> sortedVOS = detailDataVOS.stream().sorted(Comparator.comparing(DigitalScoreAllDetailVO.DetailDataVO::getActionTime).reversed()).collect(Collectors.toList());
                //封装该代理人的工号，团队，分数等
                DigitalScoreAllDetailVO digitalScoreAllDetailVO = new DigitalScoreAllDetailVO();
                digitalScoreAllDetailVO.setDetailDataList(sortedVOS);
                digitalScoreAllDetailVO.setAgentCode(k);
                digitalScoreAllDetailVO.setTeamName(y.get(0).getTeamName());
                digitalScoreAllDetailVO.setScore(y.get(0).getScore());
                digitalScoreAllDetailVO.setTeamCode(y.get(0).getTeamCode());
                digitalScoreAllDetailVO.setAgentName(y.get(0).getAgentName());
                stringListHashMap.put(x,digitalScoreAllDetailVO);
            });
            result.put(k,stringListHashMap);

        });
        this.allDigitalScoreDetailMap = result;
    }
}
