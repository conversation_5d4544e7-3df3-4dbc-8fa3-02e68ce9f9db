package com.hqins.agent.org.dao.converter;

import cn.hutool.core.util.PhoneUtil;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.model.enums.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.constants.DatePatterns;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.enums.IdType;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.utils.TimeUtil;
import org.apache.commons.lang.StringUtils;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/17
 * @Description
 */
public class PartnerConverter {

    public static PartnerVO tbepartnerToPartnerVO(Tbepartner t) {
        PartnerVO vo = new PartnerVO();
        vo.setCode(t.getCompanycode());
        vo.setName(t.getCompanyname());
        vo.setLeader(t.getLegalPerson());
        vo.setEffectiveDate(t.getCooperationStarttime());
        vo.setInvalidTime(t.getCooperationEndtime());
        return vo;
    }

    public static PartnerTeamTreeNodeVO tbsaleteamToPartnerTeamTreeNodeVO(Tbsaleteam t) {
        PartnerTeamTreeNodeVO vo = new PartnerTeamTreeNodeVO();
        vo.setCode(t.getSaleteamcode());
        vo.setName(t.getSaleteamname());
        vo.setParentCode(t.getSupersaleteamcode());
        vo.setLeader(t.getEmpinname());
        vo.setLeaderCode(t.getEmpincode());
        vo.setOrgCode(t.getInstCode());
        vo.setOrgName(t.getInstName());
        //归属最顶层的
        vo.setTopCode(t.getCompanycode());
        if ("03".equals(t.getTeamlevel())) {
            vo.setLevel(TeamLevel.AREA);
        } else if ("02".equals(t.getTeamlevel())) {
            vo.setLevel(TeamLevel.DEPT);
        } else {
            vo.setLevel(TeamLevel.TEAM);
        }
        if ("00".equals(t.getSaleteamstatus())) {
            vo.setStatus(TeamStatus.ENABLED);
        } else {
            vo.setStatus(TeamStatus.DISABLED);
        }
        return vo;
    }

    public static SimpleTeamTreeNodeVO tbsaleteamToSimpleTeamTreeNodeVO(Tbsaleteam t) {
        SimpleTeamTreeNodeVO vo = new SimpleTeamTreeNodeVO();
        vo.setCode(t.getSaleteamcode());
        vo.setName(t.getSaleteamname());
        if ("03".equals(t.getTeamlevel())) {
            vo.setLevel(TeamLevel.AREA);
        } else if ("02".equals(t.getTeamlevel())) {
            vo.setLevel(TeamLevel.DEPT);
        } else {
            vo.setLevel(TeamLevel.TEAM);
        }
        return vo;
    }

    public static ChannelVO tbepartnerToChannelVO(Tbepartner t) {
        ChannelVO vo = new ChannelVO();
        vo.setCode(t.getCompanycode());
        vo.setName(t.getCompanyname());
        vo.setLeader(t.getLegalPerson());
        vo.setParentName(t.getParentCompanyName());
        vo.setEffectiveDate(t.getCooperationStarttime());
        vo.setInvalidTime(t.getCooperationEndtime());
        vo.setChannelType(getChannelType((t.getChannelType())));
        return vo;
    }

    public static SimpleNodeVO tbepartnerToSimpleNodeVO(Tbepartner t) {
        SimpleNodeVO vo = new SimpleNodeVO();
        vo.setName(t.getCompanyname());
        vo.setCode(t.getCompanycode());
        vo.setType(t.getChannelType());
        return vo;
    }

    public static SimpleTreeNodeVO tbepartnerToSimpleTreeNodeVO(Tbepartner t) {
        SimpleTreeNodeVO vo = new SimpleTreeNodeVO();
        vo.setCode(t.getCompanycode());
        vo.setName(t.getCompanyname());
        return vo;
    }

    public static AccountVO channelEmployeeToAccountVO(ChannelEmployee t) {
        AccountVO vo = new AccountVO();
        vo.setCode(t.getCode());
        vo.setName(t.getName());
        vo.setMobile(t.getMobile());
        vo.setTopCode(t.getChannelCode());
        vo.setTopName(t.getChannelName());
        if (EmployeeStatus.SERVING.name().equals(t.getStatus())) {
            vo.setEmployeeStatus(EmployeeStatus.SERVING);
        } else {
            vo.setEmployeeStatus(EmployeeStatus.LEAVING);
        }
        return vo;
    }

    public static PartnerEmployeeVO mapToPartnerEmployeeVO(Map map) {
        PartnerEmployeeVO vo = new PartnerEmployeeVO();
        vo.setCode(StringUtil.toString(map.get("code")));
        vo.setName(StringUtil.toString(map.get("name")));
        vo.setOrgCode(StringUtil.toString(map.get("orgCode")));
        vo.setOrgName(StringUtil.toString(map.get("orgName")));
        vo.setTeamCode(StringUtil.toString(map.get("teamCode")));
        vo.setTeamName(StringUtil.toString(map.get("teamName")));
        vo.setReferences(StringUtil.toString(map.get("referencesName")));

        vo.setStatus(PartnerEmployeeStatus.getLabelByCode(StringUtil.toString(map.get("status"))));
        vo.setStatusTitle(null != PartnerEmployeeStatus.getLabelByCode(StringUtil.toString(map.get("status")))? PartnerEmployeeStatus.getLabelByCode(StringUtil.toString(map.get("status"))).getLabel():null);

        if (map.get("entryTime") != null) {
            vo.setEntryTime(((Timestamp) map.get("entryTime")).toLocalDateTime());
        }
        if (map.get("quitTime") != null) {
            vo.setQuitTime(((Timestamp) map.get("quitTime")).toLocalDateTime());
        }
        vo.setLicenseNo(StringUtil.toString(map.get("licenseNo")));
        return vo;
    }

    public static AccountVO mapToAccountVO(Map map) {
        AccountVO vo = new AccountVO();
        vo.setCode(StringUtil.toString(map.get("code")));
        vo.setName(StringUtil.toString(map.get("name")));
        vo.setMobile(StringUtil.toString(map.get("mobile")));
        vo.setTopCode(StringUtil.toString(map.get("topCode")));
        if ("01".equals(StringUtil.toString(map.get("status")))) {
            vo.setEmployeeStatus(EmployeeStatus.SERVING);
        } else {
            vo.setEmployeeStatus(EmployeeStatus.LEAVING);
        }
        return vo;
    }


    public static EmployeeVO mapToEmployeeVO(Map<String, Object> map) {
        EmployeeVO vo = new EmployeeVO();
        vo.setCode(StringUtil.toString(map.get("code")));
        vo.setName(StringUtil.toString(map.get("name")));
        if (StringUtil.isNotEmpty(StringUtil.toString(map.get("birthday")))) {
            try {
                vo.setBirthday(TimeUtil.parseLocalDate(StringUtil.toString(map.get("birthday")), DatePatterns.YYYY_MM_DD));
            } catch (Exception ignored) {
            }
        }
        vo.setTopCode(StringUtil.toString(map.get("topCode")));
        vo.setOrgCode(StringUtil.toString(map.get("orgCode")));
        vo.setOrgName(StringUtil.toString(map.get("orgName")));
        vo.setTeamCode(StringUtil.toString(map.get("teamCode")));
        vo.setTeamName(StringUtil.toString(map.get("teamName")));
        vo.setReferences(StringUtil.toString(map.get("referencesName")));
        vo.setJobNumber(StringUtil.toString(map.get("code")));
        if ("01".equals(StringUtil.toString(map.get("status")))) {
            vo.setStatus(EmployeeStatus.SERVING);
        } else {
            vo.setStatus(EmployeeStatus.LEAVING);
        }
        if (map.get("entryTime") != null) {
            vo.setEntryTime(((Timestamp) map.get("entryTime")).toLocalDateTime());
        }
        if (map.get("quitTime") != null) {
            vo.setQuitTime(((Timestamp) map.get("quitTime")).toLocalDateTime());
        }
        vo.setLicenseNo(StringUtil.toString(map.get("licenseNo")));
        Date licenseEndDate = (Date) map.get("licenseEndDate");
        Date licenseStartDate = (Date) map.get("licenseStartDate");
        vo.setLicenseEndDate(licenseEndDate == null ? null : licenseEndDate.toLocalDate());
        vo.setLicenseStartDate(licenseStartDate == null ? null : licenseStartDate.toLocalDate());
        IdType idType = getIdType(StringUtil.toString(map.get("idType")));
        vo.setIdType(idType == null ? null : idType.name());
        vo.setIdCode(StringUtil.toString(map.get("idCode")));
        if ("2".equals(map.get("gender"))) {
            vo.setGender(Gender.FEMALE);
        } else {
            vo.setGender(Gender.MALE);
        }
        if ("Y".equals(map.get("universalQualification"))) {
            vo.setUniversalQualification(true);
        } else {
            vo.setUniversalQualification(false);
        }
        vo.setMobile(StringUtil.toString(map.get("mobile")));
        return vo;
    }

    public static TreeNodeVO instToTreeNodeVo(BaseInst inst) {
        TreeNodeVO vo = new TreeNodeVO();
        vo.setId(inst.getInstId());
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setParentCode(inst.getParentInstCode());
        if ("Y".equals(inst.getCanSold())) {
            vo.setCanSold(true);
        } else {
            vo.setCanSold(false);
        }
        return vo;
    }

    public static SimpleTreeNodeVO instToSimpleTreeNodeVo(BaseInst inst) {
        SimpleTreeNodeVO vo = new SimpleTreeNodeVO();
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setParentCode(inst.getParentInstCode());
        return vo;
    }

    public static ChannelOrgVO instToChannelOrgVO(BaseInst inst, Tbepartner t) {
        ChannelOrgVO vo = new ChannelOrgVO();
        vo.setId(inst.getInstId());
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setChannelCode(t.getCompanycode());
        vo.setChannelName(t.getCompanyname());
        vo.setParentCode(inst.getParentInstCode());
        vo.setPhysicalStation(inst.getPhysicalStation());
        if ("Y".equals(inst.getCanSold())) {
            vo.setCanSold(true);
        } else {
            vo.setCanSold(false);
        }
        if ("1".equals(inst.getInstStatus())) {
            vo.setStatus(OrgStatus.ENABLED);
        } else {
            vo.setStatus(OrgStatus.DISABLED);
        }
        return vo;
    }

    public static PartnerOrgVO instToPartnerOrgVO(BaseInst inst, Tbepartner t) {
        PartnerOrgVO vo = new PartnerOrgVO();
        vo.setId(inst.getInstId());
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setPartnerCode(t.getCompanycode());
        vo.setPartnerName(t.getCompanyname());
        if ("1".equals(inst.getInstStatus())) {
            vo.setStatus(OrgStatus.ENABLED);
        } else {
            vo.setStatus(OrgStatus.DISABLED);
        }
        return vo;
    }

    public static ChannelType getChannelType(String type) {
        //渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理
        switch (type) {
            case "1":
                return ChannelType.INSURANCE_BROKER;
            case "2":
                return ChannelType.BANK_POSTAL;
            case "3":
                return ChannelType.OTHER;
            case "4":
                return ChannelType.PROFESSIONAL_AGENT;
            case "10":
                return ChannelType.DIRECT_SALES;
            default:
                return null;
        }
    }

    public static IdType getIdType(String idType) {
//        8 其它证件
//        H 台湾居民居住证
//        F 中国居民来往港澳台通行证
//        E 台湾居民来往大陆通行证
//        C 临时身份证
//        D 警官证
//        A 士兵证
//        I 外国人永久居留身份证
//        B 港澳居民来往大陆通行证
//        9 无证件
//        2 军人证（军官证）
//        3 驾照
//        4 户口本
//        G 港澳居民居住证
//        6 工作证
//        5 学生证
//        7 出生证
//        1 外国公民护照
        switch (idType) {
            case "0":
                return IdType.ID;
            case "1":
                return IdType.FOREIGNER_PASSPORT;
            case "2":
                return IdType.OFFICER_CERTIFICATE;
            case "3":
                return IdType.DRIVER_LICENSE;
            case "4":
                return IdType.HOUSEHOLD_REGISTER;
            case "5":
                return IdType.STUDENT_CARD;
            case "6":
                return IdType.EMPLOYEE_CARD;
            case "7":
                return IdType.BIRTH_CERTIFICATE;
            case "A":
                return IdType.SOLDIER_CERTIFICATE;
            case "B":
                return IdType.HK_MACAO_CN_PASS;
            case "C":
                return IdType.ID_TEMPORARY;
            case "D":
                return IdType.POLICE_CERTIFICATE;
            case "E":
                return IdType.TAIWAN_CN_PASS;
            case "F":
                return IdType.CH_HK_MACAO_TAIWAN_PASS;
            case "G":
                return IdType.ID_HK_MACAO;
            case "H":
                return IdType.ID_TAIWAN;
            case "I":
                return IdType.ID_FOREIGNER;
            default:
                return null;
        }
    }

    public static OrgVO instToOrgVO(BaseInst inst, AgentOrgType orgType) {
        OrgVO vo = new OrgVO();
        vo.setOrgType(orgType);
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setParentCode(inst.getParentInstCode());
        return vo;
    }

    public static TeamVO tbsaleteamToTeamVO(Tbsaleteam team, AgentOrgType orgType) {
        TeamVO vo = new TeamVO();
        vo.setOrgType(orgType);
        vo.setCode(team.getSaleteamcode());
        vo.setName(team.getSaleteamname());
        vo.setParentCode(team.getSupersaleteamcode());
        if ("03".equals(team.getTeamlevel())) {
            vo.setLevel(TeamLevel.AREA);
        } else if ("02".equals(team.getTeamlevel())) {
            vo.setLevel(TeamLevel.DEPT);
        } else {
            vo.setLevel(TeamLevel.TEAM);
        }
        return vo;
    }

    public static SaleTeamVO tbsaleteamToSaleTeamVO(Tbsaleteam team) {
        SaleTeamVO vo = new SaleTeamVO();
        if (StringUtils.isNotEmpty(team.getCptype())) {
            vo.setOrgType(AgentOrgType.get(team.getCptype()));
        }
        vo.setCode(team.getSaleteamcode());
        vo.setName(team.getSaleteamname());
        vo.setParentCode(team.getSupersaleteamcode());
        if ("03".equals(team.getTeamlevel())) {
            vo.setLevel(TeamLevel.AREA);
        } else if ("02".equals(team.getTeamlevel())) {
            vo.setLevel(TeamLevel.DEPT);
        } else {
            vo.setLevel(TeamLevel.TEAM);
        }
        vo.setTeamLevel(team.getTeamlevel());
        vo.setInstCode(team.getInstCode());
        vo.setInstName(team.getInstName());
        vo.setCompanyCode(team.getCompanycode());
        vo.setEmpInCode(team.getEmpincode());
        vo.setEmpInName(team.getEmpinname());
        vo.setTeamType(team.getSaleteamtype());
        return vo;
    }

    public static TeamVO channelTeamToTeamVO(ChannelTeam team, AgentOrgType orgType) {
        TeamVO vo = new TeamVO();
        vo.setOrgType(orgType);
        vo.setCode(team.getCode());
        vo.setName(team.getName());
        vo.setParentCode(team.getParentCode());
        if (TeamLevel.AREA.name().equals(team.getLevel())) {
            vo.setLevel(TeamLevel.AREA);
        } else if (TeamLevel.DEPT.name().equals(team.getLevel())) {
            vo.setLevel(TeamLevel.DEPT);
        } else {
            vo.setLevel(TeamLevel.TEAM);
        }
        return vo;
    }

    public static InsureOrgVO instToInsureOrgVO(BaseInst inst, AgentOrgType orgType, Tbepartner b) {
        InsureOrgVO vo = new InsureOrgVO();
        vo.setOrgType(orgType);
        vo.setOwnerCode(inst.getInstCode());
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setTopCode(b.getCompanycode());
        vo.setTopName(b.getCompanyname());
        vo.setManageOrgCode(inst.getManageorgcode());
        vo.setManageOrgName(inst.getManageorgname());
        vo.setCooperationStatus(inst.getCooperationStatus());
        //2021-12-10 分销商处理，必须展示终端机构  distributorName  投保流程使用这个展示终端分销机构
        if ("1".equals(inst.getDistributionchannelflag())) {
            vo.setDistributorName(inst.getTerminalchannelsname());
        }

        if ("1".equals(inst.getInstStatus())) {
            vo.setStatus(OrgStatus.ENABLED);
        } else {
            vo.setStatus(OrgStatus.DISABLED);
        }
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            vo.setOrgCodeInsure(vo.getCode());
        } else {
            vo.setOrgCodeInsure("C999999");
        }
        setChannel(vo, orgType, b.getChannelType());
        return vo;
    }

    public static InsureOrgMgrVO instToInsureOrgMgrVO(BaseInst inst, AgentOrgType orgType, Tbepartner b) {
        InsureOrgMgrVO vo = new InsureOrgMgrVO();
        vo.setOrgType(orgType);
        vo.setParentCode(inst.getParentInstCode());
        vo.setCode(inst.getInstCode());
        vo.setName(inst.getInstName());
        vo.setTopCode(b.getCompanycode());
        vo.setTopName(b.getCompanyname());
        vo.setManageOrgCode(inst.getManageorgcode());
        vo.setManageOrgName(inst.getManageorgname());

        if ("1".equals(inst.getInstStatus())) {
            vo.setStatus(OrgStatus.ENABLED);
        } else {
            vo.setStatus(OrgStatus.DISABLED);
        }
        return vo;
    }


    /**
     * 设置投保机构的一二三级渠道
     *
     * @param vo
     * @param channelType
     */
    private static void setChannel(InsureOrgVO vo, AgentOrgType orgType, String channelType) {
        if (AgentOrgType.CHANNEL.equals(orgType)) {
            switch (channelType) {
                case "2":
                    vo.setChannelOne("05");
                    vo.setChannelTwo("02");
                    vo.setChannelThree("457");

                    vo.setChannelOneOffline("04");
                    vo.setChannelTwoOffline("02");
                    vo.setChannelThreeOffline("452");
                    break;
                case "3":
                    vo.setChannelOne("05");
                    vo.setChannelTwo("03");
                    vo.setChannelThree("458");

                    vo.setChannelOneOffline("04");
                    vo.setChannelTwoOffline("03");
                    vo.setChannelThreeOffline("453");
                    break;
                case "4":
                    vo.setChannelOne("05");
                    vo.setChannelTwo("04");
                    vo.setChannelThree("459");

                    vo.setChannelOneOffline("04");
                    vo.setChannelTwoOffline("04");
                    vo.setChannelThreeOffline("454");
                    break;
                default:
                    //其他情况按“1”的逻辑去走
                    vo.setChannelOne("05");
                    vo.setChannelTwo("01");
                    vo.setChannelThree("456");

                    vo.setChannelOneOffline("04");
                    vo.setChannelTwoOffline("01");
                    vo.setChannelThreeOffline("451");
                    break;
            }
        } else {
            vo.setChannelOne("05");
            vo.setChannelTwo("05");
            vo.setChannelThree("455");

            vo.setChannelOneOffline("02");
            vo.setChannelTwoOffline("01");
            vo.setChannelThreeOffline("450");
        }
    }

    public static SimpleEmployeeVO channelEmployeeToSimpleEmployeeVO(ChannelEmployee o) {
        SimpleEmployeeVO vo = new SimpleEmployeeVO();
        vo.setCode(o.getCode());
        vo.setName(o.getName());
        if (EmployeeStatus.SERVING.name().equals(o.getStatus())) {
            vo.setStatus(EmployeeStatus.SERVING);
        } else {
            vo.setStatus(EmployeeStatus.LEAVING);
        }
        vo.setTeamName(o.getTeamName());
        vo.setOrgType(AgentOrgType.CHANNEL);
        return vo;
    }

    public static SimpleEmployeeVO mapToSimpleEmployeeVO(Map<String, Object> map) {
        SimpleEmployeeVO vo = new SimpleEmployeeVO();
        vo.setCode(StringUtil.toString(map.get("code")));
        vo.setName(StringUtil.toString(map.get("name")));
        vo.setTeamName(StringUtil.toString(map.get("teamName")));
        if ("01".equals(StringUtil.toString(map.get("status")))) {
            vo.setStatus(EmployeeStatus.SERVING);
        } else {
            vo.setStatus(EmployeeStatus.LEAVING);
        }
        vo.setOrgType(AgentOrgType.PARTNER);
        return vo;
    }

    public static MyEmployeeVO mapToMyEmployeeVO(Map<String, Object> map) {
        MyEmployeeVO vo = new MyEmployeeVO();
        vo.setCode(StringUtil.toString(map.get("code")));
        vo.setName(StringUtil.toString(map.get("name")));
        vo.setLicenseNo(StringUtil.toString(map.get("licenseNo")));
        if ("2".equals(map.get("gender"))) {
            vo.setGender(Gender.FEMALE);
        } else {
            vo.setGender(Gender.MALE);
        }
        vo.setMobile(StringUtil.toString(map.get("mobile")));
        if (!StringUtil.isBlank(vo.getMobile())) {
            vo.setMobile(PhoneUtil.hideBetween(vo.getMobile()).toString());
        }
        return vo;
    }
}
