package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.NodeLevel;
import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.PartnerVO;
import com.hqins.agent.org.model.vo.SimpleNodeVO;
import com.hqins.agent.org.model.vo.SimpleTreeNodeVO;
import com.hqins.agent.org.service.PartnerService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "合伙人管理")
@RestController
@RequestMapping("/partners")
@RefreshScope
@Slf4j
public class PartnerController {

    @Autowired
    private PartnerService partnerService;

    @ApiOperation("分页查询合伙人")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<PartnerVO>> listMy(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(partnerService.listMy(queryRequest));
    }

    /**
     * 获取合伙人简化版，只展示名称编码
     * 有管理权限的且生效状态的合伙人
     * 下拉框中使用
     */
    @ApiOperation("获取有管理权限的合伙人，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> listMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();

        return ApiResult.ok(partnerService.listMySimple(queryRequest));
    }

    /**
     * 获取合伙人简化版，只展示名称编码
     * 有管理权限的且生效状态的合伙人
     * 下拉框中使用
     */
    @ApiOperation("获取有管理权限的合伙人，督导账号H5使用")
    @GetMapping("/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> frontListMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();

        RequestContextHolder.setAdminAppIdLocal(1L);
        RequestContextHolder.setStaffIdLocal(1L);
        return ApiResult.ok(partnerService.listMySimple(queryRequest));
    }

    @ApiOperation("获取合伙人，简化版下拉框里使用,线索后台使用")
    @GetMapping("/clue/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> clueListMySimple(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder().code(code).name(name).current(Math.max(current, 1L)).size(size).build();

        return ApiResult.ok(partnerService.clueListMySimple(queryRequest));
    }


    /**
     * 获取合伙人简化版，只展示名称编码
     * 下拉框中使用
     */
    @ApiOperation("获取所有的合伙人，简化版下拉框里使用")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> listAllSimple(
            @ApiParam("合伙人代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("合伙人名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();

        return ApiResult.ok(partnerService.listAllSimple(queryRequest));
    }

    /**
     * 获取有效的组织机构树,包含合伙人、合伙人销售机构、销售团队
     * 数据授权时使用
     */
    @ApiOperation("获取有效的组织机构树,包含合伙人、合伙人销售机构、销售团队")
    @GetMapping("/tree")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleTreeNodeVO>> getAllTree(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("要查到哪一级：TOP-合伙人；ORG-机构, 默认为ORG") @RequestParam(value = "nodeLevel", required = false, defaultValue = "ORG") NodeLevel nodeLevel) {
        log.info("[getAllTree] enter controller ...");
        OrgTreeRequest treeRequest = OrgTreeRequest.builder()
                .topCode(partnerCode).orgCode(orgCode).nodeLevel(nodeLevel).build();

        return ApiResult.ok(partnerService.getAllTree(treeRequest));
    }
}
