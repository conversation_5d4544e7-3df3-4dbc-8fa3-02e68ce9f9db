package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.OrgService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "销售机构-feign专用")
@RestController
@RequestMapping("/orgs")
@Slf4j
public class OrgController {

    @Autowired
    private OrgService orgService;

    /**
     * 根据机构代码查其上的所属公司（Tbepartner）信息
     *
     * @param orgCode 机构代码
     * @return Tbepartner
     */
    @ApiOperation("根据机构代码查其上的所属公司（Tbepartner）信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/getTbepartnerByOrgCode")
    public ApiResult<TbepartnerVO> getTbepartnerByOrgCode(@ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {
        return ApiResult.ok(orgService.getTbepartnerByOrgCode(orgCode));
    }

    /**
     * 根据合伙人或渠道商代码查其下的所有顶级机构
     *
     * @param orgType 机构类型
     * @param topCode 合伙人或渠道商代码
     * @return 其下所有的顶级机构列表
     */
    @GetMapping("/top")
    @ApiOperation("根据合伙人或渠道商代码查其下的所有顶级机构")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<OrgVO>> getOrgsByTop(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                               @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", required = false, defaultValue = "false") boolean levels,
                                               @ApiParam("合伙人或渠道商代码") @RequestParam(value = "topCode") String topCode) {

        return ApiResult.ok(orgService.getOrgsByTop(orgType, topCode, levels));
    }

    /**
     * 根据机构代码查其下的所有机构
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有的顶级机构列表
     */
    @ApiOperation("根据机构代码查其下的所有机构（有BUG）")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping
    public ApiResult<List<OrgVO>> getOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                          @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", required = false, defaultValue = "false") boolean levels,
                                          @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {

        return ApiResult.ok(orgService.getOrgs(orgType, orgCode, levels));
    }

    /**
     * 根据机构代码查其上的所有父级机构
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有的顶级机构列表
     */
    @ApiOperation("根据机构代码查其上的所有父级机构")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/parents")
    public ApiResult<List<OrgVO>> getParentsOrgs(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                          @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", required = false, defaultValue = "false") boolean levels,
                                          @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {

        return ApiResult.ok(orgService.getParentsOrgs(orgType, orgCode, levels));
    }

    /**
     * 根据机构代码查其下的所有团队
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 其下所有团队
     */
    @ApiOperation("根据机构代码查其下的所有团队")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/teams/org")
    public ApiResult<List<TeamVO>> getTeamsByOrg(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                                 @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", required = false, defaultValue = "false") boolean levels,
                                                 @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {

        return ApiResult.ok(orgService.getTeamsByOrg(orgType, orgCode, levels));
    }

    /**
     * 根据团队代码查其下的所有下级团队
     *
     * @param orgType  机构类型
     * @param teamCode 团队代码
     * @return 其下所有下级团队
     */
    @ApiOperation("根据团队代码查其下的所有下级团队")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/teams")
    public ApiResult<List<TeamVO>> getTeams(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                            @ApiParam("多层标记 true-返回所有子层 false-只返回一层数据") @RequestParam(value = "levels", required = false, defaultValue = "false") boolean levels,
                                            @ApiParam("上级团队代码") @RequestParam(value = "teamCode") String teamCode) {

        return ApiResult.ok(orgService.getTeams(orgType, teamCode, levels));
    }

    /**
     * 根据机构代码查询机构的经销商
     *
     * @param orgCode 机构代码
     */
    @ApiOperation("根据机构代码查询机构的经销商")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/dealer")
    public ApiResult<DealerVO> getDealer(@ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {

        return ApiResult.ok(orgService.getDealer(orgCode));
    }


    /**
     * 查询所有顶级机构
     */
    @ApiOperation("查询所有顶级机构")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/topOrg")
    public ApiResult<List<OrgVO>> topOrg() {
        return ApiResult.ok(orgService.topOrg());
    }

    /**
     * 根据机构代码查公司信息
     *
     * @param orgType 机构类型
     * @param orgCode 机构代码
     * @return 公司信息
     */
    @ApiOperation("根据机构代码查公司信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/company")
    public ApiResult<CompanyVO> getCompany(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                           @ApiParam("机构代码") @RequestParam(value = "orgCode") String orgCode) {
        return ApiResult.ok(orgService.getCompany(orgType, orgCode));
    }

    /**
     * 根据机构代码列表，批量查公司信息
     *
     * @param orgType 机构类型
     * @param orgCodes 机构代码列表
     * @return 公司信息
     */
    @ApiOperation("根据机构代码列表，批量查公司信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/companys")
    public ApiResult<List<CompanyVO>> getCompanys(@ApiParam("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商") @RequestParam(value = "orgType") AgentOrgType orgType,
                                           @ApiParam("机构代码列表") @RequestParam(value = "orgCodes") String[] orgCodes) {
        return ApiResult.ok(orgService.getCompanyList(orgType, orgCodes));
    }

    /**
     * 根据机构代码，查机构信息
     */
    @ApiOperation("查机构信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/inst")
    public ApiResult<InstResultVo> queryInst(@ApiParam("机构编码orgCode") @RequestParam(value = "orgCode") String orgCode) {
        return ApiResult.ok(orgService.queryInstByOrgCode(orgCode));
    }

    @ApiOperation("根据合伙人二级机构编码，查询所有网点信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/companyInst")
    public ApiResult<List<PartassignmanagerVO>> queryCompanyInst(@ApiParam("合伙人二级机构编码") @RequestParam(value = "companyInstCode") String companyInstCode) {
        return ApiResult.ok(orgService.queryCompanyInst(companyInstCode));
    }
    /**
     * 查所有网点的地址信息、机构信息
     */
    @ApiOperation("查所有网点的地址信息、机构信息")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping("/queryAll")
    public ApiResult<List<QueryAllVO>> queryAll() {
        return ApiResult.ok(orgService.queryAll());
    }
}
