package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.AccountQueryRequest;
import com.hqins.agent.org.model.vo.AccountVO;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;

/**
 * <AUTHOR>
 * @date 2021/5/25
 * @Description
 */
public interface AccountService {

    PageInfo<AccountVO> listMy(AccountQueryRequest queryRequest);

    void blocked(AgentOrgType orgType,String employeeCode,long agentId);

    void unblocked(AgentOrgType orgType,String employeeCode,long agentId);

    void unbindWx(AgentOrgType orgType,String employeeCode,long agentId);

    void resetPwd(AgentOrgType orgType,String employeeCode,long agentId);

    void synchronizationUm(AgentOrgType orgType, String employeeCode);
}
