package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/25
 * @Description
 */
public interface SensorsService {

    void addEmployee(ChannelEmployee employee, String umId);

    void addEmployee(List<Tbemp> tbempList);

    void updateEmployee(String employeeCode, String umId, String name, String gender, LocalDate birthday, String mobile);

    void updateEmployeeList(List<ChannelEmployee> employeeList);

    void addSupervisorEmployee(SupervisorEmployee supervisorEmployee, String umId);
}
