package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface ChannelService {

    PageInfo<ChannelVO> listMy(TopQueryRequest queryRequest);

    PageInfo<SimpleNodeVO> listMySimple(TopQueryRequest queryRequest);

    PageInfo<SimpleNodeVO> listAllSimple(TopQueryRequest queryRequest);

    List<SimpleTreeNodeVO> getAllTree(OrgTreeRequest treeRequest);

    void configLogo(ChannelConfigLogoRequest request);

    String getLogo(String employeeCode);

    List<OrgVO> findChannelTypeInsts(String channelType);

    Collection<SimpleChannelOrgVO> list1st5thOrgs(String teamCode);

    List<ChannelOrgVO> listSubEmpChannel(String teamLeaderCode, boolean includeLeader);

    Boolean ifpTeam(String teamCode);
}
