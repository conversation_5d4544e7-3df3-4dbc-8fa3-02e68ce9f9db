package com.hqins.agent.org.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.dao.TbSaleTeamDao;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.request.TeamHonorRequest;
import com.hqins.agent.org.model.vo.EmpHonorVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.HonorVO;
import com.hqins.agent.org.service.TeamService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeamServiceImpl implements TeamService {
    @Autowired
    private TbSaleTeamDao teamDao;

    @Autowired
    SupervisorEmployeeMapper supervisorEmployeeMapper;

    @Override
    public List<EmployeeVO> getBirthDayEmpList(String saleTeamCode, Date startDate, Date endDate) throws Exception {
        if (startDate.compareTo(endDate)>0){
            throw new Exception("开始日期不能大于结束日期");
        }
        List<EmployeeVO> employeeVOList = new ArrayList<>();
        log.info("查询生日信息:{},{},{}", saleTeamCode, startDate, endDate);
        List<EmployeeVO> birthDayEmpList = teamDao.getBirthDayEmpList(saleTeamCode, startDate, endDate);
        if (CollectionUtils.isEmpty(birthDayEmpList)){
            log.info("查询生日信息结果为空");
            return new ArrayList<>();
        }
        birthDayEmpList.forEach(o->{
            if("PARTY".equals(o.getCpType())){
                o.setOrgType(AgentOrgType.PARTNER);
            }else if ("CHANNEL".equals(o.getCpType())){
                o.setOrgType(AgentOrgType.CHANNEL);
            }
        });
        return birthDayEmpList;
    }

    @Override
    public EmployeeVO getEmpInfo(String employeeCode) {
        log.info("查询员工信息:{}", employeeCode);
        EmployeeVO employeeInfo = teamDao.getEmployeeInfo(employeeCode);
        if (employeeInfo == null){
            log.info("查询员工信息结果为空:{}", employeeCode);
            EmployeeVO employeeVO = new EmployeeVO();
            employeeVO.setName("未查到人员信息");
            employeeVO.setRank("未查到人员信息");
            employeeVO.setRankName("未查到人员信息");
            employeeVO.setRankSeqCode("未查到人员信息");
            employeeVO.setRankSeqName("未查到人员信息");
            if (!StringUtils.isEmpty(employeeCode) && employeeCode.startsWith("S")){
                SupervisorEmployee employee = supervisorEmployeeMapper.selectOne(new LambdaQueryWrapper<SupervisorEmployee>()
                        .eq(SupervisorEmployee::getEmployeeCode, employeeCode)
                        .last("limit 1") );
                if (employee != null){
                    employeeVO.setName(employee.getName());
                    employeeVO.setRankName(employee.getPosition());
                }else {
                    employeeVO.setRankName("督导账号");
                }
                employeeVO.setRank("督导账号");
                employeeVO.setRankSeqCode("督导账号");
                employeeVO.setRankSeqName("督导账号");
            }
            return employeeVO;
        }
        if (StringUtils.isEmpty(employeeInfo.getRankName())){
            if (StringUtils.isEmpty(employeeInfo.getRank())){
                employeeInfo.setRankName("未查到职级信息");
            }else{
                employeeInfo.setRankName("未查到职级名称信息");
            }
        }
        return employeeInfo;
    }


}
