package com.hqins.agent.org.rpc.client;

import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.insurance.goods.api.GoodsBlackListApi;
import com.hqins.insurance.goods.model.request.admin.GoodsBlackEmployeesAddRequest;
import com.hqins.insurance.goods.model.request.admin.GoodsBlackEmployeesDeleteRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
@Slf4j
public class GoodsClient {

    private final GoodsBlackListApi goodsBlackListApi;

    public GoodsClient(GoodsBlackListApi goodsBlackListApi) {
        this.goodsBlackListApi = goodsBlackListApi;
    }

    /**
     * 增加黑名单
     */
    public void addEmployees(String employeeCode, AgentOrgType orgType) {
        try{
            log.info("B端增加黑名单接口,addEmployees,入参,employeeCode:{},orgType:{}", employeeCode,orgType);
            GoodsBlackEmployeesAddRequest request = new GoodsBlackEmployeesAddRequest();
            request.setOrgType(orgType);
            request.setEmployeeCodeList(Arrays.asList(employeeCode));
            request.setGoodsCode("ALL");
            Boolean result = goodsBlackListApi.addEmployees(request);
            log.info("B端增加黑名单接口,addEmployees,返回,employeeCode:{},orgType:{},result:{}", employeeCode,orgType,result);
        }catch (Exception e){
            log.error("addEmployees_error",e);
        }
    }

    /**
     * 删除黑名单
     */
    public void deleteEmployees(String employeeCode, AgentOrgType orgType) {
        try{
            log.info("B端删除黑名单接口,addEmployees,入参,employeeCode:{},orgType:{}", employeeCode,orgType);
            GoodsBlackEmployeesDeleteRequest request = new GoodsBlackEmployeesDeleteRequest();
            request.setOrgType(orgType);
            request.setEmployeeCodeList(Arrays.asList(employeeCode));
            request.setGoodsCode("ALL");
            goodsBlackListApi.deleteEmployees(request);
            log.info("B端删除黑名单接口,addEmployees,返回,employeeCode:{},orgType:{}", employeeCode,orgType);
        }catch (Exception e){
            log.error("deleteEmployees_error",e);
        }
    }
}
