package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.TeamPerformanceRequest;
import com.hqins.agent.org.model.vo.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @Description 个人和团队收入和业绩
 */
public interface PerformanceService {


    PersonalVO getCurrentMonthIncomeInfo(String empCode);

    List<PersonalIncomeVO> getPreSettleIncomeInfo();

    List<CommissionItemVO> getCommissionItemList();

    List<CommissionItemVO> getCommissionTypeList();

    List<PersonalIncomeVO> getSettleIncomeTrendInfo();

    PersonalIncomeVO getSettleIncomeInfo(String settleMonth);

    List<PerformancePolicyVO> getSettlePolicyInfo(String commissionItem, String settleMonth, String queryType);

    PersonalVO getCurrentMonthPerformanceInfo(String empCode);

    PerformanceVO getPerformanceFycMonthInfo(String empCode, String performanceMonth);

    PerformanceVO getPerformanceRycMonthInfo(String empCode, String performanceMonth);

    PerformanceVO getPerformanceCr13Info(String empCode, String performanceMonth);

    List<PerformancePolicyVO> getPerformancePolicyInfo(String queryType, String performanceMonth);

    TeamManageVO getTeamCurrentPersonList(String saleTeamCode);
    TeamManageVO getTeamCurrentPersonQuitList(String saleTeamCode);

    TeamManageVO getCurrentTeamPerformance(String saleTeamCode);

    List<PerformanceTrendVO> getTeamTrendPerformanceTrend(String saleTeamCode, String type);

    TeamVersionVO  getTeamRangePerformance(String saleTeamCode, String type, String performanceMonth, String paymentYears, Date startDate, Date endDate);

    PerformancePolicyVO getTeamRangePerformancePolicyList(TeamPerformanceRequest request);

    List<String> getPerformancePolicyNoList(String empCode, String queryType, String performanceMonth);

    List<PerformancePolicyVO>  getPerformancePolicyList(String empCode, String qureyString);

    PolicyCommissionVO getPolicyCommissionList(String policyNo);

    PerformancePolicyVO queryTeamRangePerformancePolicyList(TeamPerformanceRequest request);

    PerformanceVO getPersonalPerformance(String employeeCode,String loginEmpCode, String queryYear, String queryMonth) ;

    PerformanceVO getTeamTargetPerformance(String saleTeamCode,String loginEmpCode, String queryYear, String queryMonth) throws ExecutionException, InterruptedException;

    List<String> getBasicLawVersionTypeList(String employeeCode);

    String getBasicLawVersionType(String employeeCode, String teamCode, String commissionMonth);

    List<String> getSupervisorOrgList(String loginEmpCode);

    List<SimpleNodeVO> getSelfPolicyList();


    List<MarkDetailVO> getSupervisorCrList(List<String> instCodeList, String teamCode, String commissionMonth);

    List<SupervisorPerformanceDetailNumberVO> getSupervisorIncreaseVOList(Map map);
}
