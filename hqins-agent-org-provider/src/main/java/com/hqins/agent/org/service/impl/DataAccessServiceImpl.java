package com.hqins.agent.org.service.impl;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.admin.cas.api.RoleApi;
import com.hqins.admin.cas.model.vo.RoleIdsVO;
import com.hqins.agent.org.dao.entity.org.DataAccess;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.DataAccessMapper;
import com.hqins.agent.org.model.request.DataAccessSaveRequest;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.web.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
@Service
@Slf4j
public class DataAccessServiceImpl implements DataAccessService {

    private final RoleApi roleApi;
    private final BaseInstMapper baseInstMapper;
    private final DataAccessMapper dataAccessMapper;

    public DataAccessServiceImpl(DataAccessMapper dataAccessMapper, BaseInstMapper baseInstMapper, RoleApi roleApi) {
        this.roleApi = roleApi;
        this.baseInstMapper = baseInstMapper;
        this.dataAccessMapper = dataAccessMapper;
    }


    @CreateCache(name = "DataAccess:role", cacheType = CacheType.REMOTE)
    private Cache<Long, MyDataAccessVO> dataAccessCache;

    /**
     * 更新角色数据权限
     * TODO 这个接口是只有超管才能调用的，因此，输入合法性校验暂时可以不做，后续这个地方的输入校验，从中央缓存取数据进行校验，性能就非常好了
     *
     * @param request
     */

    @Override
    public void updateDataAccess(DataAccessSaveRequest request) {
        Long staffId = RequestContextHolder.getStaffId();
        String staffName = RequestContextHolder.getStaffUsername();
        updateDataAccessAsync(request,staffId,staffName);
    }

    @Async("executor")
    @Transactional(rollbackFor = Exception.class)
    void updateDataAccessAsync(DataAccessSaveRequest request,Long staffId,String staffName){
        log.info("updateDataAccess,start,staffId:{},staffName:{}",staffId,staffName);
        RequestContextHolder.setStaffIdLocal(staffId);
        RequestContextHolder.setStaffUsernameLocal(staffName);
        // 删除该角色所有授权记录
        long startTime = System.currentTimeMillis();
        dataAccessMapper.delete(new LambdaQueryWrapper<DataAccess>().eq(DataAccess::getRoleId, request.getRoleId()));
        log.info("updateDataAccess delete.time:{}ms", System.currentTimeMillis() - startTime);

        // 重新授权
        List<DataAccess> dataAccesses = new LinkedList<>();
        request.getPartners().forEach(dav -> dataAccesses.add(new DataAccess(request.getRoleId(), dav.getCode(), dav.getDataType().name())));
        request.getChannels().forEach(dav -> dataAccesses.add(new DataAccess(request.getRoleId(), dav.getCode(), dav.getDataType().name())));

        if (!dataAccesses.isEmpty()) {
            dataAccessMapper.insertBatchSomeColumn(dataAccesses);
            log.info("updateDataAccess insertBatchSomeColumn.time:{}ms", System.currentTimeMillis() - startTime);
        }

        dataAccessCache.remove(request.getRoleId());
        log.info("updateDataAccess,end,耗时:{} MS", System.currentTimeMillis() - startTime);
    }

    @Override
    public MyDataAccessVO getMyDataAccessByAppIdStaffId(Long adminAppId, Long staffId) {
        //跨服务抓取roleIds;
        log.debug("appId:{} ,staffId:{}", adminAppId, staffId);
        RoleIdsVO roleIdsVO = roleApi.getRoleIds(adminAppId, staffId);
        log.debug("RoleIdsVO++++++:{}", JsonUtil.toJSON(roleIdsVO));
        if (Boolean.TRUE.equals(roleIdsVO.getContainsSuperAdmin())) {
            MyDataAccessVO myDataAccessVO = new MyDataAccessVO();
            myDataAccessVO.setContainsSuperAdmin(true);
            log.debug("MyDataAccessVO++++++:{}", JsonUtil.toJSON(myDataAccessVO));
            return myDataAccessVO;
        }
        MyDataAccessVO vo = getMyDataAccessByRoleIds(roleIdsVO.getRoleIds());
        log.debug("MyDataAccessVO++++++:{}", JsonUtil.toJSON(vo));
        return vo;
    }

    /**
     * 获取当前登录人，所有有权限的机构编码
     *
     * @return
     */
    @Override
    public MyDataAccessVO getMyDataAccess() {
        return getMyDataAccessByAppIdStaffId(RequestContextHolder.getAdminAppId(), RequestContextHolder.getStaffId());
    }

    public MyDataAccessVO getMyDataAccess(Long adminAppId,Long staffId) {
        return getMyDataAccessByAppIdStaffId(adminAppId, staffId);
    }

    private MyDataAccessVO getMyDataAccessByRoleIds(List<Long> roleIds) {
        MyDataAccessVO vo = null;
        for (Long id : roleIds) {
            if (vo == null) {
                vo = getDataAccessByRoleId(id);
            } else {
                MyDataAccessVO tmp = getDataAccessByRoleId(id);
                vo.getPartnerCodes().addAll(tmp.getPartnerCodes());
                vo.getPartnerOrgCodes().addAll(tmp.getPartnerOrgCodes());
                vo.getChannelCodes().addAll(tmp.getChannelCodes());
                vo.getChannelOrgCodes().addAll(tmp.getChannelOrgCodes());
                vo.getSelectChannelCodes().addAll(tmp.getSelectChannelCodes());
                vo.getSelectPartnerCodes().addAll(tmp.getSelectPartnerCodes());
            }
        }
        if (vo == null) {
            vo = new MyDataAccessVO();
        }
        //防止sql中使用in表达式时出问题。
        if (vo.getPartnerCodes().isEmpty()) {
            vo.getPartnerCodes().add("none1");
        }
        if (vo.getPartnerOrgCodes().isEmpty()) {
            vo.getPartnerOrgCodes().add("none1");
        }
        if (vo.getChannelCodes().isEmpty()) {
            vo.getChannelCodes().add("none1");
        }
        if (vo.getChannelOrgCodes().isEmpty()) {
            vo.getChannelOrgCodes().add("none1");
        }
        if (vo.getSelectPartnerCodes().isEmpty()) {
            vo.getSelectPartnerCodes().add("none1");
        }
        if (vo.getSelectChannelCodes().isEmpty()) {
            vo.getSelectChannelCodes().add("none1");
        }
        return vo;
    }

    /**
     * 根据角色id获取数据权限
     * 封装此方法是为了方便添加缓存
     *
     * @param roleId
     * @return
     */
    @Override
    public MyDataAccessVO getDataAccessByRoleId(Long roleId) {
        MyDataAccessVO myDataAccessVO = dataAccessCache.get(roleId);
        if (myDataAccessVO != null) {
            return myDataAccessVO;
        }
        List<DataAccess> dataAccesses = dataAccessMapper.selectList(new LambdaQueryWrapper<DataAccess>()
                .in(DataAccess::getRoleId, roleId));
        Set<String> partnerCodes = new HashSet<>();
        Set<String> partnerOrgCodes = new HashSet<>();
        Set<String> channelCodes = new HashSet<>();
        Set<String> channelOrgCodes = new HashSet<>();
        Set<String> selectPartnerCodes = new HashSet<>();
        Set<String> selectChannelCodes = new HashSet<>();
        dataAccesses.forEach(da -> {
            //授权资源的类型 PARTNER:合伙人、CHANNEL:渠道商、PARTNER_ORG:合伙人销售机构、CHANNEL_ORG:渠道商销售机构
            switch (da.getDataType()) {
                case "PARTNER":
                    partnerCodes.add(da.getDataCode());
                    break;
                case "PARTNER_ORG":
                    partnerOrgCodes.add(da.getDataCode());
                    break;
                case "CHANNEL":
                    channelCodes.add(da.getDataCode());
                    break;
                case "CHANNEL_ORG":
                    channelOrgCodes.add(da.getDataCode());
                    break;
                default:
                    break;
            }
        });

        //把授权的合伙人和渠道商下的所有销售机构和直接授权的销售机构做数据并集
        if (!partnerCodes.isEmpty()) {
            List<Map<String, Object>> partnerOrgMaps = baseInstMapper.listByTopCodes(partnerCodes);
            for (Map m : partnerOrgMaps) {
                partnerOrgCodes.add(m.get("code").toString());
            }
            selectPartnerCodes.addAll(partnerCodes);
        }
        if (!channelCodes.isEmpty()) {
            List<Map<String, Object>> channelOrgMaps = baseInstMapper.listByTopCodes(channelCodes);
            for (Map m : channelOrgMaps) {
                channelOrgCodes.add(m.get("code").toString());
            }
            selectChannelCodes.addAll(channelCodes);
        }
        //获取下拉框专用合伙人渠道商编码数据
        if (!partnerOrgCodes.isEmpty()) {
            List<Map<String, Object>> partnerMaps = baseInstMapper.listByOrgCodes(partnerOrgCodes);
            for (Map m : partnerMaps) {
                selectPartnerCodes.add(m.get("topCode").toString());
            }
        }
        if (!channelOrgCodes.isEmpty()) {
            List<Map<String, Object>> channelMaps = baseInstMapper.listByOrgCodes(channelOrgCodes);
            for (Map m : channelMaps) {
                selectChannelCodes.add(m.get("topCode").toString());
            }
        }

        myDataAccessVO = MyDataAccessVO.builder().partnerCodes(partnerCodes).partnerOrgCodes(partnerOrgCodes)
                .channelCodes(channelCodes).channelOrgCodes(channelOrgCodes).containsSuperAdmin(false)
                .selectChannelCodes(selectChannelCodes).selectPartnerCodes(selectPartnerCodes).build();
        dataAccessCache.put(roleId, myDataAccessVO);
        return myDataAccessVO;
    }
}
