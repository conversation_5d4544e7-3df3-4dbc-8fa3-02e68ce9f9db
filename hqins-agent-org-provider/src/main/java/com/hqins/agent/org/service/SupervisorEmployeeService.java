package com.hqins.agent.org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @Date 2023/4/3 17:25
 */
public interface SupervisorEmployeeService extends IService<SupervisorEmployee> {

    PageInfo<SupervisorEmployeeVO> listMy(SupervisorEmployeeRequest supervisorEmployeeRequest);

    List<SupervisorEmployeeVO> selectSupervisorEmployeeList(SupervisorEmployeeRequest supervisorEmployeeRequest);

    List<SupervisorEmployeeVO> selectSupervisorEmployeeListPost(SelectSupervisorEmployeeListPostRequest supervisorEmployeeRequest);

    /**
     * 查询督导账号
     */
    EmployeeVO getSupervisorEmployee(String employeeCode);

    EmployeeVO getSupervisorEmployeeByMobile(String mobile);

    void checkAndSave(SupervisorEmployeeAddRequest request);

    void leave(Long id);

    void reentry(Long id);

    void updateInformation(SupervisorEmployeeUpdateRequest request);

    void conversions(SupervisorEmployeeOrgUpdateRequest request);

    PageInfo<AccountVO> listMyAccountVO(PartnerEmployeeRequest queryRequest);

    Map<String, List<String>> getCodeList();

    List<SimpleNodeVO> getTop();

    List<TreeNodeVO> getOrg(OrgQueryRequest queryRequest);

    void updateCode(String type);
}
