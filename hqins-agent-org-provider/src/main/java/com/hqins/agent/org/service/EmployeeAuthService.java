package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.request.OuterOrgRequest;
import com.hqins.agent.org.model.vo.AuthEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.SimpleEmployeeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/525
 * @Description
 */
public interface EmployeeAuthService {

    /**
     * 根据授权ID 查询销售员授权信息
     *
     * @param authId 授权ID
     * @return 销售员信息
     */
    AuthEmployeeVO getEmployeeByAuthId(String authId);

    /**
     * 根据执业证号 查询销售员授权信息
     *
     * @param licenseNo 执业证号
     * @return 销售员信息
     */
    AuthEmployeeVO getEmployeeByLicenseNo(String licenseNo);

    List<EmployeeVO> getEmployeesByOrgCode(OuterOrgRequest request);
}
