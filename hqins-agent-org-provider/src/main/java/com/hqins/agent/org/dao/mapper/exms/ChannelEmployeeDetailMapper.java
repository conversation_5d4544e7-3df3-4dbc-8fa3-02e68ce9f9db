package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.ChannelEmployeeDetail;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 渠道商代理人查询结果明细数据访问接口
 * <AUTHOR>
 * @since 2023-09-18
 */
@Mapper
@DS("exms")
@Repository
public interface ChannelEmployeeDetailMapper extends BaseMapper<ChannelEmployeeDetail> {

    Long insertSelective(ChannelEmployeeDetail channelEmployeeDetail);

    int updateByPrimaryKeySelective(ChannelEmployeeDetail channelEmployeeDetail);


}

