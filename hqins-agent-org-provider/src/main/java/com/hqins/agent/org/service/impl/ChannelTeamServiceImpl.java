package com.hqins.agent.org.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheLoader;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.ChannelEmployeeDao;
import com.hqins.agent.org.dao.ChannelTeamDao;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.dao.mapper.org.ChannelTeamMapper;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.request.ChannelTeamAddRequest;
import com.hqins.agent.org.model.request.ChannelTeamQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.*;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.common.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class ChannelTeamServiceImpl implements ChannelTeamService {

    @Autowired
    private ChannelTeamMapper channelTeamMapper;
    @Autowired
    private ChannelOrgService channelOrgService;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ChannelTeamDao channelTeamDao;
    @Autowired
    private ChannelEmployeeDao channelEmployeeDao;
    @Autowired
    private ChannelEmployeeService channelEmployeeService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private CacheLoader cacheLoader;

    @Override
    public PageInfo<ChannelTeamTreeNodeVO> listMyTree(ChannelTeamQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode()) || !StringUtil.isBlank(queryRequest.getOrgName())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getChannelOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getChannelOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }


        LambdaQueryWrapper<ChannelTeam> w = new LambdaQueryWrapper<ChannelTeam>()
                .orderByDesc(ChannelTeam::getCode);
        if (null != queryRequest.getLevel()) {
            w.eq(ChannelTeam::getLevel, queryRequest.getLevel().name());
        }
        if (StringUtil.isNotEmpty(queryRequest.getTeamLevel())) {
            List<String> teamLevels = Arrays.asList(queryRequest.getTeamLevel().split(","));
            w.in(ChannelTeam::getLevel, teamLevels);
        }
        if (!CollectionUtils.isEmpty(orgCodes)) {
            w.in(ChannelTeam::getOrgCode, orgCodes);
        }
        if (!StringUtil.isBlank(queryRequest.getChannelCode())) {
            w.eq(ChannelTeam::getChannelCode, queryRequest.getChannelCode());
        }
        if (!StringUtil.isBlank(queryRequest.getChannelName())) {
            w.like(ChannelTeam::getChannelName, "%" + queryRequest.getChannelName() + "%");
        }
        if (!StringUtil.isBlank(queryRequest.getOrgCode())) {
            w.eq(ChannelTeam::getOrgCode, queryRequest.getOrgCode());
        }
        if (!StringUtil.isBlank(queryRequest.getOrgName())) {
            w.like(ChannelTeam::getOrgName, "%" + queryRequest.getOrgName() + "%");
        }
        if (!StringUtil.isBlank(queryRequest.getCode())) {
            w.eq(ChannelTeam::getCode, queryRequest.getCode());
        }
        if (!StringUtil.isBlank(queryRequest.getName())) {
            w.like(ChannelTeam::getName, "%" + queryRequest.getName() + "%");
        }
        if (!StringUtil.isBlank(queryRequest.getStatus())) {
            w.eq(ChannelTeam::getStatus, queryRequest.getStatus());
        }

        Page<ChannelTeam> p = channelTeamMapper.selectPage(new Page<>(queryRequest.getCurrent(), queryRequest.getSize()), w);
        //转换数据结构
//        PageInfo<ChannelTeamTreeNodeVO> result = PageUtil.convert(p, channelTeam -> BeanCopier.copyObject(channelTeam, ChannelTeamTreeNodeVO.class));

        PageInfo<ChannelTeamTreeNodeVO> result = PageUtil.convert(p, channelTeam -> convert(channelTeam));

        if (result.getRecords() == null || result.getRecords().isEmpty()) {
            return result;
        }
        for (ChannelTeamTreeNodeVO vo : result.getRecords()) {
            if (TeamLevel.TEAM.equals(vo.getLevel())) {
                continue;
            }
            vo.setChildren(getChannelTeamTreeNodeVO(vo.getCode(),queryRequest.getStatus()));
        }
        return result;
    }

    /**
     * 数据类型转换
     * 重新赋值操作
     *
     * @param channelTeam
     * @return
     */
    private static ChannelTeamTreeNodeVO convert(ChannelTeam channelTeam) {
        ChannelTeamTreeNodeVO vo = BeanCopier.copyObject(channelTeam, ChannelTeamTreeNodeVO.class);
        vo.setTopCode(channelTeam.getChannelCode());
        return vo;
    }

    @Override
    public PageInfo<SimpleTeamTreeNodeVO> listMyTreeSimple(ChannelTeamQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode()) || !StringUtil.isBlank(queryRequest.getOrgName())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getChannelOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getChannelOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }

        List<ChannelTeam> list = new ArrayList<>();
        for (ChannelTeam t : cacheService.getAllChannelTeams()) {
            if ("ENABLED".equals(queryRequest.getStatus()) && !TeamStatus.ENABLED.name().equals(t.getStatus())) {
                continue;
            }
            if (orgCodes != null && !orgCodes.isEmpty() && !orgCodes.contains(t.getOrgCode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getCode()) && !queryRequest.getCode().equals(t.getCode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getName()) && !t.getName().contains(queryRequest.getName())) {
                continue;
            }
            list.add(t);
        }
        Page<ChannelTeam> p = PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());

        //转换数据结构
        PageInfo<SimpleTeamTreeNodeVO> result = PageUtil.convert(p, channelTeam -> BeanCopier.copyObject(channelTeam, SimpleTeamTreeNodeVO.class));
        if (result.getRecords() == null || result.getRecords().isEmpty()) {
            return result;
        }
        result.setRecords(TreeUtils.buildTrees(result.getRecords()));
        return result;
    }

    @Override
    @DS("org")
    @DSTransactional
    public void save(ChannelTeamAddRequest request, boolean auth) {
        ChannelOrgVO vo = channelOrgService.getChannelOrgVOByOrgCode(request.getOrgCode());
        AssertUtil.notNull(vo, new ApiException("机构编码错误"));
        //检查当前用户有没有操作该机构的权限
        if (auth && Boolean.FALSE.equals(dataAccessService.getMyDataAccess().getContainsSuperAdmin())) {
            AssertUtil.isTrue(dataAccessService.getMyDataAccess().getChannelOrgCodes().contains(vo.getCode()), new ApiException("无权操作"));
        }
        if (StringUtil.isBlank(request.getParentCode())) {
            //顶层团队，必须是区
            AssertUtil.isTrue(TeamLevel.AREA.equals(request.getLevel()), new ApiException("顶层机构级别必须是区域"));
        } else {
            AssertUtil.isTrue(!TeamLevel.AREA.equals(request.getLevel()), new ApiException("创建营业区直接选择机构，不要选择上级团队"));
            ChannelTeam parent = channelTeamDao.getByCode(request.getParentCode());
            AssertUtil.notNull(parent, new ApiException("父团队编码错误"));
            AssertUtil.isTrue(!TeamLevel.TEAM.name().equals(parent.getLevel()), new ApiException("不能在营业组下创建团队"));
            if (TeamLevel.DEPT.name().equals(parent.getLevel())) {
                AssertUtil.isTrue(TeamLevel.TEAM.equals(request.getLevel()), new ApiException("营业部下只能创建营业组"));
            }
            if (TeamLevel.AREA.name().equals(parent.getLevel())) {
                AssertUtil.isTrue(TeamLevel.DEPT.equals(request.getLevel()), new ApiException("营业区下不能直接创建营业组"));
            }
        }
        //生成团队编码
        ChannelTeam ct = BeanCopier.copyObject(request, ChannelTeam.class);
        ChannelTeam replace = generateTeamCode();
        ct.setId(replace.getId());
        ct.setCode(replace.getCode());
        ct.setCreateTime(replace.getCreateTime());
        ct.setCreator(replace.getCreator());
        ct.setCreatorId(replace.getCreatorId());
        if (ct.getParentCode() == null) {
            ct.setParentCode("");
        }
        ct.setChannelCode(vo.getChannelCode());
        ct.setChannelName(StringUtils.trimToEmpty(vo.getChannelName()));
        ct.setOrgCode(vo.getCode());
        ct.setOrgName(StringUtils.trimToEmpty(vo.getName()));
        ct.setStatus(TeamStatus.ENABLED.name());
        ct.setLeader("");
        ct.setLeaderCode("");
        ct.setEffectiveDate(LocalDate.now());
        channelTeamMapper.updateById(ct);
        if (auth) {
            //控制器创建的团队，需要刷新缓存。
            cacheLoader.refreshChannelTeams();
        }
    }

    private ChannelTeam generateTeamCode() {
        //CT+YYMM(年月)+五位顺序
        //如CT210500001

        ChannelTeam ct = new ChannelTeam();
        //时间戳占位
        ct.setCode(System.currentTimeMillis() + "");
        ct.setName("");
        ct.setParentCode("");
        ct.setChannelCode("");
        ct.setChannelName("");
        ct.setOrgCode("");
        ct.setOrgName("");
        ct.setStatus(TeamStatus.DISABLED.name());
        ct.setLeader("");
        ct.setLeaderCode("");
        ct.setLevel(TeamLevel.TEAM.name());
        channelTeamMapper.insert(ct);
        String teamCode = "CT" + TimeUtil.format(new Date(), "yyMM") + String.format("%05d", ct.getId());
        ct.setCode(teamCode);
        return ct;
    }

    @Override
    public void updateLeader(Long id, Long leaderId) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        //根据id查询出被修改的团队
        ChannelTeam ct = channelTeamDao.getById(id);
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(ct.getOrgCode()), new ApiException("您没有操作该团队的权限"));
        }
        ChannelEmployee leader = channelEmployeeDao.getById(leaderId);
        //检测主管离职状态
        AssertUtil.isTrue(EmployeeStatus.SERVING.name().equals(leader.getStatus()), new ApiException("主管已经离职"));
        if (!leader.getTeamCode().equals(ct.getCode())) {
            //检测leaderId是否属于该组。
            Set<String> teamCodes = StreamEx.of(orgService.getTeams(AgentOrgType.CHANNEL, ct.getCode(), true)).map(TeamVO::getCode).toSet();
            AssertUtil.isTrue(teamCodes.contains(leader.getTeamCode()), new ApiException("主管需要该团队下的代理人"));
        }
        ct.setLeader(leader.getName());
        ct.setLeaderCode(leader.getCode());
        channelTeamMapper.updateById(ct);
    }

    @Override
    @DS("org")
    @DSTransactional
    public void updateStatus(Long id, TeamStatus status) {
        log.info("团队状态变更,开始,id:{},status:{}", id, status);
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        //根据id查询出被修改的团队
        ChannelTeam ct = channelTeamDao.getById(id);
        log.info("团队状态变更,ChannelTeam:{}", ct);
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(ct.getOrgCode()), new ApiException("您没有操作该团队的权限"));
        }
        if (TeamStatus.DISABLED.equals(status)) {
            if (TeamStatus.ENABLED.name().equals(ct.getStatus())) {
                //递归失效所有子团队
                disableTeam(ct.getCode());
                //从有效变成失效
                channelEmployeeService.leaveByTeamCode(ct.getCode());
                ct.setStatus(status.name());
                channelTeamMapper.updateById(ct);
                cacheLoader.refreshChannelTeams();
            }
        } else {
            if (TeamStatus.DISABLED.name().equals(ct.getStatus())) {
                //从失效变成有效
                ct.setStatus(status.name());
                channelTeamMapper.updateById(ct);
                cacheLoader.refreshChannelTeams();
            }
        }
    }

    /**
     * 递归失效所有子团队
     */
    private void disableTeam(String parentCode) {
        List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .eq(ChannelTeam::getParentCode, parentCode).eq(ChannelTeam::getStatus, TeamStatus.ENABLED.name()));
        for (ChannelTeam ct : channelTeams) {
            log.info("团队状态变更,disableTeam,---处理ChannelTeam:{}", ct);
            if (!TeamLevel.TEAM.equals(ct.getLevel())) {
                disableTeam(ct.getCode());
            }
            //从有效变成失效
            channelEmployeeService.leaveByTeamCode(ct.getCode());
            ct.setStatus(TeamStatus.DISABLED.name());
            channelTeamMapper.updateById(ct);
        }
    }

    @Override
    public ChannelTeamVO getById(Long id) {
        ChannelTeam channelTeam = channelTeamDao.getById(id);
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            AssertUtil.isTrue(myDataAccess.getChannelOrgCodes().contains(channelTeam.getOrgCode()), new ApiException("无权访问"));
        }
        ChannelTeamVO channelTeamVO = BeanCopier.copyObject(channelTeam, ChannelTeamVO.class);
        ChannelTeam parentTeam = null;
        if (StringUtils.isNotBlank(channelTeam.getParentCode())) {
            parentTeam = channelTeamDao.getByCode(channelTeam.getParentCode());
            channelTeamVO.setParentName(parentTeam.getName());
        }
        return channelTeamVO;
    }

    @Override
    public void disableTeamByOrgCode(String orgCode) {
        List<ChannelTeam> channelTeams = channelTeamMapper.selectList(new LambdaQueryWrapper<ChannelTeam>()
                .eq(ChannelTeam::getStatus, TeamStatus.ENABLED)
                .eq(ChannelTeam::getOrgCode, orgCode));
        for (ChannelTeam ct : channelTeams) {
            //从有效变成失效
            channelEmployeeService.leaveByTeamCode(ct.getCode());
            ct.setStatus(TeamStatus.DISABLED.name());
            channelTeamMapper.updateById(ct);
        }
    }

    @Override
    public Set<String> selectAllChildCodes(String teamCode, String teamName, MyDataAccessVO myDataAccess) {
        List<ChannelTeam> list = new ArrayList<>();
        for (ChannelTeam t : cacheService.getAllChannelTeams()) {
            if (!TeamStatus.ENABLED.name().equals(t.getStatus())) {
                continue;
            }
            if (!StringUtil.isBlank(teamCode) && !t.getCode().equals(teamCode)) {
                continue;
            }
            if (!StringUtil.isBlank(teamName) && !t.getName().contains(teamName)) {
                continue;
            }
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin()) && !myDataAccess.getChannelOrgCodes().contains(t.getOrgCode())) {
                continue;
            }
            list.add(t);
        }
        Set<String> allTeamCodes = new HashSet<>();
        //转换数据结构
        for (ChannelTeam vo : list) {
            allTeamCodes.add(vo.getCode());
            if (TeamLevel.TEAM.name().equals(vo.getLevel())) {
                continue;
            }
            allTeamCodes.addAll(getChildTeamCodes(vo.getCode(), allTeamCodes));
        }
        return allTeamCodes;
    }

    private Set<String> getChildTeamCodes(String parentCode, Set<String> allTeamCodes) {
        List<ChannelTeam> list = new ArrayList<>();
        for (ChannelTeam t : cacheService.getAllChannelTeams()) {
            if (!TeamStatus.ENABLED.name().equals(t.getStatus())) {
                continue;
            }
            if (!StringUtil.isBlank(parentCode) && !parentCode.equals(t.getParentCode())) {
                continue;
            }
            list.add(t);
        }
        for (ChannelTeam vo : list) {
            if (TeamLevel.TEAM.name().equals(vo.getLevel())) {
                continue;
            }
            allTeamCodes.addAll(getChildTeamCodes(vo.getCode(), allTeamCodes));
        }
        return StreamEx.of(list).map(ChannelTeam::getCode).toSet();
    }


    /**
     * 递归获取团队树
     *
     * @param parentCode
     * @return
     */
    private List<ChannelTeamTreeNodeVO> getChannelTeamTreeNodeVO(String parentCode,String status) {
        List<ChannelTeam> list = new ArrayList<>();
        for (ChannelTeam t : cacheService.getAllChannelTeams()) {
            if (!StringUtil.isBlank(parentCode) && !parentCode.equals(t.getParentCode())) {
                continue;
            }
            if(StringUtil.isNotBlank(status)){
                if(!status.equals(t.getStatus())){
                    continue;
                }
            }

            list.add(t);
        }
        List<ChannelTeamTreeNodeVO> vos = BeanCopier.copyList(list, ChannelTeamTreeNodeVO.class);
        for (ChannelTeamTreeNodeVO vo : vos) {
            if (TeamLevel.TEAM.equals(vo.getLevel())) {
                continue;
            }
            if(StringUtil.isNotBlank(status)){
                if(!status.equals(vo.getStatus())){
                    continue;
                }
            }
            vo.setChildren(getChannelTeamTreeNodeVO(vo.getCode(),status));
        }
        return vos;
    }

    /**
     * 递归获取团队树
     *
     * @param parentCode
     * @return
     */
    private List<SimpleTeamTreeNodeVO> getSimpleTeamTreeNodeVO(String parentCode, String status) {
        List<ChannelTeam> list = new ArrayList<>();
        for (ChannelTeam t : cacheService.getAllChannelTeams()) {
            if ("ENABLED".equals(status) && !TeamStatus.ENABLED.name().equals(t.getStatus())) {
                continue;
            }
            if (!StringUtil.isBlank(parentCode) && !parentCode.equals(t.getParentCode())) {
                continue;
            }
            list.add(t);
        }
        List<SimpleTeamTreeNodeVO> vos = BeanCopier.copyList(list, SimpleTeamTreeNodeVO.class);
        for (SimpleTeamTreeNodeVO vo : vos) {
            if (TeamLevel.TEAM.equals(vo.getLevel())) {
                continue;
            }
            vo.setChildren(getSimpleTeamTreeNodeVO(vo.getCode(), status));
        }
        return vos;
    }

}
