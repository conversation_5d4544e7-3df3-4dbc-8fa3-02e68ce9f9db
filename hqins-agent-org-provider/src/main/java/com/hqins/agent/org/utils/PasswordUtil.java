package com.hqins.agent.org.utils;

import com.hqins.agent.org.constants.AppConsts;
import com.hqins.common.utils.StringUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2021/5/26
 * @Description
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PasswordUtil {

    public static String generatePwd(String idCode) {
        if (StringUtil.isBlank(idCode)) {
            return base64En(AppConsts.DEFAULT_PASS_WORD);
        }
        if (idCode.length() > AppConsts.NUMBER_6) {
            return base64En(idCode.substring(idCode.length() - AppConsts.NUMBER_6));
        }
        if (idCode.length() < AppConsts.NUMBER_6) {
            idCode = StringUtil.leftPad(idCode, AppConsts.NUMBER_6, AppConsts.STRING_0);
        }
        return base64En(idCode);
    }


    /**
     * base64加密
     */
    public static String base64En(String password) {
        try {
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(password.getBytes());
        } catch (Exception ex) {
            log.error("PasswordUtil #base64En:{}", ex, "加密服务执行异常");
        }
        return null;
    }
}
