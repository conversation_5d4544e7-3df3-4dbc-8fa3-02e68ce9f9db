package com.hqins.agent.org.cache;

import com.hqins.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: lijian
 * @Date: 2021-11-26
 */
@Service
@Slf4j
public class RedisService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 发布消息
     * @param topic          主题
     * @param id             id
     */
    public void publishMsgToTopic(String topic, Long id) {
        redisTemplate.convertAndSend(topic, id);
    }

    /**
     * 发布更新缓存消息
     * @param topic
     * @param str
     */
    public void publishRefreshCache(String topic, String str) {
        redisTemplate.convertAndSend(topic, str);
    }

    /**
     * 根据key读取数据
     */
    public Object get(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("查询redis失败，key：{}", key, e);
        }
        return null;
    }

    /**
     * 写入数据
     */
    public boolean set(String key, Object value) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        try {
            redisTemplate.opsForValue().set(key, value, 3, TimeUnit.DAYS);
            log.info("存入redis成功，key：{}，value：{}", key, value);
            return true;
        } catch (Exception e) {
            log.error("存入redis失败，key：{}，value：{}", key, value, e);
        }
        return false;
    }

    /**
     * 写入数据 带过期时间
     */
    public boolean setWithExpire(String key, Object value, int hour) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        try {
            redisTemplate.opsForValue().set(key, value, hour, TimeUnit.HOURS);
            log.info("存入redis成功，key：{}，value：{}", key, value);
            return true;
        } catch (Exception e) {
            log.error("存入redis失败，key：{}，value：{}", key, value, e);
        }
        return false;
    }

    public boolean setWithDayExpire(String key, Object value, int day) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        try {
            redisTemplate.opsForValue().set(key, value, day, TimeUnit.DAYS);
            log.info("存入redis成功，key：{}，value：{}", key, value);
            return true;
        } catch (Exception e) {
            log.error("存入redis失败，key：{}，value：{}", key, value, e);
        }
        return false;
    }

    /**
     * 写入数据 带过期时间
     */
    public boolean setExpire(String key, Object value, int date, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        try {
            redisTemplate.opsForValue().set(key, value, date, unit);
            log.info("存入redis成功，key：{}，value：{}", key, value);
            return true;
        } catch (Exception e) {
            log.error("存入redis失败，key：{}，value：{}", key, value, e);
        }
        return false;
    }

    public boolean remove (String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        try {
            boolean success = redisTemplate.delete(key);
            String msg = success == true ? "成功" : "失败";
            log.info("删除redis" + msg + "，key：{}", key);
            return success;
        } catch (Exception e) {
            log.error("存入redis失败，key：{}", key, e);
        }
        return false;
    }

    public void removePattern(String pattern) {
        log.info("模糊匹配redisKey：" + pattern);
        Set<String> keys = redisTemplate.keys(pattern);
        if (CollectionUtils.isEmpty(keys)) {
            log.info("未匹配到redisKey：" + pattern);
            return;
        }
        log.info("匹配到的keys:{}", JsonUtil.toJSON(keys));
        for (String key : keys) {
            remove(key);
        }
    }

}