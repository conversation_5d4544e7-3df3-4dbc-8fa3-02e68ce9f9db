package com.hqins.agent.org.exception;

import com.hqins.common.base.errors.IException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/4/10
 * @Description
 */
@Getter
@AllArgsConstructor
public enum UserExceptionCode implements IException {
    /**
     * 手机号异常信息
     */
    MOBILE_EXISTS("800000", "手机号【{}】已经注册过！"),
    /**
     * id异常信息
     */
    DATA_NOT_FOUND_BY_ID("800001", "id为【{}】的【{}】不存在！");

    private String code;
    private String message;

}
