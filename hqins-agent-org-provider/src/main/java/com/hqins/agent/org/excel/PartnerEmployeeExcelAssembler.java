package com.hqins.agent.org.excel;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.common.base.enums.IdType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 类型转换
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@Service
public class PartnerEmployeeExcelAssembler {


    public PartnerEmployeeExcel assembler(EmployeeVO employeeVO) {
        PartnerEmployeeExcel partnerEmployeeExcel = new PartnerEmployeeExcel();
        partnerEmployeeExcel.setCode(StringUtils.isNotEmpty(employeeVO.getCode()) ? employeeVO.getCode():" ");
        partnerEmployeeExcel.setName(StringUtils.isNotEmpty(employeeVO.getName()) ? employeeVO.getName():" ");
        partnerEmployeeExcel.setOrgCode(StringUtils.isNotEmpty(employeeVO.getOrgCode()) ? employeeVO.getOrgCode():" ");
        partnerEmployeeExcel.setOrgName(StringUtils.isNotEmpty(employeeVO.getOrgName()) ? employeeVO.getOrgName():" ");
        partnerEmployeeExcel.setTeamCode(StringUtils.isNotEmpty(employeeVO.getTeamCode()) ? employeeVO.getTeamCode():" ");
        partnerEmployeeExcel.setTeamName(StringUtils.isNotEmpty(employeeVO.getTeamName()) ? employeeVO.getTeamName():" ");
        partnerEmployeeExcel.setMobile(StringUtils.isNotEmpty(employeeVO.getMobile()) ? employeeVO.getMobile():" ");
        partnerEmployeeExcel.setIdType(null != employeeVO.getIdType() ? null != IdType.get(employeeVO.getIdType()) ? IdType.get(employeeVO.getIdType()).getLabel() : " " : " ");
        partnerEmployeeExcel.setIdCode(StringUtils.isNotEmpty(employeeVO.getIdCode()) ? employeeVO.getIdCode():" ");
        partnerEmployeeExcel.setGender(null!= employeeVO.getGender() ? employeeVO.getGender().getLabel():" ");
        if (employeeVO.getUniversalQualification()) {
            partnerEmployeeExcel.setUniversalQualification("是");
        } else {
            partnerEmployeeExcel.setUniversalQualification("否");
        }

        partnerEmployeeExcel.setLicenseNo(StringUtils.isNotEmpty(employeeVO.getLicenseNo()) ? employeeVO.getLicenseNo():" ");
        partnerEmployeeExcel.setLicenseStartDate(employeeVO.getLicenseStartDate() != null ? employeeVO.getLicenseStartDate().toString():" ");
        partnerEmployeeExcel.setLicenseEndDate(employeeVO.getLicenseEndDate() != null ? employeeVO.getLicenseEndDate().toString():" ");
        partnerEmployeeExcel.setEntryTime(employeeVO.getEntryTime() != null ? employeeVO.getEntryTime().toString():" ");
        partnerEmployeeExcel.setQuitTime(employeeVO.getQuitTime() != null ? employeeVO.getQuitTime().toString():" ");
        partnerEmployeeExcel.setReferences(StringUtils.isNotEmpty(employeeVO.getReferences()) ? employeeVO.getReferences():" ");
        return partnerEmployeeExcel;
    }
}
