package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.vo.DigitalDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreAllDetailVO;
import com.hqins.agent.org.model.vo.DigitalScoreCodeVO;
import com.hqins.agent.org.service.IFPDigitalScoreService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/8/9 17:55
 */
@RestController
@RequestMapping("/digital-score")
@Api(tags = "ifp数字化积分")
@Slf4j
public class IFPDigitalScoreController {

    @Autowired
    private IFPDigitalScoreService ifpDigitalScoreService;

    @ApiOperation("代理人IFP全量月份和积分数据查询")
    @GetMapping("/all-month-score")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<DigitalDataVO>> getAllMonthAndScoreData(
            @ApiParam("代理人工号") @RequestParam(value = "agentCode", required = true) String agentCode,
            @ApiParam("开始年月") @RequestParam(value = "startDate", required = true) String startDate){


        return ApiResult.ok(ifpDigitalScoreService.getAllMonthAndScoreData(agentCode,startDate));
    }

    @ApiOperation("IFP积分查询")
    @GetMapping("/score")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<DigitalScoreCodeVO>> getScore(
            @ApiParam("代理人工号集合") @RequestParam(value = "agentCodeList", required = true) List<String> agentCodeList,
            @ApiParam("查询年月") @RequestParam(value = "queryDate", required = true) String queryDate){


        return ApiResult.ok(ifpDigitalScoreService.getScore(agentCodeList,queryDate));
    }

    @ApiOperation("IFP积分明细查询")
    @GetMapping("/score-detail")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<DigitalScoreAllDetailVO>> getScoreDetail(
            @ApiParam("代理人工号集合") @RequestParam(value = "agentCodeList", required = true) List<String> agentCodeList,
            @ApiParam("查询年月") @RequestParam(value = "queryDate", required = true) String queryDate,
            @ApiParam("是否可记分 Y:是 N:否 不传查全部") @RequestParam(value = "recordPointsFlag", required = false) String recordPointsFlag
    ){


        return ApiResult.ok(ifpDigitalScoreService.getScoreDetail(agentCodeList,queryDate,recordPointsFlag));
    }
}
