package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.MerchantCodeAndOrgCodeRequest;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.model.vo.PartassignmanagerVO;
import com.hqins.agent.org.service.PartnerChannelRelationService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/31
 */
@Api(tags = "客户经理与理财经理关系")
@RestController
@RequestMapping("/partnerChannelRelation")
@Slf4j
public class PartnerChannelRelationController {

    @Autowired
    private PartnerChannelRelationService partnerChannelRelationService;

    @ApiOperation("查询网点下的客户经理列表")
    @GetMapping("/org/custManagers")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PartassignmanagerVO>> getOrgCustManagerList(
            @ApiParam("商户组织机构(网点)代码") @RequestParam(value = "orgCode") String orgCode) {
        return ApiResult.ok(partnerChannelRelationService.getCustManagerListByOrgCode(orgCode));
    }

    @ApiOperation("查询网点下的理财经理列表")
    @GetMapping("/org/employees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getOrgEmployeeList(@ApiParam("商户组织机构(网点)代码") @RequestParam(value = "orgCode") String orgCode) {
        return ApiResult.ok(partnerChannelRelationService.getEmployeeInfoByOrgCode(orgCode));
    }

    @ApiOperation("查询客户经理下的网点列表")
    @GetMapping("/custManager/orgs")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PartassignmanagerVO>> getMerchantOrgList(@ApiParam("客户经理代码") @RequestParam(value = "custManagerCode") String custManagerCode) {
        return ApiResult.ok(partnerChannelRelationService.getMerchantOrgListByCustManagerCode(custManagerCode));
    }

    @ApiOperation("查询客户经理下的理财经理列表")
    @GetMapping("/custManager/employees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getCustManagerEmployeeList(@ApiParam("客户经理代码") @RequestParam(value = "custManagerCode") String custManagerCode) {
        return ApiResult.ok(partnerChannelRelationService.getEmployeeListByCustManagerCode(custManagerCode));
    }

    @ApiOperation("根据“商户代码（银行）”和“商户组织机构代码（网点）”查询客户经理列表")
    @PostMapping("/custManagers")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PartassignmanagerVO>> getCustManagers(@Valid @RequestBody MerchantCodeAndOrgCodeRequest request) {
        return ApiResult.ok(partnerChannelRelationService.getCustManagerListByMerchantCodeAndOrgCode(request.getMerchantCodes(),request.getOrgCodes()));
    }
}
