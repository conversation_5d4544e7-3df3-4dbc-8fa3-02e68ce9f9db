package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.DataType;
import com.hqins.agent.org.model.enums.NodeLevel;
import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.agent.org.service.PartnerService;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2021/5/18
 * @Description
 */
@Service
@Slf4j
public class PartnerServiceImpl implements PartnerService {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private CacheService cacheService;

    /**
     * 获取当前登录人有权限管理的合伙人列表
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<PartnerVO> listMy(TopQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<Tbepartner> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, myDataAccess.getPartnerCodes());
        } else {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        }
        return PageUtil.convert(p, PartnerConverter::tbepartnerToPartnerVO);
    }

    /**
     * 获取当前登录人有权限管理的合伙人列表
     * 简化版，只获取code、name
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<SimpleNodeVO> listMySimple(TopQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<Tbepartner> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, myDataAccess.getSelectPartnerCodes());
        } else {
            p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        }
        return PageUtil.convert(p, PartnerConverter::tbepartnerToSimpleNodeVO);
    }

    /**
     * 获取所有的合伙人，简化版下拉框里使用
     *
     * @param queryRequest
     * @return
     */
    @Override
    public PageInfo<SimpleNodeVO> listAllSimple(TopQueryRequest queryRequest) {
        Page<Tbepartner> p = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        return PageUtil.convert(p, PartnerConverter::tbepartnerToSimpleNodeVO);
    }

    /**
     * 获取有效的组织机构树,包含合伙人、合伙人销售机构
     * 数据授权时使用
     *
     * @param treeRequest
     * @return
     */
    @Override
    public List<SimpleTreeNodeVO> getAllTree(OrgTreeRequest treeRequest) {
        //加载第一层树
        List<Tbepartner> tbepartners = cacheService.selectTbepartners(AppConsts.CPTYPE_PARTNER, TopQueryRequest.builder().code(treeRequest.getTopCode()).build(), null);

        //机构map
        Map<String, List<BaseInst>> baseInstsMap = null;
        if (!NodeLevel.TOP.equals(treeRequest.getNodeLevel())) {
            List<BaseInst> baseInstList = StreamEx.of(cacheService.getAllBaseInsts()).filter(t ->
                    "1".equals(t.getInstStatus()) && (StringUtil.isBlank(treeRequest.getOrgCode()) || treeRequest.getOrgCode().equals(t.getInstCode()))
            ).toList();
            baseInstsMap = StreamEx.of(baseInstList).groupingBy(BaseInst::getCompanyid);
        }

        Map<String, Tbepartner> tbepartnersMap = StreamEx.of(tbepartners).toMap(Tbepartner::getCompanycode, Function.identity());
        List<SimpleTreeNodeVO> topTreeNodeVos = BeanCopier.copyList(tbepartners, PartnerConverter::tbepartnerToSimpleTreeNodeVO);
        //加载机构层
        for (SimpleTreeNodeVO top : topTreeNodeVos) {
            if (NodeLevel.TOP.equals(treeRequest.getNodeLevel())) {
                continue;
            }
            top.setDataType(DataType.PARTNER);
            List<BaseInst> baseInsts = baseInstsMap.getOrDefault(tbepartnersMap.get(top.getCode()).getCompanyid(), new ArrayList<>());
            if (CollectionUtils.isEmpty(baseInsts)) {
                continue;
            }
            List<SimpleTreeNodeVO> orgTreeNodeVos = BeanCopier.copyList(baseInsts, PartnerConverter::instToSimpleTreeNodeVo);
            for (SimpleTreeNodeVO org : orgTreeNodeVos) {
                org.setDataType(DataType.PARTNER_ORG);
            }
            top.setChildren(TreeUtils.buildTrees(orgTreeNodeVos));
        }
        return topTreeNodeVos;
    }

    @Override
    public PageInfo<SimpleNodeVO> clueListMySimple(TopQueryRequest queryRequest) {
        return listAllSimple(queryRequest);
    }

}
