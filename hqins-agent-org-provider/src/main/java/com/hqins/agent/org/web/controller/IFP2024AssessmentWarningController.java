package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.vo.IFP2024GroupVO;
import com.hqins.agent.org.model.vo.IFP2024TabVO;
import com.hqins.agent.org.service.IFP2024AssessmentWarningService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "自营2024员工考核预警")
@RestController
@RequestMapping("/ifp-2024/assessment")
@RequiredArgsConstructor
@Slf4j
public class IFP2024AssessmentWarningController {

    private final IFP2024AssessmentWarningService warningService;

    @ApiOperation("个团2024-获取员工个人考核预警信息")
    @GetMapping("/queryEmployeeInfo")
    public ApiResult<List<IFP2024TabVO>> queryEmployeeInfo(@ApiParam("1-当期；2-上期") @RequestParam(value = "paramType") String paramType,
                                                           @ApiParam("员工工号") @RequestParam(value = "employeeCode") String employeeCode){
        try {
            List<IFP2024TabVO> ifp2024TabVOS = warningService.queryEmployeeInfo(paramType, employeeCode);
            return ApiResult.ok(ifp2024TabVOS);
        } catch (Exception e) {
            log.error("{}：{}", "个人信息查询异常", e.getMessage(), e);
            return ApiResult.fail("个人信息查询异常");
        }
    }

    @ApiOperation("个团2024-获取团队考核预警信息")
    @GetMapping("/queryGroupInfo")
    public ApiResult<IFP2024GroupVO> queryGroupInfo(@ApiParam("1-当期；2-上期")  @RequestParam(value = "paramType") String paramType,
                                                    @ApiParam("团队代码")        @RequestParam(value = "teamCode") String teamCode){
        try {
            IFP2024GroupVO ifp2024GroupVO = warningService.queryGroupInfo(paramType, teamCode);
            return ApiResult.ok(ifp2024GroupVO);
        } catch (Exception e) {
            log.error("{}：{}", "团队信息查询异常", e.getMessage(), e);
            return ApiResult.fail("团队信息查询异常");
        }
    }
}
