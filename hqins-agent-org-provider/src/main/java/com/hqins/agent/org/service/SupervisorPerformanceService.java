package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.SupervisorPerformanceRequest;
import com.hqins.agent.org.model.vo.*;

import java.util.Date;
import java.util.List;

/**
 * @Description 督导业绩
 */
public interface SupervisorPerformanceService {


    List<TreeNodeVO> getSupervisorPartnerInstList();

    List<TreeNodeVO> getSupervisorTeamList(String partnerInstCode);

    PerformanceVO getMonthPerformance();

    SupervisorPerformanceVO getPerformanceList(String partnerInstCode, String teamCode, Date startDate, Date endDate, String accType, String groupType);

    SupervisorMarkVO getSelfPolicy(SupervisorPerformanceRequest request);

    SupervisorMarkVO getDisCompleteCr13(SupervisorPerformanceRequest request);

    SupervisorMarkVO getCtPolicy(SupervisorPerformanceRequest request);

    SupervisorPerformanceVO getPerformanceList(SupervisorPerformanceRequest request);

    SupervisorCheckInfoVO getCheckInoList(SupervisorPerformanceRequest request);

    SupervisorMarkVO getAllMark(SupervisorPerformanceRequest request);

    String getPerformanceListExport(SupervisorPerformanceRequest request);

    String getSelfPolicyExport(SupervisorPerformanceRequest request);

    String getDisCompleteCr13Export(SupervisorPerformanceRequest request);

    String getCtPolicyExport(SupervisorPerformanceRequest request);

    String getCheckInoListExport(SupervisorPerformanceRequest request);
}
