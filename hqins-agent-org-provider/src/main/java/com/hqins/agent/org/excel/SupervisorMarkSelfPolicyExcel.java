package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 指标预警--自互保件
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class SupervisorMarkSelfPolicyExcel implements Serializable {

    @ExcelProperty("销售机构")
    @ApiModelProperty("销售机构")
    private String instName;

    @ExcelProperty("出单业务员")
    @ApiModelProperty("出单业务员")
    private String agentName;

    @ExcelProperty("保单号")
    @ApiModelProperty("保单号")
    private String policyNo;

    @ExcelProperty("投保人")
    @ApiModelProperty("投保人")
    private String appntName;

    @ExcelProperty("被保险人")
    @ApiModelProperty("被保险人")
    private String insuredName;

    @ExcelProperty("类型")
    @ApiModelProperty("类型")
    private String typeName;

}
