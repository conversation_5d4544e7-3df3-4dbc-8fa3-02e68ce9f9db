package com.hqins.agent.org.dao.mapper.hologres;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@DS("hologres")
@Repository
public interface DaDetailUwMapper<T> extends BaseMapper<T> {

    List<HoloDaDetailUwVo> selectDaDetailUwList(Map requestMap);

    PerformanceVO selectPerformanceByInstCode(Map map);

    List<SupervisorPerformanceDetailVO> selectPerformanceList(Map map);

    List<MarkDetailVO> getHologresSelfPolicyList(Map map);

    List<MarkDetailVO> getSupervisorCtList(Map map);

    List<SupervisorPerformanceDetailNumberVO> selectAppNumList(Map map);
}
