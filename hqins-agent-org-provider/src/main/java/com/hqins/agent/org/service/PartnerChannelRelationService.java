package com.hqins.agent.org.service;

import com.hqins.agent.org.model.vo.*;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/31
 * @Description
 */
public interface PartnerChannelRelationService {

    /**
     * 查询网点下的客户经理列表
     * @param orgCode   网点code编码
     * @return
     */
    List<PartassignmanagerVO> getCustManagerListByOrgCode(String orgCode);

    /**
     * 根据机构编码（orgCode）获取销售员（理财经理）详情
     * @param orgCode
     * @return
     */
    List<EmployeeVO> getEmployeeInfoByOrgCode(String orgCode);

    /**
     * 查询客户经理下的网点列表
     * @param custManagerCode   客户经理编码
     * @return
     */
    List<PartassignmanagerVO> getMerchantOrgListByCustManagerCode(String custManagerCode);

    /**
     * 查询客户经理下的理财经理列表
     * @param custManagerCode   客户经理编码
     * @return
     */
    List<EmployeeVO> getEmployeeListByCustManagerCode(String custManagerCode);

    /**
     * 根据“商户代码（银行）”和“商户组织机构代码（网点）”查询客户经理列表
     * @param merchantCodeList
     * @param orgCodeList
     * @return
     */
    List<PartassignmanagerVO> getCustManagerListByMerchantCodeAndOrgCode(String[] merchantCodeList,String[] orgCodeList);
}
