package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.ChannelEmployeeService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "渠道商-销售员管理")
@RestController
@RequestMapping("/channel/employees")
@RefreshScope
@Slf4j
public class ChannelEmployeeController {

    private ChannelEmployeeService channelEmployeeService;

    public ChannelEmployeeController(ChannelEmployeeService channelEmployeeService) {
        this.channelEmployeeService = channelEmployeeService;
    }

    @ApiOperation("分页查询渠道商销售员")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelEmployeeVO>> listMy(
            @ApiParam("归属渠道商代码") @RequestParam(value = "channelCode", required = false) String channelCode,
            @ApiParam("归属渠道商名称") @RequestParam(value = "channelName", required = false) String channelName,
            @ApiParam("归属销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
            @ApiParam("归属销售团队代码") @RequestParam(value = "teamCode", required = false) String teamCode,
            @ApiParam("归属销售团队名称") @RequestParam(value = "teamName", required = false) String teamName,
            @ApiParam("销售员代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售员证件号") @RequestParam(value = "idCode", required = false) String idCode,
            @ApiParam("销售员名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售员手机号") @RequestParam(value = "mobile", required = false) String mobile,
            @ApiParam("入职日期-开始") @RequestParam(value = "entryTimeStart", required = false) LocalDate entryTimeStart,
            @ApiParam("入职日期-结束") @RequestParam(value = "entryTimeEnd", required = false) LocalDate entryTimeEnd,
            @ApiParam("离职日期-开始") @RequestParam(value = "quitTimeStart", required = false) LocalDate quitTimeStart,
            @ApiParam("离职日期-结束") @RequestParam(value = "quitTimeEnd", required = false) LocalDate quitTimeEnd,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        ChannelEmployeeRequest queryRequest = ChannelEmployeeRequest.builder()
                .code(code).idCode(idCode).name(name).mobile(mobile).channelCode(channelCode).channelName(channelName)
                .orgCode(orgCode).orgName(orgName).teamCode(teamCode).teamName(teamName)
                .entryTimeStart(entryTimeStart).entryTimeEnd(entryTimeEnd).quitTimeStart(quitTimeStart).quitTimeEnd(quitTimeEnd)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();
        return ApiResult.ok(channelEmployeeService.listMy(queryRequest));
    }

    @ApiOperation("渠道商代理人员导出")
    @GetMapping(value = "/export")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> export(@ApiParam("归属渠道商名称") @RequestParam(value = "channelName", required = false) String channelName,
                                    @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
                                    @ApiParam("归属销售团队名称") @RequestParam(value = "teamName", required = false) String teamName,
                                    @ApiParam("销售员代码") @RequestParam(value = "code", required = false) String code,
                                    @ApiParam("销售员证件号") @RequestParam(value = "idCode", required = false) String idCode,
                                    @ApiParam("销售员名称") @RequestParam(value = "name", required = false) String name,
                                    @ApiParam("销售员手机号") @RequestParam(value = "mobile", required = false) String mobile,
                                    @ApiParam("入职日期-开始") @RequestParam(value = "entryTimeStart", required = false) LocalDate entryTimeStart,
                                    @ApiParam("入职日期-结束") @RequestParam(value = "entryTimeEnd", required = false) LocalDate entryTimeEnd,
                                    @ApiParam("离职日期-开始") @RequestParam(value = "quitTimeStart", required = false) LocalDate quitTimeStart,
                                    @ApiParam("离职日期-结束") @RequestParam(value = "quitTimeEnd", required = false) LocalDate quitTimeEnd) {
        ChannelEmployeeRequest queryRequest = ChannelEmployeeRequest.builder()
                .code(code).idCode(idCode).name(name).mobile(mobile).channelName(channelName).orgName(orgName).teamName(teamName)
                .entryTimeStart(entryTimeStart).entryTimeEnd(entryTimeEnd).quitTimeStart(quitTimeStart).quitTimeEnd(quitTimeEnd).build();
        return ApiResult.ok(channelEmployeeService.exportData(queryRequest));
    }

    @ApiOperation("获取有管理权限的渠道商销售员，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleEmployeeVO>> listMyEmployeeSimple(
            @ApiParam("渠道商编码") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("渠道商机构编码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("团队编码") @RequestParam(value = "teamCode", required = false) String teamCode,
            @ApiParam("销售员名称或者代码") @RequestParam(value = "value", required = false) String value,
            @ApiParam("状态：SERVING、ALL") @RequestParam(value = "status", required = false, defaultValue = "SERVING") String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current",required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "20") long size) {
        EmployeeQueryRequest queryRequest = EmployeeQueryRequest.builder().status(status)
                .topCode(topCode).orgCode(orgCode).teamCode(teamCode).value(value).current(current).size(size).build();
        queryRequest.correctPageQueryParameters();
        return ApiResult.ok(channelEmployeeService.listEmployeeSimple(queryRequest));
    }

    @ApiOperation("创建销售员")
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> save(@Valid @RequestBody ChannelEmployeeAddRequest request) {
        channelEmployeeService.checkAndSave(request);
        return ApiResult.ok();
    }

    @ApiOperation("更新销售员信息")
    @PutMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> update(@Valid @RequestBody ChannelEmployeeUpdateRequest request) {
        ChannelEmployeeCheckVo checkVo = channelEmployeeService.update(request);
        if(!checkVo.isSuccess()){
            return ApiResult.fail(checkVo.getDescription());
        }
        return ApiResult.ok();
    }


    @ApiOperation("批量新增销售员预校验")
    @PostMapping("/batch/check")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> checkBatchs(@Valid @RequestBody List<ChannelEmployeeAddRequest> employees , @ApiParam("上传文件名") @RequestParam(value = "fileName",required = false)  String fileName ) {
        //线程外获取其他线程数据
        String staffUsername = RequestContextHolder.getStaffUsername();
        Long staffId = RequestContextHolder.getStaffId();
        Long adminAppId = RequestContextHolder.getAdminAppId();
        new Thread("batch-channel-emp-thread") {
            @Override
            public void run() {
                RequestContextHolder.setStaffUsernameLocal(staffUsername);
                RequestContextHolder.setStaffIdLocal(staffId);
                RequestContextHolder.setAdminAppIdLocal(adminAppId);
                channelEmployeeService.checkBatch(employees ,fileName);
            }
        }.start();
        return ApiResult.ok();
    }

    @ApiOperation("批量新增销售员")
    @PostMapping("/batch/save")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> batchSave(@Valid @RequestBody List<ChannelEmployeeAddRequest> employees) {
        channelEmployeeService.saveBatch(employees);
        return ApiResult.ok();
    }

    @ApiOperation("销售员离职")
    @PutMapping("/leave/{id:" + Strings.REGEX_ID + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> leave(@ApiParam("销售员id") @PathVariable("id") Long id) {
        try {
        channelEmployeeService.leave(id);
        return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("批定公司销售员离职")
    @PostMapping("/leave-for-channel")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> leaveForChannel(@Valid @RequestBody ChannelEmployeeDeleteRequest deleteRequest) {
        try {
            channelEmployeeService.leaveForChannel(deleteRequest);
            return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("销售员二次入职")
    @PutMapping("/reentry/{id:" + Strings.REGEX_ID + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> reentry(@ApiParam("销售员id") @PathVariable("id") Long id) {
        ChannelEmployeeCheckVo checkVo = channelEmployeeService.reentry(id);
        if (!checkVo.isSuccess()) {
            return ApiResult.fail(checkVo.getDescription());
        }
        return ApiResult.ok();
    }

    @ApiOperation("根据销售员代码获取销售员名称，设置主管使用。")
    @GetMapping("/{teamCode:" + Strings.REGEX_STRING + "}/{code:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<ChannelEmployeeVO> getEmployee(@ApiParam("销售分组编码") @PathVariable("teamCode") String teamCode,
                                                    @ApiParam("销售员代码") @PathVariable("code") String code) {
        return ApiResult.ok(channelEmployeeService.getMyEmployee(teamCode, code));
    }

    @ApiOperation("获取当前登录人的项目经理名称、手机号、销售员代码")
    @GetMapping("/manager")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<SimpleManager> getManager() {
        return ApiResult.ok(channelEmployeeService.getManager());
    }


    @ApiOperation("更新销售员执业证编号")
    @PutMapping("/license")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<ChannelEmployeeCheckVo> updateLicenseNo(@ApiParam("销售员代码") @RequestParam(value = "employeeCode") String employeeCode,
                                           @ApiParam("执业证编号") @RequestParam(value = "licenseNo") String licenseNo) {
        return ApiResult.ok(channelEmployeeService.updateLicenseNo(employeeCode, licenseNo));
    }


    @ApiOperation("根据参数获取渠道商代理人信息")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getChannelEmployeeByOrgCodeAndStatus(
            @ApiParam("渠道商代码") @RequestParam(value = "channelCode",required = false) String channelCode,
            @ApiParam("销售员状态 SERVING-有效 INVALID-无效 null查全部") @RequestParam(value = "status" ,required = false ) String status) {
        return ApiResult.ok(channelEmployeeService.getChannelEmployeeByOrgCodeAndStatus(channelCode,status));
    }

    @ApiOperation("导入文件列表")
    @GetMapping("/queryFileUpload")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<FileUploadRecordVO>> queryFileUpload(@ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
                                                                   @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        return ApiResult.ok(channelEmployeeService.queryFileUpload(current,size));
    }
    @ApiOperation("导入文件列表详细信息")
    @GetMapping("/queryFileDetailed")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<BatchFailInfoVO>> queryFileDetailed(@ApiParam("导入文件列表id") @RequestParam("id") long id,@ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
                                                                  @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        return ApiResult.ok(channelEmployeeService.queryFileDetailed(id,current,size));
    }

    @ApiOperation("渠道代理人验证")
    @PostMapping("/check-for-channel")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<CheckEmployeeResultVO> checkForChannel(@Valid @RequestBody CheckChannelEmployeeRequest checkChannelEmployeeRequest) {
        try {
            CheckEmployeeResultVO vo = channelEmployeeService.checkForChannel(checkChannelEmployeeRequest);
            return ApiResult.ok(vo);
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("渠道代理人白名单缓存更新")
    @GetMapping("/refresh-white-cache")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> refreshWhiteCache(@RequestParam("idType") String idType,
                                             @RequestParam("idNo") String idNo) {
        try {
            channelEmployeeService.refreshWhiteCache(idType, idNo);
            return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("渠道代理人缓存删除")
    @GetMapping("/delete-redis-cache")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> deleteRedisCache(@RequestParam("idType") String idType,
                                             @RequestParam("idNo") String idNo) {
        try {
            channelEmployeeService.deleteRedisCache(idType, idNo);
            return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("渠道商代理人模糊查询")
    @GetMapping("/list-page")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelEmployeeVO>> listPage(@RequestParam("codeOrNameLike") String codeOrNameLike,
                                     @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") Long current,
                                     @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") Long size) {
        try {
            if (current == null) {
                current = 0L;
            }
            if (size == null) {
                size = 50L;
            }
            return ApiResult.ok(channelEmployeeService.listPage(codeOrNameLike, current, size));
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("查看中银保信人员开关")
    @GetMapping("/list-turnon")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map> listTurnOn() {

            return ApiResult.ok(channelEmployeeService.listTurnOn());

    }

    @ApiOperation("渠道代理人验证-执业证号")
    @PostMapping("/check-for-channel-license")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<CheckEmployeeResultVO> checkForChannelByLicense(@Valid @RequestBody CheckChannelEmployeeRequest checkChannelEmployeeRequest) {
        try {
            CheckEmployeeResultVO vo = channelEmployeeService.checkForChannelByLicense(checkChannelEmployeeRequest);
            return ApiResult.ok(vo);
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }

    @ApiOperation("渠道代理人缓存删除-执业证号")
    @GetMapping("/delete-redis-cache-license")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> deleteRedisCacheByLicense(@RequestParam("licenseNo") String employeeName,
                                            @RequestParam("employeeName") String licenseNo) {
        try {
            channelEmployeeService.deleteRedisCacheByLicense(employeeName, licenseNo);
            return ApiResult.ok();
        } catch (Exception e) {
            return ApiResult.fail(e.getMessage());
        }
    }
}
