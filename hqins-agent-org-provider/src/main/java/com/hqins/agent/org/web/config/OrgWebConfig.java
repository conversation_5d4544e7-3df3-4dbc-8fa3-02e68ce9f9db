package com.hqins.agent.org.web.config;

import com.google.common.collect.Lists;
import com.hqins.agent.org.web.filter.SystemInitFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2021/6/11
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class OrgWebConfig implements WebMvcConfigurer {

    @Bean
    public FilterRegistrationBean systemInitFilter() {
        FilterRegistrationBean<SystemInitFilter> systemInitFilterRegistrationBean = new FilterRegistrationBean<>();
        systemInitFilterRegistrationBean.setFilter(new SystemInitFilter());
        systemInitFilterRegistrationBean.setUrlPatterns(Lists.newArrayList("/*"));
        systemInitFilterRegistrationBean.setEnabled(true);
        return systemInitFilterRegistrationBean;
    }
}
