package com.hqins.agent.org.web.controller;

import com.alibaba.nacos.api.utils.StringUtils;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.model.enums.NodeLevel;
import com.hqins.agent.org.model.request.OrgTreeRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.ChannelService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Api(tags = "渠道商管理")
@RestController
@RequestMapping("/channels")
@RefreshScope
@Slf4j
public class ChannelController {

    @Autowired
    private ChannelService channelService;


    /**
     * 根据渠道商类型，查询其所有渠道商机构
     * 渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理
     */
    @ApiOperation("根据渠道商类型，查询其所有渠道商机构。 入参说明 -渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理")
    @GetMapping("/find-channel-type-insts")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<OrgVO>> findChannelTypeInsts(
            @ApiParam("渠道商类型") @RequestParam(value = "channelType", required = true) String channelType) {
        log.info("find-channel-type-insts:{}", channelType);
        return ApiResult.ok(channelService.findChannelTypeInsts(channelType));
    }

    /**
     * 配置渠道商logo
     */
    @ApiOperation("配置渠道商logo")
    @PostMapping("/config-logo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> configLogo(@Valid @RequestBody ChannelConfigLogoRequest request) {
        channelService.configLogo(request);
        return ApiResult.ok();
    }

    @ApiOperation("查询渠道商LOGO图")
    @GetMapping("/config-logo")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> getLogo(@RequestParam String employeeCode) {
        if (StringUtils.isEmpty(employeeCode)){
            return ApiResult.ok();
        }
        return ApiResult.ok(channelService.getLogo(employeeCode));
    }

    /**
     * 分页查询渠道商
     * 有管理权限的且生效状态的渠道商
     */
    @ApiOperation("分页查询道商")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelVO>> listMy(
            @ApiParam("渠道商代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("渠道商名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(channelService.listMy(queryRequest));
    }

    /**
     * 获取渠道商简化版，只展示名称编码
     * 有管理权限的且生效状态的渠道商
     * 下拉框中使用
     */
    @ApiOperation("获取有管理权限的渠道商，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> listMySimple(
            @ApiParam("渠道商代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("渠道商名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam("每页显示记录数 小于0则返回全部数据") @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();

        return ApiResult.ok(channelService.listMySimple(queryRequest));
    }
    /**
     * 获取渠道商简化版，只展示名称编码
     * 有管理权限的且生效状态的渠道商
     * 下拉框中使用
     */
    @ApiOperation("获取有管理权限的渠道商，简化版下拉框H5使用")
    @GetMapping("/H5/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> frontListMySimple(
            @ApiParam("渠道商代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("渠道商名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam("每页显示记录数 小于0则返回全部数据") @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();
        RequestContextHolder.setAdminAppIdLocal(1L);
        RequestContextHolder.setStaffIdLocal(1L);
        return ApiResult.ok(channelService.listMySimple(queryRequest));
    }

    /**
     * 获取全量渠道商简化版，只展示名称编码
     * 下拉框中使用
     */
    @ApiOperation("获取所有的渠道商，简化版下拉框里使用")
    @GetMapping("/all")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleNodeVO>> listAllSimple(
            @ApiParam("渠道商代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("渠道商名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam("每页显示记录数 小于0则返回全部数据") @RequestParam(value = "size", required = false, defaultValue = "10") long size) {

        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .code(code).name(name)
                .current(Math.max(current, 1L)).size(size).build();

        return ApiResult.ok(channelService.listAllSimple(queryRequest));
    }

    /**
     * 获取有效的组织机构树,包含渠道商、渠道商销售机构、销售团队
     * 数据授权时使用
     */
    @ApiOperation("获取有效的组织机构树,包含渠道商、渠道商销售机构、销售团队")
    @GetMapping("/tree")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleTreeNodeVO>> tree(
            @ApiParam("渠道商代码") @RequestParam(value = "channelCode", required = false) String channelCode,
            @ApiParam("销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("要查到哪一级：TOP-渠道商；ORG-机构, 默认为ORG") @RequestParam(value = "nodeLevel", required = false, defaultValue = "ORG") NodeLevel nodeLevel) {

        OrgTreeRequest treeRequest = OrgTreeRequest.builder()
                .topCode(channelCode).orgCode(orgCode).nodeLevel(nodeLevel).build();

        return ApiResult.ok(channelService.getAllTree(treeRequest));
    }

    /**
     * 获取全量渠道商简化版，只展示名称编码
     * 下拉框中使用
     */
    @ApiOperation("获取所有的渠道商，简化版下拉框里使用")
    @GetMapping("/list-1st-5th-orgs")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Collection<SimpleChannelOrgVO>> listAllSimple(
            @ApiParam("团队代码") @RequestParam(value = "teamCode", required = true) String teamCode) {

        return ApiResult.ok(channelService.list1st5thOrgs(teamCode));
    }

    /**
     * 获取个领导下所有的员工以及所属的渠道
     */
    @ApiOperation("获取团队长下所有的员工以及所属的渠道")
    @GetMapping("/list-sub-emp-channel")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<ChannelOrgVO>> listSubEmpChannel(
            @ApiParam("团队leaderCode") @RequestParam(value = "teamLeaderCode") String teamLeaderCode,
            @ApiParam("是否包含团队长") @RequestParam(value = "includeLeader", required = false) boolean includeLeader
    ) {
        return ApiResult.ok(channelService.listSubEmpChannel(teamLeaderCode, includeLeader));
    }

    @ApiOperation("判断团队是否为ifp")
    @GetMapping("/ifpTeam")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Boolean> ifpTeam(
            @ApiParam("团队编码") @RequestParam(value = "teamCode") String teamCode
    ) {
        return ApiResult.ok(channelService.ifpTeam(teamCode));
    }
}
