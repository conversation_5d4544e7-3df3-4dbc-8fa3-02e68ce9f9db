package com.hqins.agent.org.dao;


import com.hqins.agent.org.model.vo.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;


public interface PerformanceDao {


    BigDecimal getCurrentMonthIncomeInfo(String empCode);

    List<CommissionItemVO> getPreSettleCommissionVOList(String currentLoginEmpCode);

    List<PersonalIncomeVO> getMonthsSettleIncomeInfo(String currentLoginEmpCode, List<String> monthList);

    PersonalIncomeVO getSettleIncomeAmountInfo(String currentLoginEmpCode, String settleMonth, String settleBatchId);

    List<PerformancePolicyVO> getSettlePolicyInfo(String currentLoginEmpCode, String commissionItem, String settleMonth, String settleBatchId, String versionType, String queryType);

    List<PerformancePolicyVO> getPreSettlePolicyInfo(String currentLoginEmpCode, String commissionItem, String settleMonth, boolean firstPreMonthFlag, String versionId, String queryType);

    List<CommissionItemVO> getSettleCommissionVOList(String currentLoginEmpCode, String settleMonth);

    PersonalVO getEmpCr(String empCode, String performanceMonth);

    BigDecimal getEmpPerformanceFycAmount(String empCode, String performanceMonth, List<String> esPolicyNoList);

    List<PerformancePolicyVO> getPerformancePolicyInfo(String empCode, String performanceMonth, List<String> wtPolNoList, String commissionItem, String queryType);

    List<PerformancePolicyVO> getPerformanceMonthPolicyInfo(String empCode, String performanceMonth, List<String> wtPolNoList, String commissionItem, String queryType);

    List<PerformancePolicyVO> getPerformancePolicyList(String empCode, String qureyString, List<String> wtPolNoList);

    List<PersonalVO> getTeamCurrentPersonList(String saleTeamCode);

    List<PersonalVO> getTeamCurrentPersonQuitList(String saleTeamCode);

    TeamManageVO getCommissionTeamInfo(String commissionMonth, String saleTeamCode, String empCode);

    List<PersonalVO> getPerformanceListByCodeList(List<String> agentCodeList, String performanceMonth, List<String> wtPolNoList);

    List<PerformanceTrendVO> getTeamTrendPerformanceList(String saleTeamCode, List<String> agentCodeList, List<String> wtPolNoList, String type, String startDateString, String endDateString);

    List<PerformanceTrendVO> getTeamActiveTrendInfoList(String saleTeamCode, List<String> commissionMonthList, List<String> agentCodeList);

    List<PersonalVO> getPerformancemonthListByCodeList(List<String> agentCodeList, String performanceMonth, List<String> wtPolNoList);

    List<TeamPerformanceVO> getRangePerformanceVOList(String saleTeamCode, List<String> agentCodeList, List<String> wtPolNoList, Date startDate, Date endDate, String paymentYears, String type);

    List<TeamPerformanceVO> getAgentRankSeqInfoList(List<String> agentCodeList);

    List<PerformancePolicyVO> getPerformancePolicyListById(List<String> policyIdList);

    List<PolicyCommissionVO> getPolicyCommissionList(String policyNo);

    List<TeamPerformanceVO> getTeamCrInfoByAgentCodeList(String saleTeamCode, String performanceMonth);

    List<PersonalVO> getTeamMonthPersonQuitList(String saleTeamCode, LocalDate lastDayOfMonth);

    List<PerformancePolicyVO> getTeamPerformancePolicyList(List<String> agentCodeList, String performanceMonth, List<String> wtPolNoList);

    List<PerformancePolicyVO> getTeamPersonalPerformancePolicyList(String agentCode, Date startDate, Date endDate, List<String> wtPolNoList, String paymentYears);

    List<PerformancePolicyVO> getTeamProductPerformancePolicyList(List<String> agentCodeList, String riskCode, Date startDate, Date endDate, List<String> wtPolNoList, String paymentYears);

    List<PerformancePolicyVO> getTeamRankSeqPerformancePolicyList(List<String> agentCodeList, String rankSeqCode, Date startDate, Date endDate, List<String> wtPolNoList, String paymentYears);

    PerformanceVO getMonthPersonalPerformance(String empCode, String performanceMonth, List<String> wtPolNoList, List<String> appntNoList);

    List<String> getPersonalAppntNoList(String empCode, String queryMonth, List<String> wtPolNoList, List<String> appntNoList);

    PerformanceVO getYearPersonalPerformance(String empCode, String queryYear, List<String> wtPolNoList, List<String> appntNoList);

    PerformanceVO getMonthTeamPerformance(List<String> agentCodeList, String queryMonth, List<String> wtPolNoList, List<String> appntNoList);

    PerformanceVO getYearTeamPerformance(List<String> agentCodeList, String queryYear, List<String> wtPolNoList, List<String> appntNoList);

    List<TeamPerformanceVO> getTeamCr13(List<String> agentCodeList, String performanceMonth);

    List<String> getOrgListVersionTypeList(List<String> orgList);

    List<String> getVersionTypeListByEmpTeam(String employeeCode, List<String> list);

    List<String> getVersionTypeListByEmpInst(String employeeCode);

    String getAgentSaleTeamCode(String employeeCode, String commissionMonth);

    String getTeamVersionType(String employeeCode, String commissionMonth);

    Integer getAgentIncreaseNum(String empCode, String actualMonth, String queryYear);

    Integer getTeamIncreaseNum(String teamCode, String actualMonth, String queryYear);

    List<SimpleNodeVO> getSelfPolicyList();

    List<MarkDetailVO> getSupervisorCrList(List<String> instCodeList, String teamCode, String commissionMonth);

    List<SupervisorPerformanceDetailNumberVO> getSupervisorIncreaseVOList(Map map);
}



