package com.hqins.agent.org.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.model.vo.HoloDaDetailUwVo;
import com.hqins.agent.org.service.DaDetailService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RequestMapping("/hologres")
@Api(tags = "清单查询")
@RestController
@Slf4j
public class DaDetailController {

    @Autowired
    private DaDetailService daDetailService;


    @ApiOperation("查询全渠道业绩清单列表")
    @PostMapping("/getDaDetailList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<HoloDaDetailUwVo>> selectDaDetailUwList(@RequestBody Map request) {
        if(request.get("BEGIN") == null || request.get("COUNT") == null){
            request.put("BEGIN", 0);
            request.put("COUNT", 15);
        }
        List<HoloDaDetailUwVo> result = daDetailService.selectDaDetailUwList(request);
        log.info(JSONObject.toJSONString(result));
        return ApiResult.ok(result);
    }

}
