package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.mapper.exms.TbpartassignmanagerMapper;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.DataType;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.ChannelOrgVO;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.agent.org.service.ChannelOrgService;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class ChannelOrgServiceImpl implements ChannelOrgService {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;

    @Override
    public PageInfo<ChannelOrgVO> listMy(OrgQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<BaseInst> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_CHANNEL, queryRequest, myDataAccess.getChannelOrgCodes());
        } else {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        }
        PageInfo<ChannelOrgVO> page = PageUtil.convert(p, inst -> PartnerConverter.instToChannelOrgVO(inst, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
        page.setRecords(setCustManageCode(page.getRecords()));
        return page;
    }

    @Override
    public List<TreeNodeVO> listMySimple(OrgQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<BaseInst> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_CHANNEL, queryRequest, myDataAccess.getChannelOrgCodes());
        } else {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        }

        List<TreeNodeVO> treeNodeVOS = BeanCopier.copyList(p.getRecords(), PartnerConverter::instToTreeNodeVo);
        for (TreeNodeVO vo : treeNodeVOS) {
            vo.setDataType(DataType.CHANNEL_ORG);
        }
        return TreeUtils.buildTrees(treeNodeVOS);
    }

    @Override
    public List<TreeNodeVO> listAllSimple(OrgQueryRequest queryRequest) {
        Page<BaseInst> p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_CHANNEL, queryRequest, null);
        List<TreeNodeVO> treeNodeVOS = BeanCopier.copyList(p.getRecords(), PartnerConverter::instToTreeNodeVo);
        for (TreeNodeVO vo : treeNodeVOS) {
            vo.setDataType(DataType.CHANNEL_ORG);
        }
        return TreeUtils.buildTrees(treeNodeVOS);
    }

    @Override
    public ChannelOrgVO getChannelOrgVOByOrgCode(String orgCode) {
        BaseInst inst = cacheService.getAllBaseInstsMap().get(orgCode);
        if (inst == null) {
            return null;
        }
        return PartnerConverter.instToChannelOrgVO(inst, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid()));
    }


    /**
     * 设置项目经理编码、经理名称
     *
     * @param list
     * @return
     */
    private List<ChannelOrgVO> setCustManageCode(List<ChannelOrgVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        Set<String> orgCodes = StreamEx.of(list).map(ChannelOrgVO::getCode).toSet();
        List<Tbpartassignmanager> tbpartassignmanagers = tbpartassignmanagerMapper.selectList(new LambdaQueryWrapper<Tbpartassignmanager>()
                .in(Tbpartassignmanager::getMerchantOrgCode, orgCodes).eq(Tbpartassignmanager::getMainManagerFlag, "0"));
        Map<String, Tbpartassignmanager> custManageCodeMap = StreamEx.of(tbpartassignmanagers).toMap(Tbpartassignmanager::getMerchantOrgCode, Function.identity(), (key1, key2) -> key2);
        for (ChannelOrgVO vo : list) {
            Tbpartassignmanager manager = custManageCodeMap.get(vo.getCode());
            if (manager != null) {
                vo.setLeader(manager.getCustManagerName());
                vo.setLeaderCode(manager.getCustManagerCode());
            }
        }
        return list;
    }
}
