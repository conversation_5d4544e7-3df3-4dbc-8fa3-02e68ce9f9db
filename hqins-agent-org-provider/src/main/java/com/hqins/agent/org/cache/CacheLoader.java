package com.hqins.agent.org.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.dao.mapper.exms.*;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.iips.TbepartnerMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelTeamMapper;
import com.hqins.agent.org.dao.mapper.settle.IFPDigitalScoreMapper;
import com.hqins.agent.org.model.vo.DigitalAllDataVO;
import com.hqins.agent.org.model.vo.DigitalScoreDetailVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @since 2021/6/11
 */
@Component
@Slf4j
public class CacheLoader extends TimerTask {

    @Autowired
    private CacheService cacheService;
    @Autowired
    private TbsaleteamMapper tbsaleteamMapper;
    @Autowired
    private BaseInstMapper baseInstMapper;
    @Autowired
    private TbepartnerMapper tbepartnerMapper;
    @Autowired
    private ChannelTeamMapper channelTeamMapper;
    @Autowired
    private TbempMapper tbempMapper;
    @Autowired
    private RankDefMapper rankDefMapper;
    @Autowired
    private RankSequMapper rankSequMapper;
    @Autowired
    private TbpartassignmanagerMapper tbpartassignmanagerMapper;

    @Autowired
    private IFPDigitalScoreMapper ifpDigitalScoreMapper;

    private static final int PAGE_SIZE = 1000;

    private AtomicInteger errorCount = new AtomicInteger(0);

    private volatile boolean initCacheCompleted = false;

    private static final int MAX_ERROR_COUNT = 3;

    public boolean isInitCacheCompleted() {
        return initCacheCompleted;
    }

    @Override
    public void run() {
        log.info("开始初始化缓存信息");
        reset();
        refresh();
    }

    private void reset() {
        errorCount.set(0);
    }

    /**
     * 手动刷新所有渠道商-团队
     */
    public void refreshChannelTeams() {
        new Thread("refresh-channel-teams-thread") {
            @Override
            public void run() {
                List<ChannelTeam> channelTeams = loadByPage(channelTeamMapper, ChannelTeam.class);
                cacheService.setAllChannelTeams(channelTeams);
            }
        }.start();
    }

    private void refresh() {
        log.info("cache refresh start ...");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        //自底向上的顺序依次加载数据：团队->机构->合伙人或渠道商
        List<Tbsaleteam> tbsaleteams = loadByPage(tbsaleteamMapper, Tbsaleteam.class);
        if(CollUtil.isNotEmpty(tbsaleteams)){
            List<String> saleTeamCodes = tbsaleteams.stream()
                    .map(Tbsaleteam::getSaleteamcode)  // 提取 saleteamcode 字段
                    .filter(Objects::nonNull)          // 过滤掉 null 值
                    .collect(Collectors.toList());     // 收集结果到 List
            if(CollUtil.isNotEmpty(saleTeamCodes)){
                log.info("缓存中所有的销售团队码值:{}", JSONObject.toJSONString(saleTeamCodes));
            }
        }
        cacheService.setAllTbsaleteams(tbsaleteams);
        List<ChannelTeam> channelTeams = loadByPage(channelTeamMapper, ChannelTeam.class);
        cacheService.setAllChannelTeams(channelTeams);
        List<BaseInst> baseInsts = loadByPage(baseInstMapper, BaseInst.class);
        cacheService.setAllBaseInsts(baseInsts);
        List<Tbepartner> tbepartners = loadByPage(tbepartnerMapper, Tbepartner.class);
        cacheService.setAllTbepartners(tbepartners);
        List<Tbemp> tbemps = tbempMapper.queryEmployee(null);
        cacheService.setAllTbempMap(tbemps);
        List<RankDef> rankDefList = rankDefMapper.getAllUsefulRankDef();
        cacheService.setAllRankDefMap(rankDefList);
        List<RankSequ> rankSequList = rankSequMapper.getAllUsefulRankSequ();
        cacheService.setAllRankSequMap(rankSequList);
        List<Tbpartassignmanager> allAssignManagers = loadByPage(tbpartassignmanagerMapper, Tbpartassignmanager.class);
        cacheService.setAllAssignManager(allAssignManagers);

        //tbempMapper中已经查出了所有有效的非虚拟代理人的代理人，此处直接复用，实现过滤无效和虚拟的代理人,下述明细同理
        // 测试时先走不缓存，等正式上线时在走缓存
        //注意：下述两个缓存是依赖于上方tbemps和tbsaleteams的
        // 20240227 由于数据量过大，触发频繁fullgc 暂时不缓存
//        List<DigitalAllDataVO> digitalAllDataVOList = ifpDigitalScoreMapper.getAllMonthAndScoreData(new ArrayList<>(cacheService.getAllTbempMap().values()).stream().map(Tbemp::getEmpcode).collect(Collectors.toList()));
//        cacheService.setAllDigitalDataMap(digitalAllDataVOList);
//        List<DigitalScoreDetailVO> digitalDetailList = ifpDigitalScoreMapper.getScoreDetail(new ArrayList<>(cacheService.getAllTbempMap().values()).stream().map(Tbemp::getEmpcode).collect(Collectors.toList()));
//        cacheService.setDigitalDetailMap(digitalDetailList);
        //中银保信校验开头
        cacheService.resetTurnOn();
        //设置首次缓存初始化完成的标识
        if (!initCacheCompleted) {
            initCacheCompleted = true;
        }

        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        log.info("cache refresh complete, it costs {} millis", totalTimeMillis);
    }

    private <T> List<T> loadByPage(BaseMapper<T> baseMapper, Class<T> clazz) {
        List<T> allList = Lists.newArrayList();
        long current = 1L;
        while (true) {
            DataLoadResult<T> result = loadDataMaybeRetry(baseMapper, current, clazz);
            if (!result.getHasError()) {
                Page<T> page = result.getPage();
                if (CollectionUtils.isEmpty(page.getRecords())) {
                    break;
                }
                allList.addAll(page.getRecords());
            }
            current++;
            if (errorCount.get() >= MAX_ERROR_COUNT) {
                log.error("network is so bad, please check network....");
                //TODO 通常系统运维人员之类的什么事儿。。。。
                break;
            }
        }
        return allList;
    }

    /**
     * 分页查询,排除偶发性网络拥塞、闪断或数据库查询错误对业务的影响，即可一轮更新中，共允许最多3次的出错（即共允许3次重试查询的机构）
     *
     * @param baseMapper mapper实例
     * @param current    当前页码
     * @param clazz      要查询的实体类
     * @param <T>        泛型类型
     * @return 当前页数据
     */
    private <T> DataLoadResult<T> loadDataMaybeRetry(BaseMapper<T> baseMapper, long current, Class<T> clazz) {
        DataLoadResult<T> result = new DataLoadResult<>();
        Page<T> page = null;
        boolean hasError = false;
        try {
            page = baseMapper.selectPage(new Page<>(current, PAGE_SIZE), null);
            log.info("load the {}th page data of {} success", current, clazz.getSimpleName());
        } catch (Exception e) {
            hasError = true;
            log.error("load the {}th page data of {} error", current, clazz.getSimpleName(), e);
        }
        if (hasError && errorCount.incrementAndGet() < MAX_ERROR_COUNT) {
            try {
                page = baseMapper.selectPage(new Page<>(current, PAGE_SIZE), null);
                /**
                 * 当前页重试查询成功，需要重置hasError和errorCount的值
                 */
                hasError = false;
                errorCount.decrementAndGet();
                log.info("load the {}th page data of {} success", current, clazz.getSimpleName());
            } catch (Exception e) {
                log.error("load the {}th page data of {} error", current, clazz.getSimpleName(), e);
            }
        }
        result.setHasError(hasError);
        result.setPage(page);
        return result;
    }

    @Data
    public static class DataLoadResult<T> {
        private Page<T> page;
        private Boolean hasError;
    }

}
