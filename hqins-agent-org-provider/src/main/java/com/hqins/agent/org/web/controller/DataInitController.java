package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.InitEmployeeAddRequest;
import com.hqins.agent.org.model.vo.InitResultVo;
import com.hqins.agent.org.service.DataProcessService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.web.RequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/26
 * @Description
 */
@Api(tags = "数据初始化")
@RestController
@RequestMapping("/data-init")
@RefreshScope
@Slf4j
@Validated
public class DataInitController {
    @Autowired
    private DataProcessService dataProcessService;

    @ApiOperation("加载销管机构数据到本地，并处理（初始化团队、更新名称、监测状态的变化）")
    @PutMapping("/xg/orgs")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> downloadProcessOrg(
            @ApiParam("测试标记：true-是 false-否") @RequestParam(value = "testFlag", required = false, defaultValue = "false") String testFlag
    ) {
        Long staffId = RequestContextHolder.getStaffId();
        dataProcessService.downloadProcessOrg(null == staffId ? -1L : staffId, testFlag);
        return ApiResult.ok();
    }

    @ApiOperation("加载销管用户数据到本地,并处理（创建um账号、停用、启用）")
    @PutMapping("/xg/employees")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> downloadProcessEmployee(
            @ApiParam("测试标记：true-是 false-否") @RequestParam(value = "testFlag", required = false, defaultValue = "false") String testFlag
    ) {
        Long staffId = RequestContextHolder.getStaffId();
        dataProcessService.downloadProcessEmployee(null == staffId ? -1L : staffId, testFlag);
        return ApiResult.ok();
    }

    @ApiOperation("加载销管用户数据上报到神策埋点")
    @PutMapping("/xg/upload-sensors")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> uploadSensors() {
        dataProcessService.uploadSensors();
        return ApiResult.ok();
    }

    @ApiOperation("新增销售员")
    @PostMapping("/employee")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<InitResultVo>> saveEmployeeBatch(@Valid @RequestBody List<InitEmployeeAddRequest> request) {
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request), new ApiException("同步信息不能为空"));
        Long staffId = RequestContextHolder.getStaffId();
        List<InitResultVo> list = dataProcessService.saveEmployeeBatch(request, null == staffId ? -1L : staffId);
        return ApiResult.ok(list);

    }
}
