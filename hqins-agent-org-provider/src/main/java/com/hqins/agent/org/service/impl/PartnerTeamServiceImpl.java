package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.request.PartnerTeamQueryRequest;
import com.hqins.agent.org.model.request.TopQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.agent.org.service.PartnerTeamService;
import com.hqins.agent.org.service.SupervisorEmployeeService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class PartnerTeamServiceImpl implements PartnerTeamService {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private TbempMapper tbempMapper;
    @Autowired
    private SupervisorEmployeeService supervisorEmployeeService;

    @Override
    public PageInfo<PartnerTeamTreeNodeVO> listMyTree(PartnerTeamQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode()) || !StringUtil.isBlank(queryRequest.getOrgName())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getPartnerOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getPartnerOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }

        //获取经过筛选后的合伙人编码
        Set<String> topCodes = null;
        if (!StringUtil.isBlank(queryRequest.getPartnerCode())) {
            //合伙人编码不为空
            topCodes = new HashSet<>();
            topCodes.add(queryRequest.getPartnerCode());
        } else if (!StringUtil.isBlank(queryRequest.getPartnerName())) {
            //合伙人名称不为空则去数据库里查出交集
            List<Tbepartner> tbepartners = cacheService.selectTbepartners(AppConsts.CPTYPE_PARTNER, TopQueryRequest.builder().name(queryRequest.getPartnerName()).build(), null);
            topCodes = StreamEx.of(tbepartners).map(Tbepartner::getCompanycode).toSet();
        }

        //处理支持团队级别多选的情况
        List<String> levelConvert = new ArrayList<>();
        if (StringUtil.isNotEmpty(queryRequest.getTeamLevel())) {
            String[] teamLevels = queryRequest.getTeamLevel().split(",");
            for (String string : teamLevels) {
                if (TeamLevel.AREA.name().equals(string)) {
                    levelConvert.add("03");
                }
                if (TeamLevel.DEPT.name().equals(string)) {
                    levelConvert.add("02");
                }
                if (TeamLevel.TEAM.name().equals(string)) {
                    levelConvert.add("01");
                }
            }
        }
        List<Tbsaleteam> tbsaleteamList = cacheService.getAllTbsaleteams();
        List<Tbsaleteam> filterList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(levelConvert)){
            //过滤出符合条件的数据
           filterList =  tbsaleteamList.stream().filter(t -> levelConvert.contains(t.getTeamlevel())).collect(Collectors.toList());
           tbsaleteamList = filterList;
        }

        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : tbsaleteamList) {
            if ("ENABLED".equals(queryRequest.getStatus()) && !"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (null != queryRequest.getLevel()) {
                if (TeamLevel.AREA.equals(queryRequest.getLevel()) && !"03".equals(t.getTeamlevel())) {
                    continue;
                }
                if (TeamLevel.DEPT.equals(queryRequest.getLevel()) && !"02".equals(t.getTeamlevel())) {
                    continue;
                }
                if (TeamLevel.TEAM.equals(queryRequest.getLevel()) && !"01".equals(t.getTeamlevel())) {
                    continue;
                }
            }
            if (orgCodes != null && !orgCodes.isEmpty() && !orgCodes.contains(t.getInstCode())) {
                continue;
            }
            if (topCodes != null && !topCodes.isEmpty() && !topCodes.contains(t.getCompanycode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getCode()) && !queryRequest.getCode().equals(t.getSaleteamcode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getName()) && !t.getSaleteamname().contains(queryRequest.getName())) {
                continue;
            }

            list.add(t);
        }
        Page<Tbsaleteam> p = PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());
        //转换数据结构
        PageInfo<PartnerTeamTreeNodeVO> result = PageUtil.convert(p, PartnerConverter::tbsaleteamToPartnerTeamTreeNodeVO);
        if (result.getRecords() == null || result.getRecords().isEmpty()) {
            return result;
        }
        for (PartnerTeamTreeNodeVO vo : result.getRecords()) {
            if ("01".equals(vo.getLevel())) {
                continue;
            }
            vo.setChildren(getPartnerTeamTreeNodeVO(vo.getCode()));
        }
        return result;
    }

    @Override
    public PageInfo<SimpleTeamTreeNodeVO> listMyTreeSimple(PartnerTeamQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode()) || !StringUtil.isBlank(queryRequest.getOrgName())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getPartnerOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getPartnerOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new PageInfo(queryRequest.getCurrent(), queryRequest.getSize());
            }
        }


        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
            if ("ENABLED".equals(queryRequest.getStatus()) && !"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (orgCodes != null && !orgCodes.isEmpty() && !orgCodes.contains(t.getInstCode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getCode()) && !queryRequest.getCode().equals(t.getInstCode())) {
                continue;
            }
            if (!StringUtil.isBlank(queryRequest.getName()) && !t.getSaleteamname().contains(queryRequest.getName())) {
                continue;
            }
            list.add(t);
        }
        Page<Tbsaleteam> p = PageUtil.getPage(list, queryRequest.getCurrent(), queryRequest.getSize());
        //转换数据结构
        PageInfo<SimpleTeamTreeNodeVO> result = PageUtil.convert(p, PartnerConverter::tbsaleteamToSimpleTeamTreeNodeVO);
        if (result.getRecords() == null || result.getRecords().isEmpty()) {
            return result;
        }
        result.setRecords(TreeUtils.buildTrees(result.getRecords()));
        return result;
    }

    @Override
    public Set<String> selectAllChildCodes(String teamCode, String teamName, MyDataAccessVO myDataAccess) {

        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
            if (!"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (!StringUtil.isBlank(teamCode) && !teamCode.equals(t.getSaleteamcode())) {
                continue;
            }
            if (!StringUtil.isBlank(teamName) && !teamName.equals(t.getSaleteamname())) {
                continue;
            }
            if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin()) && !myDataAccess.getPartnerOrgCodes().contains(t.getInstCode())) {
                continue;
            }
            list.add(t);
        }

        Set<String> allTeamCodes = new HashSet<>();
        //转换数据结构
        for (Tbsaleteam vo : list) {
            allTeamCodes.add(vo.getSaleteamincode());
            if ("01".equals(vo.getTeamlevel())) {
                continue;
            }
            allTeamCodes.addAll(getChildTeamCodes(vo.getSaleteamcode(), allTeamCodes));
        }
        return allTeamCodes;
    }

    @Override
    public List<TeamVO> listManagePartnerTeams(String employeeCode) {
        List<Map<String, Object>> lst = tbempMapper.list(employeeCode);
        if (CollectionUtils.isEmpty(lst)){
            EmployeeVO employeeVO = supervisorEmployeeService.getSupervisorEmployee(employeeCode);
            AssertUtil.isTrue(employeeVO != null, new ApiException("销售员不存在"));
            return new ArrayList<>(0);
        }
        AssertUtil.isTrue(!lst.isEmpty(), new ApiException("销售员不存在"));
        String empIncode = StringUtil.toString(lst.get(0).get("empInCode"));
        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
            if (!"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (!empIncode.equals(t.getEmpincode())) {
                continue;
            }
            list.add(t);
        }
        List<TeamVO> result = BeanCopier.copyList(list, tbsaleteam -> PartnerConverter.tbsaleteamToTeamVO(tbsaleteam, AgentOrgType.PARTNER));
        return result;
    }

    private Set<String> getChildTeamCodes(String code, Set<String> allTeamCodes) {
        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
            if (!"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (!code.equals(t.getSupersaleteamcode())) {
                continue;
            }
            list.add(t);
        }
        for (Tbsaleteam vo : list) {
            if (!"01".equals(vo.getTeamlevel())) {
                continue;
            }
            allTeamCodes.addAll(getChildTeamCodes(vo.getSaleteamcode(), allTeamCodes));
        }
        return StreamEx.of(list).map(Tbsaleteam::getSaleteamincode).toSet();
    }


    /**
     * 递归获取团队树
     *
     * @param parentCode
     * @return
     */
//    private List<SimpleTeamTreeNodeVO> getSimpleTeamTreeNodeVO(String parentCode, String status) {
//        List<Tbsaleteam> list = new ArrayList<>();
//        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
//            if ("ENABLED".equals(status) && !"00".equals(t.getSaleteamstatus())) {
//                continue;
//            }
//            if (!StringUtil.isBlank(parentCode) && !parentCode.equals(t.getSupersaleteamcode())) {
//                continue;
//            }
//            list.add(t);
//        }
//        List<SimpleTeamTreeNodeVO> treeNodeVOs = BeanCopier.copyList(list, tbsaleteam -> PartnerConverter.tbsaleteamToSimpleTeamTreeNodeVO(tbsaleteam));
//        for (SimpleTeamTreeNodeVO vo : treeNodeVOs) {
//            if (TeamLevel.TEAM.equals(vo.getLevel())) {
//                continue;
//            }
//            vo.setChildren(getSimpleTeamTreeNodeVO(vo.getCode(), status));
//        }
//        return treeNodeVOs;
//    }

    /**
     * 递归获取团队树
     *
     * @param parentCode
     * @return
     */
    private List<PartnerTeamTreeNodeVO> getPartnerTeamTreeNodeVO(String parentCode) {
        List<Tbsaleteam> list = new ArrayList<>();
        for (Tbsaleteam t : cacheService.getAllTbsaleteams()) {
            if (!"00".equals(t.getSaleteamstatus())) {
                continue;
            }
            if (!StringUtil.isBlank(parentCode) && !parentCode.equals(t.getSupersaleteamcode())) {
                continue;
            }
            list.add(t);
        }
        List<PartnerTeamTreeNodeVO> treeNodeVOs = BeanCopier.copyList(list, PartnerConverter::tbsaleteamToPartnerTeamTreeNodeVO);
        for (PartnerTeamTreeNodeVO team : treeNodeVOs) {
            if (TeamLevel.TEAM.equals(team.getLevel())) {
                continue;
            }
            team.setChildren(getPartnerTeamTreeNodeVO(team.getCode()));
        }
        return treeNodeVOs;
    }

    @Override
    public List<SaleTeamVO> listSaleTeam(String instCode, TeamLevel teamLevel) {
        List<Tbsaleteam> allTbsaleteams = cacheService.getAllTbsaleteams();
        /**
         * 过滤当前机构 且有效的
         */
        List<Tbsaleteam> filterSaleTeamList = allTbsaleteams.stream().filter(saleTeam ->
                instCode.equals(saleTeam.getInstCode()) && AppConsts.STR_00.equals(saleTeam.getSaleteamstatus())).collect(Collectors.toList());

        if (org.springframework.util.CollectionUtils.isEmpty(filterSaleTeamList)) {
            return null;
        }

        List<Tbsaleteam> tbsaleteams = generateResult(filterSaleTeamList, teamLevel);
        if (org.springframework.util.CollectionUtils.isEmpty(tbsaleteams)) {
            TeamLevel nextLevel = teamLevel == TeamLevel.AREA ? TeamLevel.DEPT : TeamLevel.TEAM;
            tbsaleteams = generateResult(filterSaleTeamList, nextLevel);
        }

        if (CollectionUtils.isEmpty(tbsaleteams)) {
            return null;
        }
        List<SaleTeamVO> result = BeanCopier.copyList(tbsaleteams, tbsaleteam -> PartnerConverter.tbsaleteamToSaleTeamVO(tbsaleteam));
        for (SaleTeamVO vo : result) {
            if (StringUtils.isNotEmpty(vo.getEmpInCode())) {
                Tbemp tbemp = cacheService.getAllTbempInCodeMap().get(vo.getEmpInCode());
                //过滤有效
                if(tbemp != null && "01".equals(tbemp.getEmpstatus())) {
                    vo.setEmpCode(tbemp.getEmpcode());
                    vo.setEmpName(tbemp.getEmpname());
                }
            }
        }
        return result;
    }

    private List<Tbsaleteam> getAllAreaDeptTeamList() {
        return cacheService.getAllTbsaleteams().stream().filter(saleTeam ->
            StringUtils.isNotEmpty(saleTeam.getTeamlevel())
                    && ("03".equals(saleTeam.getTeamlevel()) || "02".equals(saleTeam.getTeamlevel()) || "01".equals(saleTeam.getTeamlevel()))
        ).collect(Collectors.toList());
    }

    @Override
    public List<SaleTeamVO> listTopSaleTeam(String instCode) {
        List<Tbsaleteam> allTbsaleteams = getAllAreaDeptTeamList();
        /**
         * 过滤当前机构 且有效的,且没有父级 组的 销售 组信息
         */
        List<Tbsaleteam> filterSaleTeamList = allTbsaleteams.stream().filter(saleTeam ->
                instCode.equals(saleTeam.getInstCode())
                && isTopTeam(saleTeam, allTbsaleteams)
        ).collect(Collectors.toList());

        if (org.springframework.util.CollectionUtils.isEmpty(filterSaleTeamList)) {
            return null;
        }

        return BeanCopier.copyList(filterSaleTeamList, tbsaleteam -> PartnerConverter.tbsaleteamToSaleTeamVO(tbsaleteam));
    }

    /**
     * 判断是否为顶部team逻辑
     * 1、有效
     * 2、父节点为空
     * 3、爷节点为非有效
     * @param saleTeam
     * @param allTbsaleteams
     * @return
     */
    private boolean isTopTeam(Tbsaleteam saleTeam, List<Tbsaleteam> allTbsaleteams) {
        if (!AppConsts.STR_00.equals(saleTeam.getSaleteamstatus())){
            return false;
        }
        //无上级
        if (StringUtils.isEmpty(saleTeam.getSupersaleteamcode())) {
            return true;
        }
        //或者上级的状态为 非有效 20230830去除该部分逻辑
//        String superSaleTeamCode = saleTeam.getSupersaleteamcode();
//        for (Tbsaleteam team : allTbsaleteams) {
//            if (superSaleTeamCode.equals(team.getSaleteamcode())) {
//                if (!AppConsts.STR_00.equals(team.getSaleteamstatus())) {
//                    return true;
//                }
//                break;
//            }
//        }
        return false;
    }

    private List<Tbsaleteam> generateResult(List<Tbsaleteam> filterSaleTeamList, TeamLevel teamLevel) {
        List<Tbsaleteam> resultList = Lists.newArrayList();
        for (Tbsaleteam tbsaleteam : filterSaleTeamList) {
            String teamlevel = tbsaleteam.getTeamlevel();
            if (StringUtils.isEmpty(teamlevel)) {
                continue;
            }
            if (teamLevel == TeamLevel.AREA && "03".equals(teamlevel)) {
                resultList.add(tbsaleteam);
            } else if (teamLevel == TeamLevel.DEPT && "02".equals(teamlevel)) {
                resultList.add(tbsaleteam);
            }
            else if (teamLevel == TeamLevel.TEAM && "01".equals(teamlevel)) {
                resultList.add(tbsaleteam);
            }
        }

        return resultList;
    }
}
