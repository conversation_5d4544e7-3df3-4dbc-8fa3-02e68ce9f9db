package com.hqins.agent.org.dao.mapper.settle;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.settle.PolicyExpire;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * IHomePro满期金明细清单数据访问接口
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
@Repository
@DS("settle")
public interface PolicyExpireMapper extends BaseMapper<PolicyExpire> {

    Long insertSelective(PolicyExpire policyExpire);

    int updateByPrimaryKeySelective(PolicyExpire policyExpire);


}

