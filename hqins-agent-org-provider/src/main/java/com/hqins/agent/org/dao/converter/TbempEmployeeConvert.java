package com.hqins.agent.org.dao.converter;

import com.google.common.collect.Lists;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.common.base.enums.AgentOrgType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @describle
 * @Date
 */
@Service
@RequiredArgsConstructor
public class TbempEmployeeConvert {

    public EmployeeVO convert(Tbemp tbemp) {
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setCode(tbemp.getEmpcode());
        //00-新增中 01-人员有效 02-人员失效 03-人员暂存 04-人员离职  05-待报备
        employeeVO.setStatus("01".equals(tbemp.getEmpstatus()) ? EmployeeStatus.SERVING : EmployeeStatus.LEAVING);
        employeeVO.setStatus("Y".equals(tbemp.getIsvirtualemp()) ? EmployeeStatus.LEAVING : employeeVO.getStatus());

        employeeVO.setName(tbemp.getEmpname());
        employeeVO.setTopCode(tbemp.getCompanycode());
        employeeVO.setOrgCode(tbemp.getInstCode());
        employeeVO.setOrgName(tbemp.getInstName());
        employeeVO.setOrgType(AgentOrgType.PARTNER);
        return employeeVO;
    }

    public List<EmployeeVO> convert(List<Tbemp> tbempList) {
        if (CollectionUtils.isEmpty(tbempList)) {
            return Lists.newArrayList();
        }

        List<EmployeeVO> employeeVOList = new ArrayList<>();
        for (Tbemp tbemp : tbempList) {
            employeeVOList.add(convert(tbemp));
        }

        return employeeVOList;
    }

}
