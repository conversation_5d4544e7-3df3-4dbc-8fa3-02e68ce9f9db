package com.hqins.agent.org.service.impl;

import com.google.common.collect.Lists;
import com.hqins.agent.org.model.vo.ClueEmployeeVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.rpc.client.FileFeignClient;
import com.hqins.common.utils.JsonUtil;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-09-07
 * @Description
 */
@Service
@Slf4j
public class EmployeeExternalService {

    private final FileFeignClient fileFeignClient;

    public EmployeeExternalService(FileFeignClient fileFeignClient) {
        this.fileFeignClient = fileFeignClient;
    }


    /**
     * 文件上传
     *
     * @param workbook
     * @return
     */
    public String uploadExcel(Workbook workbook, String tag) {
        String fileName = tag + System.currentTimeMillis() + ".xlsx";
        File file = new File(fileName);
        log.warn("uploadExcel file path : " + file.getAbsolutePath());
        try {
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            } catch (IOException e) {
                log.error("ListExportService uploadExcel: IOException occurred", e);
                //throw new RuntimeException("Excel 文件写入失败", e);
            } finally {
                workbook.close();
            }
            MultipartFile multipartFile = new MockMultipartFile("files", fileName, ContentType.APPLICATION_FORM_URLENCODED.getMimeType(), new FileInputStream(file));
            List<MultipartFile> multipartFileList = new ArrayList<>();
            multipartFileList.add(multipartFile);
            FileGetUrlsVO fileGetUrlsVO = fileFeignClient.upload("agent", null, null, multipartFileList);
            log.debug("ListExportService #uploadExcel  fileGetUrlsVO:{}", JsonUtil.toJSON(fileGetUrlsVO));
            return fileGetUrlsVO.firstOuterUrl();
        } catch (Exception exception) {
            log.warn("ListExportService uploadExcel", exception);
        } finally {
            file.delete();
        }
        return null;
    }


    public List<ClueEmployeeVO> assembler(List<EmployeeVO> employeeVOList) {
        if (CollectionUtils.isEmpty(employeeVOList)) {
            return Lists.newArrayList();
        }

        List<ClueEmployeeVO> clueEmployeeVOList = Lists.newArrayList();
        for (EmployeeVO employeeVO : employeeVOList) {
            ClueEmployeeVO clueEmployeeVO = new ClueEmployeeVO();
            clueEmployeeVO.setCode(employeeVO.getCode());
            clueEmployeeVO.setStatus(employeeVO.getStatus().name());
            clueEmployeeVO.setName(employeeVO.getName());
            clueEmployeeVO.setTopCode(employeeVO.getTopCode());
            clueEmployeeVO.setTopName(employeeVO.getTopName());
            clueEmployeeVO.setOrgCode(employeeVO.getOrgCode());
            clueEmployeeVO.setOrgName(employeeVO.getOrgName());
            clueEmployeeVO.setOrgType(employeeVO.getOrgType());
            clueEmployeeVOList.add(clueEmployeeVO);
        }
        return clueEmployeeVOList;
    }
}
