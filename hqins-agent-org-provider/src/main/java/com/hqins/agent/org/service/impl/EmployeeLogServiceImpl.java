package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.EmployeeLogService;
import com.hqins.common.base.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmployeeLogServiceImpl implements EmployeeLogService {

    private final CacheService cacheService;

    private final SupervisorEmployeeMapper supervisorEmployeeMapper;

    private final TbsaleteamMapper tbsaleteamMapper;

    @Override
    public EmployeeLogResultVO employeeLogQuery(String employeeCode) {
        //人员工作日志批阅范围查询接口
        List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

        //机构内勤
        EmployeeVO employeeVO = EmployeeVO.builder()
                .code("1440010800")
                .name("陈靓")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO1 = EmployeeVO.builder()
                .code("1440010803")
                .name("周卫华")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO2 = EmployeeVO.builder()
                .code("1440011207")
                .name("林海荣")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO3 = EmployeeVO.builder()
                .code("1441001397")
                .name("徐惜珍")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO4 = EmployeeVO.builder()
                .code("1441001398")
                .name("吴文霞")
                .teamLevel("01")
                .build();

        List<EmployeeVO> employeeVOS = new ArrayList<>();
        employeeVOS.add(employeeVO);
        employeeVOS.add(employeeVO1);
        employeeVOS.add(employeeVO2);
        employeeVOS.add(employeeVO3);
        employeeVOS.add(employeeVO4);

        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                .code("T250300011")
                .name("直辖区-直辖部")
                .level("团队")
                .employeeList(employeeVOS)
                .build();

        employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);

        EmployeeLogResultVO resultVO= EmployeeLogResultVO.builder()
                .employeeRole(employeeCode)
                .employeeRole("机构内勤")
                .hierarchyList(employeeLogHierarchyLevelVOList)
                .build();
        return resultVO;
    }

    @Override
    public EmployeeLogResultVO employeeLogAudit(String employeeCode) {

        if(StrUtil.isEmpty(employeeCode)){
            throw new RuntimeException("传入的代理人工号不能为空");
        }

        //判断传入的请求参数是 代理人/督导账号
        if(employeeCode.startsWith("S")){
            //督导账号(即为机构内勤)
            //判断是否为总部/机构督导
            SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
            if(supervisorEmployee == null){
                throw new RuntimeException("未获取到相关机构内勤人员的信息");
            }
            if(SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){

            }else {
                return null;
            }
        }else {
            //代理人
            //从缓存中获取所有人员
            Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
            if(CollUtil.isEmpty(allTbempMap)){
                throw new RuntimeException("从缓存中获取人员信息失败");
            }
            //获取指定代理人信息
            if(!allTbempMap.containsKey(employeeCode)){
                throw new RuntimeException("该代理人工号在系统中不存在");
            }
            Tbemp tbemp = allTbempMap.get(employeeCode);

            //判断该代理人是否为团队直属领导
            List<Tbsaleteam> manageTeamList = tbsaleteamMapper.selectByEmpInCode(tbemp.getEmpincode());
            if(CollUtil.isEmpty(manageTeamList)){
                return null;
            }
            //根据团队等级取最高的团队等级
            manageTeamList.stream().sorted()
        }


        //人员工作日志批阅范围查询接口
        List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

        //机构内勤
        EmployeeVO employeeVO = EmployeeVO.builder()
                .code("1440000672")
                .name("张小佩")
                .teamLevel("01")
                .build();
        List<EmployeeVO> employeeVOS = new ArrayList<>();
        employeeVOS.add(employeeVO);

        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                .code("000000000759")
                .name("直辖区-张小佩")
                .level("团队")
                .employeeList(employeeVOS)
                .build();

        employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);

        EmployeeLogResultVO resultVO= EmployeeLogResultVO.builder()
                .employeeRole(employeeCode)
                .employeeRole("机构内勤")
                .hierarchyList(employeeLogHierarchyLevelVOList)
                .build();
        return resultVO;
    }

}
