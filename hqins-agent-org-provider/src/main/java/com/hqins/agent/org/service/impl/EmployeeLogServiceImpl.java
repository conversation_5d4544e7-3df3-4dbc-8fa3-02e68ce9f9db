package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.EmployeeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmployeeLogServiceImpl implements EmployeeLogService {

    private final CacheService cacheService;

    private final SupervisorEmployeeMapper supervisorEmployeeMapper;

    private final TbsaleteamMapper tbsaleteamMapper;

    private final TbempMapper tbempMapper;

    @Override
    public EmployeeLogResultVO employeeLogQuery(String employeeCode) {
        //人员工作日志批阅范围查询接口
        List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

        //机构内勤
        EmployeeVO employeeVO = EmployeeVO.builder()
                .code("1440010800")
                .name("陈靓")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO1 = EmployeeVO.builder()
                .code("1440010803")
                .name("周卫华")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO2 = EmployeeVO.builder()
                .code("1440011207")
                .name("林海荣")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO3 = EmployeeVO.builder()
                .code("1441001397")
                .name("徐惜珍")
                .teamLevel("01")
                .build();

        EmployeeVO employeeVO4 = EmployeeVO.builder()
                .code("1441001398")
                .name("吴文霞")
                .teamLevel("01")
                .build();

        List<EmployeeVO> employeeVOS = new ArrayList<>();
        employeeVOS.add(employeeVO);
        employeeVOS.add(employeeVO1);
        employeeVOS.add(employeeVO2);
        employeeVOS.add(employeeVO3);
        employeeVOS.add(employeeVO4);

        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                .code("T250300011")
                .name("直辖区-直辖部")
                .level("团队")
                .employeeList(employeeVOS)
                .build();

        employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);

        EmployeeLogResultVO resultVO= EmployeeLogResultVO.builder()
                .employeeRole(employeeCode)
                .employeeRole("机构内勤")
                .hierarchyList(employeeLogHierarchyLevelVOList)
                .build();
        return resultVO;
    }

    @Override
    public EmployeeLogResultVO employeeLogAudit(String employeeCode) {

        if(StrUtil.isEmpty(employeeCode)){
            throw new RuntimeException("传入的代理人工号不能为空");
        }

        //从缓存中获取所有人员
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        if(CollUtil.isEmpty(allTbempMap)){
            throw new RuntimeException("从缓存中获取人员信息失败");
        }

        //判断传入的请求参数是 代理人/督导账号
        if(employeeCode.startsWith("S")){

            Map<String, BaseInst> allBaseInstsMap = cacheService.getAllBaseInstsMap();
            if(CollUtil.isEmpty(allBaseInstsMap)){
                throw new RuntimeException("从缓存中获取机构信息失败");
            }

            //督导账号(即为机构内勤)
            //判断是否为总部/机构督导
            SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
            if(supervisorEmployee == null){
                throw new RuntimeException("未获取到相关机构内勤人员的信息");
            }
            if(SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
                String orgCodeList = supervisorEmployee.getOrgCodeList();
                if(StrUtil.isNotBlank(orgCodeList) && !"[]".equals(orgCodeList)){
                    List<String> strings = convertToList(orgCodeList);
                    //最终返回结果
                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                    if(CollUtil.isNotEmpty(strings)){
                        strings.forEach(each -> {
                            log.info("机构内勤的机构编码为:{}", each);
                            //查询该机构对应的银保人员
                            List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                    .eq(Tbemp::getCompanycode, "P00001")
                                    .eq(Tbemp::getInstCode, each)
                                    .eq(Tbemp::getEmpstatus, "01")
                                    .eq(Tbemp::getIsvirtualemp, "N"));
                            if(CollUtil.isNotEmpty(empList)){
                                //使用stream流操作人员集合获取每个人员的团队并去重
                                Set<String> teamCodes = empList.stream()
                                        .map(Tbemp::getSaleteamincode)
                                        .collect(Collectors.toSet());
                                //查询团队信息
                                List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                        .eq(Tbsaleteam::getCompanycode, "P00001")
                                        .in(Tbsaleteam::getSaleteamincode, teamCodes));
                                if(CollUtil.isNotEmpty(saleTeams)){
                                    //组装机构
                                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                            .code(each)
                                            .name(allBaseInstsMap.get(each).getInstName())
                                            .level("机构")
                                            .build();
                                    //组装团队父机构为空的
                                    saleTeams.forEach(saleTeam -> {
                                       if(StrUtil.isEmpty(saleTeam.getSupersaleteamcode())){
                                           EmployeeLogHierarchyLevelVO children = EmployeeLogHierarchyLevelVO.builder()
                                                   .code(saleTeam.getSaleteamcode())
                                                   .name(saleTeam.getSaleteamname())
                                                   .level("团队")
                                                   .build();
                                           if(StrUtil.isNotEmpty(saleTeam.getEmpincode())){
                                               EmployeeVO employeeVO = EmployeeVO.builder()
                                                       .code(saleTeam.getEmpincode().substring(4))
                                                       .name(allTbempMap.get(saleTeam.getEmpincode().substring(4)).getEmpname())
                                                       .teamLevel(saleTeam.getTeamlevel())
                                                       .build();
                                               children.setEmployeeList(Collections.singletonList(employeeVO));
                                           }else {
                                               //判断是否存在下级机构
                                               if("01".equals(saleTeam.getTeamlevel())){
                                                   //不存在下级
                                                   empList.forEach(emp -> {
                                                       if(StrUtil.equals(saleTeam.getSaleteamincode(), emp.getSaleteamincode())){
                                                           EmployeeVO employeeVO = EmployeeVO.builder()
                                                                   .code(emp.getEmpcode())
                                                                   .name(emp.getEmpname())
                                                                   .teamLevel("01")
                                                                   .build();
                                                           children.getEmployeeList().add(employeeVO);
                                                       }
                                                   });
                                               }
                                               if("02".equals(saleTeam.getTeamlevel())){
                                                   //查询下级组
                                                   List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                           .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                           .eq(Tbsaleteam::getSupersaleteamcode, saleTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                   if(CollUtil.isNotEmpty(childrenSaleTeams)){
                                                       List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();
                                                       childrenSaleTeams.forEach(children -> {
                                                           if (StrUtil.isNotBlank(children.getEmpincode())) {
                                                               List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                               Tbemp emp = allTbempMap.get(children.getEmpincode().substring(4));
                                                               //返回小组主管信息
                                                               EmployeeVO employeeVO = EmployeeVO.builder()
                                                                       .code(emp.getEmpcode())
                                                                       .name(emp.getEmpname())
                                                                       .teamLevel("01")
                                                                       .build();
                                                               employeeVOS.add(employeeVO);
                                                               EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                                       .code(children.getSaleteamcode())
                                                                       .name(children.getSaleteamname())
                                                                       .level("团队")
                                                                       .employeeList(employeeVOS)
                                                                       .build();
                                                               subLevels.add(subEmployeeLogHierarchyLevelVO);
                                                           } else {
                                                               List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                               //该代理人为小组主管 获取组内人员信息
                                                               empList.forEach(emp -> {
                                                                   if (StrUtil.equals(children.getSaleteamincode(), emp.getSaleteamincode())) {
                                                                       EmployeeVO employeeVO = EmployeeVO.builder()
                                                                               .code(emp.getEmpcode())
                                                                               .name(emp.getEmpname())
                                                                               .teamLevel("01")
                                                                               .build();
                                                                       employeeVOS.add(employeeVO);
                                                                   }
                                                               });
                                                               EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                                       .code(children.getSaleteamcode())
                                                                       .name(children.getSaleteamname())
                                                                       .level("团队")
                                                                       .employeeList(employeeVOS)
                                                                       .build();
                                                               subLevels.add(subEmployeeLogHierarchyLevelVO);
                                                           }
                                                       }
                                                   );
                                                       children.setSubLevels(subLevels);
                                                   }
                                               }
                                           }
                                           employeeLogHierarchyLevelVO.getSubLevels().add(children);
                                       }
                                    });
                                    employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                                }
                            }
                        });








                        //根据strings集合查询对应的团队等级为03的团队
                        List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                .eq(Tbsaleteam::getSaleteamstatus, "00")
                                .eq(Tbsaleteam::getCompanycode, "P00001")
                                .in(Tbsaleteam::getInstCode, strings)
                                .eq(Tbsaleteam::getTeamlevel, "03"));

                        //根据instCode进行分组
                        Map<String, List<Tbsaleteam>> saleTeamMap = saleTeams.stream().collect(Collectors.groupingBy(Tbsaleteam::getInstCode));
                        //存放机构层级
                        List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                        //存放下级
                        List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();

                        strings.forEach( instCode -> {
                            log.info("机构内勤的机构编码为:{}", instCode);

                            BaseInst baseInst = allBaseInstsMap.get(instCode);

                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(baseInst.getInstCode())
                                    .name(baseInst.getInstName())
                                    .level("机构")
                                    .build();

                            List<Tbsaleteam> groupSaleTeams = saleTeamMap.get(instCode);

                            List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList1 = new ArrayList<>();

                            if(CollUtil.isNotEmpty(groupSaleTeams)){
                                //存放区总监层级
                                groupSaleTeams.forEach(each -> {
                                    if(StrUtil.isNotBlank(each.getEmpincode())){
                                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                .code(each.getSaleteamcode())
                                                .name(each.getSaleteamname())
                                                .level("机构")
                                                .build();
                                        //区总监存在
                                        Tbemp tbemp = allTbempMap.get(each.getEmpincode().substring(4));
                                        EmployeeVO employeeVO = EmployeeVO.builder()
                                                .code(tbemp.getEmpcode())
                                                .name(tbemp.getEmpname())
                                                .teamLevel("03")
                                                .build();
                                        employeeLogHierarchyLevelVO1.setEmployeeList(Collections.singletonList(employeeVO));
                                        subLevels.add(employeeLogHierarchyLevelVO1);
                                    }else {
                                        //区总监不存在 查询下级部团队
                                        log.info("区总监为空,团队信息为:{}", JSONObject.toJSONString(each));
                                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                .code(each.getSaleteamcode())
                                                .name(each.getSaleteamname())
                                                .level("机构")
                                                .build();
                                        List<EmployeeLogHierarchyLevelVO> subLevels1 = new ArrayList<>();
                                        //查询下级部团队
                                        List<Tbsaleteam> saleTeams1 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                .eq(Tbsaleteam::getSupersaleteamcode, each.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                        if(CollUtil.isNotEmpty(saleTeams1)){
                                            saleTeams1.forEach(children -> {
                                                if (StrUtil.isNotBlank(children.getEmpincode())) {
                                                    List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                    //部经理存在
                                                    Tbemp emp = allTbempMap.get(children.getEmpincode().substring(4));
                                                    EmployeeVO employeeVO = EmployeeVO.builder()
                                                            .code(emp.getEmpcode())
                                                            .name(emp.getEmpname())
                                                            .teamLevel("02")
                                                            .build();
                                                    employeeVOS.add(employeeVO);
                                                    EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                            .code(children.getSaleteamcode())
                                                            .name(children.getSaleteamname())
                                                            .level("机构")
                                                            .employeeList(employeeVOS)
                                                            .build();
                                                    subLevels1.add(subEmployeeLogHierarchyLevelVO);
                                                }else {

                                                }
                                                employeeLogHierarchyLevelVO1.setSubLevels(subLevels1);
                                            });
                                            subLevels.add(employeeLogHierarchyLevelVO1);
                                        }
                                    }
                                    //存储各区总监
                                    employeeLogHierarchyLevelVO.setSubLevels(employeeLogHierarchyLevelVOList1);
                                });
                                //存储各机构
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }
                        });
                    }


                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                    if(CollUtil.isNotEmpty(saleTeams)){
                        saleTeams.forEach(each -> {


                            if(StrUtil.isNotBlank(each.getEmpincode())){
                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                Tbemp tbemp = allTbempMap.get(each.getEmpincode().substring(4));
                                EmployeeVO employeeVO = EmployeeVO.builder()
                                        .code(tbemp.getEmpcode())
                                        .name(tbemp.getEmpname())
                                        .teamLevel("03")
                                        .build();
                                employeeVOS.add(employeeVO);
                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                        .code(each.getSaleteamcode())
                                        .name(each.getSaleteamname())
                                        .level("机构")
                                        .employeeList(employeeVOS)
                                        .build();
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }else {
                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                        .code(each.getSaleteamcode())
                                        .name(each.getSaleteamname())
                                        .level("机构")
                                        .build();
                                log.info("区总监为空,团队信息为:{}", JSONObject.toJSONString(each));
                                //查询下级部团队
                                List<Tbsaleteam> saleTeams1 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                        .eq(Tbsaleteam::getSupersaleteamcode, each.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                if(!CollUtil.isEmpty(saleTeams1)){
                                    List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();
                                    saleTeams1.forEach(children -> {
                                        if(StrUtil.isNotBlank(children.getEmpincode())){
                                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                                            Tbemp emp = allTbempMap.get(children.getEmpincode().substring(4));
                                            //返回小组主管信息
                                            EmployeeVO employeeVO = EmployeeVO.builder()
                                                    .code(emp.getEmpcode())
                                                    .name(emp.getEmpname())
                                                    .teamLevel("02")
                                                    .build();
                                            employeeVOS.add(employeeVO);
                                            EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                    .code(children.getSaleteamcode())
                                                    .name(children.getSaleteamname())
                                                    .level("机构")
                                                    .employeeList(employeeVOS)
                                                    .build();
                                            subLevels.add(subEmployeeLogHierarchyLevelVO);
                                        }else {
                                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                    .code(children.getSaleteamcode())
                                                    .name(children.getSaleteamname())
                                                    .level("机构")
                                                    .build();
                                            log.info("部主管为空,部团队信息为:{}", JSONObject.toJSONString(children));
                                            //查询下级组团队
                                            List<Tbsaleteam> saleTeams2 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                    .eq(Tbsaleteam::getSupersaleteamcode, children.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                            if(!CollUtil.isEmpty(saleTeams2)){
                                                List<EmployeeLogHierarchyLevelVO> subLevels1 = new ArrayList<>();
                                                saleTeams2.forEach(children1 -> {
                                                    if(StrUtil.isNotBlank(children1.getEmpincode())){
                                                        List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                        Tbemp emp = allTbempMap.get(children1.getEmpincode().substring(4));
                                                        //返回小组主管信息
                                                        EmployeeVO employeeVO = EmployeeVO.builder()
                                                                .code(emp.getEmpcode())
                                                                .name(emp.getEmpname())
                                                                .teamLevel("01")
                                                                .build();
                                                        employeeVOS.add(employeeVO);
                                                        EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                                .code(children1.getSaleteamcode())
                                                                .name(children1.getSaleteamname())
                                                                .level("机构")
                                                                .employeeList(employeeVOS)
                                                                .build();
                                                        subLevels1.add(subEmployeeLogHierarchyLevelVO);
                                                    }else {
                                                        log.info("组主管为空,组团队信息为:{}", JSONObject.toJSONString(children1));
                                                        //查询该小组对应的有效的银保人员
                                                        List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                                                .eq(Tbemp::getSaleteamincode, children1.getSaleteamincode())
                                                                .eq(Tbemp::getCompanycode, "P00001")
                                                                .eq(Tbemp::getEmpstatus, "01")
                                                                .eq(Tbemp::getIsvirtualemp, "N"));
                                                        if(CollUtil.isNotEmpty(empList)){
                                                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                            empList.forEach(emp -> {
                                                                EmployeeVO employeeVO = EmployeeVO.builder()
                                                                        .code(emp.getEmpcode())
                                                                        .name(emp.getEmpname())
                                                                        .teamLevel("01")
                                                                        .build();
                                                                employeeVOS.add(employeeVO);
                                                            });
                                                            EmployeeLogHierarchyLevelVO subEmployeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                                    .code(children1.getSaleteamcode())
                                                                    .name(children1.getSaleteamname())
                                                                    .level("机构")
                                                                    .employeeList(employeeVOS)
                                                                    .build();
                                                            subLevels1.add(subEmployeeLogHierarchyLevelVO);
                                                        }
                                                    }
                                                });
                                                if(CollUtil.isNotEmpty(subLevels1)){
                                                    employeeLogHierarchyLevelVO1.setSubLevels(subLevels1);
                                                }
                                            }
                                        }
                                    });
                                    if(CollUtil.isNotEmpty(subLevels)){
                                        employeeLogHierarchyLevelVO.setSubLevels(subLevels);
                                    }
                                    employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                                }
                            }
                        });
                    }
                    if(CollUtil.isNotEmpty(employeeLogHierarchyLevelVOList)){
                        return EmployeeLogResultVO.builder()
                                .employeeCode(employeeCode)
                                .employeeRole("机构内勤")
                                .hierarchyList(employeeLogHierarchyLevelVOList)
                                .build();
                    }
                }
            }
            return null;
        }else {
            //代理人
            //获取指定代理人信息
            if(!allTbempMap.containsKey(employeeCode)){
                throw new RuntimeException("该代理人工号在系统中不存在");
            }
            Tbemp tbemp = allTbempMap.get(employeeCode);

            //判断该代理人是否为团队直属领导
            List<Tbsaleteam> manageTeamList = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                    .eq(Tbsaleteam::getEmpincode, tbemp.getEmpincode())
                    .eq(Tbsaleteam::getCompanycode, "P00001").orderByAsc(Tbsaleteam::getTeamlevel));

//            tbsaleteamMapper.selectByEmpInCode(tbemp.getEmpincode());
            if(CollUtil.isEmpty(manageTeamList)){
                log.info("代理人[{}]没有管理的团队", employeeCode);
                return null;
            }
            //根据团队等级取最高等级的团队
            Tbsaleteam highestLevelTeam = manageTeamList.get(0);

            if(StrUtil.isBlank(highestLevelTeam.getTeamlevel())){
                log.info("团队等级为空,团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                throw new RuntimeException("团队等级为空");
            }

            if("01".equals(highestLevelTeam.getTeamlevel())){
                //该代理人为小组主管 获取组内人员信息
                List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                        .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                        .eq(Tbemp::getCompanycode, "P00001")
                        .eq(Tbemp::getEmpstatus, "01")
                        .eq(Tbemp::getIsvirtualemp, "N"));

                if(CollUtil.isEmpty(empList)){
                    log.info("团队中没有有效人员,团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                    return null;
                }else {
                    //组装人员信息数据
                    List<EmployeeVO> employeeVOS = new ArrayList<>();
                    empList.forEach(each -> {
                        EmployeeVO employeeVO = EmployeeVO.builder()
                                .code(each.getEmpcode())
                                .name(each.getEmpname())
                                .teamLevel("01")
                                .build();
                        employeeVOS.add(employeeVO);
                    });

                    //组装人员工作日志查询结果层级列表
                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder().
                            code(highestLevelTeam.getSaleteamcode())
                            .name(highestLevelTeam.getSaleteamname())
                            .level("团队")
                            .employeeList(employeeVOS)
                            .build();
                    employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);

                    return EmployeeLogResultVO.builder()
                            .employeeCode(employeeCode)
                            .employeeRole("组主管")
                            .hierarchyList(employeeLogHierarchyLevelVOList)
                            .build();
                }
            }

            if("02".equals(highestLevelTeam.getTeamlevel())){
                //查询下级团队
                List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                        .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                if(CollUtil.isEmpty(saleTeams)){
                    log.info("团队中没有有效下级团队,团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                    return null;
                }else {
                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                    saleTeams.forEach(each -> {
                        log.info("下级团队信息为:{}", JSONObject.toJSONString(each));
                        Tbemp team = allTbempMap.get(each.getEmpincode().substring(4));
                        if(StrUtil.isNotBlank(each.getEmpincode())){
                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                            //返回小组主管信息
                            EmployeeVO employeeVO = EmployeeVO.builder()
                                    .code(team.getEmpcode())
                                    .name(team.getEmpname())
                                    .teamLevel("01")
                                    .build();
                            employeeVOS.add(employeeVO);
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(each.getSaleteamcode())
                                    .name(each.getSaleteamname())
                                    .level("团队")
                                    .employeeList(employeeVOS)
                                    .build();
                            employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                        }else {
                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                            //该代理人为小组主管 获取组内人员信息
                            List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                    .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                                    .eq(Tbemp::getCompanycode, "P00001")
                                    .eq(Tbemp::getEmpstatus, "01")
                                    .eq(Tbemp::getIsvirtualemp, "N"));
                            if(!CollUtil.isEmpty(empList)){
                                empList.forEach(emp -> {
                                    EmployeeVO employeeVO = EmployeeVO.builder()
                                            .code(emp.getEmpcode())
                                            .name(emp.getEmpname())
                                            .teamLevel("01")
                                            .build();
                                    employeeVOS.add(employeeVO);
                                });
                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                        .code(each.getSaleteamcode())
                                        .name(each.getSaleteamname())
                                        .level("团队")
                                        .employeeList(employeeVOS)
                                        .build();
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }
                        }
                    });
                    if(CollUtil.isEmpty(employeeLogHierarchyLevelVOList)){
                        log.info("未查询到部经理相应的工作日志查询范围");
                        return null;
                    }else {
                        return EmployeeLogResultVO.builder()
                                .employeeCode(employeeCode)
                                .employeeRole("部经理")
                                .hierarchyList(employeeLogHierarchyLevelVOList)
                                .build();
                    }
                }
            }

            if("03".equals(highestLevelTeam.getTeamlevel())){
                //查询下级部团队
                List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                        .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                if(CollUtil.isEmpty(saleTeams)){
                    log.info("团队中没有有效下级部团队,团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                    return null;
                }else {
                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                    saleTeams.forEach(each -> {
                        log.info("下级部团队信息为:{}", JSONObject.toJSONString(each));
                        if(StrUtil.isNotBlank(each.getEmpincode())){
                            Tbemp partManager = allTbempMap.get(each.getEmpincode().substring(4));
                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                            //返回小组主管信息
                            EmployeeVO employeeVO = EmployeeVO.builder()
                                    .code(partManager.getEmpcode())
                                    .name(partManager.getEmpname())
                                    .teamLevel("02")
                                    .build();
                            employeeVOS.add(employeeVO);
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(each.getSaleteamcode())
                                    .name(each.getSaleteamname())
                                    .level("团队")
                                    .employeeList(employeeVOS)
                                    .build();
                            employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                        }else {
                            //查询部下级团队
                            List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                                    .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                            if(CollUtil.isEmpty(childrenSaleTeams)){
                                log.info("区下级的部的下级团队中没有有效团队,部团队信息为:{}", JSONObject.toJSONString(each));
                            }else {
                                List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();
                                childrenSaleTeams.forEach(children -> {
                                    if(StrUtil.isNotBlank(children.getEmpincode())){
                                        List<EmployeeVO> employeeVOS = new ArrayList<>();
                                        Tbemp emp = allTbempMap.get(children.getEmpincode().substring(4));
                                        //返回小组主管信息
                                        EmployeeVO employeeVO = EmployeeVO.builder()
                                                .code(emp.getEmpcode())
                                                .name(emp.getEmpname())
                                                .teamLevel("01")
                                                .build();
                                        employeeVOS.add(employeeVO);
                                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                .code(children.getSaleteamcode())
                                                .name(children.getSaleteamname())
                                                .level("团队")
                                                .employeeList(employeeVOS)
                                                .build();
                                        subLevels.add(employeeLogHierarchyLevelVO);
                                    }else {
                                        //该代理人为小组主管 获取组内人员信息
                                        List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                                .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                                                .eq(Tbemp::getCompanycode, "P00001")
                                                .eq(Tbemp::getEmpstatus, "01")
                                                .eq(Tbemp::getIsvirtualemp, "N"));
                                        if(CollUtil.isNotEmpty(empList)){
                                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                                            empList.forEach(emp -> {
                                                EmployeeVO employeeVO = EmployeeVO.builder()
                                                        .code(emp.getEmpcode())
                                                        .name(emp.getEmpname())
                                                        .teamLevel("01")
                                                        .build();
                                                employeeVOS.add(employeeVO);
                                            });
                                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                                    .code(children.getSaleteamcode())
                                                    .name(children.getSaleteamname())
                                                    .level("团队")
                                                    .employeeList(employeeVOS)
                                                    .build();
                                            subLevels.add(employeeLogHierarchyLevelVO);
                                        }
                                    }
                                });
                                if(CollUtil.isNotEmpty(subLevels)){
                                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                            .code(each.getSaleteamcode())
                                            .name(each.getSaleteamname())
                                            .level("团队")
                                            .subLevels(subLevels)
                                            .build();
                                    employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                                }
                            }
                        }
                    });
                    if(CollUtil.isEmpty(employeeLogHierarchyLevelVOList)){
                        log.info("未查询到该区总监相应的工作日志查询范围");
                        return null;
                    }else {
                        return EmployeeLogResultVO.builder()
                                .employeeCode(employeeCode)
                                .employeeRole("区总监")
                                .hierarchyList(employeeLogHierarchyLevelVOList)
                                .build();
                    }
                }
            }

            log.info("该代理人对应的团队等级异常未查询到数据");
            return null;
        }
    }

    /**
     * 根据团队等级获取最高等级的团队
     * 团队等级：01-营业组，02-营业部，03-营业区
     * 数字越大，等级越高
     *
     * @param teamList 团队列表
     * @return 最高等级的团队，如果没有有效团队则返回null
     */
    private Tbsaleteam getHighestLevelTeam(List<Tbsaleteam> teamList) {
        if (CollUtil.isEmpty(teamList)) {
            return null;
        }

        return teamList.stream()
                .filter(team -> team.getTeamlevel() != null && !team.getTeamlevel().trim().isEmpty())
                .max((team1, team2) -> team1.getTeamlevel().compareTo(team2.getTeamlevel()))
                .orElse(null);
    }

    /**
     * 将字符串转化为List<String>
     * @param str   字符串
     * @return      集合
     */
    public List<String> convertToList(String str) {
        return Arrays.stream(str.replaceAll("[\\[\\]\"]", "").split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

}
