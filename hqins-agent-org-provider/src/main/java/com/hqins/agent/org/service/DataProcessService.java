package com.hqins.agent.org.service;


import com.hqins.agent.org.model.request.InitEmployeeAddRequest;
import com.hqins.agent.org.model.vo.InitResultVo;
import com.hqins.agent.org.mq.vo.HomePartnerInterMessageVo;
import com.hqins.common.web.RequestContextHolder;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/26
 * @Description
 */
public interface DataProcessService {

    void downloadProcessOrg(long staffId, String testFlag);

    void downloadProcessEmployee(long staffId, String testFlag);

    void mqProcessEmployee(HomePartnerInterMessageVo homePartnerInterMessageVo);

    void uploadSensors();

    List<InitResultVo> saveEmployeeBatch(@Valid  List<InitEmployeeAddRequest> request, Long staffId);

}
