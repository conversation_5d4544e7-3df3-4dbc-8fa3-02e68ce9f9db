package com.hqins.agent.org.service;

import com.hqins.agent.org.model.vo.IFP2024GroupVO;
import com.hqins.agent.org.model.vo.IFP2024TabVO;

import java.util.List;

public interface IFP2024AssessmentWarningService {

    /**
     *
     * 获取员工个人考核预警信息
     * @param paramType     上期/当期类型
     * @param employeeCode  员工工号
     * @return              返回参数
     */
    List<IFP2024TabVO> queryEmployeeInfo(String paramType, String employeeCode);

    /**
     *
     * 获取团队考核预警信息
     * @param paramType 上期/当期类型
     * @param teamCode  团队编码
     * @return          返回参数
     */
    IFP2024GroupVO queryGroupInfo(String paramType, String teamCode);
}
