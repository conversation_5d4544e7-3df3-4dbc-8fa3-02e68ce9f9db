package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.TreeUtils;
import com.hqins.agent.org.model.enums.DataType;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.MyDataAccessVO;
import com.hqins.agent.org.model.vo.PartnerOrgVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.agent.org.service.DataAccessService;
import com.hqins.agent.org.service.PartnerOrgService;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class PartnerOrgServiceImpl implements PartnerOrgService {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private CacheService cacheService;

    @Override
    public PageInfo<PartnerOrgVO> listMy(OrgQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<BaseInst> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, myDataAccess.getPartnerOrgCodes());
        } else {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        }
        return PageUtil.convert(p, inst -> PartnerConverter.instToPartnerOrgVO(inst, cacheService.getAllIdTbepartnersMap().get(inst.getCompanyid())));
    }

    @Override
    public List<TreeNodeVO> listMySimple(OrgQueryRequest queryRequest) {
        //获取当前登录人权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        Page<BaseInst> p = null;
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, myDataAccess.getPartnerOrgCodes());
        } else {
            p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        }
        List<TreeNodeVO> treeNodeVos = BeanCopier.copyList(p.getRecords(), PartnerConverter::instToTreeNodeVo);
        for (TreeNodeVO vo : treeNodeVos) {
            vo.setDataType(DataType.PARTNER_ORG);
        }
        return TreeUtils.buildTrees(treeNodeVos);
    }

    @Override
    public List<TreeNodeVO> listAllSimple(OrgQueryRequest queryRequest) {
        Page<BaseInst> p = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, null);
        List<TreeNodeVO> treeNodeVos = BeanCopier.copyList(p.getRecords(), PartnerConverter::instToTreeNodeVo);
        for (TreeNodeVO vo : treeNodeVos) {
            vo.setDataType(DataType.PARTNER_ORG);
        }
        return TreeUtils.buildTrees(treeNodeVos);
    }

    @Override
    public List<TreeNodeVO> clueListMySimple(OrgQueryRequest queryRequest) {
        return listAllSimple(queryRequest);
    }
}
