package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合伙人人员指派表数据访问接口
 * <AUTHOR>
 * @since 2021-05-28
 */
@Mapper
@DS("exms")
@Repository
public interface TbpartassignmanagerMapper extends BaseMapper<Tbpartassignmanager> {

    List<Tbpartassignmanager> getValidCustManagerList(@Param("merchantCodes") List<String> merchantCodes,@Param("orgCodes") List<String> orgCodes);

    /**
     * 查询网点下理财经理列表
     * @param orgCode
     * @return
     */
    List<Tbpartassignmanager> getValidCustManagerListByOrgCode(@Param("orgCode") String orgCode);

    List<Tbpartassignmanager> queryCompanyInstByCompanyInstCode(@Param("companyInstCode") String companyInstCode);

}

