package com.hqins.agent.org;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.common.boot.feign.FeignConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.function.RouterFunction;
import org.springframework.web.servlet.function.ServerResponse;
import springfox.documentation.oas.annotations.EnableOpenApi;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;

import static org.springframework.web.servlet.function.RequestPredicates.GET;
import static org.springframework.web.servlet.function.RouterFunctions.route;

/**
 * <AUTHOR> Luo
 * @date 2021/4/5
 * @Description
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class, FreeMarkerAutoConfiguration.class})
@EnableAsync
@Slf4j
public class AppServer {

    public static void main(String[] args) throws IOException {
        SpringApplication app = new SpringApplicationBuilder(AppServer.class).web(WebApplicationType.SERVLET).build();
        Environment env = app.run(args).getEnvironment();
        String protocol = null != env.getProperty(AppConsts.SERVER_SSL_KEY_STORE)
                ? AppConsts.HTTPS : AppConsts.HTTP;
        log.info(AppConsts.APP_RUN_LOG,
                env.getProperty(AppConsts.APP_NAME),
                protocol,
                InetAddress.getLocalHost().getHostAddress(),
                env.getProperty(AppConsts.APP_PORT),
                AppConsts.SLASH.equals(env.getProperty(AppConsts.SERVER_CONTEXT_PATH))
                        || null == env.getProperty(AppConsts.SERVER_CONTEXT_PATH)
                        ? AppConsts.STRING_NULL : env.getProperty(AppConsts.SERVER_CONTEXT_PATH),
                env.getActiveProfiles());
    }

    @Bean
    @Profile(AppConsts.SWAGGER)
    public RouterFunction<ServerResponse> indexRouter() {
        return route(GET(AppConsts.SLASH), serverRequest -> ServerResponse.temporaryRedirect(URI.create(AppConsts.SWAGGER_URI)).build());
    }

    @ComponentScan(basePackages = AppConsts.BASE_PACKAGE)
    @EnableDiscoveryClient
    @EnableOpenApi
    @EnableMethodCache(basePackages = AppConsts.BASE_PACKAGE)
    @EnableCreateCacheAnnotation
    @EnableFeignClients(basePackages = AppConsts.BASE_PACKAGE, defaultConfiguration = FeignConfig.class)
    @Configuration
    @EnableAspectJAutoProxy(exposeProxy = true)
    protected static class ConfigurationImports {
    }
}
