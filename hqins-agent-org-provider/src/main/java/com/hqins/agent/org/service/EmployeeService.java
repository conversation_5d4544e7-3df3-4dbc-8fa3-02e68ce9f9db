package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.AllEmpByPageRequest;
import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.page.PageQueryRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/525
 * @Description
 */
public interface EmployeeService {

    /**
     * 根据条件查询
     *
     * @param queryRequest 查询条件
     * @return
     */
    List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest);

    /**
     * 上面的接口UM限制100个一次，不符合绝大业务场景，此接口不返回UM相关内容，放开了数量限制
     *
     * @param queryRequest 查询条件
     * @return
     */
    List<SimpleEmployeeVO> listEmployeeSimpleCopy(EmployeeQueryRequest queryRequest);

    /**
     * 查询所有的代理人信息
     *
     * @return
     */
    List<EmployeeVO> allEmployees();

    /**
     * 根据公司代码，查询所有有效的代理人信息
     *
     * @return
     */
    Map<String, EmployeeVO> allEmployeesByCompanycode(String companycode);

    /**
     * 根据公司代码-获取所有的销售员
     *
     * @param companycode 公司代理
     * @param status      状态
     * @return
     */
    List<EmployeeVO> allEmployeesByCompanycodeAndStatus(String companycode, String status);

    /**
     * 查询销售员主管
     *
     * @param employeeCode 代理人工号
     * @return
     */
    EmployeeVO queryEmployeeLeader(String employeeCode);

    /**
     * 查询员工信息与组织信息
     * @param employeeCode
     * @return
     */
    EmployeeOrgVO queryEmployeeOrgInfo(String employeeCode);

    /**
     * 根据组编码查询相关人员信息
     * @param saleTeamCode
     * @return
     */
    BusinessOrgVO querySaleTeamEmployeeInfo(String saleTeamCode);

    /* 根据代理人工号及组织机构类型查询代理人信息
     *
     * @param orgType      组织机构类型
     * @param employeeCode 代理人工号
     * @return
     */
    EmployeeVO getEmployeeByEmployeeCode(AgentOrgType orgType, String employeeCode);

    /**
     * 根据代理人工号集合、组织机构类型查询代理人状态信息
     *
     * @param employeeCodeList 代理人工号集合
     * @param orgType          组织机构类型
     * @return
     */
    List<ClueEmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList, String orgType);

    PageInfo<EmployeeVO> allEmployeesByPageAndType(PageQueryRequest queryRequest,AllEmpByPageRequest request);

    /**
     *
     * 根据手机号查询代理人
     * @param birthdayList  生日集合
     * @return              合伙人集合信息
     */
    List<EmployeeVO> getEmployeeInfoByBirthday(List<String> birthdayList);

    /**
     * 根据传递的代理人工号集合进行查询
     *
     * @param employeeCodeList  代理人工号集合
     * @return                  代理人信息集合
     */
    List<EmployeeOrgVO> queryEmployeeOrgInfoByList(List<String> employeeCodeList);

    List<EmployeeOrgVO> queryTeamEmpInfo(String saleTeamCode);

    HonorEmpVO queryEmpInfo(String empCode);
}
