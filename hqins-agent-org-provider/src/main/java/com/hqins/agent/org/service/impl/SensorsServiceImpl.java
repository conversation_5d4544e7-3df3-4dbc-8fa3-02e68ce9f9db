package com.hqins.agent.org.service.impl;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.SensorsService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.TimeUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.um.model.dto.AccountInfoDTO;
import com.sensorsdata.analytics.javasdk.ISensorsAnalytics;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.UserRecord;
import com.sensorsdata.analytics.javasdk.consumer.ConcurrentLoggingConsumer;
import com.sensorsdata.analytics.javasdk.exceptions.InvalidArgumentException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR> Luo
 * @date 2021/7/25
 * @Description
 */
@Service
@Slf4j
public class SensorsServiceImpl implements SensorsService {

    @Resource
    private UmClient umClient;

    @Value("${hqins.common.sensors.log-path}")
    private String sensorsLogPath;


    /**
     * LoggingConsumer
     */
    private ISensorsAnalytics sa;

    public ISensorsAnalytics getISensorsAnalytics() throws IOException, InvalidArgumentException {
        if (sa != null) {
            return sa;
        }
        sa = new SensorsAnalytics(new ConcurrentLoggingConsumer(sensorsLogPath));
        return sa;
    }

    @Override
    @Async("executor")
    public void addEmployee(ChannelEmployee employee, String umId) {
        try {
            UserRecord.Builder builder = UserRecord.builder()
                    .setDistinctId(umId == null ? getUmId(employee.getCode()) : umId)
                    .isLoginId(Boolean.TRUE)
                    .addProperty("$signup_time", Calendar.getInstance().getTime())
                    .addProperty("name", employee.getName())
                    .addProperty("gender", getGender(employee.getGender()))
                    .addProperty("mobile", employee.getMobile())
                    .addProperty("org_type", AgentOrgType.CHANNEL.name())
                    .addProperty("org_code", employee.getOrgCode())
                    .addProperty("org_name", employee.getOrgName())
                    .addProperty("employee_code", employee.getCode());
            if (employee.getBirthday() != null) {
                builder.addProperty("birthday", TimeUtil.toDate(employee.getBirthday()));
            }
            getISensorsAnalytics().profileSet(builder.build());
            getISensorsAnalytics().flush();
        } catch (InvalidArgumentException e) {
            log.error("神策上报失败");
            e.printStackTrace();
        } catch (IOException e) {
            log.error("神策初始化失败");
            e.printStackTrace();
        }
    }

    @Override
    @Async("executor")
    public void addEmployee(List<Tbemp> tbempList) {
        if (CollectionUtils.isEmpty(tbempList)) {
            return;
        }
        for (Tbemp tbemp : tbempList) {
            String distinctId = "";
            try {
                distinctId = getUmId(tbemp.getEmpcode());
            } catch (Exception e) {
                continue;
            }
            try {
                UserRecord.Builder builder = UserRecord.builder().setDistinctId(distinctId).isLoginId(Boolean.TRUE)
                        .addProperty("name", tbemp.getEmpname())
                        .addProperty("gender", getGender(tbemp.getSexcode()))
                        .addProperty("mobile", tbemp.getMaintelephone())
                        .addProperty("org_type", tbemp.getCptype())
                        .addProperty("org_code", tbemp.getInstCode())
                        .addProperty("org_name", tbemp.getInstName())
                        .addProperty("employee_code", tbemp.getEmpcode());
                if (tbemp.getBirthday() != null) {
                    builder.addProperty("birthday", TimeUtil.toDate(tbemp.getBirthday()));
                }
                getISensorsAnalytics().profileSet(builder.build());
            } catch (InvalidArgumentException e) {
                log.error("神策上报失败");
                e.printStackTrace();
            } catch (IOException e) {
                log.error("神策初始化失败");
                e.printStackTrace();
            }
        }
        try {
            getISensorsAnalytics().flush();
            log.info("神策上报完成");
        } catch (IOException e) {
            log.error("神策初始化失败");
            e.printStackTrace();
        } catch (InvalidArgumentException e) {
            log.error("神策上报失败");
            e.printStackTrace();
        }
    }

    @Override
    @Async("executor")
    public void  updateEmployeeList(List<ChannelEmployee> employeeList) {
        if (CollectionUtils.isEmpty(employeeList)) {
            return;
        }
        for (ChannelEmployee employee : employeeList) {
            String umId = "";
            try {
                umId = getUmId(employee.getCode());
            } catch (Exception e) {
                continue;
            }
            try {
                UserRecord.Builder builder = UserRecord.builder().setDistinctId(umId).isLoginId(Boolean.TRUE)
                        .addProperty("name", employee.getName())
                        .addProperty("gender", getGender(employee.getGender()))
                        .addProperty("mobile", employee.getMobile())
                        .addProperty("org_type", AgentOrgType.CHANNEL.name())
                        .addProperty("org_code", employee.getOrgCode())
                        .addProperty("org_name", employee.getOrgName())
                        .addProperty("employee_code", employee.getCode());
                if (employee.getBirthday() != null) {
                    builder.addProperty("birthday", TimeUtil.toDate(employee.getBirthday()));
                }
                getISensorsAnalytics().profileSet(builder.build());
            } catch (InvalidArgumentException e) {
                log.error("神策上报失败");
                e.printStackTrace();
            } catch (IOException e) {
                log.error("神策初始化失败");
                e.printStackTrace();
            }
        }
        try {
            getISensorsAnalytics().flush();
            log.info("神策上报完成");
        } catch (IOException e) {
            log.error("神策初始化失败");
            e.printStackTrace();
        } catch (InvalidArgumentException e) {
            log.error("神策上报失败");
            e.printStackTrace();
        }
    }

    @Override
    @Async("executor")
    public void updateEmployee(String employeeCode, String umId, String name, String gender, LocalDate birthday, String mobile) {
        try {
            boolean falg = false;
            UserRecord.Builder builder = UserRecord.builder().setDistinctId(umId == null ? getUmId(employeeCode) : umId).isLoginId(Boolean.TRUE);
            if (name != null) {
                builder.addProperty("name", name);
                falg = true;
            }
            if (gender != null) {
                builder.addProperty("gender", getGender(gender));
                falg = true;
            }
            if (birthday != null) {
                builder.addProperty("birthday", TimeUtil.toDate(birthday));
                falg = true;
            }
            if (mobile != null) {
                builder.addProperty("mobile", mobile);
                falg = true;
            }
            if (falg) {
                getISensorsAnalytics().profileSet(builder.build());
                getISensorsAnalytics().flush();
            }
        } catch (InvalidArgumentException e) {
            log.error("神策上报失败");
            e.printStackTrace();
        } catch (IOException e) {
            log.error("神策初始化失败");
            e.printStackTrace();
        }
    }

    @Override
    public void addSupervisorEmployee(SupervisorEmployee supervisorEmployee, String umId) {
        log.info("addSupervisorEmployee 神策上报 ::: request{},umId{},StaffId{},StaffUsername{},", JsonUtil.toJSON(supervisorEmployee),umId, RequestContextHolder.getStaffId(),RequestContextHolder.getStaffUsername());
        try {
            UserRecord.Builder builder = UserRecord.builder()
                    .setDistinctId(umId == null ? getUmId(supervisorEmployee.getEmployeeCode()) : umId)
                    .isLoginId(Boolean.TRUE)
                    .addProperty("$signup_time", Calendar.getInstance().getTime())
                    .addProperty("name", supervisorEmployee.getName())
                    .addProperty("gender", getGender(supervisorEmployee.getGender()))
                    .addProperty("mobile", supervisorEmployee.getMobile())
                    .addProperty("is_Supervision", Boolean.TRUE)
                    .addProperty("employee_code", supervisorEmployee.getEmployeeCode());
            getISensorsAnalytics().profileSet(builder.build());
            getISensorsAnalytics().flush();
            log.info("addSupervisorEmployee 神策上报成功!");
        } catch (InvalidArgumentException e) {
            log.error("神策上报失败");
            e.printStackTrace();
        } catch (IOException e) {
            log.error("神策初始化失败");
            e.printStackTrace();
        }
    }

    /**
     * 获取agentId
     */
    private String getUmId(String code) {
        List<AccountInfoDTO> agentUserInfos = umClient.getAgentsBatch(code);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(agentUserInfos), new ApiException("获取agentId失败"));
        return agentUserInfos.get(0).getUserId().toString();
    }

    private String getGender(String gender) {
        // UNKNOWN-保密,MALE-男性,FEMALE-女性;
        // 转换后0-男 ,1-女 ,2-未知
        switch (gender) {
            case "MALE":
                return "0";
            case "FEMALE":
                return "1";
            default:
                return "2";
        }
    }
}
