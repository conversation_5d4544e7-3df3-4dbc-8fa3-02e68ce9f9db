package com.hqins.agent.org.configuration;

import com.hqins.agent.org.service.SupervisorPerformanceBasicLawStrategy;
import com.hqins.agent.org.service.impl.Self2024Strategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.ApplicationContext;

/**
 * 策略工厂.
 *
 * <AUTHOR> MXH
 * @create 2025/3/19 9:57
 */
@Service
public class SupervisorPerformanceBasicLawStrategyFactory {

    @Autowired
    private ApplicationContext applicationContext;

    public SupervisorPerformanceBasicLawStrategy getStrategy(String type) {
        switch (type) {
            case "SELF_2024":
                return applicationContext.getBean(Self2024Strategy.class);
            case "SELF_LOTUS":
                return null; // TODO 基本法考核待开发
            default:
                throw new IllegalArgumentException("Unknown Basic Law Type: " + type);
        }
    }
}
