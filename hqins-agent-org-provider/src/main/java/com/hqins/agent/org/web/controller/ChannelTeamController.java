package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.enums.TeamStatus;
import com.hqins.agent.org.model.request.ChannelTeamAddRequest;
import com.hqins.agent.org.model.request.ChannelTeamQueryRequest;
import com.hqins.agent.org.model.vo.ChannelTeamTreeNodeVO;
import com.hqins.agent.org.model.vo.ChannelTeamVO;
import com.hqins.agent.org.model.vo.SimpleTeamTreeNodeVO;
import com.hqins.agent.org.service.ChannelTeamService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "渠道商-销售团队管理")
@RestController
@RequestMapping("/channel/teams")
@RefreshScope
@Slf4j
public class ChannelTeamController {

    @Autowired
    private ChannelTeamService channelTeamService;

    @ApiOperation("分页查询渠道商团队")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelTeamTreeNodeVO>> listTree(
            @ApiParam("归属渠道商名称") @RequestParam(value = "channelName", required = false) String channelName,
            @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
            @ApiParam("团队代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("团队名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("团队级别") @RequestParam(value = "level",required = false) TeamLevel level,
            @ApiParam("团队级别,支持多选") @RequestParam(value = "teamLevel",required = false) String teamLevel,
            @ApiParam("团队状态,ENABLED-有效；DISABLED-失效") @RequestParam(value = "status",required = false) String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        ChannelTeamQueryRequest queryRequest = ChannelTeamQueryRequest.builder()
                .channelName(channelName).orgName(orgName).code(code).name(name).level(level)
                .teamLevel(teamLevel).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(channelTeamService.listMyTree(queryRequest));
    }

    /**
     * 获取渠道商销售团队简化版，只展示名称编码
     * 有管理权限的且生效状态的渠道商销售团队
     * 下拉框中使用
     *
     * @return
     */
    @ApiOperation("获取有管理权限的渠道商销售团队，简化版下拉框里使用")
    @GetMapping("/my-simple")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SimpleTeamTreeNodeVO>> listMyTreeSimple(
            @ApiParam("销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("营销团队代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("营销团队名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("团队状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam(value = "current", required = false, defaultValue = "1") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "50") long size) {

        ChannelTeamQueryRequest queryRequest = ChannelTeamQueryRequest.builder()
                .orgCode(orgCode).code(code).name(name).status(status)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(channelTeamService.listMyTreeSimple(queryRequest));
    }

    /**
     * 获取销售团队详情
     *
     * @return
     */
    @ApiOperation("获取销售团队详情")
    @GetMapping("/{id:" + Strings.REGEX_ID + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<ChannelTeamVO> getById(
            @ApiParam("销售团队id") @PathVariable("id") Long id) {

        return ApiResult.ok(channelTeamService.getById(id));

    }


    @ApiOperation("创建销售团队")
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> save(@Valid @RequestBody ChannelTeamAddRequest request) {

        channelTeamService.save(request, true);
        return ApiResult.ok();
    }

    @ApiOperation("给销售团队设置主管")
    @PutMapping("/{id:" + Strings.REGEX_ID + "}/leader/{leaderId:" + Strings.REGEX_ID + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> updateLeader(
            @ApiParam("销售团队id") @PathVariable("id") Long id,
            @ApiParam("主管id") @PathVariable("leaderId") Long leaderId) {

        channelTeamService.updateLeader(id, leaderId);

        return ApiResult.ok();
    }

    @ApiOperation("更新销售团队状态")
    @PutMapping("/{id:" + Strings.REGEX_ID + "}/status/{status:" + Strings.REGEX_STRING + "}")
    public ApiResult<Void> updateStatus(
            @ApiParam("销售团队id") @PathVariable("id") Long id,
            @ApiParam("营销团队状态 ENABLED-有效 DISABLED-失效") @PathVariable("status") TeamStatus status) {

        channelTeamService.updateStatus(id, status);

        return ApiResult.ok();
    }
}
