package com.hqins.agent.org.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 指标预警--13个月继续率
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@HeadStyle(fillForegroundColor = 9 )
@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10)
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 10,bold= BooleanEnum.FALSE)
public class SupervisorMarkCr13PolicyExcel implements Serializable {

    @ExcelProperty("销售机构")
    @ApiModelProperty("销售机构")
    private String instName;

    @ExcelProperty("业务员姓名")
    @ApiModelProperty("业务员姓名")
    private String agentName;

    @ExcelProperty("13个月应收保费(元)")
    @ApiModelProperty("13个月应收保费(元)")
    private BigDecimal  cr13Premium;

    @ExcelProperty("13个月实收保费(元)")
    @ApiModelProperty("13个月实收保费(元)")
    private BigDecimal  cr13ActualPremium;

    @ExcelProperty("13个月继续率(%)")
    @ApiModelProperty("13个月继续率(%)")
    private BigDecimal cr13;



}
