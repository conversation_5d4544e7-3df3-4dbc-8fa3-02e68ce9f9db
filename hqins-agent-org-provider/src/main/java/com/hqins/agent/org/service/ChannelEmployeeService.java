package com.hqins.agent.org.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.ChannelTeam;
import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface ChannelEmployeeService {

    PageInfo<ChannelEmployeeVO> listMy(ChannelEmployeeRequest queryRequest);

    List<ChannelEmployee> listMyNoPage(ChannelEmployeeRequest queryRequest);

    PageInfo<AccountVO> listMyAccountVO(ChannelEmployeeRequest queryRequest);

    void save(ChannelEmployeeAddRequest request, ChannelTeam team);

    void checkAndSave(ChannelEmployeeAddRequest request);

    ChannelEmployeeCheckVo update(ChannelEmployeeUpdateRequest request);

    void leave(Long id) throws Exception;

    void leaveForChannel(ChannelEmployeeDeleteRequest deleteRequest) throws Exception;

    ChannelEmployeeCheckVo reentry(Long id);

    ChannelEmployeeVO getMyEmployee(String teamCode, String code);

    EmployeeVO getEmployeeVO(String employeeCode);
    EmployeeVO getEmployeeVOByYDL(String employeeCode);

    EmployeeVO getEmployeeVOByMobile(String mobile);

    MyEmployeeVO getMyEmployeeVO(String employeeCode);

    void leaveByTeamCode(String teamCode);

    void checkBatch(List<ChannelEmployeeAddRequest> employees , String fileName);

    void saveBatch(List<ChannelEmployeeAddRequest> employees);

    List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest);

    SimpleManager getManager();

    ChannelEmployeeCheckVo updateLicenseNo(String code, String licenseNo);

    /**
     * 根据证件号/执业证书号 全局查询渠道下销售人员信息
     *
     * @param idCode
     * @param licenseNo
     * @return
     */
    List<ChannelEmployee> getByIdCodeOrLicenseNo(String idCode, String licenseNo);

//    /**
//     * 根据证件号/执业证书号 全局查询渠道下销售人员信息
//     *
//     * @param idCodeList
//     * @param licenseNoList
//     * @return
//     */
//    List<ChannelEmployee> getListByIdCodeListOrLicenseNoList(List<String> idCodeList, List<String> licenseNoList);


    /**
     * 根据代码、人员状态查询渠道商人员信息
     *
     * @param channelCode
     * @param status
     * @return
     */
    List<EmployeeVO> getChannelEmployeeByOrgCodeAndStatus(String channelCode, String status);


    /**
     * 渠道商代理人信息导出
     *
     * @param queryRequest
     * @return
     */
    String exportData(ChannelEmployeeRequest queryRequest);

    /**
     * 根据代理人编码集合查询渠道代理人信息
     *
     * @param employeeCodeList
     * @return
     */
    List<EmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList);

    PageInfo<FileUploadRecordVO> queryFileUpload(long current, long size);

    PageInfo<BatchFailInfoVO> queryFileDetailed(long id,long current, long size);

    /**
     * 渠道商代理人校验
     * @param checkChannelEmployeeRequest
     * @return
     */
    CheckEmployeeResultVO checkForChannel(CheckChannelEmployeeRequest checkChannelEmployeeRequest);

    /**
     * mga\开放平台等创建用记时校验使用
     * @param addRequest
     * @return
     */
    CheckEmployeeResultVO checkForChannelForInit(InitEmployeeAddRequest addRequest);

    /**
     * 更新白名单缓存
     * @param idType
     * @param idNo
     */
    void refreshWhiteCache(String idType, String idNo) throws Exception ;

    /**
     * 删除缓存(包括白单与黑名单)
     * @param idType
     * @param idNo
     * @throws Exception
     */
    void deleteRedisCache(String idType, String idNo) throws Exception;

    /**
     * 模糊查询
     * @param codeOrNameLike
     * @param current
     * @param size
     * @return
     */
    PageInfo<ChannelEmployeeVO> listPage(String codeOrNameLike, long current, long size);

    Page<ChannelEmployee> listPage(long current, long size);

    Map listTurnOn();

    CheckEmployeeResultVO checkForChannelByLicense(CheckChannelEmployeeRequest checkChannelEmployeeRequest);

    void deleteRedisCacheByLicense(String employeeName, String licenseNo);
}
