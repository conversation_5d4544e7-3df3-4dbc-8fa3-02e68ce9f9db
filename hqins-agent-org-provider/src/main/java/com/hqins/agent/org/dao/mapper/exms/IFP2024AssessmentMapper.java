package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.model.vo.BasicLawInfoVO;
import com.hqins.agent.org.model.vo.CheckBatchInfoVO;
import com.hqins.agent.org.model.vo.FamilyRiskManagerCheckInfo;
import com.hqins.agent.org.model.vo.PartnerCheckInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("exms")
@Repository
public interface IFP2024AssessmentMapper {
    RankDef getRankInfo(@Param("employeeCode") String employeeCode);

    List<CheckBatchPersonInfoTmp> queryCheckPersonInfo(@Param("checkMonthList") List<String> checkMonthList, @Param("employeeCode") String employeeCode);

    List<CheckBatchPersonConfig> queryAllPersonConfig(@Param("tmpIdList") List<String> tmpIdList);

    List<TbEmpFlow> getFlowInfo(@Param("employeeCode") String employeeCode);

    List<CheckBatchPersonInfoTmp> queryCheckGroupInfo(@Param("checkMonthList") List<String> monthList,@Param("teamCode") String teamCode);

    List<CheckBatch> queryBatchList(@Param("checkMonthList") List<String> monthList,@Param("instCode") String instCode);

    String queryDate();

    List<BasicLawInfoVO> queryBasicLawList(@Param("instCodeList") List<String> instCodeList, @Param("teamCode") String teamCode,@Param("yearMonth") String yearMonth);

    List<BasicLawInfoVO> queryInstBasicLawList(@Param("instCodeList")List<String> instCodeList,@Param("teamCode") String teamCode,@Param("yearMonth")  String yearMonth);

    List<CheckBatchInfoVO> getCheckBatchInfo(@Param("partnerCode") String partnerCode,@Param("instCodeList") List<String> instCodeList, @Param("checkMonthList") List<String> checkMonthList);

    List<CheckBatchInfoVO> getCheckBatchInfoByVersionId(@Param("partnerCode") String partnerCode,@Param("checkMonthList") List<String> checkMonthList);

    List<FamilyRiskManagerCheckInfo> getRiskManagerPersonInfoListByVersionId(@Param("baseVersionDate") String baseVersionDate, @Param("instCodeList") List<String> instCodeList, @Param("teamCode") String teamCode);

    List<PartnerCheckInfo> getPartnerPersonInfoListByVersionId(@Param("partnerQuarter") String partnerQuarter,@Param("instCodeList") List<String> instCodeList,@Param("teamCode") String teamCode);

    List<FamilyRiskManagerCheckInfo> getProbationPersonInfo(@Param("checkBatchIdList") List<String> checkBatchIdList,@Param("instCodeList") List<String> instCodeList,@Param("teamCode") String teamCode);
}
