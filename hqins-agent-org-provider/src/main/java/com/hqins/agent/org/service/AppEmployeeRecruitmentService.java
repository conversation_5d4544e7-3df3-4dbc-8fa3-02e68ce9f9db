package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.RankSequ;
import com.hqins.agent.org.model.vo.AppEmployeeInfoVO;
import com.hqins.agent.org.model.vo.AppEmployeeRecruitmentInfoListVO;
import com.hqins.agent.org.model.vo.AppEmployeeRecruitmentProgressVO;

import java.util.List;

/**
 * app人员招募接口层
 *
 * <AUTHOR> MXH
 * @create 2025/2/19 9:37
 */
public interface AppEmployeeRecruitmentService {

    /**
     * 根据工号查询招募进度列表
     *
     * @param employeeCode  员工编码
     * @return              招募进度列表
     */
    AppEmployeeRecruitmentProgressVO queryRecruitmentProgressList(String employeeCode, String employeeName);

    /**
     * 根据工号查询我的代办列表
     *
     * @param employeeCode  员工编码
     * @return              我的代办列表
     */
    List<AppEmployeeRecruitmentInfoListVO> queryMyAgentList(String employeeCode, String employeeName);

    /**
     * 根据员工工号或者访客Id查询招募详情
     *
     * @param employeeCode  员工编码
     * @param visitorId     访客Id
     * @return              详情信息
     */
    AppEmployeeInfoVO queryAppRecruitmentEmployeeInfo(String employeeCode, String visitorId);

    /**
     * 查询该公司下拥有的职级序列
     * @param companyCode   公司编码
     * @return              职级序列
     */
    List<RankSequ> queryAppRankSequence(String companyCode);

    /**
     * 查寻该职级序列下的职级
     *
     * @param rankSequenceCode 职级序列编码
     * @param companyCode
     * @return 职级
     */
    List<RankDef> queryAppRankCode(String rankSequenceCode, String companyCode);

    Boolean isAuditFlag(String employeeCode);
}
