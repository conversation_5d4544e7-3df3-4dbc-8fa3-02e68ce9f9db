package com.hqins.agent.org.rpc.client.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @describe
 * @date 2022-05-18
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class PartnerEmployeeUpdate implements Serializable {

    @ApiModelProperty("代理人工号")
    private String employCode;

    @ApiModelProperty("变更前手机号")
    private String beforePhone;

    @ApiModelProperty("变更后手机号")
    private  String afterPhone;
}
