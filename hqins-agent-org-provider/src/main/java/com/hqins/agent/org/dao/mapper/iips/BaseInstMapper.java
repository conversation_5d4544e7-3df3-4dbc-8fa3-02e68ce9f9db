package com.hqins.agent.org.dao.mapper.iips;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.request.InsureOrgQueryRequest;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.vo.QueryAllVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021-05-07
 * @Description 机构表
 */
@Mapper
@DS("iips")
@Repository
public interface BaseInstMapper extends BaseMapper<BaseInst> {

    Page<Map<String, Object>> listPage(@Param("quest") OrgQueryRequest quest, @Param("cptype") String cptype, @Param("dataAccessOrgCodes") Set<String> dataAccessOrgCodes, @Param("containsSuperAdmin") boolean containsSuperAdmin, Page<?> page);

    List<Map<String, Object>> list(@Param("quest") OrgQueryRequest quest, @Param("cptype") String cptype, @Param("dataAccessOrgCodes") Set<String> dataAccessOrgCodes, @Param("containsSuperAdmin") boolean containsSuperAdmin);

    List<Map<String, Object>> listAll(@Param("quest") OrgQueryRequest queryRequest, @Param("cptype") String cptype);

    List<Map<String, Object>> listByTopCodes(@Param("topCodes") Set<String> topCodes);

    List<Map<String, Object>> listByOrgCodes(@Param("orgCodes") Set<String> orgCodes);

    List<Map<String, Object>> listAllChild(@Param("parentCode") String parentCode, @Param("cptype") String cptype);

    List<Map<String, Object>> selectInsureOrgsByCodes(@Param("orgCodes") List<String> orgCodes, @Param("cptype") String cptype);

    Page<Map<String, Object>> selectInsureOrgsAll(IPage<?> page, @Param("quest") InsureOrgQueryRequest queryRequest, @Param("cptype") String cptype);

    BaseInst queryInstByOrgCode(@Param("orgCode") String orgCode);

    List<QueryAllVO> queryAll();

    Map<String,String> queryThreeMangerCodeByOrgCode(@Param("orgCode") String orgCode);
}

