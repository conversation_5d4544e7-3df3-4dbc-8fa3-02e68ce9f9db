package com.hqins.agent.org.dao;

import com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo;
import com.hqins.agent.org.dao.entity.exms.RankDef;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.vo.AssessmentEmployeeInfoVO;
import com.hqins.agent.org.model.vo.AssessmentItemVO;
import com.hqins.agent.org.model.vo.AssessmentItemsVO;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public interface EmployeeAssessmentDao {
    String getCheckFrequency(String employeeCode);

    CheckBatchPersonInfo getCheckBatchPersonInfo(String checkMonth, String employeeCode);

    BigDecimal getPersonConfigValue(String id, String str);

    List<RankDef> getCorrespondingRankList(String basicLawVersionId, String rankSeqCode, String partnerInstCode);

    List<RankDef> getIfpCorrespondingRankList(String basicLawVersionId, String rankSeqCode, String partnerInstCode);

    AssessmentItemVO getIfpAssessmentItems(String basicLawVersionId, String rankCode, String rankSeqCode, String currentTarget);

    List<AssessmentItemsVO> getAssessmentItems(String id);

    AssessmentItemVO getYbAssessmentItems(String basicLawVersionId, String rankCode, String rankSeqCode);

    List<String> getCheckMonthHistory(String employeeCode);

    String getCheckBatchId(String checkMonth, String employeeCode);

    List<AssessmentEmployeeInfoVO> getAllAssessmentEmployeeInfo(String employeeCode, String checkBatchId);

    Tbsaleteam getTbSaleTeamInfo(String teamCode);

    List<CheckBatchPersonInfo> getAllAssessmentPersonal(String checkMonth, String teamCode);

    CheckBatchPersonInfo getMangerCheckBatchPersonInfo(String teamCode, String checkMonth);

    List<Tbsaleteam> getChildrenTbSaleTeams(String teamCode);

    List<AssessmentItemsVO> getAllItemsBySaleTeamCodes(String checkMonth, HashSet<String> saleTeamInCodes);

    List<AssessmentItemsVO> getAllActualItemsBySaleTeamCodes(String checkMonth, HashSet<String> saleTeamCodes);

    String getEmpCodeByEmpInCode(String empInCode);
}
