package com.hqins.agent.org.service.impl;

import com.alibaba.druid.sql.visitor.functions.If;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.mapper.settle.IFPDigitalScoreMapper;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.IFPDigitalScoreService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import sun.awt.AppContext;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/10 09:02
 */
@Service
public class IFPDigitalScoreServiceImpl implements IFPDigitalScoreService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private IFPDigitalScoreMapper ifpDigitalScoreMapper;


    @Override
    public List<DigitalDataVO> getAllMonthAndScoreData(String agentCode,String startDate) {
        List<DigitalDataVO> digitalDataVOList = ifpDigitalScoreMapper.getAllMonthAndScoreDataByAgentCode(agentCode,startDate);
        if (CollectionUtils.isEmpty(digitalDataVOList)){
            return null;
        }
        String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        generateMonthlyData(digitalDataVOList,startDate,endDate);
        return digitalDataVOList.stream().sorted(Comparator.comparing(DigitalDataVO::getMonthDate)).collect(Collectors.toList());
    }

    @Override
    public List<DigitalScoreCodeVO> getScore(List<String> agentCodeList, String queryDate) {
        return ifpDigitalScoreMapper.getMonthAndScoreByAgentCodeList(agentCodeList,queryDate);
    }

    @Override
    public List<DigitalScoreAllDetailVO> getScoreDetail(List<String> agentCodeList, String queryDate, String recordPointsFlag) {
        List<DigitalScoreDetailVO> scoreDetailByAgentCodeList = ifpDigitalScoreMapper.getScoreDetailByAgentCodeList(agentCodeList, queryDate, recordPointsFlag);
        if (CollectionUtils.isNotEmpty(scoreDetailByAgentCodeList)){
            Map<String, List<DigitalScoreDetailVO>> agentCodeMap = scoreDetailByAgentCodeList.stream().collect(Collectors.groupingBy(DigitalScoreDetailVO::getAgentCode));
            List<DigitalScoreAllDetailVO> digitalScoreAllDetailVOS = new ArrayList<>();
            agentCodeMap.forEach((k,v) -> {
                List<DigitalScoreAllDetailVO.DetailDataVO> detailDataVOS = new ArrayList<>();
                //封装明细列表集合
                for (DigitalScoreDetailVO digitalScoreDetailVO : v) {
                    DigitalScoreAllDetailVO.DetailDataVO detailDataVO = new DigitalScoreAllDetailVO.DetailDataVO();
                    detailDataVO.setActionTime(digitalScoreDetailVO.getActionTime());
                    detailDataVO.setActionType(digitalScoreDetailVO.getActionType());
                    detailDataVO.setRecordPointsFlag(digitalScoreDetailVO.getRecordPointsFlag());
                    detailDataVOS.add(detailDataVO);
                }
                //按完成时间倒序
                List<DigitalScoreAllDetailVO.DetailDataVO> sortedVOS = detailDataVOS.stream().sorted(Comparator.comparing(DigitalScoreAllDetailVO.DetailDataVO::getActionTime).reversed()).collect(Collectors.toList());
                //封装该代理人的工号，团队，分数等
                DigitalScoreAllDetailVO digitalScoreAllDetailVO = new DigitalScoreAllDetailVO();
                digitalScoreAllDetailVO.setDetailDataList(sortedVOS);
                digitalScoreAllDetailVO.setAgentCode(k);
                digitalScoreAllDetailVO.setTeamName(v.get(0).getTeamName());
                digitalScoreAllDetailVO.setScore(v.get(0).getScore());
                digitalScoreAllDetailVO.setTeamCode(v.get(0).getTeamCode());
                digitalScoreAllDetailVO.setAgentName(v.get(0).getAgentName());
                digitalScoreAllDetailVOS.add(digitalScoreAllDetailVO);
            });
            List<Tbsaleteam> allTbsaleteams = cacheService.getAllTbsaleteams();
            Map<String, Tbsaleteam> saleTeamMap = allTbsaleteams.stream().collect(Collectors.toMap(Tbsaleteam::getSaleteamincode, Function.identity()));
            Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
            //结果集中已经有的人员代码
            List<String> hasList = digitalScoreAllDetailVOS.stream().map(DigitalScoreAllDetailVO::getAgentCode).collect(Collectors.toList());
            for (String s : agentCodeList) {
                if (!hasList.contains(s)){
                    DigitalScoreAllDetailVO digitalScoreAllDetailVO = new DigitalScoreAllDetailVO();
                    Tbemp tbemp = allTbempMap.get(s);
                    if (null == tbemp){
                        continue;
                    }
                    digitalScoreAllDetailVO.setAgentCode(tbemp.getEmpcode());
                    digitalScoreAllDetailVO.setAgentName(tbemp.getEmpname());
                    digitalScoreAllDetailVO.setScore(0);
                    Tbsaleteam tbsaleteam = saleTeamMap.get(tbemp.getSaleteamincode());
                    if (null == tbsaleteam){
                        digitalScoreAllDetailVOS.add(digitalScoreAllDetailVO);
                        continue;
                    }

                    digitalScoreAllDetailVO.setTeamCode(tbsaleteam.getSaleteamcode());
                    digitalScoreAllDetailVO.setTeamName(tbsaleteam.getSaleteamname());
                    digitalScoreAllDetailVOS.add(digitalScoreAllDetailVO);
                }
            }
            //按分数倒序 上述逻辑已经保证digitalScoreAllDetailVOS不为空
            List<DigitalScoreAllDetailVO> sortedVOS = digitalScoreAllDetailVOS.stream().sorted(Comparator.comparing(DigitalScoreAllDetailVO::getScore).reversed()).collect(Collectors.toList());
            return sortedVOS;
        } else {
            List<DigitalScoreAllDetailVO> digitalScoreAllDetailVOS = new ArrayList<>();
            for (String s : agentCodeList) {
                List<Tbsaleteam> allTbsaleteams = cacheService.getAllTbsaleteams();
                Map<String, Tbsaleteam> saleTeamMap = allTbsaleteams.stream().collect(Collectors.toMap(Tbsaleteam::getSaleteamincode, Function.identity()));
                Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
                DigitalScoreAllDetailVO digitalScoreAllDetailVO = new DigitalScoreAllDetailVO();
                Tbemp tbemp = allTbempMap.get(s);
                if (null == tbemp){
                    continue;
                }
                digitalScoreAllDetailVO.setAgentCode(tbemp.getEmpcode());
                digitalScoreAllDetailVO.setAgentName(tbemp.getEmpname());
                digitalScoreAllDetailVO.setScore(0);
                Tbsaleteam tbsaleteam = saleTeamMap.get(tbemp.getSaleteamincode());
                if (null == tbsaleteam){
                    digitalScoreAllDetailVOS.add(digitalScoreAllDetailVO);
                    continue;
                }

                digitalScoreAllDetailVO.setTeamCode(tbsaleteam.getSaleteamcode());
                digitalScoreAllDetailVO.setTeamName(tbsaleteam.getSaleteamname());
                digitalScoreAllDetailVOS.add(digitalScoreAllDetailVO);
            }
            return digitalScoreAllDetailVOS;
        }
    }


    /**
     * 补充无数据的月份
     * @param list
     * @param startDate
     * @param endDate
     */
    private static void generateMonthlyData(List<DigitalDataVO> list, String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate start = YearMonth.parse(startDate, formatter).atDay(1);
        LocalDate end = YearMonth.parse(endDate, formatter).atEndOfMonth();

        LocalDate current = start;
        while (!current.isAfter(end)) {
            String monthString = current.format(formatter);

            // 检查月份是否存在于列表中
            boolean monthExists = false;
            for (DigitalDataVO vo : list) {
                if (vo.getMonthDate().equals(monthString)) {
                    monthExists = true;
                    break;
                }
            }

            // 如果月份不存在，则添加一个新的vo对象
            if (!monthExists) {
                DigitalDataVO newVO = new DigitalDataVO(monthString, 0);
                list.add(newVO);
            }

            // 增加一个月
            current = current.plusMonths(1);
        }
    }
}
