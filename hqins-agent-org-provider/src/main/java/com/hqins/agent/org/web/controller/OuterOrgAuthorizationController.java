package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.OuterOrgRequest;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.EmployeeAuthService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 * @Description
 */
@Api(tags = "销售员授权管理")
@RestController
@RequestMapping("/authorization/org")
@Slf4j
public class OuterOrgAuthorizationController {


    @Autowired
    private EmployeeAuthService employeeAuthService;


    @ApiOperation("根据authId获取销售员信息")
    @PostMapping()
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<EmployeeVO>> getEmployeesByOrgCode(
            @ApiParam("外部渠道查询") @RequestBody OuterOrgRequest request) {
        return ApiResult.ok(employeeAuthService.getEmployeesByOrgCode(request));
    }


}
