package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.DataAccessSaveRequest;
import com.hqins.agent.org.model.vo.MyDataAccessVO;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface DataAccessService {

    MyDataAccessVO getMyDataAccess();

    MyDataAccessVO getMyDataAccess(Long adminAppId,Long staffId);

    MyDataAccessVO getDataAccessByRoleId(Long roleId);

    void updateDataAccess(DataAccessSaveRequest request);

    MyDataAccessVO getMyDataAccessByAppIdStaffId(Long adminAppId, Long staffId);
}
