package com.hqins.agent.org.web.controller;


import com.alibaba.fastjson.JSON;
import com.hqins.agent.org.model.request.TeamPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.PerformanceService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.spring.web.json.Json;

import java.util.Date;
import java.util.List;


@Api(tags = "团队业绩")
@RestController
@RequestMapping("/team/manage")
@Slf4j
public class TeamManageController {

    @Autowired
    private PerformanceService performanceService;

    @ApiOperation("团队在职人力")
    @GetMapping("/getCurrentPersonList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<TeamManageVO> getTeamCurrentPersonList(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode) {
        try {
            return ApiResult.ok(performanceService.getTeamCurrentPersonList(saleTeamCode));
        } catch (Exception e) {
            log.error("当月收入业绩查询出错", e);
            return ApiResult.fail("当月收入业绩查询出错," + e.getMessage());
        }
    }


    @ApiOperation("团队当月业绩")
    @GetMapping("/getCurrentTeamPerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<TeamManageVO> getCurrentTeamPerformance(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode) {
        try {
            return ApiResult.ok(performanceService.getCurrentTeamPerformance(saleTeamCode));
        } catch (Exception e) {
            log.error("团队当月业绩查询出错", e);
            return ApiResult.fail("团队当月业绩查询出错," + e.getMessage());
        }
    }


    @ApiOperation("团队业绩趋势查询")
    @GetMapping("/getTeamTrendPerformanceTrend")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<PerformanceTrendVO>> getTeamTrendPerformanceTrend(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
                                                                            @ApiParam("查询类型:month:月业绩,half_year:半年业绩,active:活动人力") @RequestParam(value = "type") String type) {
        try {
            return ApiResult.ok(performanceService.getTeamTrendPerformanceTrend(saleTeamCode,type));
        } catch (Exception e) {
            log.error("团队当月业绩查询出错", e);
            return ApiResult.fail("团队当月业绩查询出错," + e.getMessage());
        }
    }



    @ApiOperation("团队业绩查询")
    @GetMapping("/getTeamRangePerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<TeamVersionVO> getTeamRangePerformance(@ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
                                                                      @ApiParam("查询类型:team 按团队;personal:按个人;product:按产品;rankSeqCode:按职级") @RequestParam(value = "type") String type,
                                                                      @ApiParam("业绩月(按团队查询必传)") @RequestParam(value = "performanceMonth",required = false) String performanceMonth,
                                                                      @ApiParam("缴费期间") @RequestParam(value = "paymentYears",required = false) String paymentYears,
                                                                      @ApiParam("开始日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "startDate",required = false) Date startDate,
                                                                      @ApiParam("结束日期") @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(value = "endDate",required = false) Date endDate) {
        try {
            return ApiResult.ok(performanceService.getTeamRangePerformance(saleTeamCode,type,performanceMonth,paymentYears,startDate,endDate));
        } catch (Exception e) {
            log.error("团队当月业绩查询出错", e);
            return ApiResult.fail("团队当月业绩查询出错," + e.getMessage());
        }
    }


    @ApiOperation("团队业绩保单明细查询")
    @PostMapping("/getTeamRangePerformancePolicyList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformancePolicyVO> getTeamRangePerformancePolicyList(@RequestBody TeamPerformanceRequest request) {
        try {
            log.info("团队业绩保单明细查询参数,{}", JSON.toJSON(request));
            return ApiResult.ok(performanceService.getTeamRangePerformancePolicyList(request));
        } catch (Exception e) {
            log.error("团队业绩保单明细查询出错", e);
            return ApiResult.fail("团队业绩保单明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("团队业绩保单明细查询")
    @PostMapping("/queryTeamRangePerformancePolicyList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformancePolicyVO> queryTeamRangePerformancePolicyList(@RequestBody TeamPerformanceRequest request) {
        try {
            log.info("团队业绩保单明细查询参数,{}", JSON.toJSON(request));
            return ApiResult.ok(performanceService.queryTeamRangePerformancePolicyList(request));
        } catch (Exception e) {
            log.error("团队业绩保单明细查询出错", e);
            return ApiResult.fail("团队业绩保单明细查询出错," + e.getMessage());
        }
    }


    @ApiOperation("团队目标业绩查询")
    @GetMapping("/getTeamTargetPerformance")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PerformanceVO> getTeamTargetPerformance(
            @ApiParam("团队代码") @RequestParam(value = "saleTeamCode") String saleTeamCode,
            @ApiParam("登录人代码") @RequestParam(value = "loginEmpCode",required = false) String loginEmpCode,
            @ApiParam("按年查询业绩:2024") @RequestParam(value = "queryYear",required = false) String queryYear,
            @ApiParam("按月查询业绩:2024-01") @RequestParam(value = "queryMonth",required = false) String queryMonth) {
        try {
            return ApiResult.ok(performanceService.getTeamTargetPerformance(saleTeamCode,loginEmpCode,queryYear,queryMonth));
        } catch (Exception e) {
            log.error("团队目标业绩查询出错", e);
            return ApiResult.fail("团队目标业绩查询查询出错," + e.getMessage());
        }
    }




}



