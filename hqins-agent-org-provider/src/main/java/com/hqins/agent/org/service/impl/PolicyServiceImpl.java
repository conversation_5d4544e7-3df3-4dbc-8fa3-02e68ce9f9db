package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.settle.PolicyExpire;
import com.hqins.agent.org.dao.mapper.settle.PolicyExpireMapper;
import com.hqins.agent.org.model.request.ExpireQueryListRequest;
import com.hqins.agent.org.model.vo.PartassignmanagerVO;
import com.hqins.agent.org.model.vo.PolicyExpireMapVO;
import com.hqins.agent.org.model.vo.PolicyExpireVO;
import com.hqins.agent.org.service.PartnerChannelRelationService;
import com.hqins.agent.org.service.PolicyService;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.web.RequestContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @Date 2023/12/12 14:14
 */
@Service
@Slf4j
public class PolicyServiceImpl implements PolicyService {

    private final PolicyExpireMapper policyExpireMapper;
    @Autowired
    private PartnerChannelRelationService partnerChannelRelationService;

    public PolicyServiceImpl(PolicyExpireMapper policyExpireMapper) {
        this.policyExpireMapper = policyExpireMapper;
    }

    @Override
    public PageInfo<PolicyExpireVO> getPartnerExpire(ExpireQueryListRequest request) {
        //满期小于等于今天  未满期 大于等于明天
        String employeeCode = RequestContextHolder.getEmployeeCode();
        Page<PolicyExpire> policyExpirePage = policyExpireMapper.selectPage(new Page<>(request.getCurrent(), request.getSize()), Wrappers.lambdaQuery(PolicyExpire.class)
                .eq(PolicyExpire::getAgentcode,employeeCode)
                .eq(PolicyExpire::getOrgType, AgentOrgType.PARTNER)
                .ge(PolicyExpire::getGetstartdate,request.getExpireDateStart())
                .le(PolicyExpire::getGetstartdate,request.getExpireDateEnd())
                .eq(StringUtils.isNotEmpty(request.getFlag()) ,PolicyExpire::getBalastate,request.getFlag())
                .and(StringUtils.isNotEmpty(request.getName()),wrapper -> wrapper
                        .like(PolicyExpire::getAppntname, request.getName())
                        .or()
                        .like(PolicyExpire::getInsuredname, request.getName())
                )
                .orderByAsc(PolicyExpire::getGetstartdate)
        );

        if (policyExpirePage.getTotal()>0 ){
            //计算距离满期天数
            return PageUtil.convert(policyExpirePage, x -> {
                PolicyExpireVO policyExpireVO = BeanCopier.copyObject(x, PolicyExpireVO.class);
                policyExpireVO.setDistanceExpiration(getDays(LocalDate.now(),x.getGetstartdate()));
                LocalDateTime insdalivedate = policyExpireVO.getInsdalivedate();
                LocalDate today = LocalDate.now();
                if (insdalivedate == null) {
                    // 未认证
                    policyExpireVO.setInsdalivestate("未认证");
                } else if (insdalivedate.toLocalDate().isEqual(today) || insdalivedate.toLocalDate().isAfter(today)) {
                    // 已认证
                    policyExpireVO.setInsdalivestate("已认证");
                } else {
                    // 认证已过期
                    policyExpireVO.setInsdalivestate("认证已过期");
                }

                return policyExpireVO;
            });

        }

        return new PageInfo<>();
    }

    @Override
    public PageInfo<PolicyExpireMapVO> getChannelExpire(ExpireQueryListRequest request) {

        String employeeCode = RequestContextHolder.getEmployeeCode();
        final List<PartassignmanagerVO> managerCode = partnerChannelRelationService.getMerchantOrgListByCustManagerCode(employeeCode);
        if (managerCode.isEmpty()){
            return new PageInfo<>();
        }
        final List<String> merchantOrgCode = managerCode.stream().map(PartassignmanagerVO::getMerchantOrgCode).collect(Collectors.toList());
        //merchantOrgCode去重
        final List<String> merchantOrgCodeDistinct = merchantOrgCode.stream().distinct().collect(Collectors.toList());

        List<PolicyExpire> policyExpires = policyExpireMapper.selectList(Wrappers.lambdaQuery(PolicyExpire.class)
//                .eq(PolicyExpire::getAgentcode, employeeCode)
                .eq(PolicyExpire::getOrgType, AgentOrgType.CHANNEL)
                .in(PolicyExpire::getOrgcode,merchantOrgCodeDistinct)
                .ge(PolicyExpire::getGetstartdate, request.getExpireDateStart())
                .le(PolicyExpire::getGetstartdate, request.getExpireDateEnd())
                .eq(StringUtils.isNotEmpty(request.getFlag()), PolicyExpire::getBalastate, request.getFlag())
                .and(StringUtils.isNotEmpty(request.getName()),wrapper -> wrapper
                        .like(PolicyExpire::getAppntname, request.getName())
                        .or()
                        .like(PolicyExpire::getInsuredname, request.getName())
                )
                .orderByAsc(PolicyExpire::getGetstartdate));




        List<PolicyExpireMapVO> policyExpireMapVOList = policyExpires.stream()
                .map(x -> {
                    PolicyExpireVO policyExpireVO = BeanCopier.copyObject(x, PolicyExpireVO.class);
                    policyExpireVO.setDistanceExpiration(getDays(LocalDate.now(), x.getGetstartdate()));
                    LocalDateTime insdalivedate = policyExpireVO.getInsdalivedate();
                    LocalDate today = LocalDate.now();
                    if (insdalivedate == null) {
                        // 未认证
                        policyExpireVO.setInsdalivestate("未认证");
                    } else if (insdalivedate.toLocalDate().isEqual(today) || insdalivedate.toLocalDate().isAfter(today)) {
                        // 已认证
                        policyExpireVO.setInsdalivestate("已认证");
                    } else {
                        // 认证已过期
                        policyExpireVO.setInsdalivestate("认证已过期");
                    }
                    return policyExpireVO;
                })
                .collect(Collectors.groupingBy(PolicyExpireVO::getOrgname))
                .entrySet().stream()
                .map(entry -> {
                    PolicyExpireMapVO policyExpireMapVO = new PolicyExpireMapVO();
                    policyExpireMapVO.setOrgName(entry.getKey());
                    policyExpireMapVO.setList(entry.getValue());
                    return policyExpireMapVO;
                }).sorted(Comparator.comparingInt(o -> -o.getList().size()))
                .collect(Collectors.toList());
        return PageUtil.getPageInfo(policyExpireMapVOList,request.getCurrent(),request.getSize());
    }

    @Override
    public PolicyExpireVO getPolicyExpire(ExpireQueryListRequest request) {
        PolicyExpire policyExpire = policyExpireMapper.selectOne(Wrappers.lambdaQuery(PolicyExpire.class)
                .eq(PolicyExpire::getContno, request.getContNo())
                .last("limit 1"));
        PolicyExpireVO policyExpireVO = BeanCopier.copyObject(policyExpire, PolicyExpireVO.class);
        if (policyExpireVO!=null){
            policyExpireVO.setDistanceExpiration(getDays(LocalDate.now(), policyExpireVO.getGetstartdate()));
            LocalDateTime insdalivedate = policyExpireVO.getInsdalivedate();
            LocalDate today = LocalDate.now();
            if (insdalivedate == null) {
                // 未认证
                policyExpireVO.setInsdalivestate("未认证");
            } else if (insdalivedate.toLocalDate().isEqual(today) || insdalivedate.toLocalDate().isAfter(today)) {
                // 已认证
                policyExpireVO.setInsdalivestate("已认证");
            } else {
                // 认证已过期
                policyExpireVO.setInsdalivestate("认证已过期");
            }
        }
        return policyExpireVO;
    }


    public  String getDays(LocalDate startDate, LocalDate endDate) {
        long days = ChronoUnit.DAYS.between(startDate, endDate);
        long years = ChronoUnit.YEARS.between(startDate, endDate.plusDays(1));

        if (years > 0) {
            return years + "年";
        } else {
            return days + "天";
        }
    }

}
