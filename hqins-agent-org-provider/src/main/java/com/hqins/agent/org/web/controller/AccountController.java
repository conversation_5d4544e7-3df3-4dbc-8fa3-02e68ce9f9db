package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.AccountQueryRequest;
import com.hqins.agent.org.model.vo.AccountVO;
import com.hqins.agent.org.service.AccountService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/5/25
 * @Description
 */
@Api(tags = "账号管理")
@RestController
@RequestMapping("/accounts")
@RefreshScope
@Slf4j
public class AccountController {

    @Autowired
    private AccountService accountService;

    /**
     * 分页查询合伙人、渠道商销售员账号列表
     * 有管理权限的且生效状态的账号
     */
    @ApiOperation("分页查询合伙人、渠道商销售员账号列表")
    @GetMapping("/{orgType:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<AccountVO>> listMy(
            @ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
            @ApiParam("渠道商、合伙人名称") @RequestParam(value = "topName", required = false) String topName,
            @ApiParam("归属销售机构名称") @RequestParam(value = "orgName", required = false) String orgName,
            @ApiParam("归属销售团队名称") @RequestParam(value = "teamName", required = false) String teamName,
            @ApiParam("销售员代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售员名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售员手机号") @RequestParam(value = "mobile", required = false) String mobile,
            @ApiParam("是否是督导账号") @RequestParam(value = "isSupervisor", required = false) Boolean isSupervisor,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        AccountQueryRequest queryRequest = AccountQueryRequest.builder().code(code).name(name).orgName(orgName)
                .topName(topName).teamName(teamName).mobile(mobile).orgType(orgType).isSupervisor(isSupervisor)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(accountService.listMy(queryRequest));
    }


    @ApiOperation("账号冻结")
    @PutMapping("/{orgType:" + Strings.REGEX_STRING + "}/blocked/{employeeCode:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> blocked(@ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
                             @ApiParam("销售员代码") @PathVariable("employeeCode") String employeeCode,
                             @ApiParam("agentId") @RequestParam("agentId") long agentId) {
        accountService.blocked(orgType, employeeCode, agentId);
        return ApiResult.ok();
    }

    @ApiOperation("账号解冻")
    @PutMapping("/{orgType:" + Strings.REGEX_STRING + "}/unblocked/{employeeCode:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> unblocked(@ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
                               @ApiParam("销售员代码") @PathVariable("employeeCode") String employeeCode,
                               @ApiParam("agentId") @RequestParam("agentId") long agentId) {
        accountService.unblocked(orgType, employeeCode, agentId);
        return ApiResult.ok();
    }

    @ApiOperation("账号微信解绑")
    @PutMapping("/{orgType:" + Strings.REGEX_STRING + "}/unbindWx/{employeeCode:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> unbindWx(@ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
                              @ApiParam("销售员代码") @PathVariable("employeeCode") String employeeCode,
                              @ApiParam("agentId") @RequestParam("agentId") long agentId) {
        accountService.unbindWx(orgType, employeeCode, agentId);
        return ApiResult.ok();
    }

    @ApiOperation("账号重置密码")
    @PutMapping("/{orgType:" + Strings.REGEX_STRING + "}/resetPwd/{employeeCode:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> resetPwd(@ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
                              @ApiParam("销售员代码") @PathVariable("employeeCode") String employeeCode,
                              @ApiParam("agentId") @RequestParam("agentId") long agentId) {
        accountService.resetPwd(orgType, employeeCode, agentId);
        return ApiResult.ok();
    }

    @ApiOperation("给没有账号的用户，同步um（创建账号）")
    @PutMapping("/{orgType:" + Strings.REGEX_STRING + "}/synchronization/{employeeCode:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> synchronizationUm(@ApiParam("组织机构类型 CHANNEL-渠道商 PARTNER-合伙人") @PathVariable(value = "orgType") AgentOrgType orgType,
                                     @ApiParam("销售员代码") @PathVariable("employeeCode") String employeeCode) {
        accountService.synchronizationUm(orgType, employeeCode);
        return ApiResult.ok();
    }


}
