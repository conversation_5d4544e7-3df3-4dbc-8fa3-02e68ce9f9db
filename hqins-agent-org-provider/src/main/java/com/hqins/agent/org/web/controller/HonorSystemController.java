package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.HonorSystemService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/3/12
 */
@Api(tags = "荣誉体系管理")
@RestController
@RequestMapping("/honor-system")
@Slf4j
public class HonorSystemController {

    @Autowired
    private HonorSystemService honorSystemService;

    @ApiOperation("荣誉贺报列表查询")
    @GetMapping("/honor-announcements-teams")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<HonoraryTitleVO> getTeamHonorAnnouncements(@ApiParam("团队编码") @RequestParam(value = "saleTeamCode", required = true) String saleTeamCode,
                                                                @ApiParam("荣誉类型") @RequestParam(value = "honorType", required = false) String honorType,
                                                                @ApiParam("年度") @RequestParam(value = "year", required = true) String year) {
        try {
            return ApiResult.ok(honorSystemService.getTeamHonoraryTitleList(saleTeamCode, honorType, year));
        } catch (Exception e) {
            log.error("荣誉贺报列表查询出错", e);
            return ApiResult.fail("荣誉贺报列表查询出错," + e.getMessage());
        }
    }

    @ApiOperation("荣誉贺报详情")
    @GetMapping("/honor-announcements-agents")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<HonoraryTitleInfoVO> getAgentHonorAnnouncementDetail(@ApiParam("贺报ID") @RequestParam(value = "honorId", required = true) String honorId) {
        try {
            return ApiResult.ok(honorSystemService.getEmpHonoraryDetail(honorId));
        } catch (Exception e) {
            log.error("荣誉贺报详情出错", e);
            return ApiResult.fail("荣誉贺报详情出错," + e.getMessage());
        }
    }

    @ApiOperation("荣誉列表查询")
    @GetMapping("/honor-agents")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<HonorListVO> queryHonorList(@ApiParam("代理人编码") @RequestParam(value = "empCode", required = true) String empCode) {
        try {
            return ApiResult.ok(honorSystemService.getHonorList(empCode));
        } catch (Exception e) {
            log.error("荣誉列表查询出错", e);
            return ApiResult.fail("荣誉列表查询出错," + e.getMessage());
        }
    }


    @ApiOperation("荣誉详情")
    @GetMapping("/honor-detail")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<HonorDetailVO> queryHonorDetail(@ApiParam("代理人编码") @RequestParam(value = "empCode", required = true) String empCode,
                                                     @ApiParam("荣誉类型") @RequestParam(value = "honorType", required = true) String honorType) throws Exception {
        try {
            return ApiResult.ok(honorSystemService.getHonorDetail(empCode, honorType));
        } catch (Exception e) {
            log.error("荣誉详情查询异常", e);
            return ApiResult.fail("荣誉详情查询异常," + e.getMessage());
        }
    }

    @ApiOperation("荣誉历史")
    @GetMapping("/honor-history")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<HistoryHonorDetailVO> queryHonorHistory(@ApiParam("代理人编码") @RequestParam(value = "empCode", required = true) String empCode) {
        try {
            return ApiResult.ok(honorSystemService.getHonorHistory(empCode));
        } catch (Exception e) {
            log.error("荣誉历史查询出错", e);
            return ApiResult.fail("荣誉历史查询出错," + e.getMessage());
        }
    }

}
