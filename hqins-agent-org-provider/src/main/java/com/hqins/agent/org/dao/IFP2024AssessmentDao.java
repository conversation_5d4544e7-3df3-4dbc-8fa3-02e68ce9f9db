package com.hqins.agent.org.dao;

import com.hqins.agent.org.dao.entity.exms.*;
import com.hqins.agent.org.model.vo.BasicLawInfoVO;

import java.time.LocalDate;
import java.util.List;

public interface IFP2024AssessmentDao {
    RankDef getRankInfo(String employeeCode);

    List<CheckBatchPersonInfoTmp> queryCheckPersonInfo(List<String> checkMonthList, String employeeCode);

    List<CheckBatchPersonConfig> queryAllPersonConfig(List<String> tmpIdList);

    List<TbEmpFlow> getFlowInfo(String employeeCode);

    List<CheckBatchPersonInfoTmp> queryCheckGroupInfo(List<String> monthList, String teamCode);

    List<CheckBatch> queryBatchList(List<String> monthList, String instCode);

    LocalDate queryDate();

    List<BasicLawInfoVO> queryBasicLawList(List<String> instCodeList, String tamCode, String yearMonth);
}
