package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.service.EmployeeLogService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * 人员工作日志控制层.
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:14
 */
@Api(tags = "人员工作日志")
@RestController
@RequestMapping("/employeeLog")
@Slf4j
@RequiredArgsConstructor
public class EmployeeLogController {

    private final EmployeeLogService employeeLogService;

    @ApiOperation("查阅范围")
    @GetMapping("/query")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<EmployeeLogResultVO> employeeLogQuery(@ApiParam("代理人工号") @RequestParam(value = "employeeCode", required = true) String employeeCode){
        return ApiResult.ok(employeeLogService.employeeLogQuery(employeeCode));
    }

    @ApiOperation("批阅范围")
    @GetMapping("/audit")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<EmployeeLogResultVO> employeeLogAudit(@ApiParam("代理人工号") @RequestParam(value = "employeeCode", required = true) String employeeCode){
        return ApiResult.ok(employeeLogService.employeeLogAudit(employeeCode));
    }
}
