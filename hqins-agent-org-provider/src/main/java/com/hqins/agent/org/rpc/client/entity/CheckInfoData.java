package com.hqins.agent.org.rpc.client.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 返回数据实体类
 * @Author: lijian
 * @Date: 2023/9/17 10:21 下午
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CheckInfoData implements Serializable {

    private String chargeSign;

    private String informationDate;

    private String ifExist;

    private String name;

    private String sex;

    private String carType;

    private String cardNo;

    private List<CheckInfoDetail> detailList;
}
