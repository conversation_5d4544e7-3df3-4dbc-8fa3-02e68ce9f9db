package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.model.enums.AppTopCode;
import com.hqins.agent.org.model.enums.LoginType;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.AppService;
import com.hqins.agent.org.service.PartnerEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * APP外网接口
 * <AUTHOR>
 * @since 2024-06-13
 */
@Service
@Slf4j
public class AppServiceImpl  implements AppService {

    @Resource
    private PartnerEmployeeService partnerEmployeeService;


    @Override
    public boolean checkLogin(String code, LoginType loginType) {
        /**
         * P00003，团险业务部
         * P00004，个险业务部
         */
        try {
            if (loginType.equals(LoginType.EMP_CODE)){
                EmployeeVO employeeVO = partnerEmployeeService.getEmployeeVO(code);
                return checkTopCode(employeeVO);
            }else if (loginType.equals(LoginType.MOBILE)){
                EmployeeVO employeeVO = partnerEmployeeService.getEmployeeVOByMobile(code);
                return checkTopCode(employeeVO);
            }
            return Boolean.FALSE;
        }catch (Exception e){
            log.error("获取员工信息失败，异常信息：", e);
            return Boolean.FALSE;
        }
    }
    private Boolean checkTopCode(EmployeeVO employeeVO){
        if (employeeVO != null && AppTopCode.isValid(employeeVO.getTopCode())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
