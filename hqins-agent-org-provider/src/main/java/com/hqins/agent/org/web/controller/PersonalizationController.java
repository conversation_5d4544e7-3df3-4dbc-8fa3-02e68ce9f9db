package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.dao.entity.org.CustomMenu;
import com.hqins.agent.org.dao.entity.org.Personalization;
import com.hqins.agent.org.model.enums.AppType;
import com.hqins.agent.org.model.request.PersonalizationSaveByFeatureRequest;
import com.hqins.agent.org.model.request.PersonalizationSaveRequest;
import com.hqins.agent.org.model.request.PersonalizationrQueryRequest;
import com.hqins.agent.org.model.vo.CustomMenuVO;
import com.hqins.agent.org.model.vo.MenuGroupVO;
import com.hqins.agent.org.service.PersonalizationService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "个性化设置")
@RestController
@RequestMapping("/personalization")
@RefreshScope
@Slf4j
public class PersonalizationController {

    @Autowired
    private PersonalizationService personalizationService;


    @ApiOperation("获取合伙人全量菜单，包含选中有权限节点")
    @GetMapping("/{appType:" + Strings.REGEX_STRING + "}/custom-menu/{code:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<MenuGroupVO>> getCustomMenu(
            @ApiParam("合伙人或者渠道商编码") @PathVariable("code") String code,
            @ApiParam("类型") @PathVariable(value = "appType") AppType appType) {

        PersonalizationrQueryRequest request = PersonalizationrQueryRequest.builder().code(code).appType(appType).build();
        return ApiResult.ok(personalizationService.getAllCustomMenu(request));
    }

    @ApiOperation("更新机构的个性化设置")
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> updateCustomMenu(@Valid @RequestBody PersonalizationSaveRequest request) {
        personalizationService.updateCustomMenu(request);
        return ApiResult.ok(null);
    }

    @ApiOperation("按照功能更新机构权限")
    @PostMapping("/by-feature")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> updateCustomMenuByFeature(@Valid @RequestBody PersonalizationSaveByFeatureRequest request) {
        personalizationService.updateCustomMenuByFeature(request);
        return ApiResult.ok(null);
    }

    @ApiOperation("获取个性化菜单")
    @GetMapping("/{appType:" + Strings.REGEX_STRING + "}")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<CustomMenuVO>> getPartnerCfg(
            @ApiParam("应用类型：H5-H5, MINI_PROGRAM-小程序") @PathVariable("appType") AppType appType) {

        PersonalizationrQueryRequest request = PersonalizationrQueryRequest.builder().appType(appType).build();

        return ApiResult.ok(personalizationService.getMyCustomMenu(request));
    }


    @ApiOperation("根据状态查询功能列表")
    @GetMapping("/by-conditions")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<CustomMenu>> getCustomMenuNode(
            @RequestParam(value = "platform",required = false) String platform,
            @RequestParam(value = "node",required = false) String node,
            @RequestParam(value = "name",required = false) String name,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") Long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") Long size
            ) {
        return ApiResult.ok(personalizationService.getCustomMenuNode(platform, node,name,current,size));
    }


    @ApiOperation("功能选择机构的编辑回显")
    @GetMapping("/get-org")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<Personalization>> getOrg(
            @RequestParam(value = "code") String code) {
        return ApiResult.ok(personalizationService.getOrg(code));
    }

    @ApiOperation("新建功能菜单")
    @PutMapping("/menu")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<String> createMenu(@Valid @RequestBody CustomMenu request) {
        return ApiResult.ok(personalizationService.createMenu(request));
    }

    @ApiOperation("删除功能菜单")
    @DeleteMapping("/menu")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<String> deleteMenu(@RequestParam(value = "id") Long id) {
        personalizationService.deleteMenu(id);
        return ApiResult.ok(null);
    }

    @ApiOperation("更新功能菜单")
    @PostMapping("/menu")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<String> updateMenu(@Valid @RequestBody CustomMenu request) {
        return ApiResult.ok(personalizationService.updateMenu(request));
    }
}
