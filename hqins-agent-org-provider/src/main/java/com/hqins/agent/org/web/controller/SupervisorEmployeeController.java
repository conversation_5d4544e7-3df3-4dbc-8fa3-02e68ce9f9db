package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.SimpleNodeVO;
import com.hqins.agent.org.model.vo.SupervisorEmployeeVO;
import com.hqins.agent.org.model.vo.TreeNodeVO;
import com.hqins.agent.org.service.SupervisorEmployeeService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/4/3 17:22
 */
@Api(tags = "督导账号")
@RestController
@RequestMapping("/supervisor")
@RefreshScope
@Slf4j
public class SupervisorEmployeeController {
   final private SupervisorEmployeeService supervisorEmployeeService;

    public SupervisorEmployeeController(SupervisorEmployeeService supervisorEmployeeService) {
        this.supervisorEmployeeService = supervisorEmployeeService;
    }

    @ApiOperation("分页查询督导账号")
    @GetMapping("/my")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<SupervisorEmployeeVO>> listMy(
            @ApiParam("账号类型") @RequestParam(value = "roleType", required = false) SupervisorType roleType,
            @ApiParam("合伙人编码") @RequestParam(value = "topCode", required = false) String topCode,
            @ApiParam("机构编码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("人员姓名") @RequestParam(value = "name", required = false) String name,
            @ApiParam("人员代码") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("手机号") @RequestParam(value = "mobile", required = false) String mobile,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        SupervisorEmployeeRequest supervisorEmployeeRequest = SupervisorEmployeeRequest.builder()
                .roleType(roleType)
                .topCode(topCode)
                .orgCode(orgCode)
                .name(name)
                .employeeCode(employeeCode)
                .mobile(mobile)
                .current(current)
                .size(size)
                .build();
        supervisorEmployeeRequest.correctPageQueryParameters();
        return ApiResult.ok(supervisorEmployeeService.listMy(supervisorEmployeeRequest));
    }

    @ApiOperation("查询督导账号")
    @GetMapping("/supervisorEmps")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SupervisorEmployeeVO>> selectSupervisorEmployeeList(
            @ApiParam("账号类型") @RequestParam(value = "roleType", required = false) SupervisorType roleType,
            @ApiParam("机构编码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("人员代码") @RequestParam(value = "employeeCode", required = false) String employeeCode,
            @ApiParam("手机号") @RequestParam(value = "mobile", required = false) String mobile) {
        SupervisorEmployeeRequest supervisorEmployeeRequest = SupervisorEmployeeRequest.builder()
                .roleType(roleType)
                .orgCode(orgCode)
                .employeeCode(employeeCode)
                .mobile(mobile)
                .build();
        supervisorEmployeeRequest.correctPageQueryParameters();
        return ApiResult.ok(supervisorEmployeeService.selectSupervisorEmployeeList(supervisorEmployeeRequest));
    }

    @ApiOperation("查询督导账号POST 与ORG的selectSupervisorEmployeeList能力相同")
    @PostMapping("/supervisorEmps-post")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SupervisorEmployeeVO>> selectSupervisorEmployeeListPost(@RequestBody SelectSupervisorEmployeeListPostRequest request) {
        return ApiResult.ok(supervisorEmployeeService.selectSupervisorEmployeeListPost(request));
    }

    @ApiOperation("创建账号")
    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> save(@Valid @RequestBody SupervisorEmployeeAddRequest request) {
        supervisorEmployeeService.checkAndSave(request);
        return ApiResult.ok();
    }

    @ApiOperation("更新账号信息")
    @PostMapping("/update")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> update(@Valid @RequestBody SupervisorEmployeeUpdateRequest request) {
        supervisorEmployeeService.updateInformation(request);
        return ApiResult.ok();
    }

    @ApiOperation("人员停用")
    @PutMapping("/leave")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> leave(@ApiParam("人员id") @RequestParam Long id) {
        supervisorEmployeeService.leave(id);
        return ApiResult.ok();
    }

    @ApiOperation("人员起用")
    @PutMapping("/reentry")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> reentry(@ApiParam("人员id") @RequestParam("id") Long id) {
        supervisorEmployeeService.reentry(id);
        return ApiResult.ok();
    }
    @ApiOperation("督导账号机构切换")
    @PutMapping("/conversion")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> reentry(@Valid @RequestBody SupervisorEmployeeOrgUpdateRequest request) {
        supervisorEmployeeService.conversions(request);
        return ApiResult.ok();
    }


    @ApiOperation("查询账号切换机构")
    @PutMapping("/getCodeList")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Map<String, List<String>>> getCodeList() {
        return ApiResult.ok(supervisorEmployeeService.getCodeList());
    }


    @ApiOperation("获取有管理权限的合伙人，督导账号H5使用")
    @GetMapping("/getTop")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<SimpleNodeVO>> getTop() {
        return ApiResult.ok(supervisorEmployeeService.getTop());
    }

    @ApiOperation("获取有管理权限的合伙人销售机构，督导账号H5使用")
    @GetMapping("/getOrg")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<List<TreeNodeVO>> getOrg(
            @ApiParam("合伙人代码") @RequestParam(value = "partnerCode", required = false) String partnerCode,
            @ApiParam("销售机构代码") @RequestParam(value = "code", required = false) String code,
            @ApiParam("销售机构名称") @RequestParam(value = "name", required = false) String name,
            @ApiParam("销售机构状态 ENABLED-有效 ALL-所有") @RequestParam(value = "status", required = false, defaultValue = "ENABLED") String status,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam(value = "size", required = false, defaultValue = "999") long size) {
        OrgQueryRequest queryRequest = OrgQueryRequest.builder()
                .code(code).name(name).topCode(partnerCode).status(status).current(1L).size(size).build();
        return ApiResult.ok(supervisorEmployeeService.getOrg(queryRequest));
    }


    @ApiOperation("更新数据")
    @GetMapping("/updateCode")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<Void> updateCode(@RequestParam(value = "type") String type) {
        supervisorEmployeeService.updateCode(type);
        return ApiResult.ok();
    }

}
