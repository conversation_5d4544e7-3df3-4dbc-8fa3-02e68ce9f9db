package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hqins.agent.org.dao.entity.exms.CheckBatchPersonConfig;
import com.hqins.agent.org.dao.mapper.exms.IFP2024AssessmentMapper;
import com.hqins.agent.org.model.request.SupervisorPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.SupervisorPerformanceBasicLawStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 内勤看板-2024基本法.
* <AUTHOR> MXH
* @create 2025/3/19 9:59
*/

@Service
public class Self2024Strategy implements SupervisorPerformanceBasicLawStrategy {

    private static final Logger log = LoggerFactory.getLogger(Self2024Strategy.class);

    @Autowired
    private IFP2024AssessmentMapper assessmentMapper;

    @Override
    public List<BasicLawInfoVO> generateBasicLawInfo(SupervisorPerformanceRequest request) {
        // 使用try-with-resources来管理线程池
        ExecutorService executor = Executors.newFixedThreadPool(4);
        try {
            // 创建四个异步任务
            CompletableFuture<BasicLawInfoVO> future1 = CompletableFuture.supplyAsync(() -> this.queryNonProbationP3(request), executor);
            CompletableFuture<BasicLawInfoVO> future2 = CompletableFuture.supplyAsync(() -> this.queryNonProbationP4(request), executor);
            CompletableFuture<BasicLawInfoVO> future3 = CompletableFuture.supplyAsync(() -> this.queryProbationP3(request), executor);
            CompletableFuture<BasicLawInfoVO> future4 = CompletableFuture.supplyAsync(() -> this.queryProbationP4(request), executor);

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3, future4);
            allFutures.join(); // 阻塞直到所有任务完成


            // 初始化两个列表用于存储结果
            List<FamilyRiskManagerCheckInfo> familyRiskManagerCheckInfos = new ArrayList<>();
            List<PartnerCheckInfo> partnerCheckInfos = new ArrayList<>();

            // 获取每个任务的结果并合并到相应的列表中
            for (CompletableFuture<BasicLawInfoVO> future : Arrays.asList(future1, future2, future3, future4)) {
                BasicLawInfoVO basicLawInfoVO = null;
                try {
                    basicLawInfoVO = future.get();
                } catch (InterruptedException | ExecutionException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Error executing async task", e);
                }
                if (basicLawInfoVO != null) {
                    addIfNotNull(familyRiskManagerCheckInfos, basicLawInfoVO.getFamilyRiskManagerInfoVO(), FamilyRiskManagerInfoVO::getFamilyRiskManagerCheckInfos);
                    addIfNotNull(partnerCheckInfos, basicLawInfoVO.getPartnerInfoVO(), PartnerInfoVO::getPartnerCheckInfoList);
                }
            }

            //过滤家庭风险管理师集合中的重复数据
            filterFamilyRiskManagerCheckInfos(familyRiskManagerCheckInfos);
            //过滤合伙人集合中的重复数据
            filterPartnerCheckInfos(partnerCheckInfos);

            if (CollUtil.isEmpty(familyRiskManagerCheckInfos) && CollUtil.isEmpty(partnerCheckInfos)) {
                return new ArrayList<>();
            }

            // 根据BasicLawId对familyRiskManagerCheckInfos进行分组
            Map<String, List<FamilyRiskManagerCheckInfo>> familyRiskManagerGrouped = groupByBasicLawIdForFamilyRiskManager(familyRiskManagerCheckInfos);

            // 根据BasicLawId对partnerCheckInfos进行分组
            Map<String, List<PartnerCheckInfo>> partnerGrouped = groupByBasicLawIdForPartner(partnerCheckInfos);

            List<BasicLawInfoVO> basicLawInfoVOS = new ArrayList<>();

            // 处理家庭风险管理师的数据
            for (String basicLawId : familyRiskManagerGrouped.keySet()) {
                processBasicLawInfo(basicLawInfoVOS, familyRiskManagerGrouped.get(basicLawId), partnerGrouped.getOrDefault(basicLawId, Collections.emptyList()));
            }

            // 处理仅存在于partnerCheckInfos中的数据
            for (String basicLawId : partnerGrouped.keySet()) {
                if (!familyRiskManagerGrouped.containsKey(basicLawId)) {
                    processBasicLawInfo(basicLawInfoVOS, Collections.emptyList(), partnerGrouped.get(basicLawId));
                }
            }
            return basicLawInfoVOS;
        }finally {
            executor.shutdown();
        }
    }

    private void filterPartnerCheckInfos(List<PartnerCheckInfo> partnerCheckInfos) {
        if(!CollUtil.isEmpty(partnerCheckInfos)) {
            // 使用 Stream 处理，按 agentCode 分组，保留 checkMonth 较小的元素
            Map<String, PartnerCheckInfo> filteredMap = partnerCheckInfos.stream()
                    .collect(Collectors.toMap(
                            // 键：agentCode
                            PartnerCheckInfo::getAgentCode,
                            // 值：当前对象
                            Function.identity(),
                            // 合并策略：保留 checkMonth 较小的元素
                            (existing, candidate) -> {
                                // 将字符串转换为 YearMonth 对象进行比较
                                YearMonth existingMonth = YearMonth.parse(existing.getCheckMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
                                YearMonth candidateMonth = YearMonth.parse(candidate.getCheckMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
                                return existingMonth.isBefore(candidateMonth) ? existing : candidate;
                            }
                    ));

            // 清空原列表并填充过滤后的结果
            partnerCheckInfos.clear();
            partnerCheckInfos.addAll(filteredMap.values());
        }
    }

    private void filterFamilyRiskManagerCheckInfos(List<FamilyRiskManagerCheckInfo> familyRiskManagerCheckInfos) {
        if(!CollUtil.isEmpty(familyRiskManagerCheckInfos)) {
            // 使用 Stream 处理，按 agentCode 分组，保留 checkMonth 较小的元素
            Map<String, FamilyRiskManagerCheckInfo> filteredMap = familyRiskManagerCheckInfos.stream()
                    .collect(Collectors.toMap(
                            // 键：agentCode
                            FamilyRiskManagerCheckInfo::getAgentCode,
                            // 值：当前对象
                            Function.identity(),
                            // 合并策略：保留 checkMonth 较小的元素
                            (existing, candidate) -> {
                                // 将字符串转换为 YearMonth 对象进行比较
                                YearMonth existingMonth = YearMonth.parse(existing.getCheckMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
                                YearMonth candidateMonth = YearMonth.parse(candidate.getCheckMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));
                                return existingMonth.isBefore(candidateMonth) ? existing : candidate;
                            }
                    ));

            // 清空原列表并填充过滤后的结果
            familyRiskManagerCheckInfos.clear();
            familyRiskManagerCheckInfos.addAll(filteredMap.values());
        }
    }

    private <S, T> void addIfNotNull(List<T> targetList, S sourceObject, Function<S, List<T>> getter) {
        if (sourceObject != null) {
            List<T> items = getter.apply(sourceObject);
            if (items != null) {
                targetList.addAll(items);
            }
        }
    }

    private Map<String, List<FamilyRiskManagerCheckInfo>> groupByBasicLawIdForFamilyRiskManager(List<FamilyRiskManagerCheckInfo> checkInfos) {
        if (CollUtil.isEmpty(checkInfos)) {
            return Collections.emptyMap();
        }
        return checkInfos.stream()
                .collect(Collectors.groupingBy(FamilyRiskManagerCheckInfo::getBasicLawId));
    }

    private Map<String, List<PartnerCheckInfo>> groupByBasicLawIdForPartner(List<PartnerCheckInfo> checkInfos) {
        if (CollUtil.isEmpty(checkInfos)) {
            return Collections.emptyMap();
        }
        return checkInfos.stream()
                .collect(Collectors.groupingBy(PartnerCheckInfo::getBasicLawId));
    }

    private void processBasicLawInfo(List<BasicLawInfoVO> basicLawInfoVOS, List<FamilyRiskManagerCheckInfo> familyRiskManagerCheckInfos, List<PartnerCheckInfo> partnerCheckInfos) {
        String basicLawId = !familyRiskManagerCheckInfos.isEmpty() ? familyRiskManagerCheckInfos.get(0).getBasicLawId() : partnerCheckInfos.get(0).getBasicLawId();
        String basicLawName = !familyRiskManagerCheckInfos.isEmpty() ? familyRiskManagerCheckInfos.get(0).getBasicLawName() : partnerCheckInfos.get(0).getBasicLawName();
        String basicLawType = !familyRiskManagerCheckInfos.isEmpty() ? familyRiskManagerCheckInfos.get(0).getBasicLawType() : partnerCheckInfos.get(0).getBasicLawType();

        BasicLawInfoVO basicLawInfoVO = new BasicLawInfoVO();
        basicLawInfoVO.setBasicLawId(basicLawId);
        basicLawInfoVO.setBasicLawName(basicLawName);
        basicLawInfoVO.setBasicLawType(basicLawType);

        // 家庭风险管理师
        if (!familyRiskManagerCheckInfos.isEmpty()) {
            sortFamilyRiskManagerCheckInfos(familyRiskManagerCheckInfos);
            long qualifiedCount = countQualifiedFamilyRiskManagerCheckInfos(familyRiskManagerCheckInfos);
            long unqualifiedCount = familyRiskManagerCheckInfos.size() - qualifiedCount;

            FamilyRiskManagerInfoVO familyRiskManagerInfoVO = new FamilyRiskManagerInfoVO();
            familyRiskManagerInfoVO.setKeepSuccessCount(String.valueOf(qualifiedCount));
            familyRiskManagerInfoVO.setKeepFailedCount(String.valueOf(unqualifiedCount));
            familyRiskManagerInfoVO.setFamilyRiskManagerCheckInfos(familyRiskManagerCheckInfos);

            basicLawInfoVO.setFamilyRiskManagerInfoVO(familyRiskManagerInfoVO);
        }else {
            FamilyRiskManagerInfoVO familyRiskManagerInfoVO = new FamilyRiskManagerInfoVO();
            familyRiskManagerInfoVO.setKeepSuccessCount("0");
            familyRiskManagerInfoVO.setKeepFailedCount("0");
            familyRiskManagerInfoVO.setFamilyRiskManagerCheckInfos(new ArrayList<>());

            basicLawInfoVO.setFamilyRiskManagerInfoVO(familyRiskManagerInfoVO);
        }

        // 合伙人
        if (!partnerCheckInfos.isEmpty()) {
            sortPartnerCheckInfos(partnerCheckInfos);
            long partnerQualifiedCount = countQualifiedPartnerCheckInfos(partnerCheckInfos);
            long partnerUnqualifiedCount = partnerCheckInfos.size() - partnerQualifiedCount;

            PartnerInfoVO partnerInfoVO = new PartnerInfoVO();
            partnerInfoVO.setPartnerKeepSuccessCount(String.valueOf(partnerQualifiedCount));
            partnerInfoVO.setPartnerKeepFailedCount(String.valueOf(partnerUnqualifiedCount));
            partnerInfoVO.setPartnerCheckInfoList(partnerCheckInfos);

            basicLawInfoVO.setPartnerInfoVO(partnerInfoVO);
        }else {
            PartnerInfoVO partnerInfoVO = new PartnerInfoVO();
            partnerInfoVO.setPartnerKeepSuccessCount("0");
            partnerInfoVO.setPartnerKeepFailedCount("0");
            partnerInfoVO.setPartnerCheckInfoList(new ArrayList<>());

            basicLawInfoVO.setPartnerInfoVO(partnerInfoVO);
        }

        basicLawInfoVOS.add(basicLawInfoVO);
    }

    private void sortFamilyRiskManagerCheckInfos(List<FamilyRiskManagerCheckInfo> checkInfos) {
        checkInfos.sort(Comparator.comparing(FamilyRiskManagerCheckInfo::getInstCode)
                .thenComparing(FamilyRiskManagerCheckInfo::getTeamCode)
                .thenComparing((o1, o2) -> {
                    String distanceGap1 = o1.getDistanceGap().replace(",", "");
                    String distanceGap2 = o2.getDistanceGap().replace(",", "");

                    // 判断 DistanceGap 是否为数字
                    boolean isNumeric1 = !"-".equals(distanceGap1);
                    boolean isNumeric2 = !"-".equals(distanceGap2);

                    if (isNumeric1 && isNumeric2) {
                        // 如果都是数字，按照数值升序排序
                        return Double.compare(Double.parseDouble(distanceGap1), Double.parseDouble(distanceGap2));
                    } else if (isNumeric1) {
                        // 非数字排在后面
                        return -1;
                    } else if (isNumeric2) {
                        // 非数字排在后面
                        return 1;
                    } else {
                        // 都是非数字，按照字典序排序
                        return distanceGap1.compareTo(distanceGap2);
                    }
                }));
    }

    private long countQualifiedFamilyRiskManagerCheckInfos(List<FamilyRiskManagerCheckInfo> checkInfos) {
        return checkInfos.stream()
                .filter(info -> !"10".equals(info.getCheckSettleResult()) && "0".compareTo(info.getCheckSettleResult()) <= 0)
                .count();
    }

    private void sortPartnerCheckInfos(List<PartnerCheckInfo> checkInfos) {
        checkInfos.sort(Comparator.comparing(PartnerCheckInfo::getInstCode).thenComparing(PartnerCheckInfo::getTeamCode).thenComparing(PartnerCheckInfo::getAgencyDistanceGap));
    }

    private long countQualifiedPartnerCheckInfos(List<PartnerCheckInfo> checkInfos) {
        return checkInfos.stream()
                .filter(info -> !"10".equals(info.getCheckSettleResult()) && "0".compareTo(info.getCheckSettleResult()) <= 0)
                .count();
    }

    private BasicLawInfoVO queryProbation(String partnerCode, SupervisorPerformanceRequest request) {
        BasicLawInfoVO basicLawInfoVO = new BasicLawInfoVO();
        LocalDateTime now = LocalDateTime.now();
        String yearMonth = now.getYear() + "-" + now.getMonthValue();
        String previousMonthYearMonthString = getPreviousMonthYearMonthString(now);
        List<String> checkMonthList = CollUtil.newArrayList();
        checkMonthList.add(previousMonthYearMonthString);

        // 获取符合条件的所有历史批次
        List<CheckBatchInfoVO> batchInfoVOS = assessmentMapper.getCheckBatchInfo(partnerCode, request.getInstCodeList(), checkMonthList);
        if (CollUtil.isNotEmpty(batchInfoVOS)) {
            List<CheckBatchInfoVO> needQueryBatchList = new ArrayList<>();
            for (CheckBatchInfoVO batchInfoVO : batchInfoVOS) {
                if (!"1".equals(batchInfoVO.getBatchStatus())) {
                    needQueryBatchList.add(batchInfoVO);
                } else {
                    checkMonthList.clear();
                    checkMonthList.add(yearMonth);
                    List<CheckBatchInfoVO> result = assessmentMapper.getCheckBatchInfoByVersionId(partnerCode, checkMonthList);
                    needQueryBatchList.addAll(result);
                }
            }

            if (CollUtil.isNotEmpty(needQueryBatchList)) {
                List<String> checkBatchIdList = needQueryBatchList.stream()
                        .map(CheckBatchInfoVO::getBatchId)
                        .collect(Collectors.toList());

                List<FamilyRiskManagerCheckInfo> result = assessmentMapper.getProbationPersonInfo(checkBatchIdList, request.getInstCodeList(), request.getTeamCode());
                if (CollUtil.isNotEmpty(result)) {
                    FamilyRiskManagerInfoVO familyRiskManagerInfoVO = new FamilyRiskManagerInfoVO();
                    familyRiskManagerInfoVO.setFamilyRiskManagerCheckInfos(result);
                    basicLawInfoVO.setFamilyRiskManagerInfoVO(familyRiskManagerInfoVO);
                    return basicLawInfoVO;
                } else {
                    log.info("见习职级未查询到参加考核的人-合伙人-{}", partnerCode);
                    return null;
                }
            } else {
                log.info("见习职级未查询到需要查询的考核批次-合伙人-{}", partnerCode);
                return null;
            }
        } else {
            return null;
        }
    }

    private BasicLawInfoVO queryProbationP4(SupervisorPerformanceRequest request) {
        return queryProbation("P00004", request);
    }

    private BasicLawInfoVO queryProbationP3(SupervisorPerformanceRequest request) {
        return queryProbation("P00003", request);
    }

    private BasicLawInfoVO queryNonProbation(String partnerCode, SupervisorPerformanceRequest request) {
        BasicLawInfoVO basicLawInfoVO = new BasicLawInfoVO();
        LocalDateTime now = LocalDateTime.now();
        int month = now.getMonthValue();
        int year = now.getYear();
        List<String> checkMonthList = CollUtil.newArrayList();

        // 根据月份确定检查月份列表
        if (month <= 3) {
            checkMonthList.add(((year - 1) + "-12")); // 去年12月
        } else if (month <= 6) {
            checkMonthList.add(year + "-03"); // 当年3月
        } else if (month <= 9) {
            checkMonthList.add(year + "-06"); // 当年6月
        } else { // 当前月份为[10-12]月时
            checkMonthList.add(year + "-09"); // 当年9月
        }

        // 获取符合条件的所有历史批次
        List<CheckBatchInfoVO> batchInfoVOS = assessmentMapper.getCheckBatchInfo(partnerCode, request.getInstCodeList(), checkMonthList);
        if (CollUtil.isEmpty(batchInfoVOS)) {
            return null;
        }

        List<FamilyRiskManagerCheckInfo> riskManagerInfoVOS = new ArrayList<>();
        List<PartnerCheckInfo> partnerCheckInfos = new ArrayList<>();

        for (CheckBatchInfoVO batchInfoVO : batchInfoVOS) {

            String baseVersionDate = "1".equals(batchInfoVO.getBatchStatus()) ? getNextQuarter(year, month) : getCurrentOrPreviousQuarter(year, month);

            // 根据基本法id查询顾问序列
            riskManagerInfoVOS.addAll(assessmentMapper.getRiskManagerPersonInfoListByVersionId(baseVersionDate, request.getInstCodeList(), request.getTeamCode()));

            // 根据基本法id查询合伙人序列
            partnerCheckInfos.addAll(assessmentMapper.getPartnerPersonInfoListByVersionId(getPartnerQuarter(baseVersionDate), request.getInstCodeList(), request.getTeamCode()));
        }

        processRiskManagerInfoVOS(riskManagerInfoVOS);

        if (CollUtil.isNotEmpty(riskManagerInfoVOS)) {
            FamilyRiskManagerInfoVO familyRiskManagerInfoVO = new FamilyRiskManagerInfoVO();
            familyRiskManagerInfoVO.setFamilyRiskManagerCheckInfos(riskManagerInfoVOS);
            basicLawInfoVO.setFamilyRiskManagerInfoVO(familyRiskManagerInfoVO);
        }

        if (CollUtil.isNotEmpty(partnerCheckInfos)) {
            List<String> tmpIdList = partnerCheckInfos.stream().map(PartnerCheckInfo::getTmpId).collect(Collectors.toList());
            List<CheckBatchPersonConfig> checkBatchPersonConfigs = assessmentMapper.queryAllPersonConfig(tmpIdList);
            if (CollUtil.isNotEmpty(checkBatchPersonConfigs)) {
                // 根据中间表id进行分组
                Map<String, List<CheckBatchPersonConfig>> groupedByCheckPersonTmpId = checkBatchPersonConfigs.stream()
                        .collect(Collectors.groupingBy(CheckBatchPersonConfig::getCheckPersonTmpId));
                updatePartnerCheckInfosWithConfigs(partnerCheckInfos, groupedByCheckPersonTmpId);
                processPartnerCheckInfos(partnerCheckInfos);
            }
            PartnerInfoVO partnerCheckInfoVO = new PartnerInfoVO();
            partnerCheckInfoVO.setPartnerCheckInfoList(partnerCheckInfos);
            basicLawInfoVO.setPartnerInfoVO(partnerCheckInfoVO);
        }

        return basicLawInfoVO;
    }

    private BasicLawInfoVO queryNonProbationP4(SupervisorPerformanceRequest request) {
        return queryNonProbation("P00004", request);
    }

    private BasicLawInfoVO queryNonProbationP3(SupervisorPerformanceRequest request) {
        return queryNonProbation("P00003", request);
    }

    private void processRiskManagerInfoVOS(List<FamilyRiskManagerCheckInfo> riskManagerInfoVOS) {
        for (FamilyRiskManagerCheckInfo infoVO : riskManagerInfoVOS) {
            if (StrUtil.isNotEmpty(infoVO.getMaintenanceMonthlyFyc()) && StrUtil.isNotEmpty(infoVO.getKeepActualStp())) {
                BigDecimal maintenanceMonthlyFyc = new BigDecimal(infoVO.getMaintenanceMonthlyFyc());
                BigDecimal keepActualStp = new BigDecimal(infoVO.getKeepActualStp());
                BigDecimal distanceGap = keepActualStp.subtract(maintenanceMonthlyFyc).setScale(2, RoundingMode.HALF_UP);
                infoVO.setDistanceGap(formatBigDecimal(distanceGap));
                infoVO.setMaintenanceMonthlyFyc(formatBigDecimal(maintenanceMonthlyFyc.setScale(2, RoundingMode.HALF_UP)));
                infoVO.setKeepActualStp(formatBigDecimal(keepActualStp.setScale(2, RoundingMode.HALF_UP)));
            } else {
                infoVO.setDistanceGap("-");
            }
        }
    }

    private void processPartnerCheckInfos(List<PartnerCheckInfo> partnerCheckInfos) {
        partnerCheckInfos.forEach(partnerCheckInfo -> {
            if(StrUtil.isEmpty(partnerCheckInfo.getPartnerMaintainFycStandard())){
                partnerCheckInfo.setPartnerMaintainFycStandard("-");
            }
            if(StrUtil.isEmpty(partnerCheckInfo.getPartnerKeepActualStp())){
                partnerCheckInfo.setPartnerKeepActualStp("-");
            }
            if(StrUtil.isEmpty(partnerCheckInfo.getPartnerDistanceGap())){
                partnerCheckInfo.setPartnerDistanceGap("-");
            }
            if(StrUtil.isEmpty(partnerCheckInfo.getAgencyMaintainStandardFyc())){
                partnerCheckInfo.setAgencyMaintainStandardFyc("-");
            }
            if(StrUtil.isEmpty(partnerCheckInfo.getAgencyMaintainActualFyc())){
                partnerCheckInfo.setAgencyMaintainActualFyc("-");
            }
            if(StrUtil.isEmpty(partnerCheckInfo.getAgencyDistanceGap())){
                partnerCheckInfo.setAgencyDistanceGap("-");
            }
        });
    }

    private void updatePartnerCheckInfosWithConfigs(List<PartnerCheckInfo> partnerCheckInfos, Map<String, List<CheckBatchPersonConfig>> groupedByCheckPersonTmpId) {
        for (PartnerCheckInfo partnerCheckInfo : partnerCheckInfos) {
            List<CheckBatchPersonConfig> personConfigs = groupedByCheckPersonTmpId.get(partnerCheckInfo.getTmpId());
            if (CollUtil.isNotEmpty(personConfigs)) {
                Map<String, String> itemToContentMap = personConfigs.stream()
                        .collect(Collectors.toMap(
                                CheckBatchPersonConfig::getCheckItem,
                                CheckBatchPersonConfig::getContentValue,
                                (existingValue, newValue) -> existingValue // 保留第一个出现的值
                        ));
                // 合伙人维持标准
                if (itemToContentMap.containsKey("partner_maintain_fyc_standard")) {
                    BigDecimal partnerMaintainFycStandard = new BigDecimal(itemToContentMap.get("partner_maintain_fyc_standard"));
                    partnerCheckInfo.setPartnerMaintainFycStandard(partnerMaintainFycStandard.toString());
                }

                // 事务所FYC维持考核标准
                if (itemToContentMap.containsKey("agency_maintain_fyc_standard")) {
                    BigDecimal agencyMaintainStandardFyc = new BigDecimal(itemToContentMap.get("agency_maintain_fyc_standard"));
                    partnerCheckInfo.setAgencyMaintainStandardFyc(agencyMaintainStandardFyc.toString());
                }
                // 事务所FYC实际达成
                if (itemToContentMap.containsKey("agency_average_actual_fyc")) {
                    BigDecimal agencyMaintainActualFyc = new BigDecimal(itemToContentMap.get("agency_average_actual_fyc"));
                    partnerCheckInfo.setAgencyMaintainActualFyc(agencyMaintainActualFyc.toString());
                }
            }
            // 合伙人维持月均实际达成
            if (StrUtil.isNotEmpty(partnerCheckInfo.getPartnerKeepActualStp())) {
                BigDecimal partnerKeepActualStp = new BigDecimal(partnerCheckInfo.getPartnerKeepActualStp());
                partnerCheckInfo.setPartnerKeepActualStp(partnerKeepActualStp.toString());
            }
            if (StrUtil.isNotEmpty(partnerCheckInfo.getPartnerKeepActualStp()) && StrUtil.isNotEmpty(partnerCheckInfo.getPartnerMaintainFycStandard())) {
                BigDecimal maintenanceMonthlyFyc = new BigDecimal(partnerCheckInfo.getPartnerMaintainFycStandard());
                BigDecimal keepActualStp = new BigDecimal(partnerCheckInfo.getPartnerKeepActualStp());
                BigDecimal distanceGap = keepActualStp.subtract(maintenanceMonthlyFyc).setScale(2, RoundingMode.HALF_UP);
                partnerCheckInfo.setPartnerDistanceGap(formatBigDecimal(distanceGap));
                partnerCheckInfo.setPartnerKeepActualStp(formatBigDecimal(keepActualStp.setScale(2, RoundingMode.HALF_UP)));
                partnerCheckInfo.setPartnerMaintainFycStandard(formatBigDecimal(maintenanceMonthlyFyc.setScale(2, RoundingMode.HALF_UP)));
            } else {
                if(StrUtil.isNotEmpty(partnerCheckInfo.getPartnerKeepActualStp())){
                    partnerCheckInfo.setPartnerKeepActualStp(formatBigDecimal(new BigDecimal(partnerCheckInfo.getPartnerKeepActualStp()).setScale(2, RoundingMode.HALF_UP)));
                }
                if(StrUtil.isNotEmpty(partnerCheckInfo.getPartnerMaintainFycStandard())){
                    partnerCheckInfo.setPartnerMaintainFycStandard(formatBigDecimal(new BigDecimal(partnerCheckInfo.getPartnerMaintainFycStandard()).setScale(2, RoundingMode.HALF_UP)));
                }
                partnerCheckInfo.setPartnerDistanceGap("-");
            }
            if (StrUtil.isNotEmpty(partnerCheckInfo.getAgencyMaintainStandardFyc()) && StrUtil.isNotEmpty(partnerCheckInfo.getAgencyMaintainActualFyc())) {
                BigDecimal maintenanceMonthlyFyc = new BigDecimal(partnerCheckInfo.getAgencyMaintainStandardFyc());
                BigDecimal keepActualStp = new BigDecimal(partnerCheckInfo.getAgencyMaintainActualFyc());
                BigDecimal distanceGap = keepActualStp.subtract(maintenanceMonthlyFyc).setScale(2, RoundingMode.HALF_UP);
                partnerCheckInfo.setAgencyDistanceGap(formatBigDecimal(distanceGap));
                partnerCheckInfo.setAgencyMaintainStandardFyc(formatBigDecimal(maintenanceMonthlyFyc.setScale(2, RoundingMode.HALF_UP)));
                partnerCheckInfo.setAgencyMaintainActualFyc(formatBigDecimal(keepActualStp.setScale(2, RoundingMode.HALF_UP)));
            } else {
                if(StrUtil.isNotEmpty(partnerCheckInfo.getAgencyMaintainStandardFyc())){
                    partnerCheckInfo.setAgencyMaintainStandardFyc(formatBigDecimal(new BigDecimal(partnerCheckInfo.getAgencyMaintainStandardFyc()).setScale(2, RoundingMode.HALF_UP)));
                }
                if(StrUtil.isNotEmpty(partnerCheckInfo.getAgencyMaintainActualFyc())){
                    partnerCheckInfo.setAgencyMaintainActualFyc(formatBigDecimal(new BigDecimal(partnerCheckInfo.getAgencyMaintainStandardFyc()).setScale(2, RoundingMode.HALF_UP)));
                }
                partnerCheckInfo.setAgencyDistanceGap("-");
            }
        }
    }

    private String getPreviousMonthYearMonthString(LocalDateTime dateTime) {
        YearMonth previousMonth = YearMonth.from(dateTime).minusMonths(1);
        return previousMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    private String getNextQuarter(int year, int month) {
        if (month <= 3) {
            return year + "-03";
        } else if (month <= 6) {
            return year + "-06";
        } else if (month <= 9) {
            return year + "-09";
        } else {
            return year + "-12";
        }
    }

    private String getCurrentOrPreviousQuarter(int year, int month) {
        if (month <= 3) {
            return (year - 1) + "-12";
        } else if (month <= 6) {
            return year + "-03";
        } else if (month <= 9) {
            return year + "-06";
        } else {
            return year + "-09";
        }
    }

    private String getPartnerQuarter(String baseVersionDate) {
        String monthPart = baseVersionDate.substring(5); // 提取月份部分
        switch (monthPart) {
            case "03":
                return baseVersionDate.substring(0, 4) + "-06"; // 返回当年6月
            case "06":
                return baseVersionDate.substring(0, 4) + "-12"; // 返回当年12月
            case "09":
                return baseVersionDate.substring(0, 4) + "-12"; // 返回当年12月
            default:
                return baseVersionDate.substring(0, 4) + "-12"; // 默认返回当年12月
        }
    }

    private String formatBigDecimal(BigDecimal number) {
        if (number == null) {
            throw new IllegalArgumentException("Number cannot be null");
        }

        // 显式指定 Locale.US 确保格式统一
        DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.US);
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00", symbols);
        decimalFormat.setGroupingUsed(true);
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP); // 明确舍入模式

        String formattedNumber = decimalFormat.format(number);

        if (number.compareTo(BigDecimal.ZERO) > 0) {
            return "+" + formattedNumber;
        } else {
            return formattedNumber;
        }
    }
}
