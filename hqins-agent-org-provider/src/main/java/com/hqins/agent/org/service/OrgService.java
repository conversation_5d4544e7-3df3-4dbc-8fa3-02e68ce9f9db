package com.hqins.agent.org.service;

import com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.enums.AgentOrgType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/525
 * @Description
 */
public interface OrgService {

    List<OrgVO> getOrgsByTop(AgentOrgType orgType, String topCode, boolean levels);

    List<OrgVO> getOrgs(AgentOrgType orgType, String orgCode, boolean levels);

    /**
     * 根据机构代码查其上的所有父级机构
     * @param orgType
     * @param orgCode
     * @param levels
     * @return
     */
    List<OrgVO> getParentsOrgs(AgentOrgType orgType, String orgCode, boolean levels);

    List<TeamVO> getTeamsByOrg(AgentOrgType orgType, String orgCode, boolean levels);

    List<TeamVO> getTeams(AgentOrgType orgType, String teamCode, boolean levels);

    DealerVO getDealer(String orgCode);

    List<OrgVO> topOrg();

    CompanyVO getCompany(AgentOrgType orgType,String orgCode) ;

    List<CompanyVO> getCompanyList(AgentOrgType orgType,String[] orgCodes) ;

    InstResultVo queryInstByOrgCode(String orgCode) ;

    List<PartassignmanagerVO> queryCompanyInst(String companyInstCode) ;

    List<QueryAllVO>  queryAll();
    
    TbepartnerVO getTbepartnerByOrgCode(String orgCode);

    void fillMerchantOrgInfo(PartassignmanagerVO vo, Tbpartassignmanager tbpartassignmanager);
}
