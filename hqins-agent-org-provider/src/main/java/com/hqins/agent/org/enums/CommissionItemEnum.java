package com.hqins.agent.org.enums;

import com.alibaba.druid.util.StringUtils;


public enum CommissionItemEnum {

    FYC("FYC", "首年佣金"),
    RYC("RYC", "续年佣金"),
    MMA("MMA", "月度管理津贴"),
    YBCM("YBCM", "年终奖常规奖励"),
    YBEA("YBEA", "年终奖额外奖励"),
    YEB("YEB", "年度超额奖励"),
    RSB("RSB", "实发底薪"),
    EXA("EXA", "展业津贴"),
    CRB("CRB", "继续率奖金"),
    EPA("EPA", "赋能津贴"),
    SSS("SSS", "社保补贴"),
    CQD("CQD", "当月品质保证金"),
    DCP("DCP", "折标保费"),

    GSC("GSC", "团单销售佣金"),
    FSC("FSC", "新单销售奖金"),
    FSC_JCXY("FSC_JCXY", "新单销售奖金"),
    FSC_FAZX("FSC_FAZX", "新单销售奖金专项方案"),
    YEC("YEC", "个人年终奖"),
    RSC("RSC", "续期服务奖金"),
    REC("REC", "推荐奖金"),
    MAC("MAC", "管理奖金"),
    ORF("ORF", "运营基金"),
    BRC("BRC", "育成奖金")

    ;


    private String value;
    private String label;

    public static String getLabelByValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        for (CommissionItemEnum feeItem : CommissionItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem.getLabel();
            }
        }
        return "";
    }

    public static String getValueByLabel(String label) {
        if (StringUtils.isEmpty(label)) {
            return "";
        }
        for (CommissionItemEnum feeItem : CommissionItemEnum.values()) {
            if (feeItem.getLabel().equals(label)) {
                return feeItem.value;
            }
        }
        return "";
    }
    public static String getLabelByValueOther(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        for (CommissionItemEnum feeItem : CommissionItemEnum.values()) {
            if (feeItem.getValue().equals(value)) {
                return feeItem.getLabel();
            }
        }
        return value;
    }


    CommissionItemEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
