server:
  port: 8067
spring:
  application:
    name: hqins-agent-org
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      username: liyang
      password: liyang
      config:
        server-addr: 172.16.2.120:8898
        namespace: dcd595ab-2eff-49f3-863f-b4ab14c9c150
        file-extension: yml
        # group: DEFAULT_GROUP
        extension-configs:
          - dataId: hqins-common.yml
            refresh: true
            group: DEFAULT_GROUP
          - dataId: ${spring.application.name}.yml
            refresh: true
            group: DEFAULT_GROUP
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        namespace: ${spring.cloud.nacos.config.namespace}
        register-enabled: false
        weight: -1
  profiles:
    active: dev
