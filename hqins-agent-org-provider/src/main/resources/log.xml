<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">

    <contextName>logback</contextName>
    <property name="info.path" value="logs/hqins-agent-org-info.log"/>
    <property name="error.path" value="logs/hqins-agent-org-error.log"/>

    <property name="FILE_LOG_PATTERN" value="%d [%thread] %-5p [%c:%L] [trace=%X{X-B3-TraceId:-},span=%X{X-B3-SpanId:-},parent=%X{X-B3-ParentSpanId:-}] - %msg%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${info.path}</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${info.path}.%d{yyyy-MM-dd}.log%i.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>7</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${error.path}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${error.path}.%d{yyyy-MM-dd}.log%i.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>7</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" charset="UTF-8">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
    </root>

    <logger name="com.hqins" level="INFO" additivity="true"/>
    <logger name="org.springframework" level="info"/>
    <logger name="org.springframework.web" level="info"/>
    <logger name="org.hibernate" level="info"/>

</configuration>