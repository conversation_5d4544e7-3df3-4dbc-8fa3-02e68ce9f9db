<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.FileUploadRecordMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.FileUploadRecord">
            <result property="id" column="id"/>
            <result property="fileName" column="file_name"/>
            <result property="personNumber" column="person_number"/>
            <result property="successNumber" column="success_number"/>
            <result property="failNumber" column="fail_number"/>
            <result property="status" column="status"/>
            <result property="creator" column="creator"/>
            <result property="creatorId" column="creator_id"/>
            <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,file_name,person_number,success_number,fail_number,status,creator,creator_id,create_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.org.FileUploadRecord"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into file_upload_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                file_name,
            </if>
            <if test="personNumber != null">
                person_number,
            </if>
            <if test="successNumber != null">
                success_number,
            </if>
            <if test="failNumber != null">
                fail_number,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="personNumber != null">
                #{personNumber},
            </if>
            <if test="successNumber != null">
                #{successNumber},
            </if>
            <if test="failNumber != null">
                #{failNumber},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIME},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.org.FileUploadRecord">
        update file_upload_record
        <set>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="personNumber != null">
                person_number = #{personNumber},
            </if>
            <if test="successNumber != null">
                success_number = #{successNumber},
            </if>
            <if test="failNumber != null">
                fail_number = #{failNumber},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIME},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


</mapper>
