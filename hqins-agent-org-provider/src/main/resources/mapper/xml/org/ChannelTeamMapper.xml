<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.ChannelTeamMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.ChannelTeam">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="parentCode" column="parent_code"/>
        <result property="name" column="name"/>
        <result property="level" column="level"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="leader" column="leader"/>
        <result property="leaderCode" column="leader_code"/>
        <result property="status" column="status"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`code`,`parent_code`,`name`,`level`,`channel_code`,`channel_name`,`org_code`,`org_name`,`leader`,`leader_code`,`status`,`effective_date`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>

    <update id="updateOrgNameByOrgCode" >
        update channel_team
        <set>
            org_name=#{orgName}
        </set>
        where org_code=#{orgCode}
    </update>

    <update id="updateTopNameByTopCode" >
        update channel_team
        <set>
            channel_name=#{topName}
        </set>
        where channel_code=#{topCode}
    </update>

</mapper>
