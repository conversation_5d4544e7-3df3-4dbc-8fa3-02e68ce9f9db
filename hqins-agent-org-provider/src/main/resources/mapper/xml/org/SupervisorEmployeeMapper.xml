<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.SupervisorEmployee">
        <result property="id" column="id"/>
        <result property="employeeCode" column="employee_code"/>
        <result property="name" column="name"/>
        <result property="idType" column="id_type"/>
        <result property="idCode" column="id_code"/>
        <result property="roleType" column="role_type"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="mobile" column="mobile"/>
        <result property="topCode" column="top_code"/>
        <result property="topCodeName" column="top_code_name"/>
        <result property="teamCode" column="team_code"/>
        <result property="teamName" column="team_name"/>
        <result property="teamLevel" column="team_level"/>
        <result property="orgName" column="org_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="topCodeList" column="top_code_list"/>
        <result property="orgCodeList" column="org_code_list"/>
        <result property="status" column="status"/>
        <result property="position" column="position"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,employee_code,name,id_type,id_code,role_type,gender,birthday,mobile,top_code,top_code_name,team_code,team_name,team_level,org_name,org_code,top_code_list,org_code_list,status,creator,creator_id,create_time,modifier,modifier_id,update_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.org.SupervisorEmployee"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into supervisor_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeCode != null">
                employee_code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="idType != null">
                id_type,
            </if>
            <if test="idCode != null">
                id_code,
            </if>
            <if test="roleType != null">
                role_type,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="topCode != null">
                top_code,
            </if>
            <if test="topCodeName != null">
                top_code_name,
            </if>
            <if test="teamCode != null">
                team_code,
            </if>
            <if test="teamName != null">
                team_name,
            </if>
            <if test="teamLevel != null">
                team_level,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="topCodeList != null">
                top_code_list,
            </if>
            <if test="orgCodeList != null">
                org_code_list,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="modifierId != null">
                modifier_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeCode != null">
                #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idCode != null">
                #{idCode,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                #{roleType,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="topCode != null">
                #{topCode,jdbcType=VARCHAR},
            </if>
            <if test="topCodeName != null">
                #{topCodeName,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="teamName != null">
                #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="teamLevel != null">
                #{teamLevel,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="topCodeList != null">
                #{topCodeList,jdbcType=VARCHAR},
            </if>
            <if test="orgCodeList != null">
                #{orgCodeList},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIME},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIME},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.org.SupervisorEmployee">
        update supervisor_employee
        <set>
            <if test="employeeCode != null">
                employee_code = #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                id_type = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idCode != null">
                id_code = #{idCode,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                role_type = #{roleType,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="topCode != null">
                top_code = #{topCode,jdbcType=VARCHAR},
            </if>
            <if test="topCodeName != null">
                top_code_name = #{topCodeName,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                team_code = #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="teamName != null">
                team_name = #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="teamLevel != null">
                team_level = #{teamLevel,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="topCodeList != null">
                top_code_list = #{topCodeList,jdbcType=VARCHAR},
            </if>
            <if test="orgCodeList != null">
                org_code_list = #{orgCodeList},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                position = #{position,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIME},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIME},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="getAllEmpByPage" resultType="com.hqins.agent.org.dao.entity.org.SupervisorEmployee">
        select
            <include refid="Base_Column_List" />
            from supervisor_employee
        where
            status = 'SERVING'
    </select>

    <select id="getSupervisorEmployeeByCode" parameterType = "map" resultType="com.hqins.agent.org.dao.entity.org.SupervisorEmployee">
        select
        <include refid="Base_Column_List" />
        from supervisor_employee
        where
        employee_code = #{employeeCode}
    </select>


</mapper>
