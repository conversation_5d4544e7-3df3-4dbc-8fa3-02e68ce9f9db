<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.ChannelLogoMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.ChannelLogo">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="url" column="url"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`code`,`url`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>


</mapper>
