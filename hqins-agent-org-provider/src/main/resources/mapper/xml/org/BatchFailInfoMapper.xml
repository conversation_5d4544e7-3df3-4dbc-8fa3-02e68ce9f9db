<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hqins.agent.org.dao.mapper.org.BatchFailInfoMapper">
    <update id="updateByIdCode">
        update batch_fail_info
        set modifier = #{modifier},
            file_update_id = #{fileUpdateId},
            modifier_id = #{modifierId},
            update_time= #{updateTime},
            is_deleted  = true
        where id_code = #{idCode}
              and is_deleted = false
    </update>
    <update id="updatesById">
        update batch_fail_info
        set modifier = #{modifier},
            file_update_id = #{fileUpdateId},
            modifier_id = #{modifierId},
            update_time= #{updateTime},
            error_message = #{errorMessage}
        where id = #{id}
          and is_deleted = false
    </update>
</mapper>