<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.ChannelDealerMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.ChannelDealer">
        <result property="id" column="id"/>
        <result property="ownerOrgCode" column="owner_org_code"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`owner_org_code`,`org_code`,`org_name`,`channel_code`,`channel_name`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>

    <update id="updateOrgNameByOrgCode" >
        update channel_dealer
        <set>
            org_name=#{orgName}
        </set>
        where org_code=#{orgCode}
    </update>

    <update id="updateTopNameByTopCode" >
        update channel_dealer
        <set>
            channel_name=#{topName}
        </set>
        where channel_code=#{topCode}
    </update>

</mapper>
