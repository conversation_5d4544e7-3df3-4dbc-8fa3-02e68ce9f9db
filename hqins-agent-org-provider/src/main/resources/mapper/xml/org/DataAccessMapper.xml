<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.DataAccessMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.DataAccess">
        <result property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="dataCode" column="data_code"/>
        <result property="dataType" column="data_type"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`role_id`,`data_code`,`data_type`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>


</mapper>
