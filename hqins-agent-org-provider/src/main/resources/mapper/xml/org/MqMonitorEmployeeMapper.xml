<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.MqMonitorEmployeeMapper">

    <sql id="Base_Column_List">
        `id`,`code`,`name`,`id_type`,`id_code`,`mobile`,`status`,`status_old`,`is_sync`,`sync_result`,`sync_status`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>

    <select id="selectByCode" resultType="com.hqins.agent.org.dao.entity.org.MqMonitorEmployee">
        select * from mq_monitor_employee where code = #{code}
    </select>

    <update id="updateMobileByCode" parameterType="java.lang.String">
        update mq_monitor_employee  set mobile = #{mobile} where code = #{code}
    </update>

</mapper>
