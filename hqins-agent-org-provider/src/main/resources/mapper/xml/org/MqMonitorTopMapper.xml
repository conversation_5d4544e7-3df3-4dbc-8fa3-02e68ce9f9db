<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.MqMonitorTopMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.MqMonitorTop">
        <result property="id" column="id"/>
        <result property="companyid" column="companyid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="nameOld" column="name_old"/>
        <result property="statusOld" column="status_old"/>
        <result property="orgType" column="org_type"/>
        <result property="sync" column="is_sync"/>
        <result property="syncResult" column="sync_result"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`code`,`companyid`,`name`,`status`,`name_old`,`status_old`,`org_type`,`is_sync`,`sync_result`,`sync_status`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`
    </sql>


</mapper>
