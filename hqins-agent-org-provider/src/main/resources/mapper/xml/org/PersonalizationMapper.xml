<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.PersonalizationMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.Personalization">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="orgType" column="org_type"/>
        <result property="customMenuId" column="custom_menu_id"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="type" column="app_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`code`,`org_type`,`custom_menu_id`,`creator`,`creator_id`,`create_time`,`modifier`,`modifier_id`,`update_time`,`type`
    </sql>


</mapper>
