<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.org.ChannelEmployee">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="idType" column="id_type"/>
        <result property="idCode" column="id_code"/>
        <result property="universalQualification" column="universal_qualification"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="mobile" column="mobile"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="teamCode" column="team_code"/>
        <result property="teamName" column="team_name"/>
        <result property="licenseNo" column="license_no"/>
        <result property="licenseStartDate" column="license_start_date"/>
        <result property="licenseEndDate" column="license_end_date"/>
        <result property="jobNumber" column="job_number"/>
        <result property="entryTime" column="entry_time"/>
        <result property="quitTime" column="quit_time"/>
        <result property="status" column="status"/>
        <result property="sourceSystem" column="source_system"/>
        <result property="isAuthorized" column="is_authorized"/>
        <result property="isWaitCreatCode" column="is_wait_creat_code"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,code,name,id_type,id_code,universal_qualification,gender,birthday,mobile,channel_code,channel_name,org_code,org_name,team_code,team_name,license_no,license_start_date,license_end_date,job_number,entry_time,quit_time,status,source_system,is_authorized,is_wait_creat_code,creator,creator_id,create_time,modifier,modifier_id,update_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.org.ChannelEmployee"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into channel_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="idType != null">
                id_type,
            </if>
            <if test="idCode != null">
                id_code,
            </if>
            <if test="universalQualification != null">
                universal_qualification,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="channelName != null">
                channel_name,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="teamCode != null">
                team_code,
            </if>
            <if test="teamName != null">
                team_name,
            </if>
            <if test="licenseNo != null">
                license_no,
            </if>
            <if test="licenseStartDate != null">
                license_start_date,
            </if>
            <if test="licenseEndDate != null">
                license_end_date,
            </if>
            <if test="jobNumber != null">
                job_number,
            </if>
            <if test="entryTime != null">
                entry_time,
            </if>
            <if test="quitTime != null">
                quit_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="sourceSystem != null">
                source_system,
            </if>
            <if test="isAuthorized != null">
                is_authorized,
            </if>
            <if test="isWaitCreatCode != null">
                is_wait_creat_code,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="modifierId != null">
                modifier_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idCode != null">
                #{idCode,jdbcType=VARCHAR},
            </if>
            <if test="universalQualification != null">
                #{universalQualification,jdbcType=BIT},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelName != null">
                #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="teamName != null">
                #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null">
                #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="licenseStartDate != null">
                #{licenseStartDate,jdbcType=DATE},
            </if>
            <if test="licenseEndDate != null">
                #{licenseEndDate,jdbcType=DATE},
            </if>
            <if test="jobNumber != null">
                #{jobNumber,jdbcType=VARCHAR},
            </if>
            <if test="entryTime != null">
                #{entryTime,jdbcType=TIME},
            </if>
            <if test="quitTime != null">
                #{quitTime,jdbcType=TIME},
            </if>
            <if test="status != null">
                #{status,jdbcType=CHAR},
            </if>
            <if test="sourceSystem != null">
                #{sourceSystem,jdbcType=VARCHAR},
            </if>
            <if test="isAuthorized != null">
                #{isAuthorized,jdbcType=VARCHAR},
            </if>
            <if test="isWaitCreatCode != null">
                #{isWaitCreatCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIME},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIME},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.org.ChannelEmployee">
        update channel_employee
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                id_type = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idCode != null">
                id_code = #{idCode,jdbcType=VARCHAR},
            </if>
            <if test="universalQualification != null">
                universal_qualification = #{universalQualification,jdbcType=BIT},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelName != null">
                channel_name = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                team_code = #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="teamName != null">
                team_name = #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null">
                license_no = #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="licenseStartDate != null">
                license_start_date = #{licenseStartDate,jdbcType=DATE},
            </if>
            <if test="licenseEndDate != null">
                license_end_date = #{licenseEndDate,jdbcType=DATE},
            </if>
            <if test="jobNumber != null">
                job_number = #{jobNumber,jdbcType=VARCHAR},
            </if>
            <if test="entryTime != null">
                entry_time = #{entryTime,jdbcType=TIME},
            </if>
            <if test="quitTime != null">
                quit_time = #{quitTime,jdbcType=TIME},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem,jdbcType=VARCHAR},
            </if>
            <if test="isAuthorized != null">
                is_authorized = #{isAuthorized,jdbcType=VARCHAR},
            </if>
            <if test="isWaitCreatCode != null">
                is_wait_creat_code = #{isWaitCreatCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIME},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIME},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateOrgNameByOrgCode">
        update channel_employee
        <set>
            org_name=#{orgName}
        </set>
        where org_code=#{orgCode}
    </update>

    <update id="updateTopNameByTopCode">
        update channel_employee
        <set>
            channel_name=#{topName}
        </set>
        where channel_code=#{topCode}
    </update>


    <select id="getByIdCodeOrLicenseNo" resultMap="Base_Result_Map">
        select * from channel_employee where status = 'SERVING'
        <if test="idCode !=null and idCode !=''">
            and id_code = #{idCode}
        </if>
        <if test="licenseNo !=null and licenseNo !=''">
            and license_no = #{licenseNo}
        </if>
    </select>

    <update id="creatCode">
        update channel_employee
        SET `code` = CONCAT(SUBSTR(`code`, 1, 7), LPAD(id, 6, '0')),
        is_wait_creat_code = 'NO'
        WHERE is_wait_creat_code = 'YES';
    </update>

    <select id="getByMga" resultMap="Base_Result_Map">
        SELECT id,CODE,NAME,ID_TYPE,ID_CODE,universal_qualification,GENDER,BIRTHDAY,MOBILE,CHANNEL_CODE,CHANNEL_NAME,ORG_CODE,ORG_NAME,TEAM_CODE,TEAM_NAME,LICENSE_NO,LICENSE_START_DATE,LICENSE_END_DATE,JOB_NUMBER,ENTRY_TIME,QUIT_TIME,STATUS,SOURCE_SYSTEM,IS_AUTHORIZED,IS_WAIT_CREAT_CODE,CREATOR,CREATOR_ID,CREATE_TIME,MODIFIER,MODIFIER_ID,UPDATE_TIME
        FROM channel_employee
        WHERE STATUS = 'SERVING' AND ((ID_TYPE = #{idType} AND ID_CODE = #{idCode}) OR MOBILE = #{mobile} OR LICENSE_NO = #{licenseNo})
    </select>
    <select id="getAllEmpByPage" resultType="com.hqins.agent.org.dao.entity.org.ChannelEmployee">
        select
            <include refid="Base_Column_List"/>
            from channel_employee
        where status = 'SERVING'
    </select>

</mapper>
