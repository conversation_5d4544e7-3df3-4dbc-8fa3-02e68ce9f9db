<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.hologres.DaDetailUwMapper">
    <select id="selectDaDetailUwList"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.HoloDaDetailUwVo">
        SELECT
        buName,
        companyName,
        performanceGroup,
        performanceGroupName,
        manageCom2v,
        manageComName2v,
        manageCom3v,
        manageComName3v,
        manageCom4v,
        manageComName4v,
        manageCom5v,
        manageComName5v,
        agentComType,
        agentCom,
        (case
        when agentCom = 'C999999' then '横琴自营'
        else agentComName
        end) agentComName,
        orgComCode,
        orgComName,
        orgCode,
        orgName,
        orgcode1v,
        orgname1v,
        orgcode2v,
        orgname2v,
        orgcode3v,
        orgname3v,
        orgcode4v,
        orgname4v,
        agentCom1v,
        agentComName1v,
        agentCom2v,
        agentComName2v,
        agentCom3v,
        agentComName3v,
        agentCom4v,
        agentComName4v,
        riskCode,
        riskName,
        productComCode,
        productComName,
        riskPeriodName,
        riskKind1Name,
        riskKind2Name,
        riskType,
        subRiskFlag,
        prtNo,
        grpContNo,
        contNo,
        appntName,
        appntNo,
        appntApplyAge,
        appntSex,
        insuredName,
        insuredNo,
        insuredApplyAge,
        insuredSex,
        insPeriod,
        payPeriod,
        payIntv,
        contYear,
        premYear,
        amnt,
        signPrem,
        prem,
        fyc,
        stdPrem_base,
        stdPrem,
        signAgent,
        signAgentName,
        signagentlic signAgentLic,
        agentCode,
        agentName,
        agentLicense,
        agentGrade,
        agentGradeName,
        agt_insideFlag agtInsideFlag,
        upBranchAttr,
        upBranchAttrName,
        upBranchManager,
        upBranchManagerName,
        branchAttr,
        branchAttrName,
        branchManager,
        branchManagerName,
        agentGroup,
        agentGroupName,
        agtGrpManager,
        agtGrpManagerName,
        saleChnlName,
        sellTypeName,
        agentTypeName,
        tradeType,
        (case
        when contstatus = 'XD' then '承保'
        when contstatus = 'SX' then '失效'
        when contstatus = 'ZZ' then '终止'
        else null
        end ) contStatus,
        contState,
        makeDate,
        applyDate,
        applyTime,
        signDate,
        signTime,
        valiDate,
        getPolDate,
        customGetPolDate,
        tradeDate,
        inputDate,
        ctDate,
        visitDate,
        hesitateEnd,
        isHesitate,
        insEndDate,
        ctMoney,
        ctmode,
        dutyCode,
        dutyName,
        butype,
        zyFlag,
        shqFlag,
        jnfjmFlag,
        chkComName,
        mgaFlag,
        isStkType,
        payconfdate,
        agentcode_new agentCodeNew,
        agentname_new agentNameNew,
        terminate_date terminateDate,
        polno polNo,
        fixedpayno_ni fixedPayNoNi,
        fixedpayno_zt fixedPayNoZt,
        rnewhmflag,
        coinsflag coinsFlag,
        famcontflag famContFlag,
        edoracceptno eDorAcceptNo,
        payperiod_rate payPeriodRate
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        <if test="fixedPayNo != null and fixedPayNo != ''">
            AND (detail.fixedpayno_ni = #{fixedPayNo} or detail.fixedpayno_zt = #{fixedPayNo})
        </if>
        <if test="ZYFlag != null and ZYFlag != ''">
            AND detail.zyflag = #{ZYFlag}
        </if>
        <if test="giveRiskFlag != null and giveRiskFlag != ''">
            AND detail.giveriskflag = #{giveRiskFlag}
        </if>
        <if test="contType != null and contType != ''">
            AND detail.conttype = #{contType}
        </if>

        <if test="signStarDate != null and signEndDate != null">
            AND detail.signdate &gt;= TO_DATE(#{signStarDate}, 'YYYY-MM-DD')
            AND detail.signdate &lt;= TO_DATE(#{signEndDate}, 'YYYY-MM-DD')
        </if>
        <if test="validStarDate != null and validEndDate != null">
            AND detail.validate &gt;= TO_DATE(#{validStarDate}, 'YYYY-MM-DD')
            AND detail.validate &lt;= TO_DATE(#{validEndDate}, 'YYYY-MM-DD')
        </if>
        <if test="orgCodeRoleDate != null and orgCodeRoleDate.size() >0 ">
            AND detail.orgcode in
            <foreach collection="orgCodeRoleDate" index="index" item="orgCode" open="(" separator=","
                     close=")">
                #{orgCode}
            </foreach>
        </if>
        <if test="performanceGroupRoleDate != null and performanceGroupRoleDate.size() > 0 ">
            and detail.performancegroup in
            <foreach collection="performanceGroupRoleDate" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="buNameList != null and buNameList.size()>0">
            AND detail.bucode in
            <foreach collection="buNameList" index="index" item="buCode" open="(" separator=","
                     close=")">
                #{buCode}
            </foreach>
        </if>
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performancegroup" open="(" separator=","
                     close=")">
                #{performancegroup}
            </foreach>
        </if>
        <if test="pathStr != null and pathStr != ''">         -- 渠道商机构条件
            AND #{pathStr} like concat ('%.',orgCode,'.%')
        </if>
        <if test="manageComList != null and manageComList.size()>0">
            AND
            (
            <foreach collection="manageComList" index="index" item="manageCom" separator="OR">
                detail.managecom like concat(#{manageCom},'%')
            </foreach>
            )
        </if>
        <if test="agentCode != null and agentCode.size()>0">
            AND detail.agentcode in
            <foreach collection="agentCode" index="index" item="agentCo" open="(" separator="," close=")">
                #{agentCo}
            </foreach>
        </if>
        <if test="contNoList != null and contNoList.size()>0 ">
            AND detail.contno in
            <foreach collection="contNoList" index="index" item="contNo" open="(" separator=","
                     close=")">
                #{contNo}
            </foreach>
        </if>
        <if test="riskCodeList != null and riskCodeList.size()>0">
            AND detail.riskcode in
            <foreach collection="riskCodeList" index="index" item="riskcode" open="(" separator="," close=")">
                #{riskcode}
            </foreach>
        </if>
        <if test="mgaFlag != null and mgaFlag != ''">
            AND detail.mgaflag = #{mgaFlag}
        </if>
        <if test="payConfStarDate != null and payConfEndDate != null">
            AND detail.payconfdate &gt;= TO_DATE(#{payConfStarDate}, 'YYYY-MM-DD')
            AND detail.payconfdate &lt;= TO_DATE(#{payConfEndDate}, 'YYYY-MM-DD')
        </if>
        <if test="coinsFlag != null and coinsFlag != ''">
            AND detail.coinsflag = #{coinsFlag}
        </if>
        <if test="eDorAcceptNo != null and eDorAcceptNo != ''">
            AND detail.edoracceptno like concat('%', #{eDorAcceptNo}, '%')
        </if>
        order by detail.orgcode desc, detail.POLNO desc, detail.TRADETYPE desc, detail.dutycode  desc
        <if test="BEGIN !=null and COUNT !=null">
            limit #{COUNT} OFFSET #{BEGIN}
        </if>
    </select>


    <select id="selectPerformanceByInstCode"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.PerformanceVO">


        select
        SUM ( fycPremium ) fycPremium,
        SUM ( dcp ) dcp,
        SUM ( periodPremium) periodPremium

        from
        (
        SELECT
        sum(signprem) fycPremium,
        sum(case when COALESCE(hbj_type1,'0') not in ('自保件','互保件') then stdprem else 0 end) dcp,
        sum(case when (payintv in ('趸缴','一次性交清')) then 0 else (case when riskperiod = 'L' then signprem else 0 end ) end ) periodPremium
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        and premyear = 1
        and contstate != '犹豫期退保'
        AND detail.signdate between #{signStarDate} and #{signEndDate}
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND agentgroup = #{teamCode}
        </if>

        UNION all

        SELECT
        SUM ( sumactupaymoney ) fycPremium,
        SUM ( CASE WHEN COALESCE(hbj_type1,'0') not in ('自保件','互保件') THEN stand_prem ELSE 0 END ) dcp,
        SUM ( CASE WHEN paymode NOT IN ( '趸缴', '一次性交清' ) AND riskperiodcode = 'L' THEN sumactupaymoney ELSE 0 END ) periodPremium
        FROM
        dataservice_public.dwd_rnw_ssqd_qqd detail
        where 1=1 and payyear = '1' AND detail.paydate between #{signStarDate} and #{signEndDate}
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND xq_yyz_code = #{teamCode}
        </if>
        ) a


    </select>


    <select id="selectPerformanceList"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.SupervisorPerformanceDetailNumberVO">

        select
        MAX ( instCode ) instCode,
        MAX ( instName ) instName,
        MAX ( teamCode ) teamCode,
        MAX ( teamName ) teamName,
        MAX ( agentcode ) agentCode,
        MAX ( agentname ) agentName,
        SUM ( fycPremium ) fycPremium,
        SUM ( dcp ) dcp,
        sum ( fycPolicyNum ) fycPolicyNum,
        SUM ( periodPremium) periodPremium,
        SUM ( periodDCP) periodDCP,
        SUM ( periodPolicyNum) periodPolicyNum,
        SUM ( fycMRiskPremium) fycMRiskPremium,
        SUM ( fycMRiskPolicyNum) fycMRiskPolicyNum,
        SUM ( fycAppntNoNum) fycAppntNoNum,
        SUM ( periodAppntNoNum) periodAppntNoNum
        from (

        SELECT
        max(performancegroup) instCode,
        max(performancegroupname) instName,
        max(agentgroup) teamCode,
        max(agentgroupname) teamName,
        max(agentcode) agentCode,
        max(agentname) agentName,
        sum(signprem) fycPremium,
        sum(case when COALESCE(hbj_type1,'0') not in ('自保件','互保件') then stdprem else 0 end) dcp,
        (count(DISTINCT case when conttype = '2' then grpcontno else null end) + count(DISTINCT case when conttype = '1'
        then contno else null end)) fycPolicyNum,
        sum(case when payintv not in ('趸缴','一次性交清') and riskperiod = 'L' then signprem else 0 end ) periodPremium,
        sum(case when payintv not in ('趸缴','一次性交清') and riskperiod = 'L' and COALESCE(hbj_type1,'0') not in ('自保件','互保件') then stdprem else 0 end ) periodDCP,
        (count(DISTINCT case when conttype = '2' and payintv not in ('趸缴','一次性交清') and riskperiod = 'L' then grpcontno
        else null end) + count(DISTINCT case when conttype = '1' and payintv not in ('趸缴','一次性交清') and riskperiod = 'L'
        then contno else null end)) periodPolicyNum,
        sum(case when riskperiod = 'M' then signprem else 0 end) fycMRiskPremium,
        (count(DISTINCT case when conttype = '2' and riskperiod = 'M' then grpcontno else null end) + count(DISTINCT
        case when conttype = '1' and riskperiod = 'M' then contno else null end)) fycMRiskPolicyNum,
        count( DISTINCT appntno) fycAppntNoNum,
        count(DISTINCT case when payintv not in ('趸缴','一次性交清') and riskperiod = 'L' then appntno else null end)
        periodAppntNoNum
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        and premyear = '1'
        and contstate != '犹豫期退保'
        <choose>
            <when test=" accType == 'sign'.toString()">
                AND detail.signdate between #{startDate} and #{endDate}
            </when>
            <otherwise>
                AND detail.validate between #{startDate} and #{endDate}
            </otherwise>
        </choose>
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND agentgroup = #{teamCode}
        </if>
        <choose>
            <when test=" groupType == 'team'.toString()">
                group by detail.agentgroup
            </when>
            <when test=" groupType == 'agent'.toString()">
                group by detail.agentcode
            </when>
            <otherwise>
                group by detail.performancegroup
            </otherwise>
        </choose>

        UNION all

        SELECT
        MAX ( performancegroup ) instCode,
        MAX ( performancegroupname ) instName,
        MAX ( xq_yyz_code ) teamCode,
        MAX ( xq_yyz_name ) teamName,
        MAX ( xq_agentcode ) agentCode,
        MAX ( xq_agentname ) agentName,
        SUM ( sumactupaymoney ) fycPremium,
        SUM ( CASE WHEN COALESCE(hbj_type1,'0') not in ('自保件','互保件') THEN stand_prem ELSE 0 END ) dcp,
        0 fycPolicyNum,
        SUM ( CASE WHEN paymode NOT IN ( '趸缴', '一次性交清' ) AND riskperiodcode = 'L' THEN sumactupaymoney ELSE 0 END ) periodPremium,
        SUM ( CASE WHEN 1 = 1 and paymode NOT IN ( '趸缴', '一次性交清' ) AND riskperiodcode = 'L' and COALESCE(hbj_type1,'0') not in ('自保件','互保件') THEN stand_prem ELSE 0 END ) periodDCP,
        0 periodPolicyNum,
        SUM ( CASE WHEN riskperiodcode = 'M' THEN sumactupaymoney ELSE 0 END ) fycMRiskPremium,
        0 fycMRiskPolicyNum,
        0 fycAppntNoNum,
        0 periodAppntNoNum
        FROM
        dataservice_public.dwd_rnw_ssqd_qqd detail
        where 1=1 and payyear = '1'
        <choose>
            <when test="type == null">
                AND detail.paydate between #{startDate} and #{endDate}
            </when>
            <when test="accType == 'sign'">
                AND detail.signdate between #{startDate} and #{endDate}
            </when>
            <otherwise>
                AND detail.cvalidate between #{startDate} and #{endDate}
            </otherwise>
        </choose>
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND xq_yyz_code = #{teamCode}
        </if>
        <choose>
            <when test="groupType == 'team'.toString()">
                GROUP BY detail.xq_yyz_code
            </when>
            <when test="groupType == 'agent'.toString()">
                group by detail.xq_agentcode
            </when>
            <otherwise>
                group by detail.performancegroup
            </otherwise>
        </choose>

        )a
        <choose>
            <when test="groupType == 'team'.toString()">
                GROUP BY a.teamCode
            </when>
            <when test="groupType == 'agent'.toString()">
                GROUP BY a.agentCode
            </when>
            <otherwise>
                GROUP BY a.instCode
            </otherwise>
        </choose>
        order by   instCode asc,teamCode asc,fycPremium desc

    </select>



    <select id="getHologresSelfPolicyList"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.MarkDetailVO">

        SELECT
        performancegroup instCode,
        performancegroupname instName,
        agentcode agentCode,
        agentname agentName,
        contno policyNo,
        appntno appntNo,
        appntname appntName,
        insuredno insuredNo,
        insuredname insuredName,
        case hbj_type1 when '自保件' then '1' else '2' end typeCode,
        hbj_type1 typeName
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        AND detail.signdate between #{startDate} and #{endDate}
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND agentgroup = #{teamCode}
        </if>
        and hbj_type1 in ('自保件','互保件')
        order by  performancegroup ,agentgroup ,agentcode

    </select>

    <select id="getSupervisorCtList"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.MarkDetailVO">

        SELECT
        performancegroup instCode,
        performancegroupname instName,
        agentcode agentCode,
        agentname agentName,
        contno policyNo,
        riskcode riskCode,
        riskname riskName,
        payintv paymentWay,
        payperiod paymentYear,
        insperiod insureYears,
        abs(signprem) premium,
        amnt,
        ctdate ctD
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        AND detail.ctdate between  #{startDate}  and #{endDate}
        AND tradetype = '犹退冲减'
        and subriskflag = '主险'
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND agentgroup = #{teamCode}
        </if>
        order by  performancegroup ,agentgroup ,agentcode

    </select>

    <select id="selectAppNumList"
            parameterType="java.util.Map"
            resultType="com.hqins.agent.org.model.vo.SupervisorPerformanceDetailNumberVO">

        SELECT
        count( DISTINCT appntno) fycAppntNoNum,
        count(DISTINCT case when payintv not in ('趸缴','一次性交清') and riskperiod = 'L' then appntno else null end)
        periodAppntNoNum
        FROM dataservice_public.da_detail_uw detail
        where
        1=1
        and premyear = '1'
        and contstate != '犹豫期退保'
        <choose>
            <when test=" accType == 'sign'.toString()">
                AND detail.signdate between #{startDate} and #{endDate}
            </when>
            <otherwise>
                AND detail.validate between #{startDate} and #{endDate}
            </otherwise>
        </choose>
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND detail.performancegroup in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND agentgroup = #{teamCode}
        </if>
    </select>


</mapper>