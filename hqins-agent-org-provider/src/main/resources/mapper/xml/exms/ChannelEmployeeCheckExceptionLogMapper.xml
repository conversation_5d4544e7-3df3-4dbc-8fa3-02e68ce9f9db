<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.ChannelEmployeeCheckExceptionLogMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckExceptionLog">
            <result property="id" column="id"/>
            <result property="reqParam" column="req_param"/>
            <result property="repMessage" column="rep_message"/>
            <result property="createUser" column="create_user"/>
            <result property="createUserName" column="create_user_name"/>
            <result property="createTime" column="create_time"/>
            <result property="modifyUser" column="modify_user"/>
            <result property="modifyUserName" column="modify_user_name"/>
            <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,req_param,rep_message,create_user,create_user_name,create_time,modify_user,modify_user_name,modify_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckExceptionLog"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into channel_employee_check_exception_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="reqParam != null">
                req_param,
            </if>
            <if test="repMessage != null">
                rep_message,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyUserName != null">
                modify_user_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="reqParam != null">
                #{reqParam,jdbcType=LONGVARCHAR},
            </if>
            <if test="repMessage != null">
                #{repMessage,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckExceptionLog">
        update channel_employee_check_exception_log
        <set>
            <if test="reqParam != null">
                req_param = #{reqParam,jdbcType=LONGVARCHAR},
            </if>
            <if test="repMessage != null">
                rep_message = #{repMessage,jdbcType=LONGVARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>


</mapper>
