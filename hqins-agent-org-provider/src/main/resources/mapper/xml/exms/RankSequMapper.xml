<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.RankSequMapper">

    <select id="getAllUsefulRankSequ" resultType="com.hqins.agent.org.dao.entity.exms.RankSequ">
        select
            s.RANKSEQUID as rankSequId,
            s.RANKSEQUCODE as rankSequCode,
            s.RANKSEQUNAME as rankSequName,
            s.SYSTEMSTATUS as systemStatus
        from tbranksequ s
        where
            s.SYSTEMSTATUS = '1';

    </select>

    <select id="getRankSequenceByCompanyCode" resultType="com.hqins.agent.org.dao.entity.exms.RankSequ">
        select
            s.RANKSEQUID as rankSequId,
            s.RANKSEQUCODE as rankSequCode,
            s.RANKSEQUNAME as rankSequName,
            s.SYSTEMSTATUS as systemStatus
        from tbranksequ s
        where
            s.CPTYPE = 'PARTY'
            and s.SYSTEMSTATUS = '1'
            and s.CHANLECODE = #{companyCode}
        order by s.RANKSEQUCODE
    </select>

</mapper>