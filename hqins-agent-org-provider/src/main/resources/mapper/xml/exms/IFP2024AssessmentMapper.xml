<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.IFP2024AssessmentMapper">

    <resultMap id="RankDef_Map" type="com.hqins.agent.org.dao.entity.exms.RankDef">
        <result property="rankCode" column="rankcode"/>
        <result property="rankSequCode" column="serialcode"/>
    </resultMap>

    <select id="getRankInfo" resultMap="RankDef_Map">
        select
            rankcode,
            serialcode
        from
            tbemp
        where
            empcode = #{employeeCode}
    </select>

    <resultMap id="PersonInfo_Map" type="com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfoTmp">
        <result property="id" column="id"/>
        <result property="checkType" column="check_type"/>
        <result property="checkMonth" column="check_month"/>
        <result property="agentCode" column="agent_code"/>
        <result property="agentName" column="agent_name"/>
        <result property="rankCode" column="rank_code"/>
        <result property="rankSeqCode" column="rank_seq_code"/>
        <result property="checkSettleResult" column="check_settle_result"/>
        <result property="checkStartMonth" column="check_start_month"/>
        <result property="checkEndMonth" column="check_end_month"/>
        <result property="checkMonthNums" column="check_month_nums"/>
        <result property="keepActualSTP" column="keep_actual_stp"/>
        <result property="promoteActualSTP" column="promote_actual_stp"/>
        <result property="keepActualMSTP" column="keep_actual_mstp"/>
        <result property="promoteActualMSTP" column="promote_actual_mstp"/>
        <result property="team1" column="team1"/>
        <result property="team2" column="team2"/>
        <result property="team3" column="team3"/>
        <result property="targetRankCode" column="target_rank_code"/>
        <result property="targetRankSeqCode" column="target_rank_seq_code"/>
    </resultMap>

    <select id="queryCheckPersonInfo" resultMap="PersonInfo_Map">
        select
            id,
            check_type,
            check_month,
            agent_code,
            agent_name,
            rank_code,
            rank_seq_code,
            check_settle_result,
            check_start_month,
            check_end_month,
            check_month_nums,
            keep_actual_stp,
            promote_actual_stp,
            keep_actual_mstp,
            promote_actual_mstp,
            team1,
            team2,
            team3,
            target_rank_code,
            target_rank_seq_code
        from
            check_batch_person_info_tmp
        where
            1 = 1
            and agent_code = #{employeeCode}
            and current_assessment_check_flag != '1'
            and check_month > '2024-06'
            <if test="checkMonthList != null and checkMonthList.size() > 0">
                and check_month in
                <foreach collection="checkMonthList" item="checkMonth" open="(" separator="," close=")">
                    #{checkMonth}
                </foreach>
            </if>
    </select>


    <resultMap id="PersonConfig_Map" type="com.hqins.agent.org.dao.entity.exms.CheckBatchPersonConfig">
        <result property="checkMonth" column="check_month"/>
        <result property="agentCode" column="agent_code"/>
        <result property="checkItem" column="check_item"/>
        <result property="month" column="month"/>
        <result property="value" column="value"/>
        <result property="contentValue" column="content_value"/>
        <result property="checkType" column="check_type"/>
        <result property="checkPersonTmpId" column="check_person_tmp_id"/>
    </resultMap>

    <select id="queryAllPersonConfig" resultMap="PersonConfig_Map">
        select
            check_month,
            agent_code,
            check_item,
            month,
            value,
            content_value,
            check_type,
            check_person_tmp_id
        from
            check_batch_person_config
        where
            1 = 1
            <if test="tmpIdList != null and tmpIdList.size() > 0">
                and check_person_tmp_id in
                <foreach collection="tmpIdList" item="tmpId" open="(" separator="," close=")">
                    #{tmpId}
                </foreach>
            </if>
    </select>

    <resultMap id="FlowInfo_Map" type="com.hqins.agent.org.dao.entity.exms.TbEmpFlow">
        <result property="changType" column="chgtype"/>
        <result property="empCode" column="empcode"/>
        <result property="empName" column="empname"/>
        <result property="serialCode" column="serialcode"/>
        <result property="rankCode" column="rankcode"/>
        <result property="rankTime" column="ranktime"/>
    </resultMap>

    <select id="getFlowInfo" resultMap="FlowInfo_Map">
        select
            chgtype,
            empcode,
            empname,
            serialcode,
            rankcode,
            ranktime
        from
            tbempflow
        where
            empcode = #{employeeCode}
        and
            chgtype in ('00', '20')
    </select>

    <select id="queryCheckGroupInfo" resultMap="PersonInfo_Map">
        select
            t.id,
            t.check_type,
            t.check_month,
            t.agent_code,
            t.agent_name,
            t.rank_code,
            t.rank_seq_code,
            t.check_settle_result,
            t.check_start_month,
            t.check_end_month,
            t.check_month_nums,
            t.keep_actual_stp,
            t.promote_actual_stp,
            t.keep_actual_mstp,
            t.promote_actual_mstp,
            t.team1,
            t.team2,
            t.team3,
            t.target_rank_code,
            t.target_rank_seq_code
        from
            check_batch_person_info_tmp t
        where
            1 = 1
            and (t.team1 = #{teamCode} or t.team2 = #{teamCode} or t.team3 = #{teamCode})
            and t.current_assessment_check_flag != '1'
            and t.check_month > '2024-06'
            <if test="checkMonthList != null and checkMonthList.size() > 0">
                and t.check_month in
                <foreach collection="checkMonthList" item="checkMonth" open="(" separator="," close=")">
                    #{checkMonth}
                </foreach>
            </if>
    </select>

    <resultMap id="Batch_Map" type="com.hqins.agent.org.dao.entity.exms.CheckBatch">
        <result property="checkMonth" column="check_month"/>
        <result property="checkStatus" column="check_status"/>
    </resultMap>

    <select id="queryBatchList" resultMap="Batch_Map">
        select
            b.check_month,
            b.check_status
        from
            check_batch b
        left join
            check_batch_inst i on b.id = i.check_batch_id
        where
            1 = 1
        and i.partner_inst_code = #{instCode}
        <if test="checkMonthList != null and checkMonthList.size() > 0">
            and i.check_month in
            <foreach collection="checkMonthList" item="checkMonth" open="(" separator="," close=")">
                #{checkMonth}
            </foreach>
        </if>
    </select>

    <select id="queryDate" resultType="java.lang.String">
        select
            sign_date
        from
            tmp_date_table
    </select>

    <select id="queryBasicLawList" resultType="com.hqins.agent.org.model.vo.BasicLawInfoVO">

        -- 生效团队
        select
            e.version_id BasicLawId,
            e.version_type BasicLawType,
            e.basic_law_version_name BasicLawName
        from  basic_law_exec_teams_config f
                                        inner join basic_law_exec e on f.exec_id = e.id and e.apply_status = 'ENABLE' and check_status =1
            AND #{yearMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.end_date, '%Y-%m')
        where f.team_code = #{teamCode}

        union

        -- 生效机构
        select
            e.version_id BasicLawId,
            e.version_type BasicLawType,
            e.basic_law_version_name BasicLawName
        from tbsaleteam t
                                       inner join basic_law_exec_teams_config f on t.inst_code =  f.inst_code
                                       inner join basic_law_exec e on f.exec_id = e.id and e.apply_status = 'ENABLE' and check_status =1
            AND #{yearMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.end_date, '%Y-%m')
        where t.saleteamcode = #{teamCode}

        union

        -- 失效团队
        (select
             e.version_id BasicLawId,
             e.version_type BasicLawType,
             e.basic_law_version_name BasicLawName
        from basic_law_exec_teams_config t
                                        inner join basic_law_exec e on t.exec_id = e.id
            and e.apply_status = 'DISABLE' and e.check_status = '1'
            AND #{yearMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.invalid_date, '%Y-%m')
         where  t.team_code = #{teamCode}
         ORDER BY e.create_time DESC limit 1)

        union

        -- 失效机构
        (select
             e.version_id BasicLawId,
             e.version_type BasicLawType,
             e.basic_law_version_name BasicLawName
        from tbsaleteam t
                                        inner join basic_law_exec_teams_config f on t.inst_code =  f.inst_code
                                        inner join basic_law_exec e on f.exec_id = e.id
            and e.apply_status = 'DISABLE' and check_status =1
            AND #{yearMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.invalid_date, '%Y-%m')
         where t.saleteamcode = #{teamCode}
         ORDER BY e.create_time DESC
        )

        limit 1
    </select>


    <select id="queryInstBasicLawList" resultType="com.hqins.agent.org.model.vo.BasicLawInfoVO">

        select
        distinct
        e.version_id BasicLawId,
        e.version_type BasicLawType,
        e.basic_law_version_name BasicLawName
        from basic_law_exec_teams_config f
        inner join basic_law_exec e on f.exec_id = e.id and check_status =1
        AND #{yearMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.end_date, '%Y-%m')
        where f.inst_code in
        <foreach collection="instCodeList" item="instCode" open="(" separator="," close=")">
            #{instCode}
        </foreach>

    </select>

    <select id="getCheckBatchInfo" resultType="com.hqins.agent.org.model.vo.CheckBatchInfoVO">
        select
            batch.id batchId,
            batch.check_month checkMonth,
            batch.check_status batchStatus,
            batch.version_id versionId,
            batch.version_name versionName,
            batch.version_type versionType
        from
            check_batch batch
        where
            batch.partner_code = #{partnerCode}
            and batch.batch_type = 'SELF_2024'
            and batch.check_all_nums != '0'
            <if test="checkMonthList != null and checkMonthList.size() > 0">
                and batch.check_month in
                <foreach collection="checkMonthList" item="checkMonth" open="(" separator="," close=")">
                    #{checkMonth}
                </foreach>
            </if>
    </select>

    <select id="getCheckBatchInfoByVersionId" resultType="com.hqins.agent.org.model.vo.CheckBatchInfoVO">
        select
            batch.id batchId,
            batch.check_month checkMonth,
            batch.check_status batchStatus,
            batch.version_id versionId,
            batch.version_name versionName,
            batch.version_type versionType
        from
            check_batch batch
        where
            batch.batch_type = 'SELF_2024'
            and batch.partner_code = #{partnerCode}
            <if test="checkMonthList != null and checkMonthList.size() > 0">
                and batch.check_month in
                <foreach collection="checkMonthList" item="checkMonth" open="(" separator="," close=")">
                    #{checkMonth}
                </foreach>
            </if>
    </select>

    <select id="getRiskManagerPersonInfoListByVersionId" resultType="com.hqins.agent.org.model.vo.FamilyRiskManagerCheckInfo">
        select
            batch.version_id basicLawId,
            batch.version_type basicLawType,
            batch.version_name basicLawName,
            tmp.partner_inst_code instCode,
            tmp.partner_inst_name instName,
            team.saleteamcode teamCode,
            team.saleteamname teamName,
            tmp.agent_code agentCode,
            tmp.agent_name agentName,
            tmp.rank_code rankCode,
            tmp.rank_name rankName,
            '-' consecutiveZeroNineMonths,
            config.content_value maintenanceMonthlyFyc,
            tmp.keep_actual_mstp keepActualStp,
            tmp.check_settle_result checkSettleResult,
            tmp.check_month checkMonth
        from
            check_batch batch
        inner join check_batch_person_info_tmp tmp on tmp.check_batch_id = batch.id
        inner join check_batch_person_config config on config.check_person_tmp_id = tmp.id and config.check_item = 'advisor_maintenance_monthly_fyc'
        left join  tbsaleteam team on team.saleteamincode = tmp.team1
        where
            batch.batch_type = 'SELF_2024'
            and batch.check_month = #{baseVersionDate}
            and tmp.check_settle_result != '100'
            and tmp.rank_code != 'FHWC01'
            and tmp.rank_seq_code = 'FHWC'
            and tmp.check_type = 'MAINTAIN'
            <if test="instCodeList != null and instCodeList.size() > 0">
                and tmp.partner_inst_code in
                <foreach collection="instCodeList" item="instCode" open="(" separator="," close=")">
                    #{instCode}
                </foreach>
            </if>
            <if test="teamCode != null and teamCode != ''">
                and (tmp.team1 = #{teamCode} or tmp.team2 = #{teamCode} or tmp.team3 = #{teamCode})
            </if>
    </select>

    <select id="getPartnerPersonInfoListByVersionId" resultType="com.hqins.agent.org.model.vo.PartnerCheckInfo">
        select
            batch.version_id basicLawId,
            batch.version_type basicLawType,
            batch.version_name basicLawName,
            tmp.partner_inst_code instCode,
            tmp.partner_inst_name instName,
            team.saleteamcode teamCode,
            team.saleteamname teamName,
            tmp.agent_code agentCode,
            tmp.agent_name agentName,
            tmp.rank_code rankCode,
            tmp.rank_name rankName,
            tmp.id tmpId,
            tmp.check_settle_result checkSettleResult,
            tmp.check_month checkMonth,
            tmp.keep_actual_mstp partnerKeepActualStp
        from
            check_batch batch
        inner join check_batch_person_info_tmp tmp on batch.id = tmp.check_batch_id
        left join  tbsaleteam team on team.saleteamincode = tmp.team1
        where
            batch.batch_type = 'SELF_2024'
            and batch.check_month = #{partnerQuarter}
            and tmp.check_settle_result != '100'
            and tmp.rank_seq_code = 'SWS'
            and tmp.check_type = 'MAINTAIN'
            <if test="instCodeList != null and instCodeList.size() > 0">
                and tmp.partner_inst_code in
                <foreach collection="instCodeList" item="instCode" open="(" separator="," close=")">
                    #{instCode}
                </foreach>
            </if>
            <if test="teamCode != null and teamCode != ''">
                and (tmp.team1 = #{teamCode} or tmp.team2 = #{teamCode} or tmp.team3 = #{teamCode})
            </if>
    </select>

    <select id="getProbationPersonInfo" resultType="com.hqins.agent.org.model.vo.FamilyRiskManagerCheckInfo">
        select
            batch.version_id basicLawId,
            batch.version_type basicLawType,
            batch.version_name basicLawName,
            tmp.partner_inst_code instCode,
            tmp.partner_inst_name instName,
            team.saleteamcode teamCode,
            team.saleteamname teamName,
            tmp.agent_code agentCode,
            tmp.agent_name agentName,
            tmp.rank_code rankCode,
            tmp.rank_name rankName,
            config.content_value consecutiveZeroNineMonths,
            '-' maintenanceMonthlyFyc,
            '-' keepActualStp,
            '-' distanceGap,
            tmp.check_settle_result checkSettleResult,
            tmp.check_month checkMonth
        from
            check_batch batch
            left join check_batch_person_info_tmp tmp on tmp.check_batch_id = batch.id
            left join check_batch_person_config config on config.check_person_tmp_id = tmp.id and config.check_item = 'consecutive_zero_9months'
            left join tbsaleteam team on team.saleteamincode = tmp.team1
        where
                batch.batch_type = 'SELF_2024'
            and tmp.check_settle_result != '100'
            and tmp.rank_code = 'FHWC01'
            and tmp.rank_seq_code = 'FHWC'
            and tmp.check_type = 'MAINTAIN'
            <if test="checkBatchIdList != null and checkBatchIdList.size() > 0">
                and tmp.check_batch_id in
                <foreach collection="checkBatchIdList" item="checkBatchId" open="(" separator="," close=")">
                    #{checkBatchId}
                </foreach>
            </if>
            <if test="instCodeList != null and instCodeList.size() > 0">
                and tmp.partner_inst_code in
                <foreach collection="instCodeList" item="instCode" open="(" separator="," close=")">
                    #{instCode}
                </foreach>
            </if>
            <if test="teamCode != null and teamCode != ''">
                and (tmp.team1 = #{teamCode} or tmp.team2 = #{teamCode} or tmp.team3 = #{teamCode})
            </if>
    </select>
</mapper>