<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.TbpartassignmanagerMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager">
        <result property="pkid" column="pkid"/>
        <result property="companyid" column="companyid"/>
        <result property="companycode" column="companycode"/>
        <result property="companyname" column="companyname"/>
        <result property="companyInstCode" column="company_inst_code"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantCode" column="merchant_code"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="merchantOrgId" column="merchant_org_id"/>
        <result property="merchantOrgCode" column="merchant_org_code"/>
        <result property="merchantOrgName" column="merchant_org_name"/>
        <result property="custManagerId" column="cust_manager_id"/>
        <result property="custManagerCode" column="cust_manager_code"/>
        <result property="custManagerName" column="cust_manager_name"/>
        <result property="mainManagerFlag" column="main_manager_flag"/>
        <result property="benefitRatio" column="benefit_ratio"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        `pkid`,`companyid`,`companycode`,`companyname`,`company_inst_code`,`merchant_id`,`merchant_code`,`merchant_name`,`merchant_org_id`,`merchant_org_code`,`merchant_org_name`,`cust_manager_id`,`cust_manager_code`,`cust_manager_name`,`main_manager_flag`,`benefit_ratio`,`create_user`,`create_time`,`modify_user`,`modify_time`
    </sql>

    <select id="getValidCustManagerList" resultType="com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager">
        SELECT t.CUST_MANAGER_CODE as custManagerCode ,t.CUST_MANAGER_NAME as custManagerName,t.MERCHANT_ORG_CODE as merchantOrgCode,t.MERCHANT_ORG_NAME as merchantOrgName
        FROM tbpartassignmanager t
        left join tbemp e on t.CUST_MANAGER_CODE = e.empcode
        WHERE e.empstatus = '01'
        <if test="merchantCodes != null and merchantCodes.size > 0">
            and t.MERCHANT_CODE in
            <foreach collection="merchantCodes" item="merchantCode" open="(" separator="," close=")">
                #{merchantCode}
            </foreach>
        </if>
        <if test="orgCodes != null and orgCodes.size > 0">
            and t.MERCHANT_ORG_CODE in
            <foreach collection="orgCodes" item="orgCode" open="(" separator="," close=")">
                #{orgCode}
            </foreach>
        </if>
    </select>

    <select id="getValidCustManagerListByOrgCode" resultType="com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager">
        SELECT t.CUST_MANAGER_CODE as custManagerCode ,t.CUST_MANAGER_NAME as custManagerName,t.MERCHANT_ORG_CODE as merchantOrgCode,t.MERCHANT_ORG_NAME as merchantOrgName,
            t.MAIN_MANAGER_FLAG as mainManagerFlag
        FROM tbpartassignmanager t
        left join tbemp e on t.CUST_MANAGER_CODE = e.empcode
        WHERE e.empstatus = '01' and t.MERCHANT_ORG_CODE = #{orgCode}
    </select>

    <select id="queryCompanyInstByCompanyInstCode" resultType="com.hqins.agent.org.dao.entity.exms.Tbpartassignmanager">
        SELECT t3.MERCHANT_ORG_CODE,t3.MERCHANT_ORG_NAME
        from zh_exms.tbpartassignmanager t3
                 LEFT JOIN zh_exms.tbemp t4 on t3.CUST_MANAGER_CODE = t4.empcode
        WHERE t3.MAIN_MANAGER_FLAG ='0' and t4.INST_CODE = #{companyInstCode}
    </select>


</mapper>
