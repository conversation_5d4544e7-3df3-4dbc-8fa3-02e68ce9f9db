<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.PerformanceMapper">


    <select id="getCurrentMonthIncomeInfo" parameterType="map" resultType="BigDecimal">
        select
        amount_pre_tax
        FROM settle_batch_person_commission_detail
        where agent_code = #{empCode} and settle_month = #{settleMonth} and commission_item = 'AMOUNT'
    </select>


    <select id="getPreSettleCommissionVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.CommissionItemVO">
        select
        version_type versionType,
        settle_month settleMonth,
        rank_seq_code rankSeqCode,
        rank_code rankCode,
        commission_type commissionType,
        commission_item commissionItem,
        commission_item_name commissionItemName,
        amount,
        rate,
        amount_pre_tax amountPreTax,
        total_paid_amount totalPaidAmount
        FROM settle_batch_person_commission_detail
        where agent_code = #{empCode} and settle_batch_id = 'PRE' and #{settleMonth}  >= settle_month and settle_month is not null
    </select>


    <select id="getSettleCommissionVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.CommissionItemVO">
        select
            version_type versionType,
            settle_month settleMonth,
            rank_seq_code rankSeqCode,
            rank_code rankCode,
            commission_type commissionType,
            commission_item commissionItem,
            commission_item_name commissionItemName,
            amount,
            rate,
            amount_pre_tax amountPreTax,
            total_paid_amount totalPaidAmount
        FROM settle_batch_person_commission_detail
        where agent_code = #{empCode} and settle_batch_id != 'PRE' and settle_month = #{settleMonth}
    </select>

    <select id="getLastSettleCommissionVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.CommissionItemVO">
        select
            version_type versionType,
            settle_month settleMonth,
            rank_seq_code rankSeqCode,
            rank_code rankCode,
            commission_type commissionType,
            commission_item commissionItem,
            commission_item_name commissionItemName,
            amount,
            rate,
            amount_pre_tax amountPreTax,
            total_paid_amount totalPaidAmount
        FROM settle_batch_person_commission_detail
        where agent_code = #{empCode}
        and settle_month  = (
        select MAX(settle_month) from settle_batch_person_commission_detail where agent_code = #{empCode}  and settle_batch_id != 'PRE'
        )

    </select>


    <select id="getMonthsSettleIncomeInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalIncomeVO">
        select
        settle_month as `month`,
        amount_pre_tax as amountPreTax,
        total_paid_amount as totalPaidAmount
        FROM settle_batch_person_commission_detail
        where
        agent_code = #{empCode}
        and commission_item = 'AMOUNT'
        and settle_batch_id != 'PRE'
        and
        <if test="monthList != null and monthList.size() > 0">
            settle_month in
            <foreach collection="monthList" item="month" open="(" separator="," close=")">
                #{month}
            </foreach>
        </if>
     </select>


    <select id="getSettleIncomeAmountInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalIncomeVO">
        select
        version_type as versionType,
        version_id as  versionId,
        settle_month as `month`,
        amount_pre_tax as amountPreTax,
        total_paid_amount as totalPaidAmount,
        settle_batch_id as settleBatchId
        FROM settle_batch_person_commission_detail
        where
        agent_code = #{empCode}
        and commission_item = 'AMOUNT'
        <if test="settleMonth != null">
            and settle_month = #{settleMonth}
        </if>
        <if test="settleBatchId != null">
            and settle_batch_id = #{settleBatchId}
        </if>
        order by settle_month asc limit 1
    </select>

    <select id="getPreSettlePolicyInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
            select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            d.commission_item commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            i.sub_risk_flag subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            i.settle_flag  settleFlag,
            null settleDate,
            i.premium  premium,
            d.amount,
            c.amount dcp,
            null contType,
            i.source
            from commission_policy_info i
            inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item = #{commissionItem}
            left join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
            where i.agent_code = #{empCode} and i.basic_law_version_id = #{versionId}
            <choose>
                <when test="firstPreMonthFlag != null">
                    and #{settleMonth} >= i.commission_month
                    and i.settle_flag in (0,1,3,4)
                </when>
                <otherwise>
                    and i.settle_flag = 0
                    and #{settleMonth} = i.commission_month
                </otherwise>
            </choose>
            <if test="queryType != null">
                and i.source = #{queryType}
            </if>
            and i.enable_settle_flag = 1
            and (i.group_no not like 'GP%' or i.group_no is null)

            union all

            select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            d.commission_item commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            'M' subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            i.settle_flag  settleFlag,
            null settleDate,
            SUM(IfNUll(i.premium,0)) premium,
            SUM(IfNUll(d.amount,0))  amount,
            SUM(IfNUll(c.amount,0))  dcp,
            1 contType,
            i.source
            from commission_policy_info i
            inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item = #{commissionItem}
            left join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
            where i.agent_code = #{empCode} and i.basic_law_version_id = #{versionId}
            <choose>
                <when test="firstPreMonthFlag != null">
                    and #{settleMonth} >= i.commission_month
                    and i.settle_flag in (0,1,3,4)
                </when>
                <otherwise>
                    and i.settle_flag = 0
                    and #{settleMonth} = i.commission_month
                </otherwise>
            </choose>
            <if test="queryType != null">
                and i.source = #{queryType}
            </if>
            and i.enable_settle_flag = 1
            and i.group_no like 'GP%'
            group by i.group_no
    </select>

    <select id="getPreSettleRSCPolicyInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        d.commission_item commissionItem,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        '已续缴' qqdFlag,
        i.sub_risk_flag subRiskFlag,
        i.sign_date signDate,
        (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
        i.settle_flag  settleFlag,
        null settleDate,
        i.premium  premium,
        d.amount,
        c.amount dcp,
        null contType,
        i.source
        from commission_policy_info i
        inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item in ('RSC','RYC')
        left join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
        where i.agent_code = #{empCode} and i.basic_law_version_id = #{versionId}
        <choose>
            <when test="firstPreMonthFlag != null">
                and #{settleMonth} >= i.commission_month
                and i.settle_flag in (0,1,3,4)
            </when>
            <otherwise>
                and i.settle_flag = 0
                and #{settleMonth} = i.commission_month
            </otherwise>
        </choose>
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>
        and i.enable_settle_flag = 1
        and (i.group_no not like 'GP%' or i.group_no is null)

    </select>

    <select id="getCommissionPolicyVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            d.commission_item  commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            i.sub_risk_flag subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            i.settle_flag  settleFlag,
            r.fd_pass_time settleDate,
            i.premium premium,
            d.amount amount,
            c.amount dcp,
            null contType,
            i.source
        from settle_batch_record r
                 inner join settle_batch_policy p on r.id = p.settle_batch_id and p.settle_plan = 1
                 inner join commission_policy_info i on p.policy_id = i.id
                 inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item = #{commissionItem}
                 left  join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
        where r.id = #{settleBatchId} and i.agent_code = #{empCode} and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>

        union all

        select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            d.commission_item  commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            'M' subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            i.settle_flag  settleFlag,
            r.fd_pass_time settleDate,
            SUM(IfNUll(i.premium,0)) premium,
            SUM(IfNUll(d.amount,0))  amount,
            SUM(IfNUll(c.amount,0))  dcp,
            1 contType,
            i.source
        from settle_batch_record r
                 inner join settle_batch_policy p on r.id = p.settle_batch_id and p.settle_plan = 1
                 inner join commission_policy_info i on p.policy_id = i.id
                 inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item = #{commissionItem}
                 left  join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
        where r.id = #{settleBatchId} and i.agent_code = #{empCode} and i.group_no like 'GP%'
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>
        group by i.group_no
    </select>

    <select id="getCommissionRSCPolicyVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        d.commission_item  commissionItem,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        '已续缴' qqdFlag,
        i.sub_risk_flag subRiskFlag,
        i.sign_date signDate,
        (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
        i.settle_flag  settleFlag,
        r.fd_pass_time settleDate,
        i.premium premium,
        d.amount amount,
        c.amount dcp,
        null contType,
        i.source
        from settle_batch_record r
        inner join settle_batch_policy p on r.id = p.settle_batch_id and p.settle_plan = 1
        inner join commission_policy_info i on p.policy_id = i.id
        inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item In ('RSC','RYC')
        left  join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
        where r.id = #{settleBatchId} and i.agent_code = #{empCode} and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>
    </select>

    <select id="getCommissionGSCPolicyVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">

        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        d.commission_item  commissionItem,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        '已续缴' qqdFlag,
        'M' subRiskFlag,
        i.sign_date signDate,
        (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
        i.settle_flag  settleFlag,
        r.fd_pass_time settleDate,
        SUM(IfNUll(i.premium,0)) premium,
        SUM(IfNUll(d.amount,0))  amount,
        SUM(IfNUll(c.amount,0))  dcp,
        1 contType,
        i.source
        from settle_batch_record r
        inner join settle_batch_policy p on r.id = p.settle_batch_id and p.settle_plan = 1
        inner join commission_policy_info i on p.policy_id = i.id
        inner join commission_policy_detail d on i.id = d.policy_id and d.commission_item = #{commissionItem}
        left  join commission_policy_detail c on i.id = c.policy_id and c.commission_item = 'DCP'
        where r.id = #{settleBatchId} and i.agent_code = #{empCode} and i.group_no like 'GP%'
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>
        group by i.group_no
    </select>

    <select id="getElePolicyVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            i.fee_item_code  commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            i.sub_risk_flag subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            null  settleFlag,
            r.fd_pass_time settleDate,
            i.premium premium,
            i.award_amount amount,
            null contType,
            i.source
        from settle_batch_record r
                 inner join settle_batch_diy_version_import_policy i on r.id  = i.settle_batch_id
        where r.id = #{settleBatchId} and i.award_agent_code = #{empCode}  and  i.fee_item_code  = #{commissionItem} and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>

        union all

        select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            i.fee_item_code  commissionItem,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            '已续缴' qqdFlag,
            'M' subRiskFlag,
            i.sign_date signDate,
            (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
            null  settleFlag,
            r.fd_pass_time settleDate,
            SUM(IfNUll(i.premium,0)) premium,
            SUM(IfNUll(i.award_amount,0))  amount,
            1 contType,
            i.source
        from settle_batch_record r
                 inner join settle_batch_diy_version_import_policy i on r.id  = i.settle_batch_id
        where r.id = #{settleBatchId} and i.award_agent_code = #{empCode}  and  i.fee_item_code  = #{commissionItem} and i.group_no like 'GP%'
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>
        group by i.group_no
    </select>


    <select id="getEleRSCPolicyVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        i.fee_item_code  commissionItem,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        '已续缴' qqdFlag,
        i.sub_risk_flag subRiskFlag,
        i.sign_date signDate,
        (case i.source when 'ZP' then i.edorvalidate else i.renewal_actual_date end) qqdDate,
        null  settleFlag,
        r.fd_pass_time settleDate,
        i.premium premium,
        i.award_amount amount,
        null contType,
        i.source
        from settle_batch_record r
        inner join settle_batch_diy_version_import_policy i on r.id  = i.settle_batch_id
        where r.id = #{settleBatchId} and i.award_agent_code = #{empCode}  and  i.fee_item_code in ('RSC','RYC') and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="queryType != null">
            and i.source = #{queryType}
        </if>

    </select>

    <select id="getCommissionCr" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
               i.agent_code agentCode,
               d.amount currentMonthCr
        from commission_person_info i
        inner join basic_salary_detail d on i.id =d.person_id and d.salary_item = 'SCR13'
        where i.agent_code = #{empCode}
        and i.commission_month =  #{performanceMonth}
        order by i.create_time desc limit 1
    </select>

    <select id="getEmpCr" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
            empcode as agentCode,
            continuation_rate13 as   cr13,
            remark as  remark
        from zh_settle.COMMISSION_CONTINUATION_RATE
        where 1=1
        and empcode = #{empCode}
        and CALCULATE_PERIOD = #{performanceMonth}
        limit 1
    </select>


    <select id="getEmpPerformanceFycAmount" parameterType="map" resultType="BigDecimal">
        select
               SUM(IfNUll(fyc,0))
        from commission_settle_policy
        where agent_code = #{empCode} and performance_month = #{performanceMonth} and settle_flag != 5
        <if test="esPolicyNoList != null and esPolicyNoList.size() > 0">
            and policy_no not in
            <foreach collection="esPolicyNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="getPerformancePolicyInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
            i.policy_no policyNo,
            i.risk_code riskCode,
            i.risk_name riskName,
            i.policy_state policyState,
            i.appntname appntName ,
            i.insuredname insureName ,
            i.insure_years insureYears,
            i.payment_years paymentYears,
            i.risk_period riskPeriod,
            i.risk_period_name riskPeriodName,
            case source WHEN 'QQD' then '已续缴' when 'ZP' then '已续缴' else  '未续缴' end  qqdFlag,
            i.performance_date qqdDate,
            case i.settle_flag WHEN 2 then '已结算' else  '未结算' end  settleFlag,
            i.settle_batch_pass_date settleDate,
            i.premium premium,
            i.fyc ,
            i.ryc ,
            i.sign_date signDate,
            ifnull(i.amount,0) amount,
            null contType,
            i.source
        from commission_settle_policy i
        where i.agent_code = #{empCode} and performance_month = #{performanceMonth} and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="commissionItem != null">
            and commission_item = #{commissionItem}
        </if>
        <if test="esPolicyNoList != null and esPolicyNoList.size() > 0">
            and policy_no not in
            <foreach collection="esPolicyNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="source != null">
            and i.source = #{source}
        </if>
        <if test="sourceList != null and sourceList.size() > 0">
            and i.source  in
            <foreach collection="sourceList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getPerformanceMonthPolicyInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.policy_no policyNo,
        i.edoracceptno edoraccepto,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        i.sub_risk_flag subRiskFlag,
        case source WHEN 'QQD' then '已续缴'  when 'ZP' then '已续缴' else  '未续缴' end  qqdFlag,
        i.performance_date qqdDate,
        case i.settle_flag WHEN 2 then '已结算' else  '未结算' end  settleFlag,
        i.settle_batch_pass_date settleDate,
        i.premium premium,
        ifnull(i.amount,0) amount,
        (case when i.settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc ,
        (case when i.settle_flag = 5 then 0 else IFNULL(ryc,0) end) ryc ,
        (case when i.settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        (case when i.commission_item = 'FSC' and i.settle_flag != 5  then i.amount else 0 end) fsc ,
        (case when i.commission_item in ('RSC','RYC') and i.settle_flag != 5  then i.amount else 0 end) rsc ,
        0 gsc ,
        i.sign_date signDate,
        null contType,
        i.source
        from commission_settle_policy i
        where
        i.agent_code = #{empCode}
        and i.performance_month = #{performanceMonth}
--         and i.settle_flag != 5
        and (i.group_no not like 'GP%' or i.group_no is null)
--         and (i.cont_type = 1)
        <choose>
            <when test="premiumYear == '1'.toString()">
                and i.premium_year = '1' and i.source != 'YT'
            </when>
            <otherwise>
                and i.premium_year > '1'
            </otherwise>
        </choose>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and i.pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="source != null">
            and i.source = #{source}
        </if>
        <if test="sourceList != null and sourceList.size() > 0">
            and i.source  in
            <foreach collection="sourceList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        union all

        select
        i.policy_no policyNo,
        i.edoracceptno edoraccepto,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        i.sub_risk_flag subRiskFlag,
        case source WHEN 'QQD' then '已续缴'   when 'ZP' then '已续缴' else  '未续缴' end  qqdFlag,
        i.performance_date qqdDate,
        null settleFlag,
        null settleDate,
        SUM(IfNUll(i.premium,0)) premium,
        SUM(IfNUll(i.amount,0)) amount,
        sum(IFNULL(fyc,0)) fyc,
        sum(IFNULL(ryc,0)) ryc,
        SUM(IfNUll(i.dcp,0)) dcp,
        0 fsc ,
        0 rsc ,
        sum((case when i.commission_item = 'GSC' and i.settle_flag != 5 then IfNUll(i.amount,0) else 0 end)) gsc ,
        i.sign_date signDate,
        1 contType,
        i.source
        from commission_settle_policy i
        where
        i.agent_code = #{empCode}
        and i.performance_month = #{performanceMonth}
        and i.settle_flag != 5
        and i.group_no like 'GP%'
        <choose>
            <when test="premiumYear == '1'.toString()">
                and i.premium_year = '1'
            </when>
            <otherwise>
                and i.premium_year > '1'
            </otherwise>
        </choose>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and i.pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="source != null">
            and i.source = #{source}
        </if>
        <if test="sourceList != null and sourceList.size() > 0">
            and i.source  in
            <foreach collection="sourceList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by i.group_no
    </select>


    <select id="getPerformancePolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        i.sub_risk_flag subRiskFlag,
        case source WHEN 'QQD' then '已续缴'  when 'ZP' then '已续缴' else  '未续缴' end  qqdFlag,
        i.performance_date qqdDate,
        case i.settle_flag WHEN 2 then '已支付' else  '未支付' end  settleFlag,
        i.settle_batch_pass_date settleDate,
        i.premium premium,
        ifnull(i.amount,0) amount,
        (case when i.settle_flag = 5 then 0 else i.fyc end) fyc ,
        (case when i.settle_flag = 5 then 0 else i.ryc end) ryc ,
        (case when i.premium_year = '1' and i.risk_period = 'L' and self_insure_flag = 0  then IFNULL(dcp,0) else null end) dcp,
        i.sign_date signDate,
        null contType,
        i.source
        from commission_settle_policy i
        where
        i.agent_code = #{empCode}
        and (i.group_no not like 'GP%' or i.group_no is null)
        <if test="policyNo != null">
            and i.policy_no = #{policyNo}
        </if>
        <if test="appntName != null">
            and i.appntname like CONCAT(#{appntName},'%')
        </if>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and i.pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        union all

        select
        i.policy_no policyNo,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.policy_state policyState,
        i.appntname appntName ,
        i.insuredname insureName ,
        i.insure_years insureYears,
        i.payment_years paymentYears,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        i.sub_risk_flag subRiskFlag,
        case source WHEN 'QQD' then '已续缴'  when 'ZP' then '已续缴' else  '未续缴' end  qqdFlag,
        i.performance_date qqdDate,
        null settleFlag,
        null settleDate,
        SUM(IfNUll(i.premium,0)) premium,
        SUM(IfNUll(i.amount,0)) amount,
        sum(IFNULL(i.fyc,0))  fyc,
        sum(IFNULL(i.ryc,0))  ryc,
        sum(IFNULL(i.dcp,0))  dcp,
        i.sign_date signDate,
        1 contType,
        i.source
        from commission_settle_policy i
        where
        i.agent_code = #{empCode}
        and i.group_no like 'GP%'
        and i.settle_flag != 5
        <if test="policyNo != null">
            and i.policy_no = #{policyNo}
        </if>
        <if test="appntName != null">
            and i.appntname like CONCAT(#{appntName},'%')
        </if>
        group by i.group_no
    </select>

    <select id="getTeamCurrentPersonList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
            e.empcode as agentCode,
            e.empname as agentName,
            r.RANKCODE as rankCode,
            r.RANKNAME as rankName,
            e.entrytime as entryDate
        from tbsaleteam t
        INNER JOIN tbemp e on e.saleteamincode = t.saleteamincode and e.empstatus = '01'
        inner join tbrankdef r on r.CHANLECODE = e.companycode and r.RANKCODE = e.rankcode and r.SYSTEMSTATUS = '1'
        where
            t.saleteamtype = '03'
          and t.saleteamcode = #{saleTeamCode}
    </select>

    <select id="getTeamCurrentPersonQuitList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
            e.empcode as agentCode,
            e.empname as agentName
        from tbsaleteam t
        INNER JOIN tbemp e on e.saleteamincode = t.saleteamincode and  (e.empstatus = '01' or (e.empstatus = '04' and DATE_FORMAT(e.quittime, '%Y-%m') = DATE_FORMAT(CURRENT_DATE(), '%Y-%m')))
        where
        t.saleteamtype = '03'
        and t.saleteamcode = #{saleTeamCode}
    </select>


    <select id="getCommissionTeamInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamManageVO">
        select
        IFNULL(sum(if(d.amount>=1000,1,0)),0) standardPersonNum,
        IFNULL(sum(if(d.amount>0,1,0)),0) activePersonNum,
        c.amount currentMonthCr
        from commission_person_info i
        left join basic_salary_detail d on i.id = d.person_id and d.salary_item = 'LFYC'
        left join basic_salary_detail c on i.id = c.person_id and c.salary_item = 'TCR13'
        where
        i.commission_month = #{commissionMonth}
        and i.team_code = #{saleTeamCode}
    </select>



    <select id="getPerformanceListByCodeList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
--         SUM(num>=1,1,IF(num <![CDATA[<=]]>  -1,-1,num))) currentMonthPolicyNum,
        SUM(num) currentMonthPolicyNum,
        SUM(premium) currentMonthFYP,
        SUM(fyc) currentMonthFYC,
        SUM(dcp) currentMonthDCP,
        agent_code agentCode,
        agent_name agentName

        from
        (
        select
        1 num,
        SUM(IFNULL(premium,0)) premium,
        SUM(case when i.settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM((case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end)) dcp,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        performance_month = #{performanceMonth}
        and premium_year = '1'
--         and i.settle_flag != 5
        and i.agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code
    </select>


    <select id="getPersonalPerformance" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceVO">
        select
        SUM(num) fycPolicyNum,
        SUM(periodNum) periodPolicyNum,
        SUM(premium) fycPremium,
        SUM(periodPremium) periodPremium,
        <if test="appntNoList != null and appntNoList.size() > 0">
            count(distinct IF( appntno in
            <foreach collection="appntNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            ,null ,appntno)) + ${appntNoList.size()} appntnoNum,
        </if>
        <if test="appntNoList == null ">
            count(distinct appntno) appntnoNum,
        </if>
        SUM(fyc) fycAmount,
        SUM(dcp) periodDCP,
        SUM(fdcp) dcp

        from
        (
        select
        1 num,
        (case when payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD') then 1 else 0 end)  periodNum,
        SUM(IFNULL(premium,0)) premium,
        SUM(if(payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD'),IFNULL(premium,0),0)) periodPremium,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(source = 'UW' and payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0 ,IFNULL(dcp,0),0) end) fdcp,
        MAX(CASE WHEN source = 'UW' THEN appntno ELSE NULL END) appntno,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        agent_code = #{empCode}
        and performance_month = #{performanceMonth}
        and premium_year = '1'
--         and i.settle_flag != 5
        and i.source != 'YT'
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code
    </select>


    <select id="getYearPersonalPerformance" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceVO">
        select
        SUM(num) fycPolicyNum,
        SUM(periodNum) periodPolicyNum,
        SUM(premium) fycPremium,
        SUM(periodPremium) periodPremium,
        <if test="appntNoList != null and appntNoList.size() > 0">
            count(distinct IF( appntno in
            <foreach collection="appntNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            ,null ,appntno)) + ${appntNoList.size()} appntnoNum,
        </if>
        <if test="appntNoList == null ">
            count(distinct appntno) appntnoNum,
        </if>
        SUM(fyc) fycAmount,
        SUM(dcp) periodDCP,
        SUM(fdcp) dcp

        from
        (
        select
        1 num,
        (case when payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD') then 1 else 0 end)  periodNum,
        SUM(IFNULL(premium,0)) premium,
        SUM(if(payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD'),IFNULL(premium,0),0)) periodPremium,
--         SUM(IFNULL(fyc,0)) fyc,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(source = 'UW' and payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) fdcp,
        MAX(CASE WHEN source = 'UW' THEN appntno ELSE NULL END) appntno,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        agent_code = #{empCode}
        and YEAR(i.performance_date) = #{performanceYear}
        and premium_year = '1'
        and i.source != 'YT'
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code
    </select>

    <select id="getPersonalAppntNoList" parameterType="map" resultType="String">
        select
        distinct i.appntno
        from commission_settle_policy i
        where
        agent_code = #{empCode}
        and performance_month = #{performanceMonth}
        and premium_year = '1'
--         and i.settle_flag != 5
        and i.source = 'UW'
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getMonthTeamPerformance" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceVO">

        select
        -- 团队层级汇总
        SUM(fycPolicyNum) fycPolicyNum,
        sum(periodPolicyNum) periodPolicyNum,
        SUM(fycPremium) fycPremium,
        SUM(periodPremium) periodPremium,
        SUM(fdcp) dcp,
        SUM(periodDCP) periodDCP
        from (

        -- 人员层级汇总
        select
        SUM(num) fycPolicyNum,
        SUM(periodNum) periodPolicyNum,
        SUM(premium) fycPremium,
        SUM(periodPremium) periodPremium,
        SUM(fdcp) fdcp,
        SUM(dcp) periodDCP

        -- 保单层级汇总
        from
        (
        select
        1 num,
        (case when payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD') then 1 else 0 end)  periodNum,
        SUM(IFNULL(premium,0)) premium,
        SUM(if(payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD'),IFNULL(premium,0),0)) periodPremium,
        SUM(case when settle_flag = 5 then 0 else if(source = 'UW' and payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) fdcp,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        i.agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and i.performance_month = #{performanceMonth}
        and i.premium_year = '1'
--         and i.settle_flag != 5
        and i.source != 'YT'
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code) t
    </select>

    <select id="getYearTeamPerformance" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceVO">

        select
        -- 团队层级汇总
        SUM(fycPolicyNum) fycPolicyNum,
        sum(periodPolicyNum) periodPolicyNum,
        SUM(fycPremium) fycPremium,
        SUM(periodPremium) periodPremium,
        SUM(fdcp) dcp,
        SUM(periodDCP) periodDCP
        from (

        -- 人员层级汇总
        select
        SUM(num) fycPolicyNum,
        SUM(periodNum) periodPolicyNum,
        SUM(premium) fycPremium,
        SUM(periodPremium) periodPremium,
        SUM(fdcp) fdcp,
        SUM(dcp) periodDCP

        -- 保单层级汇总
        from
        (
        select
        1 num,
        (case when payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD') then 1 else 0 end)  periodNum,
        SUM(IFNULL(premium,0)) premium,
        SUM(if(payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and source in ('UW','QQD'),IFNULL(premium,0),0)) periodPremium,
        SUM(case when settle_flag = 5 then 0 else if(source = 'UW' and payment_way not In ('趸缴','一次性交清') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) fdcp,
        MAX(CASE WHEN source = 'UW' THEN appntno ELSE NULL END) appntno,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        i.agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and YEAR(i.performance_date)= #{performanceYear}
        and i.premium_year = '1'
--         and i.settle_flag != 5
        and i.source != 'YT'
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code) t
    </select>



    <select id="getTeamMonthTrendPerformanceList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceTrendVO">
        select
        performance_date trendTimeString,
        SUM(IFNULL(premium,0)) FYPAmount,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) DCPAmount,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) FYCAmount
        from commission_settle_policy
        where
        1=1
<!--        and agent_code in-->
<!--        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">-->
<!--            #{item}-->
<!--        </foreach>-->
        and team_code = #{saleTeamCode}
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and performance_date BETWEEN #{startDateString} and #{endDateString}
        and premium_year = '1'
        group by performance_date
    </select>


    <select id="getTeamHalfYearTrendPerformanceList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceTrendVO">
        select
        performance_month trendTimeString,
        SUM(IFNULL(premium,0)) FYPAmount,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) DCPAmount,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) FYCAmount
        from commission_settle_policy
        where
        1=1
        <!--        and agent_code in-->
        <!--        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">-->
        <!--            #{item}-->
        <!--        </foreach>-->
        and team_code = #{saleTeamCode}
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and performance_month BETWEEN #{startDateString} and #{endDateString}
        and premium_year = '1'
        group by performance_month
    </select>

    <select id="getTeamActiveTrendInfoList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformanceTrendVO">
        select
        i.commission_month trendTimeString,
        IFNULL(sum(if(c.amount>0,1,0)),0) activePersonNum
        from commission_person_info i
        inner join basic_salary_detail c on i.id = c.person_id and c.salary_item = 'CMFYC'
        where
        i.team_code = #{saleTeamCode}
        and  i.commission_month in
        <foreach collection="commissionMonthList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by i.commission_month
    </select>



    <select id="getPerformancemonthListByCodeList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select
        SUM(num) currentMonthPolicyNum,
        GROUP_CONCAT(ids) remark,
        SUM(premium) currentMonthFYP,
        SUM(fyc) currentMonthFYC,
        SUM(dcp) currentMonthDCP,
        agent_code agentCode,
        agent_name agentName

        from
        (
        select
        1 num,
        GROUP_CONCAT(id) ids,
        SUM(IFNULL(premium,0)) premium,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        performance_month = #{performanceMonth}
        and premium_year = '1'
        and agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by agent_code,policy_no) a

        group by a.agent_code
    </select>


    <select id="getPersonalRangePerformanceList" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        select
        SUM(num) policyNum,
--         GROUP_CONCAT(ids) ids,
        SUM(premium) FYP,
        SUM(fyc) FYC,
        SUM(dcp) DCP,
        agent_code agentCode,
        agent_name agentName

        from
        (
        select
        1 num,
--         GROUP_CONCAT(id) ids,
        SUM(IFNULL(premium,0)) premium,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        premium_year = '1'
--         and settle_flag != 5
        and agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and performance_date between #{startDate} and #{endDate}
        group by agent_code,policy_no)  a

        group by a.agent_code
    </select>


    <select id="getProductRangePerformanceList" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        select
        SUM(num) policyNum,
--         GROUP_CONCAT(ids) ids,
        SUM(premium) FYP,
        SUM(fyc) FYC,
        SUM(dcp) DCP,
        risk_code riskCode,
        risk_name riskName,
        risk_code agentCode,
        risk_name agentName

        from
        (
        select
        COUNT(DISTINCT pol_no)  num,
--         GROUP_CONCAT(id) ids,
        SUM(IFNULL(premium,0)) premium,
        SUM(case when settle_flag = 5 then 0 else IFNULL(fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end ) dcp,
        risk_code,
        risk_name,
        agent_code,
        agent_name
        from commission_settle_policy i
        where
        premium_year = '1'
--         and settle_flag != 5
        and (i.group_no not like 'GP%' or i.group_no is null)
        and agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and performance_date between #{startDate} and #{endDate}
        group by risk_code)  a

        group by a.risk_code
    </select>

    <select id="getRankSeqRangePerformanceList" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        select
        SUM(num) policyNum,
--         GROUP_CONCAT(ids) ids,
        SUM(premium) FYP,
        SUM(fyc) FYC,
        SUM(dcp) DCP,
        RANKSEQUCODE agentCode,
        RANKSEQUNAME agentName,
        RANKSEQUCODE rankSeqCode,
        RANKSEQUNAME rankSeqName

        from
        (
        select
        1 num,
--         GROUP_CONCAT(p.id) ids,
        SUM(IFNULL(p.premium,0)) premium,
        SUM(case when p.settle_flag = 5 then 0 else IFNULL(p.fyc,0) end) fyc,
        SUM(case when settle_flag = 5 then 0 else if(p.source in ('UW','QQD') and p.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        s.RANKSEQUCODE,
        s.RANKSEQUNAME,
        p.agent_code,
        p.agent_name
        from commission_settle_policy p
        inner join tbemp e on p.agent_code = e.empcode
        inner join tbranksequ s on e.companycode = s.CHANLECODE
        and e.serialcode = s.RANKSEQUCODE and s.SYSTEMSTATUS = 1
        where
        p.premium_year = '1'
--         and p.settle_flag != 5
        and p.agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and p.pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and p.performance_date between #{startDate} and #{endDate}
        group by e.serialcode,p.policy_no)  a

        group by a.RANKSEQUCODE
    </select>


    <select id="getAgentRankSeqInfoList" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        SELECT
        s.RANKSEQUCODE rankSeqCode,
        s.RANKSEQUNAME rankSeqName,
        e.empcode agentCode,
        e.empName agentName
        FROM tbemp e
        inner join tbranksequ s on e.companycode = s.CHANLECODE
        and e.serialcode = s.RANKSEQUCODE and s.SYSTEMSTATUS = 1
        where
        e.empcode in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by e.empcode
    </select>


    <select id="getPerformancePolicyListById" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        id,
        risk_code riskCode,
        risk_name riskName,
        premium premium,
        insurance_amount insuranceAmount,
        agent_code agentCode,
        agent_name agentName,
        trade_type tradeType,
        performance_date performanceDate,
        policy_no policyNo,
        payment_years paymentYears,
        appntno appntNo,
        appntname appntName
        from commission_settle_policy
        where id in
        <foreach collection="policyIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="getPolicyCommissionList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PolicyCommissionVO">
        SELECT
        commission_item commissionItem,
        case commission_item when 'FYC' then '首期佣金' when 'RYC' then '续期佣金' else '' end commissionType,
        amount,
        case settle_flag when 2 then '已结算' else '未结算' end settleFlag,
        settle_batch_pass_date settleDate,
        risk_code riskCode,
        risk_name riskName
        from commission_settle_policy
        where policy_no = #{policyNo}
        and settle_flag != '5'
        and commission_item is NOT NULL
    </select>


    <select id="getTeamCrInfoByAgentCodeList" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        SELECT
            empcode agentCode,
            continuation_rate13 cr13,
            remark ids
        FROM
            zh_settle.commission_continuation_rate r
        where
        calculate_period = concat(#{performanceMonth},'SUM')
        and crc_period = 'BY_MONTH_SUM'
        and EXISTS (
        select * from zh_exms.commission_person_info p
        where
        p.commission_month = #{performanceMonth}
        and p.team_code = #{saleTeamCode}
        and p.agent_code = r.empcode
        )
    </select>


    <select id="getTeamCr13" parameterType="map" resultType="com.hqins.agent.org.model.vo.TeamPerformanceVO">
        SELECT
        empcode agentCode,
        continuation_rate13 cr13,
        remark ids
        FROM
        zh_settle.commission_continuation_rate r
        where
        calculate_period = concat(#{performanceMonth},'SUM')
        and crc_period = 'BY_MONTH_SUM'
        and r.empcode in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>



    <select id="getTeamMonthPersonQuitList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PersonalVO">
        select e.empcode  agentCode
        from tbemp e
        inner join tbempflow tb1 on e.empcode = tb1.empcode
        inner join tbsaleteam t ON  tb1.saleteamincode = t.saleteamincode
        INNER JOIN (
            SELECT empcode, MAX(APPLYTIME) AS max_chgdate
            FROM tbempflow
            WHERE chgtype = '30'
              and APPLYSTATE = '1'
              AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')>= DATE_FORMAT(chgdate, '%Y-%m-%d')
            GROUP BY empcode
        ) tb2 ON tb1.empcode = tb2.empcode
        AND tb1.APPLYTIME = tb2.max_chgdate and tb1.chgtype = '30'
        where t.saleteamcode = #{saleTeamCode}

        union

        select e.empcode agentCode
        from tbemp e
        inner join tbempflow f on e.empcode = f.empcode and f.chgtype = '00'
        inner join tbsaleteam t ON f.saleteamincode = t.saleteamincode
        where t.saleteamcode = #{saleTeamCode}
        and not EXISTS
        (select * from tbempflow tb1 WHERE e.empcode = tb1.empcode and chgtype = '30' and APPLYSTATE = '1' AND DATE_FORMAT(#{endDate}, '%Y-%m-%d')>= DATE_FORMAT(chgdate, '%Y-%m-%d'))

        union

        select e.empcode agentCode
        from tbemp e
        inner join tbsaleteam t ON e.saleteamincode = t.saleteamincode
        where t.saleteamcode = #{saleTeamCode}
        and not EXISTS
        ( select * from tbempflow tb1 WHERE tb1.chgtype in ('00','30') and e.empcode = tb1.empcode)


    </select>


    <select id="getTeamPerformancePolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        id,
        risk_code riskCode,
        risk_name riskName,
        premium premium,
        insurance_amount insuranceAmount,
        agent_code agentCode,
        agent_name agentName,
        trade_type tradeType,
        performance_date performanceDate,
        policy_no policyNo,
        risk_period riskPeriod,
        risk_period_name riskPeriodName,
        (case source when 'ZP' then 'M' else sub_risk_flag end)  subRiskFlag,
--         amount,
        (case when settle_flag = 5 then 0 else amount end) amount,
        (case when settle_flag = 5 then 0 else if(source in ('UW','QQD') and i.risk_period = 'L' and self_insure_flag = 0,IFNULL(dcp,0),0) end) dcp,
        payment_years paymentYears,
        appntno appntNo,
        appntname appntName
        from commission_settle_policy i
        where
        performance_month = #{performanceMonth}
        and premium_year = '1'
--         and settle_flag != 5
        and agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getTeamPersonalPerformancePolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        id,
        risk_code riskCode,
        risk_name riskName,
        premium premium,
        insurance_amount insuranceAmount,
        agent_code agentCode,
        agent_name agentName,
        trade_type tradeType,
        performance_date performanceDate,
        policy_no policyNo,
        risk_period riskPeriod,
        risk_period_name riskPeriodName,
        (case source when 'ZP' then 'M' else sub_risk_flag end)  subRiskFlag,
        payment_years paymentYears,
        appntno appntNo,
        appntname appntName
        from commission_settle_policy i
        where
        premium_year = '1'
--         and settle_flag != 5
        and agent_code = #{agentCode}
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and performance_date between #{startDate} and #{endDate}

    </select>



    <select id="getTeamProductPerformancePolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        id,
        risk_code riskCode,
        risk_name riskName,
        premium premium,
        insurance_amount insuranceAmount,
        agent_code agentCode,
        agent_name agentName,
        trade_type tradeType,
        performance_date performanceDate,
        policy_no policyNo,
        risk_period riskPeriod,
        risk_period_name riskPeriodName,
        (case source when 'ZP' then 'M' else sub_risk_flag end)  subRiskFlag,
        payment_years paymentYears,
        appntno appntNo,
        appntname appntName
        from commission_settle_policy i
        where
        premium_year = '1'
--         and settle_flag != 5
        and risk_code = #{riskCode}
        and (i.group_no not like 'GP%' or i.group_no is null)
        and agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and performance_date between #{startDate} and #{endDate}

    </select>


    <select id="getTeamRankSeqPerformancePolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.PerformancePolicyVO">
        select
        i.id,
        i.risk_code riskCode,
        i.risk_name riskName,
        i.premium premium,
        i.insurance_amount insuranceAmount,
        i.agent_code agentCode,
        i.agent_name agentName,
        i.trade_type tradeType,
        i.performance_date performanceDate,
        i.policy_no policyNo,
        i.risk_period riskPeriod,
        i.risk_period_name riskPeriodName,
        (case i.source when 'ZP' then 'M' else i.sub_risk_flag end)  subRiskFlag,
        i.payment_years paymentYears,
        i.appntno appntNo,
        i.appntname appntName
        from commission_settle_policy i
        inner join tbemp e on i.agent_code = e.empcode
        inner join tbranksequ s on e.companycode = s.CHANLECODE
        and e.serialcode = s.RANKSEQUCODE and s.SYSTEMSTATUS = 1 and e.serialcode = #{rankSeqCode}
        where
        i.premium_year = '1'
--         and i.settle_flag != 5
        and i.agent_code in
        <foreach collection="agentCodeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="wtPolNoList != null and wtPolNoList.size() > 0">
            and i.pol_no not in
            <foreach collection="wtPolNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="paymentYears != null">
            <choose>
                <when test="paymentYears == '趸缴'.toString()">
                    and payment_years = '趸缴' and risk_period = 'L'
                </when>
                <when test="paymentYears == '1年及以下'.toString()">
                    and (payment_years regexp '[月]' or payment_years = '1年')
                </when>
                <when test="paymentYears == '2-3年'.toString()">
                    and payment_years in ('2年','3年')
                </when>
                <when test="paymentYears == '4-5年'.toString()">
                    and payment_years in ('4年','5年')
                </when>
                <when test="paymentYears == '6-9年'.toString()">
                    and payment_years in ('6年','7年','8年','9年')
                </when>
                <when test="paymentYears == '10年及以上'.toString()">
                    and payment_years regexp '[年]' and  REGEXP_SUBSTR(payment_years, '[0-9]+') >=10
                </when>
            </choose>
        </if>
        and performance_date between #{startDate} and #{endDate}

    </select>


    <select id="getOrgListVersionTypeList" parameterType="map" resultType="String">
        select e.version_type from basic_law_exec e
        inner join basic_law_exec_teams_config t on e.id = t.exec_id and apply_status = 'ENABLE' and check_status =1
        where
        e.start_date &lt;= now()
        and e.end_date >= now()
        and (e.invalid_date is null or e.invalid_date >= now())
        and t.inst_code in
        <foreach collection="orgList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by e.version_type
    </select>


    <select id="getVersionTypeListByEmpTeam" parameterType="map" resultType="String">

        -- 机构特殊处理
        <if test="list != null and list.size() > 0">

            select 'SELF_LOTUS' version_type from tbemp e
            where 1=1
            and e.empcode = #{empCode}
            and e.inst_code in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>

            union

        </if>


        -- 按团队查询
        select distinct e.version_type
        from basic_law_exec e
        inner join basic_law_exec_teams_config t
        on e.id = t.exec_id and e.apply_status = 'ENABLE' and check_status =1
        where
        e.start_date &lt;=  now()
        and e.end_date >= now()
        and (e.invalid_date is null or e.invalid_date >= now())
        and exists (
        select * from
                      (
        select t.* from tbemp m
        inner join tbsaleteam t on m.saleteamincode = t.saleteamincode
        where m.empcode = #{empCode}
        union
        select t2.* from tbemp e
        inner join tbsaleteam t1 on e.saleteamincode = t1.saleteamincode
        inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
        where e.empcode = #{empCode}
        union
        select t3.* from tbemp e
        inner join tbsaleteam t1 on e.saleteamincode = t1.saleteamincode
        inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
        inner join tbsaleteam t3 on t2.supersaleteamcode = t3.saleteamcode
        where e.empcode = #{empCode}
                          ) s where s.saleteamcode = t.team_code
            )


        union

        -- 按机构查询
        select distinct e.version_type
        from basic_law_exec e
        inner join basic_law_exec_teams_config t
        on e.id = t.exec_id and e.apply_status = 'ENABLE' and e.check_status = 1 and t.team_code is null
        where
        e.start_date &lt;=  now()
        and e.end_date >= now()
        and (e.invalid_date is null or e.invalid_date >= now())
        and exists (
        select 1 from tbemp m
        where m.empcode = #{empCode}
        and m.inst_code = t.inst_code
        and t.team_code is null
        )

        limit 1; -- 先按团队查询，没有则按机构查询
    </select>



    <select id="getAgentSaleTeamCode" parameterType="map" resultType="String">

        select t.saleteamcode from

        (
        (
        select f.saleteamincode from tbemp e
        inner join tbempflow f on e.empcode = f.empcode and f.chgtype = '30' and f.APPLYSTATE = 1 and  DATE_FORMAT(f.chgdate , '%Y-%m-%d') &lt;= #{commissionMonth}
        where e.empcode = #{agentCode}
        order by f.APPLYTIME desc limit 1
        )

        union

        select n.saleteamincode from tbemp e
        inner join tbempflow n on e.empcode = n.empcode and n.chgtype = ('00') and n.APPLYSTATE = 1
        where e.empcode = #{agentCode}

        union

        (select e.saleteamincode from tbemp e
        where e.empcode = #{agentCode})

        limit 1
        ) s
        inner join tbsaleteam t on s.saleteamincode = t.saleteamincode
    </select>


    <select id="getTeamVersionType" parameterType="map" resultType="String">

        -- 生效团队
        select e.version_type from  basic_law_exec_teams_config f
        inner join basic_law_exec e on f.exec_id = e.id and e.apply_status = 'ENABLE' and check_status =1
        AND #{commissionMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.end_date, '%Y-%m')
        where f.team_code in
        (
            select #{teamCode}
            union ALL
            select t2.saleteamcode  from tbsaleteam t1
            inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
            where t1.saleteamcode = #{teamCode}
            union ALL
            select t3.saleteamcode  from tbsaleteam t1
            inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
            inner join tbsaleteam t3 on t2.supersaleteamcode = t3.saleteamcode
            where t1.saleteamcode = #{teamCode}
        )

        union

        -- 生效机构
        select e.version_type from tbsaleteam t
        inner join basic_law_exec_teams_config f on t.inst_code =  f.inst_code AND f.team_code is null
        inner join basic_law_exec e on f.exec_id = e.id and e.apply_status = 'ENABLE' and check_status =1
        AND #{commissionMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.end_date, '%Y-%m')
        where t.saleteamcode = #{teamCode}

        union

        -- 失效团队
        (select e.version_type from basic_law_exec_teams_config t
        inner join basic_law_exec e on t.exec_id = e.id
        and e.apply_status = 'DISABLE' and e.check_status = '1'
        AND #{commissionMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.invalid_date, '%Y-%m')
        where  t.team_code in
        (
               select #{teamCode}
               union ALL
               select t2.saleteamcode  from tbsaleteam t1
               inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
               where t1.saleteamcode = #{teamCode}
               union ALL
               select t3.saleteamcode  from tbsaleteam t1
               inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
               inner join tbsaleteam t3 on t2.supersaleteamcode = t3.saleteamcode
               where t1.saleteamcode = #{teamCode}
        )
        ORDER BY e.create_time DESC limit 1)

        union

        -- 失效机构
        (select e.version_type from tbsaleteam t
        inner join basic_law_exec_teams_config f on t.inst_code =  f.inst_code AND f.team_code is null
        inner join basic_law_exec e on f.exec_id = e.id
        and e.apply_status = 'DISABLE' and check_status =1
        AND #{commissionMonth} BETWEEN DATE_FORMAT(e.start_date, '%Y-%m') AND DATE_FORMAT(e.invalid_date, '%Y-%m')
        where t.saleteamcode = #{teamCode}
        ORDER BY e.create_time DESC
        )

        limit 1
    </select>


    <select id="getAgentIncreaseNum" parameterType="map" resultType="Integer">
        select count(*) from tbsalerel r
        where 1=1
        and bempcode = #{empCode}
        and REL_TYPE = '01'
        <choose>
            <when test="month != null and month != ''">
                and DATE_FORMAT(EFFECTIVE_TIME, '%Y-%m') = #{month}
            </when>
            <otherwise>
                and DATE_FORMAT(EFFECTIVE_TIME, '%Y') = #{year}
            </otherwise>
        </choose>
    </select>


    <select id="getTeamIncreaseNum" parameterType="map" resultType="Integer">
        select count(*) from tbsalerel r
        where 1=1
        and bsaleteamcode = #{teamCode}
        and REL_TYPE = '01'
        <choose>
            <when test="month != null and month != ''">
                and DATE_FORMAT(EFFECTIVE_TIME, '%Y-%m') = #{month}
            </when>
            <otherwise>
                and DATE_FORMAT(EFFECTIVE_TIME, '%Y') = #{year}
            </otherwise>
        </choose>
    </select>


    <select id="getSelfPolicyList" parameterType="map" resultType="com.hqins.agent.org.model.vo.SimpleNodeVO">
        select
        contno code,
        type1 type
        from zh_settle.da_fk_con

    </select>

    <select id="getSupervisorCrList" parameterType="map" resultType="com.hqins.agent.org.model.vo.MarkDetailVO">
        select
        r.INST_CODE instCode,
        r.INST_NAME instName,
        r.empcode agentCode,
        r.empname agentName,
        r.continuation_rate13 cr13,
        r.remark
        from zh_settle.COMMISSION_CONTINUATION_RATE r
        inner join zh_exms.tbemp e on r.empcode = e.empcode  and e.isinsideflag = 1 and e.empstatus = '01'
        where r.crc_period = 'BY_MONTH_SUM' and r.calculate_period  = #{commissionMonth} and r.continuation_rate13 &lt; 0.88
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND r.inst_code in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND r.saleteamcode = #{teamCode}
        </if>
    </select>


    <select id="getSupervisorIncreaseVOList" parameterType="map" resultType="com.hqins.agent.org.model.vo.SupervisorPerformanceDetailNumberVO">
        select
        r.INST_CODE instCode,
        r.INST_NAME instName,
        bsaleteamcode teamCode,
        bsaleteamname teamName,
        bempcode agentCode,
        bempinname agentName,
        count(*) increaseNum,
        0 fycPremium
        from tbsalerel r
        inner join tbemp e on r.aempcode = e.empcode and e.empstatus = '01'
        where 1=1
        <if test="performanceGroupList != null and performanceGroupList.size()>0">
            AND r.INST_CODE in
            <foreach collection="performanceGroupList" index="index" item="performanceGroup" open="(" separator=","
                     close=")">
                #{performanceGroup}
            </foreach>
        </if>
        <if test="teamCode != null">
            AND bsaleteamcode = #{teamCode}
        </if>
        and REL_TYPE = '01'
        and EFFECTIVE_TIME between #{startDate} and #{endDate}
        <choose>
            <when test=" groupType == 'team'.toString()">
                group by bsaleteamcode
            </when>
            <when test=" groupType == 'agent'.toString()">
                group by bempcode
            </when>
            <otherwise>
                group by r.INST_CODE
            </otherwise>
        </choose>
    </select>


</mapper>
