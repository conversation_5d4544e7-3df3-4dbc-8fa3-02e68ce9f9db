<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.ChannelEmployeeCheckResultMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckResult">
            <result property="id" column="id"/>
            <result property="chargeSign" column="charge_sign"/>
            <result property="informationDate" column="information_date"/>
            <result property="ifExist" column="if_exist"/>
            <result property="name" column="name"/>
            <result property="sex" column="sex"/>
            <result property="cardType" column="card_type"/>
            <result property="cardNo" column="card_no"/>
            <result property="createUser" column="create_user"/>
            <result property="createUserName" column="create_user_name"/>
            <result property="createTime" column="create_time"/>
            <result property="modifyUser" column="modify_user"/>
            <result property="modifyUserName" column="modify_user_name"/>
            <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,charge_sign,information_date,if_exist,name,sex,car_type,card_no,create_user,create_user_name,create_time,modify_user,modify_user_name,modify_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckResult">
        insert into channel_employee_check_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="chargeSign != null">
                charge_sign,
            </if>
            <if test="informationDate != null">
                information_date,
            </if>
            <if test="ifExist != null">
                if_exist,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="cardNo != null">
                card_no,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyUserName != null">
                modify_user_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="chargeSign != null">
                #{chargeSign,jdbcType=VARCHAR},
            </if>
            <if test="informationDate != null">
                #{informationDate,jdbcType=DATE},
            </if>
            <if test="ifExist != null">
                #{ifExist,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeCheckResult">
        update channel_employee_check_result
        <set>
            <if test="chargeSign != null">
                charge_sign = #{chargeSign,jdbcType=VARCHAR},
            </if>
            <if test="informationDate != null">
                information_date = #{informationDate,jdbcType=DATE},
            </if>
            <if test="ifExist != null">
                if_exist = #{ifExist,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>


</mapper>
