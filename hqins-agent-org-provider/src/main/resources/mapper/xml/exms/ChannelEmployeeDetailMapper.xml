<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.ChannelEmployeeDetailMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeDetail">
            <result property="id" column="id"/>
            <result property="checkResultId" column="check_result_id"/>
            <result property="corporationCode" column="corporation_code"/>
            <result property="developCode" column="develop_code"/>
            <result property="developState" column="develop_state"/>
            <result property="registerTime" column="register_time"/>
            <result property="cancelTime" column="cancel_time"/>
            <result property="developAreaName" column="develop_area_name"/>
            <result property="superOrgan" column="super_organ"/>
            <result property="superOrganName" column="super_organ_name"/>
            <result property="organCode" column="organ_code"/>
            <result property="organName" column="organ_name"/>
            <result property="scopeName" column="scope_name"/>
            <result property="internetBusiness" column="internet_business"/>
            <result property="createUser" column="create_user"/>
            <result property="createUserName" column="create_user_name"/>
            <result property="createTime" column="create_time"/>
            <result property="modifyUser" column="modify_user"/>
            <result property="modifyUserName" column="modify_user_name"/>
            <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,check_result_id,corporation_code,develop_code,develop_state,register_time,cancel_time,develop_area_name,super_organ,super_organ_name,organ_code,organ_name,scope_name,internet_business,create_user,create_user_name,create_time,modify_user,modify_user_name,modify_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeDetail">
        insert into channel_employee_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="checkResultId != null">
                check_result_id,
            </if>
            <if test="corporationCode != null">
                corporation_code,
            </if>
            <if test="developCode != null">
                develop_code,
            </if>
            <if test="developState != null">
                develop_state,
            </if>
            <if test="registerTime != null">
                register_time,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="developAreaName != null">
                develop_area_name,
            </if>
            <if test="superOrgan != null">
                super_organ,
            </if>
            <if test="superOrganName != null">
                super_organ_name,
            </if>
            <if test="organCode != null">
                organ_code,
            </if>
            <if test="organName != null">
                organ_name,
            </if>
            <if test="scopeName != null">
                scope_name,
            </if>
            <if test="internetBusiness != null">
                internet_business,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyUserName != null">
                modify_user_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="checkResultId != null">
                #{checkResultId,jdbcType=VARCHAR},
            </if>
            <if test="corporationCode != null">
                #{corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="developCode != null">
                #{developCode,jdbcType=VARCHAR},
            </if>
            <if test="developState != null">
                #{developState,jdbcType=VARCHAR},
            </if>
            <if test="registerTime != null">
                #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="developAreaName != null">
                #{developAreaName,jdbcType=VARCHAR},
            </if>
            <if test="superOrgan != null">
                #{superOrgan,jdbcType=VARCHAR},
            </if>
            <if test="superOrganName != null">
                #{superOrganName,jdbcType=VARCHAR},
            </if>
            <if test="organCode != null">
                #{organCode,jdbcType=VARCHAR},
            </if>
            <if test="organName != null">
                #{organName,jdbcType=VARCHAR},
            </if>
            <if test="scopeName != null">
                #{scopeName,jdbcType=VARCHAR},
            </if>
            <if test="internetBusiness != null">
                #{internetBusiness,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeDetail">
        update channel_employee_detail
        <set>
            <if test="checkResultId != null">
                check_result_id = #{checkResultId,jdbcType=VARCHAR},
            </if>
            <if test="corporationCode != null">
                corporation_code = #{corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="developCode != null">
                develop_code = #{developCode,jdbcType=VARCHAR},
            </if>
            <if test="developState != null">
                develop_state = #{developState,jdbcType=VARCHAR},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="developAreaName != null">
                develop_area_name = #{developAreaName,jdbcType=VARCHAR},
            </if>
            <if test="superOrgan != null">
                super_organ = #{superOrgan,jdbcType=VARCHAR},
            </if>
            <if test="superOrganName != null">
                super_organ_name = #{superOrganName,jdbcType=VARCHAR},
            </if>
            <if test="organCode != null">
                organ_code = #{organCode,jdbcType=VARCHAR},
            </if>
            <if test="organName != null">
                organ_name = #{organName,jdbcType=VARCHAR},
            </if>
            <if test="scopeName != null">
                scope_name = #{scopeName,jdbcType=VARCHAR},
            </if>
            <if test="internetBusiness != null">
                internet_business = #{internetBusiness,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>


</mapper>
