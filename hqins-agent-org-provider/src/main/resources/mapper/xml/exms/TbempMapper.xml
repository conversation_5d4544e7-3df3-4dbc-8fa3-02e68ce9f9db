<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.TbempMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.Tbemp">
        <result property="empincode" column="empincode"/>
        <result property="saleteamincode" column="saleteamincode"/>
        <result property="companycode" column="companycode"/>
        <result property="instCode" column="INST_CODE"/>
        <result property="instName" column="INST_NAME"/>
        <result property="fatherempincode" column="fatherempincode"/>
        <result property="empcode" column="empcode"/>
        <result property="empname" column="empname"/>
        <result property="empshortname" column="empshortname"/>
        <result property="empengname" column="empengname"/>
        <result property="idtype" column="idtype"/>
        <result property="idtypename" column="idtypename"/>
        <result property="idcode" column="idcode"/>
        <result property="sexcode" column="sexcode"/>
        <result property="sexname" column="sexname"/>
        <result property="birthday" column="birthday"/>
        <result property="arecode" column="arecode"/>
        <result property="arename" column="arename"/>
        <result property="citycode" column="citycode"/>
        <result property="cityname" column="cityname"/>
        <result property="provincecode" column="provincecode"/>
        <result property="provincename" column="provincename"/>
        <result property="mainaddress" column="mainaddress"/>
        <result property="mainzipcode" column="mainzipcode"/>
        <result property="maintelephone" column="maintelephone"/>
        <result property="mainemail" column="mainemail"/>
        <result property="highdecu" column="highdecu"/>
        <result property="isinssale" column="isinssale"/>
        <result property="party" column="party"/>
        <result property="nationcode" column="nationcode"/>
        <result property="countrycode" column="countrycode"/>
        <result property="birthplace" column="birthplace"/>
        <result property="polityvisage" column="polityvisage"/>
        <result property="workdate" column="workdate"/>
        <result property="workage" column="workage"/>
        <result property="qq" column="qq"/>
        <result property="wechart" column="wechart"/>
        <result property="highdegreecode" column="highdegreecode"/>
        <result property="forelevel" column="forelevel"/>
        <result property="marriage" column="marriage"/>
        <result property="marriagedate" column="marriagedate"/>
        <result property="rgtaddress" column="rgtaddress"/>
        <result property="archiveadress" column="archiveadress"/>
        <result property="liking" column="liking"/>
        <result property="lastoccu" column="lastoccu"/>
        <result property="lastcom" column="lastcom"/>
        <result property="noworkflag" column="noworkflag"/>
        <result property="officeflag" column="officeflag"/>
        <result property="officecontactjson" column="officecontactjson"/>
        <result property="parttimeflag" column="parttimeflag"/>
        <result property="height" column="height"/>
        <result property="bloodtype" column="bloodtype"/>
        <result property="weigth" column="weigth"/>
        <result property="healthappr" column="healthappr"/>
        <result property="healthimage" column="healthimage"/>
        <result property="posttiteljson" column="posttiteljson"/>
        <result property="educexpejson" column="educexpejson"/>
        <result property="jobexpejson" column="jobexpejson"/>
        <result property="mobilejson" column="mobilejson"/>
        <result property="websitejson" column="websitejson"/>
        <result property="emailjson" column="emailjson"/>
        <result property="faxjson" column="faxjson"/>
        <result property="telephonejson" column="telephonejson"/>
        <result property="addressandzipjson" column="addressandzipjson"/>
        <result property="imagejson" column="imagejson"/>
        <result property="familyjson" column="familyjson"/>
        <result property="cardbank" column="cardbank"/>
        <result property="cardbankbranch" column="cardbankbranch"/>
        <result property="cardno" column="cardno"/>
        <result property="cardvaliddate" column="cardvaliddate"/>
        <result property="cardname" column="cardname"/>
        <result property="cardimageurljson" column="cardimageurljson"/>
        <result property="contracttype" column="contracttype"/>
        <result property="contracttypename" column="contracttypename"/>
        <result property="contractcode" column="contractcode"/>
        <result property="contractstate" column="contractstate"/>
        <result property="grantdate" column="grantdate"/>
        <result property="noncompeteflag" column="noncompeteflag"/>
        <result property="secretflag" column="secretflag"/>
        <result property="forcedate" column="forcedate"/>
        <result property="termdate" column="termdate"/>
        <result property="contractimagejson" column="contractimagejson"/>
        <result property="enterserialcode" column="enterserialcode"/>
        <result property="enterrankcode" column="enterrankcode"/>
        <result property="serialcode" column="serialcode"/>
        <result property="rankcode" column="rankcode"/>
        <result property="entrytime" column="entrytime"/>
        <result property="checktime" column="checktime"/>
        <result property="reguilatime" column="reguilatime"/>
        <result property="ranktime" column="ranktime"/>
        <result property="quittime" column="quittime"/>
        <result property="quitreason" column="quitreason"/>
        <result property="notsaleflag" column="notsaleflag"/>
        <result property="recomtime" column="recomtime"/>
        <result property="invoicebegindate" column="invoicebegindate"/>
        <result property="invoiceenddate" column="invoiceenddate"/>
        <result property="invoiceallflag" column="invoiceallflag"/>
        <result property="effereason" column="effereason"/>
        <result property="expreason" column="expreason"/>
        <result property="qualificationno" column="qualificationno"/>
        <result property="qualificationenddate" column="qualificationenddate"/>
        <result property="empstatus" column="empstatus"/>
        <result property="empstatusname" column="empstatusname"/>
        <result property="type" column="type"/>
        <result property="offlinesalecode" column="offlinesalecode"/>
        <result property="isworflow" column="isworflow"/>
        <result property="systemstatus" column="systemstatus"/>
        <result property="isinsideflag" column="isinsideflag"/>
        <result property="trainperiods" column="trainperiods"/>
        <result property="traindate" column="traindate"/>
        <result property="headship" column="headship"/>
        <result property="isvirtualemp" column="isvirtualemp"/>
        <result property="major" column="major"/>
        <result property="graduateschool" column="graduateschool"/>
        <result property="openid" column="openid"/>
        <result property="unionid" column="unionid"/>
        <result property="createtime" column="createtime"/>
        <result property="createuser" column="createuser"/>
        <result property="modifyuser" column="modifyuser"/>
        <result property="modifytime" column="modifytime"/>
        <result property="remark" column="REMARK"/>
        <result property="source" column="SOURCE"/>
        <result property="modifyusername" column="MODIFYUSERNAME"/>
        <result property="createusername" column="CREATEUSERNAME"/>
        <result property="partnercode" column="PARTNERCODE"/>
        <result property="instClass" column="INST_CLASS"/>
        <result property="cptype" column="CPTYPE"/>
        <result property="canuniversal" column="CANUNIVERSAL"/>
        <result property="salesvcflag" column="salesvcflag"/>
        <result property="issecond" column="ISSECOND"/>
        <result property="managerorgcode" column="MANAGERORGCODE"/>
        <result property="branchtype" column="BRANCHTYPE"/>
        <result property="empType" column="EMP_TYPE"/>
        <result property="empSallType" column="EMP_SALL_TYPE"/>
        <result property="ruralSallersFlag" column="RURAL_SALLERS_FLAG"/>
        <!--        <result property="isReport" column="IS_REPORT"/>-->
        <result property="empEntryType" column="EMP_ENTRY_TYPE"/>
        <result property="teamtime" column="teamtime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `empincode`,`saleteamincode`,`companycode`,`INST_CODE`,`INST_NAME`,`fatherempincode`,`empcode`,`empname`,`empshortname`,`empengname`,`idtype`,`idtypename`,`idcode`,`sexcode`,`sexname`,`birthday`,`arecode`,`arename`,`citycode`,`cityname`,`provincecode`,`provincename`,`mainaddress`,`mainzipcode`,`maintelephone`,`mainemail`,`highdecu`,`isinssale`,`party`,`nationcode`,`countrycode`,`birthplace`,`polityvisage`,`workdate`,`workage`,`qq`,`wechart`,`highdegreecode`,`forelevel`,`marriage`,`marriagedate`,`rgtaddress`,`archiveadress`,`liking`,`lastoccu`,`lastcom`,`noworkflag`,`officeflag`,`officecontactjson`,`parttimeflag`,`height`,`bloodtype`,`weigth`,`healthappr`,`healthimage`,`posttiteljson`,`educexpejson`,`jobexpejson`,`mobilejson`,`websitejson`,`emailjson`,`faxjson`,`telephonejson`,`addressandzipjson`,`imagejson`,`familyjson`,`cardbank`,`cardbankbranch`,`cardno`,`cardvaliddate`,`cardname`,`cardimageurljson`,`contracttype`,`contracttypename`,`contractcode`,`contractstate`,`grantdate`,`noncompeteflag`,`secretflag`,`forcedate`,`termdate`,`contractimagejson`,`enterserialcode`,`enterrankcode`,`serialcode`,`rankcode`,`entrytime`,`checktime`,`reguilatime`,`ranktime`,`quittime`,`quitreason`,`notsaleflag`,`recomtime`,`invoicebegindate`,`invoiceenddate`,`invoiceallflag`,`effereason`,`expreason`,`qualificationno`,`qualificationenddate`,`empstatus`,`empstatusname`,`type`,`offlinesalecode`,`isworflow`,`systemstatus`,`isinsideflag`,`trainperiods`,`traindate`,`headship`,`isvirtualemp`,`major`,`graduateschool`,`openid`,`unionid`,`createtime`,`createuser`,`modifyuser`,`modifytime`,`REMARK`,`SOURCE`,`MODIFYUSERNAME`,`CREATEUSERNAME`,`PARTNERCODE`,`INST_CLASS`,`CPTYPE`,`CANUNIVERSAL`,`salesvcflag`,`ISSECOND`,`MANAGERORGCODE`,`BRANCHTYPE`,`EMP_TYPE`,`EMP_SALL_TYPE`,`RURAL_SALLERS_FLAG`,`EMP_ENTRY_TYPE`,`teamtime`
    </sql>


    <select id="listPage" resultType="map">
        select
        t.empcode as code,t.empname as name,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        s.empname as referencesName,t.entrytime as entryTime,t.quittime as quitTime,
        e.companycode as topCode,
        t.idtype as idType,t.idcode as idCode,
        t.canuniversal as universalQualification,t.maintelephone as mobile,t.sexcode as gender,
        f.CERCODE as licenseNo,t.qualificationenddate as licenseEndDate,
        t.empstatus as status
        from tbemp t
        left join tbcertificate f ON f.empincode = t.empincode
        left join tbemp s on t.fatherempincode = s.empincode
        left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.CPTYPE = 'PARTY' and f.CERTYPE = '02'
        <if test="partnerOrgCodes != null and partnerOrgCodes.size()>0">
            and t.INST_CODE in
            <foreach collection="partnerOrgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="topCodes != null and topCodes.size()>0">
            and t.companycode in
            <foreach collection="topCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="saleteamincodes != null and saleteamincodes.size()>0">
            and t.saleteamincode in
            <foreach collection="saleteamincodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="queryRequest.code != null and queryRequest.code != ''">
            and t.empcode = #{queryRequest.code}
        </if>
        <if test="queryRequest.mobile != null and queryRequest.mobile != ''">
            and t.maintelephone = #{queryRequest.mobile}
        </if>
        <if test="queryRequest.name != null and queryRequest.name != ''">
            and t.empname like CONCAT('%',#{queryRequest.name}, '%')
        </if>
        <if test="queryRequest.orgName != null and queryRequest.orgName != ''">
            and t.INST_NAME like CONCAT('%',#{queryRequest.orgName}, '%')
        </if>
        <if test="queryRequest.idCode != null and queryRequest.idCode != ''">
            and  t.idcode=  #{queryRequest.idCode}
        </if>
        <if test="queryRequest.entryTimeStart != null">
            and t.entrytime <![CDATA[ >= ]]> #{queryRequest.entryTimeStart}
        </if>
        <if test="queryRequest.entryTimeEnd != null">
            and t.entrytime <![CDATA[ <= ]]> #{queryRequest.entryTimeEnd}
        </if>
        <if test="queryRequest.quitTimeStart != null">
            and t.quittime <![CDATA[ >= ]]> #{queryRequest.quitTimeStart}
        </if>
        <if test="queryRequest.quitTimeEnd != null">
            and t.quittime <![CDATA[ <= ]]> #{queryRequest.quitTimeEnd}
        </if>
        <if test = "queryRequest.status != null and queryRequest.status= 'SERVING'">
            and t.empstatus = '01'
        </if>
        <if test="queryRequest.status !=null and queryRequest.status == 'INVALID'">
            and empstatus != '01'
        </if>
        order by (case when t.empstatus='01' then 1 ELSE 2 END) asc,t.empcode asc
    </select>

    <select id="listNoPage" resultType="map">
        select
        t.empcode as code,t.empname as name,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        s.empname as referencesName,t.entrytime as entryTime,t.quittime as quitTime,
        e.companycode as topCode,
        t.idtype as idType,t.idcode as idCode,
        t.canuniversal as universalQualification,t.maintelephone as mobile,t.sexcode as gender,
        f.CERCODE as licenseNo,f.CERSTART as licenseStartDate,f.CEREND as licenseEndDate,
        t.empstatus as status
        from tbemp t
        left join tbcertificate f ON f.empincode = t.empincode
        left join tbemp s on t.fatherempincode = s.empincode
        left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.CPTYPE = 'PARTY' and f.CERTYPE = '02'
        <if test="partnerOrgCodes != null and partnerOrgCodes.size()>0">
            and t.INST_CODE in
            <foreach collection="partnerOrgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="topCodes != null and topCodes.size()>0">
            and t.companycode in
            <foreach collection="topCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="saleteamincodes != null and saleteamincodes.size()>0">
            and t.saleteamincode in
            <foreach collection="saleteamincodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="queryRequest.code != null and queryRequest.code != ''">
            and t.empcode = #{queryRequest.code}
        </if>
        <if test="queryRequest.mobile != null and queryRequest.mobile != ''">
            and t.maintelephone = #{queryRequest.mobile}
        </if>
        <if test="queryRequest.name != null and queryRequest.name != ''">
            and t.empname like CONCAT('%',#{queryRequest.name}, '%')
        </if>
        <if test="queryRequest.orgName != null and queryRequest.orgName != ''">
            and t.INST_NAME like CONCAT('%',#{queryRequest.orgName}, '%')
        </if>
        <if test="queryRequest.idCode != null and queryRequest.idCode != ''">
            and  t.idcode=  #{queryRequest.idCode}
        </if>
        <if test="queryRequest.entryTimeStart != null">
            and t.entrytime <![CDATA[ >= ]]> #{queryRequest.entryTimeStart}
        </if>
        <if test="queryRequest.entryTimeEnd != null">
            and t.entrytime <![CDATA[ <= ]]> #{queryRequest.entryTimeEnd}
        </if>
        <if test="queryRequest.quitTimeStart != null">
            and t.quittime <![CDATA[ >= ]]> #{queryRequest.quitTimeStart}
        </if>
        <if test="queryRequest.quitTimeEnd != null">
            and t.quittime <![CDATA[ <= ]]> #{queryRequest.quitTimeEnd}
        </if>
    </select>


    <select id="listSimple" resultType="map">
        select
        t.empcode as code,t.empname as name,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        e.companycode as topCode,
        t.empstatus as status
        from tbemp t ,tbsaleteam e
        where t.saleteamincode = e.saleteamincode and t.CPTYPE = 'PARTY'
        <if test="partnerOrgCodes != null and partnerOrgCodes.size()>0">
            and t.INST_CODE in
            <foreach collection="partnerOrgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="topCodes != null and topCodes.size()>0">
            and t.companycode in
            <foreach collection="topCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="saleteamincodes != null and saleteamincodes.size()>0">
            and t.saleteamincode in
            <foreach collection="saleteamincodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="queryRequest.value != null and queryRequest.value != ''">
            and (t.empname like CONCAT('%',#{queryRequest.value}, '%') or t.empcode like
            CONCAT('%',#{queryRequest.value}, '%') )
        </if>
        <if test="queryRequest.status = 'SERVING'">
            and t.empstatus = '01'
        </if>
        order by t.empcode ASC
        <choose>
            <when test="null != queryRequest.start">
                limit #{queryRequest.start},#{queryRequest.size}
            </when>
            <otherwise>
                limit 0,#{queryRequest.size}
            </otherwise>
        </choose>
    </select>

    <select id="listEmployeeSimple" resultType="map">
        select
        t.empcode as code,t.empname as name,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        e.companycode as topCode,
        t.empstatus as status
        from tbemp t ,tbsaleteam e
        where t.saleteamincode = e.saleteamincode and t.CPTYPE = 'PARTY'
        and t.companycode in
        <foreach collection="queryRequest.partnerCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="queryRequest.value != null and queryRequest.value != ''">
            and (t.empname like CONCAT('%',#{queryRequest.value}, '%') or t.empcode like
            CONCAT('%',#{queryRequest.value}, '%') )
        </if>
        <if test="queryRequest.status = 'SERVING'">
            and t.empstatus = '01'
        </if>
        order by t.empcode ASC
        <choose>
            <when test="null != queryRequest.start">
                limit #{queryRequest.start},#{queryRequest.size}
            </when>
            <otherwise>
                limit 0,#{queryRequest.size}
            </otherwise>
        </choose>
    </select>

    <select id="list" resultType="map">
        select
        t.empcode as code,t.empname as name,t.birthday as birthday,t.empincode empInCode,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        s.empname as referencesName,t.entrytime as entryTime,t.quittime as quitTime,
        e.companycode as topCode,
        t.idtype as idType,t.idcode as idCode,
        t.canuniversal as universalQualification,t.maintelephone as mobile,t.sexcode as gender,
        f.CERCODE as licenseNo,t.qualificationenddate as licenseEndDate,
        t.empstatus as status
        from tbemp t
        left join tbcertificate f ON f.empincode = t.empincode and f.CERTYPE = '02'
        left join tbemp s on t.fatherempincode = s.empincode
        left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.empstatus = '01'
        and t.empcode = #{employeeCode}
        order by t.empcode ASC
    </select>

    <select id="queryByMobile" resultType="map">
        select
            t.empcode as code,t.empname as name,t.birthday as birthday,t.empincode empInCode,t.INST_CODE as orgCode,t.INST_NAME as orgName,
            e.saleteamcode as teamCode,e.saleteamname as teamName,
            s.empname as referencesName,t.entrytime as entryTime,t.quittime as quitTime,
            e.companycode as topCode,
            t.idtype as idType,t.idcode as idCode,
            t.canuniversal as universalQualification,t.maintelephone as mobile,t.sexcode as gender,
            f.CERCODE as licenseNo,t.qualificationenddate as licenseEndDate,
            t.empstatus as status
        from tbemp t
                 left join tbcertificate f ON f.empincode = t.empincode and f.CERTYPE = '02'
                 left join tbemp s on t.fatherempincode = s.empincode
                 left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.empstatus = '01'
          and t.maintelephone = #{mobile}
    </select>

    <select id="selectByEmployeeCode" resultType="map">
        select
        t.empcode as code,t.empname as name,t.empincode empInCode,t.INST_CODE as orgCode,t.INST_NAME as orgName,
        e.saleteamcode as teamCode,e.saleteamname as teamName,
        s.empname as referencesName,t.entrytime as entryTime,t.quittime as quitTime,
        e.companycode as topCode,
        t.idtype as idType,t.idcode as idCode,
        t.canuniversal as universalQualification,t.maintelephone as mobile,t.sexcode as gender,
        f.CERCODE as licenseNo,t.qualificationenddate as licenseEndDate,
        t.empstatus as status
        from tbemp t
        left join tbcertificate f ON f.empincode = t.empincode and f.CERTYPE = '02'
        left join tbemp s on t.fatherempincode = s.empincode
        left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.empcode = #{employeeCode}

        <if test="status != null">
            and t.empstatus = #{status}
        </if>
        order by t.empcode ASC
    </select>

    <select id="getByParams" resultMap="Base_Result_Map">
        select * from tbemp e
        <if test="licenseNo !=null and licenseNo!=''">
            left join tbcertificate f ON f.empincode = e.empincode
        </if>
        where e.empstatus = '01'
        <if test="idCode!=null and idCode!=''">
            and e.idcode = #{idCode}
        </if>
        <if test="licenseNo !=null and licenseNo!=''">
            and f.CERCODE = #{licenseNo}
        </if>
    </select>


    <select id="myList" resultType="map">
        select t.empcode              as code,
               t.empname              as name,
               t.INST_CODE            as orgCode,
               t.INST_NAME            as orgName,
               e.saleteamcode         as teamCode,
               e.saleteamname         as teamName,
               s.empname              as referencesName,
               t.entrytime            as entryTime,
               t.quittime             as quitTime,
               e.companycode          as topCode,
               t.idtype               as idType,
               t.idcode               as idCode,
               t.canuniversal         as universalQualification,
               t.maintelephone        as mobile,
               t.sexcode              as gender,
               f.CERCODE              as licenseNo,
               t.qualificationenddate as licenseEndDate,
               t.empstatus            as status
        from tbemp t
                 left join tbcertificate f ON f.empincode = t.empincode
                 left join tbemp s on t.fatherempincode = s.empincode
                 left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.CPTYPE = 'PARTY'
          and f.CERTYPE = '02'
          and t.empstatus = '01'
        order by (case when t.empstatus = '01' then 1 ELSE 2 END) asc, t.empcode asc
        <if test="currey !=null and size !=null">
            limit #{start} , #{size}
        </if>
    </select>

    <select id="getByLicenseNoList" resultMap="Base_Result_Map">
        select e.*,f.CERCODE as qualificationno from tbemp e
        <if test="licenseNoList !=null and licenseNoList.size()>0">
            left join tbcertificate f ON f.empincode = e.empincode
        </if>
        where e.empstatus = '01' and f.CERTYPE = '02'
        <if test="licenseNoList !=null and licenseNoList.size()>0">
            and f.CERCODE in
            <foreach collection="licenseNoList" item="licenseNo" index="index" open="(" close=")" separator=",">
                #{licenseNo}
            </foreach>
        </if>
    </select>

    <select id="getByMga" resultType="map">
        SELECT a.*, b.CERCODE
        FROM tbemp a
        LEFT JOIN tbcertificate b ON b.empincode = a.empincode AND b.CERTYPE='02'
        WHERE a.empstatus='01' AND (a.maintelephone = #{mobile} OR( a.idcode = #{idCode} AND idtype= #{idType}) OR b.CERCODE = #{licenseNo})
    </select>

    <select id="getAllTbempByCompanycode" resultMap="Base_Result_Map">
        select empcode
        from tbemp e
        where e.empstatus = '01' and e.companycode = #{companycode}
    </select>

    <select id="selectByStartSize" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select empcode,empname,idtype,idcode,empstatus,maintelephone
        from tbemp e
        where e.CPTYPE = 'PARTY'
        limit #{start},#{size}
    </select>

    <select id="queryEmployeeLeader" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select *
        from tbemp where empincode =
         (
             select m.empincode
             from tbemp e
                      LEFT JOIN tbsaleteam m on e.saleteamincode = m.saleteamincode
             where m.saleteamstatus = '00' and e.empcode = #{employeeCode}
         )
    </select>

    <select id="queryEmployee" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select *
        from tbemp where
            1=1
            <if test="employeeCode != null and employeeCode != ''">
                and empincode = #{employeeCode}
            </if>

    </select>

    <select id="allEmployeesByCompanycodeAndStatus" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select t.empcode,t.empname,t.companycode,t.INST_CODE,t.INST_NAME,t.birthday
        from tbemp t
        where 1=1
        <if test="companycode !=null and companycode != ''">
            and companycode = #{companycode}
        </if>
        <if test="status !=null and status == 'SERVING'">
            and empstatus = '01'
        </if>
        <if test="status !=null and status == 'INVALID'">
            and empstatus != '01'
        </if>
    </select>


    <select id="getByEmployeeCodeList" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select t.empcode,t.empname,t.empstatus,t.isvirtualemp,t.companycode,t.INST_CODE,t.INST_NAME
        from tbemp t
        where t.empcode in
        <foreach collection="employeeCodeList" item="employeeCode" index="index" open="(" close=")" separator=",">
            #{employeeCode}
        </foreach>

    </select>
    <select id="queryIdCode" resultType="java.lang.String">
        select  empincode from tbemp where idcode = #{idCode} and empstatus ='01'  limit 1
    </select>
    <select id="queryPhone" resultType="java.lang.String">
        select  empincode from tbemp where maintelephone = #{mobile} and empstatus ='01'  limit 1
    </select>
    <select id="myListByPage" resultType="com.hqins.agent.org.dao.entity.exms.Tbemp">
        select
        t.empcode              as empcode,
        t.empname              as empname,
        e.companycode          as companycode,
        t.isvirtualemp
        from tbemp t
        left join tbsaleteam e on t.saleteamincode = e.saleteamincode
        where t.CPTYPE = 'PARTY'
        and t.empstatus = '01'
        and t.empcode is not null
        and e.companycode is not null
        order by (case when t.empstatus = '01' then 1 ELSE 2 END) asc, t.empcode asc
    </select>

    <select id="getEmployeeExp" resultType="map">
        select
            r.RANKNAME rankName , t.teamlevel teamLevel, e.contractcode contractCode
        from tbemp e
        left join tbrankdef r on e.rankcode = r.RANKCODE and e.companycode  = r.CHANLECODE  and r.SYSTEMSTATUS  = '1'
        left join tbsaleteam t on e.saleteamincode = t.saleteamincode
        where
            e.empstatus = '01' and e.empcode = #{employeeCode}
    </select>

    <select id="queryEmployeeOrgInfoByList" resultType="com.hqins.agent.org.model.vo.EmployeeOrgVO">
        select
            t.empcode employeeCode,
            t.empname employeeName,
            t.empstatus status,
            t.isvirtualemp isVirtualEmp,
            t.isinsideflag isInsideFlag,
            t.serialcode serialCode,
            s.RANKSEQUNAME serialName,
            t.rankcode rankCode,
            d.RANKNAME rankName,
            ts.saleteamcode teamCode,
            ts.saleteamname teamName,
            ts.empincode teamLeaderCode,
            t1.empname teamLeaderName,
            t.INST_CODE orgCode,
            t.INST_NAME orgName,
            CASE
                WHEN ts.teamlevel = '01' THEN ts.saleteamcode
                ELSE NULL
            END businessTeamCode,
            CASE
                WHEN ts.teamlevel = '01' THEN ts.saleteamname
                ELSE NULL
            END businessTeamName,
            CASE
                WHEN ts.teamlevel = '01' THEN t1.empcode
                ELSE NULL
            END businessTeamLeaderCode,
            CASE
                WHEN ts.teamlevel = '01' THEN t1.empname
                ELSE NULL
            END businessTeamLeaderName,
            CASE
                WHEN ts.teamlevel = '01' THEN ts1.saleteamcode
                WHEN ts.teamlevel = '02' THEN ts.saleteamcode
                ELSE NULL
            END businessDeptCode,
            CASE
                WHEN ts.teamlevel = '01' THEN ts1.saleteamname
                WHEN ts.teamlevel = '02' THEN ts.saleteamname
                ELSE NULL
            END businessDeptName,
            CASE
                WHEN ts.teamlevel = '01' THEN t2.empcode
                WHEN ts.teamlevel = '02' THEN t1.empcode
                ELSE NULL
            END businessDeptLeaderCode,
            CASE
                WHEN ts.teamlevel = '01' THEN t2.empname
                WHEN ts.teamlevel = '02' THEN t1.empname
                ELSE NULL
            END businessDeptLeaderName,
            CASE
                WHEN ts.teamlevel = '01' THEN ts2.saleteamcode
                WHEN ts.teamlevel = '02' THEN ts1.saleteamcode
                WHEN ts.teamlevel = '03' THEN ts.saleteamcode
                ELSE NULL
            END businessAreaCode,
            CASE
                WHEN ts.teamlevel = '01' THEN ts2.saleteamname
                WHEN ts.teamlevel = '02' THEN ts1.saleteamname
                WHEN ts.teamlevel = '03' THEN ts.saleteamname
                ELSE NULL
            END businessAreaName,
            CASE
                WHEN ts.teamlevel = '01' THEN t3.empcode
                WHEN ts.teamlevel = '02' THEN t2.empcode
                WHEN ts.teamlevel = '03' THEN t1.empcode
                ELSE NULL
            END businessAreaLeaderCode,
            CASE
                WHEN ts.teamlevel = '01' THEN t3.empname
                WHEN ts.teamlevel = '02' THEN t2.empname
                WHEN ts.teamlevel = '03' THEN t1.empname
                ELSE NULL
            END businessAreaLeaderName
        from
            tbemp t
        left join tbranksequ s on t.serialcode = s.RANKSEQUCODE and s.SYSTEMSTATUS = '1'
        left join tbrankdef d on t.rankcode = d.RANKCODE and d.SYSTEMSTATUS = '1'
        left join tbsaleteam ts on t.saleteamincode = ts.saleteamincode and ts.saleteamstatus = '00'
        left join tbemp t1 on ts.empincode = t1.empincode and t1.empstatus = '01'
        left join tbsaleteam ts1 on ts.supersaleteamcode = ts1.saleteamcode and ts1.saleteamstatus = '00'
        left join tbemp t2 on ts1.empincode = t2.empincode and t2.empstatus = '01'
        left join tbsaleteam ts2 on ts1.supersaleteamcode = ts2.saleteamcode and ts2.saleteamstatus = '00'
        left join tbemp t3 on ts2.empincode = t3.empincode and t3.empstatus = '01'
        where
            t.empcode in
            <foreach collection="employeeCodeList" item="empCode" index="index" open="(" close=")" separator=",">
                #{empCode}
            </foreach>
    </select>













































































    <select id="queryRecruitmentProgressList" resultType="com.hqins.agent.org.model.vo.AppEmployeeRecruitmentInfoListVO">
        SELECT
            empcode as employeeCode,
            empname as employeeName,
            (
                case
                    when sexcode = '1' then '男'
                    when sexcode = '2' then '女'
                    else '未知'
                end
            ) gender,
            maintelephone as phone,
            birthday,
            modifytime as modifyTime,
            (
                case
                    when empstatus = '03' or app_employee_status = '19' then app_employee_status
                    else empstatus
                end
            ) as employeeStatus
        FROM
            tbemp
        WHERE
                fatherempincode = #{empInCode}
            and EMP_ENTRY_TYPE = '2'
            and empstatus != '04'
            <if test="employeeName != null and employeeName != ''">
                and empname like concat ('%', #{employeeName}, '%')
            </if>
        order by modifytime desc
    </select>

    <select id="queryMyAgentListByEmpInCodeAndInstCode" resultType="com.hqins.agent.org.model.vo.AppEmployeeRecruitmentInfoListVO">
        SELECT
            empcode as employeeCode,
            empname as employeeName,
            (
                case
                    when sexcode = '1' then '男'
                    when sexcode = '2' then '女'
                    else '未知'
                end
            ) gender,
            maintelephone as phone,
            DATE_FORMAT(birthday, '%Y-%m-%d') as birthday,
            app_employee_status as employeeStatus
        FROM
            tbemp
        WHERE
            fatherempincode = #{empInCode}
          <!-- 11:待面谈 -->
          and app_employee_status = '11'
          and EMP_ENTRY_TYPE = '2'
          <if test="employeeName != null and employeeName != ''">
              and empname like concat ('%', #{employeeName}, '%')
          </if>
          order by modifytime desc
    </select>

    <select id="queryMyAgentListByInstCodeList" resultType="com.hqins.agent.org.model.vo.AppEmployeeRecruitmentInfoListVO">
        SELECT
            t.empcode as employeeCode,
            t.empname as employeeName,
            (
                case
                    when t.sexcode = '1' then '男'
                    when t.sexcode = '2' then '女'
                    else '未知'
                end
            ) gender,
            t.maintelephone as phone,
            DATE_FORMAT(t.birthday, '%Y-%m-%d') as birthday,
            t.app_employee_status as employeeStatus
        FROM
            tbemp t
        inner join supervisor_employee_audit_config s on t.INST_CODE = s.inst_code
        WHERE
        <!-- 12:待一面 -->
            t.app_employee_status = '12'
        and t.EMP_ENTRY_TYPE = '2'
        <if test="instCoeList !=null and instCoeList.size()>0">
            and t.INST_CODE in
            <foreach collection="instCoeList" item="instCode" index="index" open="(" close=")" separator=",">
                #{instCode}
            </foreach>
        </if>
        <if test="employeeCode !=null and employeeCode!= ''">
            and s.audit_code = #{employeeCode}
        </if>
        <if test="employeeName != null and employeeName != ''">
            and t.empname like concat ('%', #{employeeName}, '%')
        </if>
        order by modifytime desc
    </select>

    <select id="queryAppRecruitmentEmployeeInfo" resultType="com.hqins.agent.org.model.vo.AppEmployeeInfoVO">
        select
            t.empincode as empInCode,
            t.empcode as empCode,
            t.empname as empName,
            t.idtype  as idType,
            t.idtypename  as idTypeName,
            t.idcode  as idCode,
            t.sexcode as sexCode,
            t.sexname as sexName,
            DATE_FORMAT(t.birthday, '%Y-%m-%d') as birthday,
            t.nationcode      as nationCode,
            t.polityvisage    as polityVisage,
            t.marriage    as marriage,
            t.liking      as liking,
            t.maintelephone   as mainTelephone,
            t.mainemail   as mainEmail,
            t.mainzipcode as mainZipcode,
            t.birthplace  as birthplace,
            t.rgtaddress  as rgtAddress,
            t.provincecode    as provinceCode,
            t.provincename    as provinceName,
            t.citycode    as  cityCode,
            t.cityname    as  cityName,
            t.arecode as  areCode,
            t.arename as  areName,
            t.mainaddress as  mainAddress,
            t.highdecu    as  highestEducation,
            t.graduateschool  as  graduateSchool,
            t.major   as  major,
            t.work_experience_years   as  workExperienceYears,
            t.isinssale   as  isInsSale,
            t.WORKAGE as  workAge,
            t.reason_for_job_change   as  reasonForJobChange,
            t.card_bank_name  as  cardBankName,
            t.cardbank    as  cardBank,
            t.cardbankbranch  as  cardBankBranch,
            t.card_bank_branch_code   as  cardBankBranchCode,
            t.cardno  as  cardNo,
            app.self_evaluation as  selfEvaluation,
            app.view_on_life_insurance_marketing    as  viewOnLifeInsuranceMarketing,
            app.proudest_achievement    as  proudestAchievement,
            app.expectation_for_future  as  expectationForFuture,
            app.career_goal as  careerGoal,
            app.interview_evaluation    as  interviewEvaluation,
            app.confidence  as  confidence,
            app.optimism    as  optimism,
            app.team_spirit as  teamSpirit,
            app.expression_ability  as  expressionAbility,
            app.innovation_ability  as  innovationAbility,
            app.hiring_level    as  hiringLevel,
            app.rank_sequence   as  rankSequence,
            app.rank_sequence_name as rankSequenceName,
            app.hiring_level_name  as hiringLevelName,
            app.entry_documents as  entry_documents,
            f.empcode  as fatherEmpCode,
            f.empname  as fatherEmpName,
            (
                case
                    when t.empstatus = '03' or t.app_employee_status = '19' then t.app_employee_status
                    else t.empstatus
                end
            ) as empStatus,
            s.audit_code as supervisorEmployeeCode,
            s.audit_name as supervisorEmployeeName,
            t.createtime as createTime
        from
            tbemp t
        left join tbemp_app_interview_info app on app.emp_code = t.empcode
        left join tbemp f on t.fatherempincode = f.empincode
        left join supervisor_employee_audit_config s on t.INST_CODE = s.inst_code
        where
            1 = 1
        <if test="employeeCode != null and employeeCode != ''">
            and t.empcode = #{employeeCode}
        </if>
        <if test="visitorId != null and visitorId != ''">
            and t.visitor_id = #{visitorId}
        </if>
        order by t.createtime desc
    </select>

    <select id="queryEmployeePersonalExperience" resultType="com.hqins.agent.org.model.vo.AppEmployeePersonalExperienceVO">
        select
            DATE_FORMAT(start_date, '%Y-%m-%d') as  startDate,
            DATE_FORMAT(end_date, '%Y-%m-%d') as  endDate,
            organization    as  organization,
            position    as  position
        from
            app_employee_personal_experience
        where
            emp_code = #{empInCode}
    </select>

    <select id="getEmployByEmployeeCode" resultType="java.lang.String">
        select
            audit_code
        from
            supervisor_employee_audit_config
        where
            audit_code = #{employeeCode}
    </select>


    <select id="getTeamEmpInfo"
            resultType="com.hqins.agent.org.model.vo.EmployeeOrgVO">

        select
        m.empcode employeeCode,
        m.empname employeeName
        from tbemp m
        inner join tbsaleteam t1 on m.saleteamincode = t1.saleteamincode
        where 1=1
        <if test="saleTeamCode != null and saleTeamCode != ''">
            and t1.saleteamcode = #{saleTeamCode}
        </if>

        union

        select
        e.empcode employeeCode,
        e.empname employeeName
        from tbemp e
        inner join tbsaleteam t1 on e.saleteamincode = t1.saleteamincode
        inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
        where 1=1
        <if test="saleTeamCode != null and saleTeamCode != ''">
            and t2.saleteamcode = #{saleTeamCode}
        </if>

        union

        select
        e.empcode employeeCode,
        e.empname employeeName
        from tbemp e
        inner join tbsaleteam t1 on e.saleteamincode = t1.saleteamincode
        inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
        inner join tbsaleteam t3 on t2.supersaleteamcode = t3.saleteamcode
        where 1=1
        <if test="saleTeamCode != null and saleTeamCode != ''">
            and t3.saleteamcode = #{saleTeamCode}
        </if>
    </select>


    <select id="getEmpInfo" resultType="com.hqins.agent.org.model.vo.HonorEmpVO">
        select
            empcode as empCode,
            DATE_FORMAT(entrytime, '%Y-%m-%d') as  entryTime
        from
            tbemp
        where
            empcode = #{empCode}
    </select>

    <select id="queryEmpAreaCode" resultType="com.hqins.agent.org.model.vo.EmployeeVO">
        select
            t3.SALETEAMCODE teamCode,
            SUBSTRING(t3.empincode, 5) AS code
        from tbemp e
                 inner join tbsaleteam t1 on e.saleteamincode = t1.saleteamincode
                 inner join tbsaleteam t2 on t1.supersaleteamcode = t2.saleteamcode
                 inner join tbsaleteam t3 on t2.supersaleteamcode = t3.saleteamcode
        where 1=1 and e.EMPCODE = #{empCode}
    </select>

</mapper>
