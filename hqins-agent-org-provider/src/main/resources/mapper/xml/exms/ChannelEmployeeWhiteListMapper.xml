<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.ChannelEmployeeWhiteListMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeWhiteList">
            <result property="id" column="id"/>
            <result property="channelCode" column="channel_code"/>
            <result property="channelName" column="channel_name"/>
            <result property="corporationCode" column="corporation_code"/>
            <result property="corporationName" column="corporation_name"/>
            <result property="employeeCode" column="employee_code"/>
            <result property="employeeName" column="employee_name"/>
            <result property="idNo" column="id_no"/>
            <result property="idType" column="id_type"/>
            <result property="licenseNo" column="license_no"/>
            <result property="description" column="description"/>
            <result property="status" column="status"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="checkResultId" column="check_result_id"/>
            <result property="createUser" column="create_user"/>
            <result property="createUserName" column="create_user_name"/>
            <result property="createTime" column="create_time"/>
            <result property="modifyUser" column="modify_user"/>
            <result property="modifyUserName" column="modify_user_name"/>
            <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        channel_code,channel_name,corporation_code,corporation_name,employee_code,employee_name,id_no,id_type,license_no,description,status,check_result_id,create_user,create_user_name,create_time,modify_user,modify_user_name,modify_time
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeWhiteList">
        insert into channel_employee_white_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="channelName != null">
                channel_name,
            </if>
            <if test="corporationCode != null">
                corporation_code,
            </if>
            <if test="corporationName != null">
                corporation_name,
            </if>
            <if test="employeeCode != null">
                employee_code,
            </if>
            <if test="employeeName != null">
                employee_name,
            </if>
            <if test="idNo != null">
                id_no,
            </if>
            <if test="idType != null">
                id_type,
            </if>
            <if test="licenseNo != null">
                license_no,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="checkResultId != null">
                check_result_id,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyUser != null">
                modify_user,
            </if>
            <if test="modifyUserName != null">
                modify_user_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelName != null">
                #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="corporationCode != null">
                #{corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="corporationName != null">
                #{corporationName,jdbcType=VARCHAR},
            </if>
            <if test="employeeCode != null">
                #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="employeeName != null">
                #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null">
                #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="checkResultId != null">
                #{checkResultId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyUser != null">
                #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.exms.ChannelEmployeeWhiteList">
        update channel_employee_white_list
        <set>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelName != null">
                channel_name = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="corporationCode != null">
                corporation_code = #{corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="corporationName != null">
                corporation_name = #{corporationName,jdbcType=VARCHAR},
            </if>
            <if test="employeeCode != null">
                employee_code = #{employeeCode,jdbcType=VARCHAR},
            </if>
            <if test="employeeName != null">
                employee_name = #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                id_no = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                id_type = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="licenseNo != null">
                license_no = #{licenseNo,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="checkResultId != null">
                check_result_id = #{checkResultId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMPSTAMP},
            </if>
            <if test="modifyUser != null">
                modify_user = #{modifyUser,jdbcType=VARCHAR},
            </if>
            <if test="modifyUserName != null">
                modify_user_name = #{modifyUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMPSTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>


</mapper>
