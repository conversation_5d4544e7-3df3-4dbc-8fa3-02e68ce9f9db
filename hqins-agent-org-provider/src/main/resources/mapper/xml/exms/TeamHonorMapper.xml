<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.TeamMapper">

    <select id="getTeamBirthDayEmpList" parameterType="map" resultType="com.hqins.agent.org.model.vo.EmployeeVO">
        SELECT
        e.empcode code,
        e.empname name,
        e.birthday birthday,
        e.CPTYPE cpType,
        e.INST_CODE orgCode,
        e.INST_NAME orgName,
        t.saleteamcode teamCode,
        t.saleteamName teamName
        FROM `tbemp` e
        inner join tbsaleteam t on e.saleteamincode = t.saleteamincode
        where
        e.empstatus = '01'
        and  e.isvirtualemp != 'Y'
        AND (
            CASE WHEN YEAR(#{endDate}) = YEAR(#{startDate})
            THEN DATE_FORMAT(#{endDate}, '%m-%d') >= DATE_FORMAT(e.birthday, '%m-%d') AND DATE_FORMAT(e.birthday, '%m-%d') >= DATE_FORMAT(#{startDate}, '%m-%d')
            ELSE (DATE_FORMAT(e.birthday, '%m-%d') >= DATE_FORMAT(#{startDate}, '%m-%d') OR DATE_FORMAT(#{endDate}, '%m-%d') >= DATE_FORMAT(e.birthday, '%m-%d'))
            END
            )
        and e.saleteamincode in (

            select saleteamincode from tbsaleteam
            where saleteamcode = #{saleTeamCode} and saleteamstatus = '00'

            union

            select saleteamincode from tbsaleteam
            where supersaleteamcode = #{saleTeamCode} and saleteamstatus = '00'

            union

            select saleteamincode from tbsaleteam
            where supersaleteamcode in (select saleteamcode from tbsaleteam where supersaleteamcode = #{saleTeamCode} and saleteamstatus = '00') and saleteamstatus = '00'
        )
    </select>


    <select id="getBirthDayEmpList" parameterType="map" resultType="com.hqins.agent.org.model.vo.EmployeeVO">
        SELECT
        e.empcode code,
        e.empname name,
        e.birthday birthday,
        e.CPTYPE cpType,
        e.INST_CODE orgCode,
        e.INST_NAME orgName,
        t.saleteamcode teamCode,
        t.saleteamName teamName
        FROM `tbemp` e
        inner join tbsaleteam t on e.saleteamincode = t.saleteamincode
        where
        e.empstatus = '01'
        and  e.isvirtualemp != 'Y'
        AND (
            CASE WHEN YEAR(#{endDate}) = YEAR(#{startDate})
            THEN DATE_FORMAT(#{endDate}, '%m-%d') >= DATE_FORMAT(e.birthday, '%m-%d') AND DATE_FORMAT(e.birthday, '%m-%d') >= DATE_FORMAT(#{startDate}, '%m-%d')
            ELSE (DATE_FORMAT(e.birthday, '%m-%d') >= DATE_FORMAT(#{startDate}, '%m-%d') OR DATE_FORMAT(#{endDate}, '%m-%d') >= DATE_FORMAT(e.birthday, '%m-%d'))
            END
            )

    </select>


    <select id="getEmployeeInfo" parameterType="map" resultType="com.hqins.agent.org.model.vo.EmployeeVO">
        select
        e.empcode code,
        e.empincode empInCode,
        e.empname name,
        e.birthday birthday,
        e.rankcode `rank`,
        r.RANKNAME rankName,
        e.serialcode rankSeqCode,
        s.RANKSEQUNAME rankSeqName,
        e.WORKAGE workAge,
        e.idcode idCode,
        e.companycode topCode,
        p.companyname topName,
        e.INST_CODE orgCode,
        e.INST_NAME orgName,
        t.saleteamcode teamCode,
        t.saleteamname teamName,
        t.empincode teamLeaderInCode,
        t.empinname teamLeaderName,
        e2.empcode teamLeaderCode,
        e.birthday birthday,
        e.entrytime entryTime,
        e.quittime quitTime,
        e.qualificationno licenseNo,
        e.qualificationenddate licenseEndDate
        from zh_exms.tbemp e
        left join zh_iips.tbepartner p on e.companycode = p.companycode
        left join tbsaleteam t on e.saleteamincode = t.saleteamincode
        left join zh_exms.tbemp e2 on t.empincode = e2.empincode
        left join tbranksequ s on e.serialcode = s.RANKSEQUCODE and e.companycode = s.CHANLECODE
        left join tbrankdef r on e.RANKCODE = r.RANKCODE and e.companycode = r.CHANLECODE
        where
        e.empcode = #{employeeCode}
        group by e.empcode

    </select>


    <select id="getPerformanceSaleTeamByCode" parameterType="map" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">

        select
        t.saleteamincode,
        t.saleteamcode,
        t.saleteamname,
        t.INST_CODE,
        t.teamlevel teamlevel,
        e.empcode empincode
        from tbsaleteam t
        left join tbemp e on t.empincode = e.empincode
        where
        t.saleteamcode = #{saleTeamCode}
        limit 1

    </select>



    <select id="getEmpTargetSaleteamCode" parameterType="map" resultType="String">
        SELECT
        t.saleteamcode
        FROM (
        (SELECT *
        FROM tbempflow
        WHERE empcode = #{empCode}
        AND #{commissionMonth}  >= DATE_FORMAT(chgdate, '%Y-%m')
        AND chgtype = '30'
        AND APPLYSTATE = 1
        ORDER BY chgdate desc,APPLYTIME DESC
        LIMIT 1)

        UNION ALL

        (SELECT *
        FROM tbempflow
        WHERE empcode = #{empCode}
        AND #{commissionMonth}  >= DATE_FORMAT(chgdate, '%Y-%m')
        AND chgtype = '00'
        AND APPLYSTATE = 1
        LIMIT 1)
        ) AS a
        inner join tbsaleteam t on a.saleteamincode = t.saleteamincode
        ORDER BY CASE WHEN chgtype = '30' THEN 1 ELSE 2 END limit 1

    </select>


    <select id="getTeamSaleTeam" parameterType="map" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">

        select *
        from (
            select * from tbsaleteam
            where saleteamcode = #{saleTeamCode} and saleteamstatus = '00'
            union
            select * from tbsaleteam
            where supersaleteamcode = #{saleTeamCode} and saleteamstatus = '00'
            union
            select * from tbsaleteam
            where supersaleteamcode in (select saleteamcode from tbsaleteam where supersaleteamcode = #{saleTeamCode}) and saleteamstatus = '00'
        ) a
        where a.teamlevel = '01'
        order by a.createtime desc
        limit 1


    </select>

</mapper>
