<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.RankDefMapper">

    <select id="getAllUsefulRankDef" resultType="com.hqins.agent.org.dao.entity.exms.RankDef">
       select
            r.RANKID as rankId,
            r.<PERSON><PERSON><PERSON><PERSON><PERSON> as rankCode,
            r.RANKNAME as  rankName,
            r.RANKSEQUCODE as rankSequCode,
            r.SYSTEMSTATUS as systemStatus,
            r.<PERSON>CODE

        from tbrankdef r
        where r.SYSTEMSTATUS = '1'

    </select>

    <select id="queryRankCodeByRankSequenceCode" resultType="com.hqins.agent.org.dao.entity.exms.RankDef">
        select
            r.<PERSON><PERSON><PERSON><PERSON> as rankId,
            r.<PERSON><PERSON> as rankCode,
            r.<PERSON><PERSON> as  rankName,
            r.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as rankSequCode,
            r.SYSTEM<PERSON>ATUS as systemStatus,
            r.CHANLECODE
        from
            tbrankdef r
        where
            r.RANKSEQUCODE = #{rankSequenceCode}
            and r.CHANLECODE = #{companyCode}
            and r.SYSTEMSTATUS = '1'
        order by r.RANKCODE
    </select>
</mapper>
