<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.EmployeeAssessmentMapper">
    <select id="getCheckFrequency" resultType="java.lang.String">
        select
            c.check_frequency
        from
            tbemp t
        inner join basic_law_exec_teams_config f on    f.inst_code = t.INST_CODE
        inner join basic_law_exec e on  e.id = f.exec_id  and now() >= e.start_date  and  e.end_date>= now() and e.apply_status = 'ENABLE'
        inner join check_standard s on e.version_id  = s.version_id  and s.check_item = 'CHECK_FREQUENCY'
        inner join check_standard_config c on s.id = c.check_standard_id  and c.rank_seq_code  = t.serialcode
        where
            t.empcode  = #{employeeCode}
        and if(t.companycode = 'P00001',1=1,c.rank_code  = t.rankcode)
    </select>

    <select id="getCheckBatchPersonInfo" resultType="com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo">
        select
            *
        from
            check_batch_person_info
        where
            check_month = #{checkMonth}
        and
            agent_code = #{employeeCode}
    </select>

    <select id="getPersonConfigValue" resultType="BigDecimal">
        select
            value
        from
            check_batch_person_config
        where
            check_person_id = #{id}
        and
            check_item = #{checkItem}
    </select>

    <select id="getCorrespondingRankList" resultType="com.hqins.agent.org.dao.entity.exms.RankDef">
        SELECT
            r.RANKSEQUCODE rankSequCode,
            r.RANKCODE rankCode,
            r.RANKNAME rankName,
            r.RANK_LEVEL rankLevel
        from
            (
                select
                    distinct c.rank_seq_code,
                             c.rank_seq_name
                from
                    check_standard s
                        inner join check_standard_config c on s.id = c.check_standard_id
                where
                    s.version_id = #{basicLawVersionId}
            and s.check_item = 'MONTHLY_STANDARD_PREMIUN'
            ) t1
                inner join tbrankdef r on t1.rank_seq_code = r.RANKSEQUCODE
                and r.CPTYPE = 'PARTY'
                and r.CHANLECODE = substr(#{partnerInstCode}, 1, 6)
                and r.RANKSEQUCODE = #{rankSeqCode}
                and r.RANK_LEVEL is not null
                and r.RANK_LEVEL != ''
        order by
            r.RANK_LEVEL
    </select>

    <select id="getIfpCorrespondingRankList" resultType="com.hqins.agent.org.dao.entity.exms.RankDef">
        SELECT
            r.RANKSEQUCODE rankSequCode,
            r.RANKCODE rankCode,
            r.RANKNAME rankName,
            r.RANK_LEVEL rankLevel
        from
            (
                select
                    distinct c.rank_seq_code,
                             c.rank_seq_name
                from
                    check_standard s
                        inner join check_standard_config c on s.id = c.check_standard_id
                where
                    s.version_id = #{basicLawVersionId}
            and s.check_item = 'IFP_GENERAL_CHECK'
            and c.check_type = 'KEEP'
            ) t1
                inner join tbrankdef r on t1.rank_seq_code = r.RANKSEQUCODE
                and r.CPTYPE = 'PARTY'
                and r.CHANLECODE = substr(#{partnerInstCode}, 1, 6)
                and r.RANKSEQUCODE = #{rankSeqCode}
                and r.RANK_LEVEL is not null
                and r.RANK_LEVEL != ''
        order by
            r.RANK_LEVEL
    </select>

    <select id="getIfpAssessmentItems" resultType="com.hqins.agent.org.model.vo.AssessmentItemVO">
        select f.behavior_points digitalBehavioralPoints,
               f.monthly_add_num householdAccountExtensions,
               f.year_cr13       m13Cr
        from check_standard d
                 inner join check_standard_config f on d.id = f.check_standard_id
        where d.version_id = #{basicLawVersionId}
          and d.check_item = 'IFP_GENERAL_CHECK'
          and f.rank_seq_code = #{rankSeqCode}
          and f.rank_code = #{rankCode}
          <choose>
              <when test="currentTarget = '维持'">
                  and f.check_type  = 'KEEP'
              </when>
              <when test="currentTarget = '晋升'">
                  and f.check_type = 'PROMOTE'
              </when>
          </choose>
    </select>
    
    <select id="getAssessmentItems" resultType="com.hqins.agent.org.model.vo.AssessmentItemsVO">
        select
            t.check_item name,
            t.value x,
            t.month xUnit,
            t.id code
        from
            check_batch_person_config t
        where
            check_person_id = #{id}
    </select>

    <select id="getYbAssessmentItems" resultType="com.hqins.agent.org.model.vo.AssessmentItemVO">
        select
            f1.administer_man_number deptCurrentPersonNum,
            f1.suit_managers_rate_div_suit_department_rate agentPassRate,
            f2.year_cr13 m13Cr,
            f2.year_cr25 m25Cr
        from
            basic_law_version v
                left join check_standard d1 on v.id = d1.version_id
                and d1.check_item = 'UNDER_MANAGE_CHECK'
                left join check_standard_config f1 on d1.id = f1.check_standard_id
                and f1.rank_seq_code = #{rankSeqCode}
                and f1.rank_code = #{rankCode}
                left join check_standard d2 on v.id = d2.version_id
                and d2.check_item = 'SERVICE_QUALITY_CHECK'
                left join check_standard_config f2 on d2.id = f2.check_standard_id
                and f2.rank_seq_code = #{rankSeqCode}
        where
            v.id = #{basicLawVersionId}
    </select>

    <select id="getCheckMonthHistory" resultType="java.lang.String">
        select
            check_month
        from
            check_batch_person_info
        where
            agent_code = #{employeeCode}
        order by
            check_month desc;
    </select>

    <select id="getCheckBatchId" resultType="java.lang.String">
        select
            check_batch_id
        from
            check_batch_person_info
        where
            check_month = #{checkMonth}
        and
            agent_code = #{employeeCode}
    </select>

    <select id="getAllAssessmentEmployeeInfo" resultType="com.hqins.agent.org.model.vo.AssessmentEmployeeInfoVO">
        select
            c.agent_name name,
            c.agent_code employeeId,
            c.check_settle_result finalResult,
            c.rank_seq_code rankSeqCode,
            c.saleteamincode orgCode,
            t.saleteamname orgName,
            t.CPTYPE orgType
        from
            (
                select
                    agent_name,
                    agent_code,
                    check_settle_result,
                    rank_seq_code,
                    (
                        case
                            when team1_leader = #{employeeCode} then team1
                            when team2_leader = #{employeeCode} then team2
                            when team3_leader = #{employeeCode} then team3
                            else ''
                            end
                        ) saleteamincode
                from
                    check_batch_person_info
                where
                    check_batch_id = #{checkBatchId}
                  and (
                            team1_leader = #{employeeCode}
                        or team2_leader = #{employeeCode}
                        or team3_leader = #{employeeCode}
                    )
                 and agent_code != #{employeeCode}
            ) c
                left join tbsaleteam t on c.saleteamincode = t.saleteamincode
    </select>

    <select id="getTbSaleTeamInfo" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">
        select
            *
        from
            tbsaleteam
        where
            saleteamcode = #{teamCode}
        and
            saleteamstatus = '00'
    </select>

    <select id="getAllAssessmentPersonal" resultType="com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo">
        select
            *
        from
            check_batch_person_info
        where
            check_month = #{checkMonth}
          and team1 in (
            select
                saleteamincode
            from
                tbsaleteam
            where
                saleteamcode = #{teamCode}
              and saleteamstatus = '00'
            union
            select
                saleteamincode
            from
                tbsaleteam
            where
                supersaleteamcode = #{teamCode}
              and saleteamstatus = '00'
            union
            select
                saleteamincode
            from
                tbsaleteam
            where
                    supersaleteamcode in (
                    select
                        saleteamcode
                    from
                        tbsaleteam
                    where
                        supersaleteamcode = #{teamCode}
                      and saleteamstatus = '00'
                )
              and saleteamstatus = '00'
        )
    </select>

    <select id="getMangerCheckBatchPersonInfo" resultType="com.hqins.agent.org.dao.entity.exms.CheckBatchPersonInfo">
        select
            cb.*
        from
            tbsaleteam ts
        left join
            tbemp tb on ts.empincode = tb.empincode
        left join
            check_batch_person_info  cb on tb.empcode  = cb.agent_code and cb.check_month = #{checkMonth}
        where
            ts.saleteamcode = #{teamCode}
        and
            ts.saleteamstatus = '00'
    </select>

    <select id="getChildrenTbSaleTeams" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">
        select
            *
        from
            tbsaleteam
        where
            supersaleteamcode = #{teamCode}
          and saleteamstatus = '00'
    </select>

    <select id="getAllItemsBySaleTeamCodes" resultType="com.hqins.agent.org.model.vo.AssessmentItemsVO">
        select
            f1.administer_man_number x,
            f1.suit_managers_rate_div_suit_department_rate y,
            ts.saleteamcode code
        from
            tbsaleteam ts
                left join tbemp tb on ts.empincode = tb.empincode
                left join check_batch_person_info cb on tb.empcode = cb.agent_code and cb.check_month = #{checkMonth}
                left join basic_law_version v on cb.basic_law_version_id = v.id
                left join check_standard d1 on v.id = d1.version_id and d1.check_item = 'UNDER_MANAGE_CHECK'
                left join check_standard_config f1 on d1.id = f1.check_standard_id and f1.rank_seq_code = cb.rank_seq_code and f1.rank_code = cb.rank_code
        where
            saleteamstatus = '00'
            <if test="saleTeamCodes != null and saleTeamCodes.size()>0 ">
                 and ts.saleteamcode in
                <foreach collection="saleTeamCodes" item="saleTeamCode" index="index" open="(" close=")" separator=",">
                    #{saleTeamCode}
                </foreach>
            </if>
    </select>

    <select id="getAllActualItemsBySaleTeamCodes" resultType="com.hqins.agent.org.model.vo.AssessmentItemsVO">
        select
            cbpc.check_item name,
            cbpc.value x,
            ts.saleteamcode code
        from
            tbsaleteam ts
                left join tbemp tb on ts.empincode = tb.empincode
                left join check_batch_person_info cb on tb.empcode = cb.agent_code and cb.check_month = #{checkMonth}
                left join check_batch_person_config cbpc on cb.agent_code = cbpc.agent_code and cbpc.check_month = #{checkMonth}
        where
            saleteamstatus = '00'
        and
            cbpc.check_item = 'agent_pass_rate'
            <if test="saleTeamCodes != null and saleTeamCodes.size()>0 ">
                and ts.saleteamcode in
                <foreach collection="saleTeamCodes" item="saleTeamCode" index="index" open="(" close=")" separator=",">
                    #{saleTeamCode}
                </foreach>
            </if>
    </select>
    <select id="getEmpCodeByEmpInCode" resultType="java.lang.String">
        select
            tb.empcode
        from
            tbemp tb
        where
            tb.empincode = #{empInCode}
    </select>
</mapper>