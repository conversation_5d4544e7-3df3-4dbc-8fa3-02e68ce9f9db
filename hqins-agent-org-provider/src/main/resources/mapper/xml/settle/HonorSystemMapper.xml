<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hqins.agent.org.dao.mapper.settle.HonorSystemMapper">



    <select id="getHonorList"
            resultType="com.hqins.agent.org.model.vo.HonorQueryListVO">
        select
            agent_code agentCode,
            honor_class honorClass,
            honor_code honorCode,
            current_star currentStar,
            honors_awards honorsAwards,
            is_achieved_current isAchievedCurrent,
            check_year checkYear,
            check_period checkPeriod
        from
            zh_settle.honor_current_info
        where
        1=1
        <if test="empCode != null and empCode != ''">
            and agent_code = #{empCode}
        </if>

    </select>


    <select id="getGuinnessMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetricsVO">

        select
            honor_code honorCode,
            metrics_type metricsType,
            CAST(metrics_value AS DECIMAL(22,2)) metricsValue,
            metrics_status metricsStatus
        from
            zh_settle.honor_monthly_metrics
        where
        1=1
        <if test="agentCode != null and agentCode != ''">
            and agent_code = #{agentCode}
        </if>
        <if test="checkPeriod != null and checkPeriod != ''">
            and check_period = #{checkPeriod}
        </if>
        <if test="honorCode != null and honorCode != ''">
            and honor_code = #{honorCode}
        </if>
        <if test="metricsType != null and metricsType != ''">
            and metrics_type = #{metricsType}
        </if>

    </select>


    <select id="getQXJYHCurrentDetail"
            resultType="com.hqins.agent.org.dao.entity.settle.HonorCurrentInfo">

        select
            honors_awards honorsAwards,
            current_star currentStar,
            longest_consecutive_star longestConsecutiveStar,
            current_field_count currentFieldCount
        from honor_current_info
        where 1=1
        <if test="empCode != null and empCode != ''">
            and agent_code = #{empCode}
        </if>
        <if test="honorType != null and honorType != ''">
            and honor_class = #{honorType}
        </if>

    </select>


    <select id="getQXJYHHistoryDetail"
            resultType="com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory">

        select
            check_year checkYear,
            check_period checkPeriod,
            is_achieved_current isAchievedCurrent,
            is_use_field isUseField,
            honor_code honorCode,
            honor_name honorName,
            honors_awards honorsAwards,
            DATE_FORMAT(honors_awards_time, '%Y-%m-%d') AS honorsAwardsTime,
            congratulation_id congratulationId
        from honor_assessment_history
        where 1=1
        <if test="empCode != null and empCode != ''">
            and agent_code = #{empCode}
        </if>
        <if test="honorType != null and honorType != ''">
            and honor_class = #{honorType}
        </if>

    </select>


    <select id="getQYBSXDetail"
            resultType="com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory">

        select
            agent_code agentCode,
            check_year checkYear,
            check_period checkPeriod,
            honors_awards honorsAwards,
            is_achieved_current isAchievedCurrent,
            congratulation_id congratulationId
        from honor_assessment_history
        where 1=1
        <if test="empCode != null and empCode != ''">
            and agent_code = #{empCode}
        </if>
        <if test="honorType != null and honorType != ''">
            and honor_class = #{honorType}
        </if>
        <if test="assessmentPeriods != null and assessmentPeriods != ''">
            and check_period in
            <foreach collection="assessmentPeriods" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="getQYBSXPeriodMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetrics">

        select
            per.check_period checkPeriod,
            per.agent_code agentCode,
            per.metrics_type metricsType,
            per.metrics_type_name metricsTypeName,
            CAST(per.metrics_value AS DECIMAL(22,2)) metricsValue,
            per.metrics_month metricsMonth,
            sta.metrics_value metricsStandard,
            sta.metrics_unit metricsStandardUnit
        from honor_monthly_metrics per
        left join honor_standard_metrics sta on per.metrics_type = sta.metrics_type and per.honor_class = sta.honor_class
        where 1=1
        and per.metrics_class = '3'
        <if test="empCode != null and empCode != ''">
            and per.agent_code = #{empCode}
        </if>
        <if test="honorType != null and honorType != ''">
            and per.honor_class = #{honorType}
        </if>
        <if test="assessmentPeriods != null and assessmentPeriods != ''">
            and per.check_period in
            <foreach collection="assessmentPeriods" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>



    <select id="getGRRYPeriodMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetrics">

        select
            y.current_position currentPosition,
            y.is_achieved_current isAchievedCurrent,
            y.is_qualified isQualified,
            y.total_qualified_count totalQualifiedCount,
            y.honors_awards honorsAwards,
            y.honors_awards_time honorsAwardsTime,
            per.agent_code agentCode,
            per.metrics_type metricsType,
            per.metrics_type_name metricsTypeName,
            CAST(per.metrics_value AS DECIMAL(22,2)) metricsValue,
            per.honor_code honorCode,
            sta.metrics_value metricsStandard,
            sta.metrics_unit metricsStandardUnit
        from
            honor_current_info y
            left join honor_monthly_metrics per ON per.agent_code = y.agent_code
            and per.honor_class = y.honor_class
            and per.honor_code = y.honor_code
            left join honor_standard_metrics sta ON per.metrics_type = sta.metrics_type
            and per.honor_class = sta.honor_class
            and per.honor_code = sta.honor_code
        where
        1 = 1
        and per.metrics_class = '3'  -- 考核指标
        <if test="empCode != null and empCode != ''">
            and per.agent_code = #{empCode}
        </if>
        <if test="honorType != null and honorType != ''">
            and per.honor_class = #{honorType}
        </if>
        <if test="period != null and period != ''">
            and per.check_period = #{period}
        </if>
        <if test="honorCodeList != null and !honorCodeList.isEmpty()">
            and per.honor_code in
            <foreach collection="honorCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="teamCode != null and teamCode != ''">
            and y.DISTRICT_TEAM_CODE = #{teamCode}
        </if>

    </select>

    <select id="getImmediateHigherMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetrics">

        select
            y.current_position currentPosition,
            y.is_achieved_current isAchievedCurrent,
            y.is_qualified isQualified,
            y.total_qualified_count totalQualifiedCount,
            y.honors_awards honorsAwards,
            y.honors_awards_time honorsAwardsTime,
            per.agent_code agentCode,
            per.metrics_type metricsType,
            per.metrics_type_name metricsTypeName,
            CAST(per.metrics_value AS DECIMAL(22,2)) metricsValue,
            per.honor_code honorCode
        from
        honor_current_info y
        left join honor_monthly_metrics per ON per.agent_code = y.agent_code
        and per.honor_class = y.honor_class
        and per.honor_code = y.honor_code
        where
        1 = 1
        and per.metrics_class = '3'  -- 考核指标
        <if test="honorType != null and honorType != ''">
            and per.honor_class = #{honorType}
        </if>
        <if test="period != null and period != ''">
            and per.check_period = #{period}
        </if>
        <if test="honorCode != null and honorCode != ''">
            and per.honor_code = #{honorCode}
        </if>
        <if test="lastPosition != null and lastPosition != ''">
            and y.current_position = #{lastPosition}
        </if>

    </select>



    <select id="getMDRTMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetrics">


        select
            c.current_position currentPosition,
            c.is_achieved_current isAchievedCurrent,
            c.is_qualified isQualified,
            c.total_qualified_count totalQualifiedCount,
            c.honors_awards honorsAwards,
            c.honors_awards_time honorsAwardsTime,
            c.congratulation_id honorId,

            m.agent_code agentCode,
            m.metrics_type metricsType,
            m.metrics_type_name metricsTypeName,
            CAST(m.metrics_value AS DECIMAL(22,2)) metricsValue,
            m.honor_code honorCode,
            s.metrics_value metricsStandard,
            s.metrics_unit metricsStandardUnit
        from honor_current_info c
                 left join honor_monthly_metrics m
                     on c.agent_code = m.agent_code and c.honor_class = m.honor_class and c.honor_code = m.honor_code
                 left join honor_standard_metrics s
                     on m.metrics_type = s.metrics_type and m.honor_class = s.honor_class and m.honor_code = s.honor_code
        where 1=1
            and m.metrics_class = '3'
            <if test="honorType != null and honorType != ''">
                and c.honor_class = #{honorType}
            </if>
            <if test="empCode != null and empCode != ''">
                and c.agent_code = #{empCode}
            </if>
            <if test="period != null and period != ''">
                and c.check_period = #{period}
            </if>

    </select>


    <select id="getQHJNXMetrics"
            resultType="com.hqins.agent.org.model.vo.HonorPeriodMetrics">


        select
            c.current_position currentPosition,
            c.is_achieved_current isAchievedCurrent,
            c.is_qualified isQualified,
            c.total_qualified_count totalQualifiedCount,
            c.honors_awards honorsAwards,
            c.honors_awards_time honorsAwardsTime,
            c.congratulation_id honorId,
            m.agent_code agentCode,
            m.metrics_type metricsType,
            m.metrics_type_name metricsTypeName,
            CAST(m.metrics_value AS DECIMAL(22,2)) AS metricsValue,
            m.honor_code honorCode,
            s.metrics_value metricsStandard,
            s.metrics_unit metricsStandardUnit
        from honor_current_info c
        left join honor_monthly_metrics m on c.agent_code = m.agent_code and c.honor_class = m.honor_class and c.honor_code = m.honor_code
        left join honor_standard_metrics s on m.metrics_type = s.metrics_type and m.honor_class = s.honor_class and m.honor_code = s.honor_code
        where 1=1
        and m.metrics_class = '3'
        <if test="honorType != null and honorType != ''">
            and c.honor_class = #{honorType}
        </if>
        <if test="empCode != null and empCode != ''">
            and c.agent_code = #{empCode}
        </if>
        <if test="period != null and period != ''">
            and c.check_period = #{period}
        </if>

    </select>



    <select id="getHonorHistory"
            resultType="com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory">

        select
        DISTINCT congratulation_id congratulationId,
        honor_code honorCode,
        agent_code agentCode,
        check_year checkYear,
        (case
            when honors_awards = '初级会员' then '琴星精英会-初级会员'
            when honors_awards = '黄金会员' then '琴星精英会-黄金会员'
            when honors_awards = '白金会员' then '琴星精英会-白金会员'
            when honors_awards = '钻石会员' then '琴星精英会-钻石会员'
            when honors_awards = '顶尖会员' then '琴星精英会-顶尖会员'
            when honors_awards = '终身会员' then '琴星精英会-终身会员'
            when check_period like '%-06H' and honors_awards = '琴韵博识轩' then concat(check_year, '上半年度游学荣誉奖')
            when check_period like '%-12H' and honors_awards = '琴韵博识轩' then concat(check_year, '下半年度游学荣誉奖')
            when honor_code = 'MDRT_MDRT' then 'MDRT普通会员'
            when honor_code = 'MDRT_COT' then 'COT内阁会员'
            when honor_code = 'MDRT_TOT' then 'TOT顶尖会员'
            else honors_awards
        end) honorsAwards,
        is_achieved_current isAchievedCurrent,
        honors_awards_time honorsAwardsTime
        from honor_assessment_history
        where 1=1
        and is_achieved_current = '1'
        <if test="empCode != null and empCode != ''">
            and agent_code = #{empCode}
        </if>

    </select>


    <select id="getHonorCongratulationList"
            resultType="com.hqins.agent.org.model.vo.HonorCongratulationVO">

        select
            congratulation_id honorId,
            honor_class honorType,
            honor_code honorCode,
            honor_name honorName,
            agent_code agentCode,
            agent_name agentName,
            honors_awards honorsAwards,
            honors_awards_time honorsAwardsTime

        from  honor_congratulation
        where 1=1
        and congratulation_message_flag = '1'
        <if test="employeeCodeList != null and employeeCodeList.size > 0">
            and agent_code in
            <foreach collection="employeeCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="honorType != null and honorType != ''">
            and honor_class = #{honorType}
        </if>
        <if test="year != null and year != ''">
            and check_year = #{year}
        </if>

    </select>



    <select id="getEmpHonoraryDetail"
            resultType="com.hqins.agent.org.model.vo.HonoraryTitleInfoVO">

        select
            congratulation_id honorId,
            agent_code empCode,
            agent_name empName,
            honors_awards awardName,
            honors_awards_time createDate

        from  honor_congratulation
        where 1=1
        <if test="honorId != null and honorId != ''">
            and congratulation_id = #{honorId}
        </if>

    </select>


</mapper>