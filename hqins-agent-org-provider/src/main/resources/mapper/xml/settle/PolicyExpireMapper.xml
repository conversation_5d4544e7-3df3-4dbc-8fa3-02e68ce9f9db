<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.settle.PolicyExpireMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.settle.PolicyExpire">
            <result property="id" column="id"/>
            <result property="orgType" column="org_type"/>
            <result property="riskname" column="riskname"/>
            <result property="riskcode" column="riskcode"/>
            <result property="contno" column="contno"/>
            <result property="appntname" column="appntname"/>
            <result property="insuredname" column="insuredname"/>
            <result property="agentname" column="agentname"/>
            <result property="agentcode" column="agentcode"/>
            <result property="cvalidate" column="cvalidate"/>
            <result property="getstartdate" column="getstartdate"/>
            <result property="balastate" column="balastate"/>
            <result property="orgcode" column="orgcode"/>
            <result property="orgname" column="orgname"/>
            <result property="insuaccbala" column="insuaccbala"/>
            <result property="bonusbala" column="bonusbala"/>
            <result property="insdalivedate" column="insdalivedate"/>
            <result property="datatime" column="datatime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,org_type,riskname,riskcode,contno,appntname,insuredname,agentname,agentcode,cvalidate,getstartdate,balastate,orgcode,orgname,insuaccbala,bonusbala,insdalivedate,datatime
    </sql>

    <insert id="insertSelective" parameterType="com.hqins.agent.org.dao.entity.settle.PolicyExpire"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into policy_expire
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgType != null">
                org_type,
            </if>
            <if test="riskname != null">
                riskname,
            </if>
            <if test="riskcode != null">
                riskcode,
            </if>
            <if test="contno != null">
                contno,
            </if>
            <if test="appntname != null">
                appntname,
            </if>
            <if test="insuredname != null">
                insuredname,
            </if>
            <if test="agentname != null">
                agentname,
            </if>
            <if test="agentcode != null">
                agentcode,
            </if>
            <if test="cvalidate != null">
                cvalidate,
            </if>
            <if test="getstartdate != null">
                getstartdate,
            </if>
            <if test="balastate != null">
                balastate,
            </if>
            <if test="orgcode != null">
                orgcode,
            </if>
            <if test="orgname != null">
                orgname,
            </if>
            <if test="insuaccbala != null">
                insuaccbala,
            </if>
            <if test="bonusbala != null">
                bonusbala,
            </if>
            <if test="insdalivedate != null">
                insdalivedate,
            </if>
            <if test="datatime != null">
                datatime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgType != null">
                #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="riskname != null">
                #{riskname,jdbcType=VARCHAR},
            </if>
            <if test="riskcode != null">
                #{riskcode,jdbcType=VARCHAR},
            </if>
            <if test="contno != null">
                #{contno,jdbcType=VARCHAR},
            </if>
            <if test="appntname != null">
                #{appntname,jdbcType=VARCHAR},
            </if>
            <if test="insuredname != null">
                #{insuredname,jdbcType=VARCHAR},
            </if>
            <if test="agentname != null">
                #{agentname,jdbcType=VARCHAR},
            </if>
            <if test="agentcode != null">
                #{agentcode,jdbcType=VARCHAR},
            </if>
            <if test="cvalidate != null">
                #{cvalidate,jdbcType=DATE},
            </if>
            <if test="getstartdate != null">
                #{getstartdate,jdbcType=DATE},
            </if>
            <if test="balastate != null">
                #{balastate,jdbcType=VARCHAR},
            </if>
            <if test="orgcode != null">
                #{orgcode,jdbcType=VARCHAR},
            </if>
            <if test="orgname != null">
                #{orgname,jdbcType=VARCHAR},
            </if>
            <if test="insuaccbala != null">
                #{insuaccbala,jdbcType=VARCHAR},
            </if>
            <if test="bonusbala != null">
                #{bonusbala,jdbcType=DECIMAL},
            </if>
            <if test="insdalivedate != null">
                #{insdalivedate,jdbcType=TIME},
            </if>
            <if test="datatime != null">
                #{datatime,jdbcType=TIME},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.hqins.agent.org.dao.entity.settle.PolicyExpire">
        update policy_expire
        <set>
            <if test="orgType != null">
                org_type = #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="riskname != null">
                riskname = #{riskname,jdbcType=VARCHAR},
            </if>
            <if test="riskcode != null">
                riskcode = #{riskcode,jdbcType=VARCHAR},
            </if>
            <if test="contno != null">
                contno = #{contno,jdbcType=VARCHAR},
            </if>
            <if test="appntname != null">
                appntname = #{appntname,jdbcType=VARCHAR},
            </if>
            <if test="insuredname != null">
                insuredname = #{insuredname,jdbcType=VARCHAR},
            </if>
            <if test="agentname != null">
                agentname = #{agentname,jdbcType=VARCHAR},
            </if>
            <if test="agentcode != null">
                agentcode = #{agentcode,jdbcType=VARCHAR},
            </if>
            <if test="cvalidate != null">
                cvalidate = #{cvalidate,jdbcType=DATE},
            </if>
            <if test="getstartdate != null">
                getstartdate = #{getstartdate,jdbcType=DATE},
            </if>
            <if test="balastate != null">
                balastate = #{balastate,jdbcType=VARCHAR},
            </if>
            <if test="orgcode != null">
                orgcode = #{orgcode,jdbcType=VARCHAR},
            </if>
            <if test="orgname != null">
                orgname = #{orgname,jdbcType=VARCHAR},
            </if>
            <if test="insuaccbala != null">
                insuaccbala = #{insuaccbala,jdbcType=VARCHAR},
            </if>
            <if test="bonusbala != null">
                bonusbala = #{bonusbala,jdbcType=DECIMAL},
            </if>
            <if test="insdalivedate != null">
                insdalivedate = #{insdalivedate,jdbcType=TIME},
            </if>
            <if test="datatime != null">
                datatime = #{datatime,jdbcType=TIME},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


</mapper>
