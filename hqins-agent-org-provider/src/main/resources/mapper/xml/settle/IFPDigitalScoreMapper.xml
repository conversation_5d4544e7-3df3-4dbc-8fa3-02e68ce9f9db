<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hqins.agent.org.dao.mapper.settle.IFPDigitalScoreMapper">


    <select id="getAllMonthAndScoreData"
            resultType="com.hqins.agent.org.model.vo.DigitalAllDataVO">
        select
            agentcode agentCode,
            action_month monthDate,
            month_points score
        from
            zh_settle.da_ifp_monthly_recordpoints
        where
            1=1
            <if test="agentCodeList != null and agentCodeList.size() > 0">
                and agentcode in
                <foreach collection="agentCodeList" item="agentCode" open="(" separator="," close=")">
                    #{agentCode}
                </foreach>
            </if>
    </select>

    <select id="getScoreDetail" resultType="com.hqins.agent.org.model.vo.DigitalScoreDetailVO">

        select
            t2.agentcode agentCode,
            t2.agentname agentName,
            t2.action_type actionType,
            t2.action_time actionTime,
            t1.action_month actionMonth,
            t2.saleteamcode teamCode,
            t2.saleteamname teamName,
            t1.month_points score,
            t2.is_recordpoints recordPointsFlag
        from
            zh_settle.da_ifp_monthly_recordpoints t1
        left join
            zh_settle.mid_ifp_action_detail t2
        on t1.agentcode = t2.agentcode and t1.action_month = DATE_FORMAT(t2.action_time,'%Y-%m')
        where
        1=1
        <if test="agentCodeList != null and agentCodeList.size() > 0">
            and t1.agentcode in
            <foreach collection="agentCodeList" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
        </if>

    </select>

    <select id="getAllMonthAndScoreDataByAgentCode" resultType="com.hqins.agent.org.model.vo.DigitalDataVO">

        select
            action_month monthDate,
            month_points score
        from
            zh_settle.da_ifp_monthly_recordpoints
        where
            agentcode = #{agentCode}
            and action_month >= #{startDate}
    </select>
    <select id="getMonthAndScoreByAgentCodeList" resultType="com.hqins.agent.org.model.vo.DigitalScoreCodeVO">
        select
            agentcode agentCode,
            month_points score
        from
            zh_settle.da_ifp_monthly_recordpoints
        where
        action_month = #{queryDate}
        and agentcode in
            <foreach collection="agentCodeList" item="agentCode" open="(" separator="," close=")">
                #{agentCode}
            </foreach>
    </select>

    <select id="getScoreDetailByAgentCodeList" resultType="com.hqins.agent.org.model.vo.DigitalScoreDetailVO">

        SELECT agentCode, agentName, actionType, actionTime, actionMonth, teamCode, teamName, score, recordPointsFlag
        FROM (
        SELECT
        t2.agentcode AS agentCode,
        t2.agentname AS agentName,
        t2.action_type AS actionType,
        t2.action_time AS actionTime,
        t1.action_month AS actionMonth,
        t2.saleteamcode AS teamCode,
        t2.saleteamname AS teamName,
        t1.month_points AS score,
        t2.is_recordpoints AS recordPointsFlag,
        ROW_NUMBER() OVER(PARTITION BY t2.agentcode ORDER BY t2.action_time) AS row_num
        FROM zh_settle.da_ifp_monthly_recordpoints t1
        LEFT JOIN zh_settle.mid_ifp_action_detail t2
        ON t1.agentcode = t2.agentcode AND t1.action_month = DATE_FORMAT(t2.action_time,'%Y-%m')
        WHERE t1.action_month = #{queryDate}
                and t1.agentcode in
                <foreach collection="agentCodeList" item="agentCode" open="(" separator="," close=")">
                    #{agentCode}
                </foreach>
        ) AS ranked_data
        WHERE row_num <![CDATA[<=]]> 1000;
    </select>
</mapper>