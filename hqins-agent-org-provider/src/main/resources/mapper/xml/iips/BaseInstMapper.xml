<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.iips.BaseInstMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.iips.BaseInst">
        <result property="instId" column="INST_ID"/>
        <result property="instName" column="INST_NAME"/>
        <result property="instCode" column="INST_CODE"/>
        <result property="instProvince" column="INST_PROVINCE"/>
        <result property="instProvincename" column="INST_PROVINCENAME"/>
        <result property="instCity" column="INST_CITY"/>
        <result property="instCityname" column="INST_CITYNAME"/>
        <result property="channelorgcode" column="CHANNELORGCODE"/>
        <result property="channelorgname" column="CHANNELORGNAME"/>
        <result property="orgtype" column="ORGTYPE"/>
        <result property="instSmpName" column="INST_SMP_NAME"/>
        <result property="instSmpEnName" column="INST_SMP_EN_NAME"/>
        <result property="parentInstId" column="PARENT_INST_ID"/>
        <result property="parentInstCode" column="PARENT_INST_CODE"/>
        <result property="parentInstName" column="PARENT_INST_NAME"/>
        <result property="companyid" column="COMPANYID"/>
        <result property="address" column="ADDRESS"/>
        <result property="zip" column="ZIP"/>
        <result property="tel" column="TEL"/>
        <result property="fax" column="FAX"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="createuser" column="CREATEUSER"/>
        <result property="modifytime" column="MODIFYTIME"/>
        <result property="modifyuser" column="MODIFYUSER"/>
        <result property="createname" column="CREATENAME"/>
        <result property="modifyname" column="MODIFYNAME"/>
        <result property="islocked" column="ISLOCKED"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="instRegion" column="INST_REGION"/>
        <result property="instRegionname" column="INST_REGIONNAME"/>
        <result property="email" column="EMAIL"/>
        <result property="mailboxdns" column="MAILBOXDNS"/>
        <result property="manageorgcode" column="MANAGEORGCODE"/>
        <result property="manageorgname" column="MANAGEORGNAME"/>
        <result property="instPath" column="INST_PATH"/>
        <result property="instLevel" column="INST_LEVEL"/>
        <result property="isHead" column="IS_HEAD"/>
        <result property="partnercode" column="PARTNERCODE"/>
        <result property="instEnName" column="INST_EN_NAME"/>
        <result property="leaderName" column="LEADER_NAME"/>
        <result property="leaderPhone" column="LEADER_PHONE"/>
        <result property="contactName" column="CONTACT_NAME"/>
        <result property="contactPhone" column="CONTACT_PHONE"/>
        <result property="enterpriseName" column="ENTERPRISE_NAME"/>
        <result property="taxpayeridno" column="TAXPAYERIDNO"/>
        <result property="enterpriseAddress" column="ENTERPRISE_ADDRESS"/>
        <result property="enterpriseTel" column="ENTERPRISE_TEL"/>
        <result property="bankname" column="BANKNAME"/>
        <result property="bankaccountNo" column="BANKACCOUNT_NO"/>
        <result property="bankaccountName" column="BANKACCOUNT_NAME"/>
        <result property="invoicebankname" column="INVOICEBANKNAME"/>
        <result property="invoiceType" column="INVOICE_TYPE"/>
        <result property="taxRate" column="TAX_RATE"/>
        <result property="costCenter" column="COST_CENTER"/>
        <result property="instClass" column="INST_CLASS"/>
        <result property="isvirtual" column="isvirtual"/>
        <result property="instRank" column="INST_RANK"/>
        <result property="bankoutletscode" column="BANKOUTLETSCODE"/>
        <result property="instTypeFlag" column="INST_TYPE_FLAG"/>
        <result property="instAgencyLevel" column="INST_AGENCY_LEVEL"/>
        <result property="instAgencyClass" column="INST_AGENCY_CLASS"/>
        <result property="instLicenseCode" column="INST_LICENSE_CODE"/>
        <result property="licenceStartDate" column="LICENCE_START_DATE"/>
        <result property="licenceEndDate" column="LICENCE_END_DATE"/>
        <result property="closureFlag" column="CLOSURE_FLAG"/>
        <result property="accountingStatus" column="ACCOUNTING_STATUS"/>
        <result property="instEstablishDate" column="INST_ESTABLISH_DATE"/>
        <result property="branchtype" column="BRANCHTYPE"/>
        <result property="uniformSocialCreditCode" column="UNIFORM_SOCIAL_CREDIT_CODE"/>
        <result property="canSold" column="CAN_SOLD"/>
        <result property="instLicenseName" column="INST_LICENSE_NAME"/>
        <result property="cooperationStarttime" column="COOPERATION_STARTTIME"/>
        <result property="cooperationEndtime" column="COOPERATION_ENDTIME"/>
        <result property="isworkflow" column="ISWORKFLOW"/>
        <result property="instStatus" column="INST_STATUS"/>
        <result property="distributionchannelflag" column="DISTRIBUTIONCHANNELFLAG"/>
        <result property="physicalStation" column="PHYSICAL_STATION"/>
        <result property="businessScope" column="BUSINESS_SCOPE"/>
        <result property="businessArea" column="BUSINESS_AREA"/>
        <result property="illegatRecord" column="ILLEGAT_RECORD"/>
        <result property="instBusinesLicenseCode" column="INST_BUSINES_LICENSE_CODE"/>
        <result property="instBusinesLicenseName" column="INST_BUSINES_LICENSE_NAME"/>
        <result property="bucode" column="BUCODE"/>
        <result property="buname" column="BUNAME"/>
        <result property="assessCode" column="ASSESS_CODE"/>
        <result property="instLicenseJson" column="INST_LICENSE_JSON"/>
        <result property="cooperationStatus" column="COOPERATION_STATUS"/>
    </resultMap>

    <sql id="Base_Column_List">
        `INST_ID`,`INST_NAME`,`INST_CODE`,`INST_PROVINCE`,`INST_PROVINCENAME`,`INST_CITY`,`INST_CITYNAME`,`CHANNELORGCODE`,`CHANNELORGNAME`,`ORGTYPE`,`INST_SMP_NAME`,`INST_SMP_EN_NAME`,`PARENT_INST_ID`,`PARENT_INST_CODE`,`PARENT_INST_NAME`,`COMPANYID`,`ADDRESS`,`ZIP`,`TEL`,`FAX`,`CREATE_TIME`,`CREATEUSER`,`MODIFYTIME`,`MODIFYUSER`,`CREATENAME`,`MODIFYNAME`,`ISLOCKED`,`START_DATE`,`END_DATE`,`DESCRIPTION`,`INST_REGION`,`INST_REGIONNAME`,`EMAIL`,`MAILBOXDNS`,`MANAGEORGCODE`,`MANAGEORGNAME`,`INST_PATH`,`INST_LEVEL`,`IS_HEAD`,`PARTNERCODE`,`INST_EN_NAME`,`LEADER_NAME`,`LEADER_PHONE`,`CONTACT_NAME`,`CONTACT_PHONE`,`ENTERPRISE_NAME`,`TAXPAYERIDNO`,`ENTERPRISE_ADDRESS`,`ENTERPRISE_TEL`,`BANKNAME`,`BANKACCOUNT_NO`,`BANKACCOUNT_NAME`,`INVOICEBANKNAME`,`INVOICE_TYPE`,`TAX_RATE`,`COST_CENTER`,`INST_CLASS`,`isvirtual`,`INST_RANK`,`BANKOUTLETSCODE`,`INST_TYPE_FLAG`,`INST_AGENCY_LEVEL`,`INST_AGENCY_CLASS`,`INST_LICENSE_CODE`,`LICENCE_START_DATE`,`LICENCE_END_DATE`,`CLOSURE_FLAG`,`ACCOUNTING_STATUS`,`INST_ESTABLISH_DATE`,`BRANCHTYPE`,`UNIFORM_SOCIAL_CREDIT_CODE`,`CAN_SOLD`,`INST_LICENSE_NAME`,`COOPERATION_STARTTIME`,`COOPERATION_ENDTIME`,`ISWORKFLOW`,`INST_STATUS`,`DISTRIBUTIONCHANNELFLAG`,`PHYSICAL_STATION`,`BUSINESS_SCOPE`,`BUSINESS_AREA`,`ILLEGAT_RECORD`,`INST_BUSINES_LICENSE_CODE`,`INST_BUSINES_LICENSE_NAME`,`BUCODE`,`BUNAME`,`ASSESS_CODE`,`INST_LICENSE_JSON`,`COOPERATION_STATUS`
    </sql>
    <sql id="Column_List">
        b.INST_ID as id,b.INST_CODE as code,b.INST_NAME as name,b.INST_STATUS as status,
        b.PARENT_INST_CODE as parentCode,b.PARENT_INST_NAME as parentName,
        b.CAN_SOLD as canSold,
        b.LEADER_NAME as leader,b.PHYSICAL_STATION as physicalStation,
        t.companycode as topCode,t.companyname as topName,t.CHANNEL_TYPE as channelType
    </sql>
    <sql id="Column_Insure_List">
        b.INST_ID as id,b.INST_CODE as code,b.INST_NAME as name,b.INST_STATUS as status,
        b.PARENT_INST_CODE as parentCode,b.PARENT_INST_NAME as parentName,
        b.MANAGEORGCODE as manageOrgCode,b.BANKOUTLETSCODE as bankOutletsCode,
        t.companycode as topCode,t.companyname as topName,t.CHANNEL_TYPE as channelType
    </sql>

    <select id="listPage" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  t.cptype=#{cptype}

        <if test="containsSuperAdmin != true">
            and b.INST_CODE in
            <foreach collection="dataAccessOrgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="quest.topCode != null and quest.topCode != ''">
            and t.companycode = #{quest.topCode}
        </if>
        <if test="quest.code != null and quest.code != ''">
            and b.INST_CODE = #{quest.code}
        </if>
        <if test="quest.name != null and quest.name != ''">
            and b.INST_NAME like CONCAT('%',#{quest.name}, '%')
        </if>
        <if test="quest.status == 'ENABLED'">
            and b.INST_STATUS = 1
        </if>
        order by b.INST_CODE ASC
    </select>

    <select id="list" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  t.cptype=#{cptype}
        <if test="containsSuperAdmin != true">
            and b.INST_CODE in
            <foreach collection="dataAccessOrgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="quest.topCode != null and quest.topCode != ''">
            and t.companycode = #{quest.topCode}
        </if>
        <if test="quest.code != null and quest.code != ''">
            and b.INST_CODE = #{quest.code}
        </if>
        <if test="quest.name != null and quest.name != ''">
            and b.INST_NAME like CONCAT('%',#{quest.name}, '%')
        </if>
        <if test="quest.status == 'ENABLED'">
            and b.INST_STATUS = 1
        </if>
        order by b.INST_CODE ASC limit 0,#{quest.size}
    </select>

    <select id="listAll" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  t.cptype=#{cptype}
        <if test="quest.topCode != null and quest.topCode != ''">
            and t.companycode = #{quest.topCode}
        </if>
        <if test="quest.parentCode != null and quest.parentCode != ''">
            <if test="quest.parentCode == 'ISNULL'">
                and b.PARENT_INST_CODE is null
            </if>
            <if test="quest.parentCode != 'ISNULL'">
                and b.PARENT_INST_CODE = #{quest.parentCode}
            </if>
        </if>
        <if test="quest.code != null and quest.code != ''">
            and b.INST_CODE = #{quest.code}
        </if>
        <if test="quest.name != null and quest.name != ''">
            and b.INST_NAME like CONCAT('%',#{quest.name}, '%')
        </if>
        <if test="quest.status == 'ENABLED'">
            and b.INST_STATUS = 1
        </if>
        order by b.INST_CODE ASC limit 0,#{quest.size}
    </select>

    <select id="listByTopCodes" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where b.INST_STATUS = 1
        and t.companycode in
        <foreach collection="topCodes" item="id"  open="(" separator="," close=")">
            #{id}
        </foreach>
        order by b.INST_CODE ASC
    </select>

    <select id="listByOrgCodes" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where b.INST_STATUS = 1
        and b.INST_CODE in
        <foreach collection="orgCodes" item="id"  open="(" separator="," close=")">
            #{id}
        </foreach>
        order by b.INST_CODE ASC
    </select>

    <select id="listAllChild" resultType="map">
        select
        <include refid="Column_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  b.INST_STATUS = 1 and t.cptype=#{cptype}
          and b.PARENT_INST_CODE like CONCAT(#{parentCode}, '%')
        order by b.INST_CODE ASC
    </select>

    <select id="selectInsureOrgsByCodes" resultType="map">
        select
        <include refid="Column_Insure_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  b.INST_STATUS = 1 and t.cptype=#{cptype}
        and b.INST_CODE in
        <foreach collection="orgCodes" item="id"  open="(" separator="," close=")">
            #{id}
        </foreach>
        order by b.INST_CODE ASC
    </select>

    <select id="selectInsureOrgsAll" resultType="map">
        select
        <include refid="Column_Insure_List"/>
        from u_base_inst b inner join tbepartner t on b.companyid = t.companyid
        where  b.INST_STATUS = 1 and t.cptype=#{cptype}
        <if test="quest.topCode != null and quest.topCode != ''">
            and t.companycode = #{quest.topCode}
        </if>
        <if test="quest.topName != null and quest.topName != ''">
            and t.companyname like CONCAT('%',#{quest.topName}, '%')
        </if>
        <if test="quest.code != null and quest.code != ''">
            and b.INST_CODE = #{quest.code}
        </if>
        <if test="quest.name != null and quest.name != ''">
            and b.INST_NAME like CONCAT('%',#{quest.name}, '%')
        </if>
        <if test="quest.orgLevels != null and quest.orgLevels.size()>0">
            and  b.INST_LEVEL in
            <foreach collection="quest.orgLevels" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by b.INST_CODE ASC
    </select>

    <select id="queryInstByOrgCode" resultType="com.hqins.agent.org.dao.entity.iips.BaseInst">
        select *
        from u_base_inst where INST_CODE = #{orgCode}
    </select>
    <select id="queryAll" resultType="com.hqins.agent.org.model.vo.QueryAllVO">
        SELECT t5.companycode companyCode,t5.companyname companyName ,t4.INST_CODE companyInstCode ,
               t4.INST_NAME  companyInstName ,t2.companycode  merchantCode,t2.companyname merchantName,
               t1.INST_CODE  merchantOrgCode ,t1.INST_NAME  merchantOrgName,t1.address,t1.INST_STATUS status
        from zh_iips.u_base_inst t1
                 LEFT JOIN zh_iips.tbepartner t2 on t1.COMPANYID=t2.companyid
                 LEFT JOIN zh_exms.tbpartassignmanager t3 on t1.INST_CODE = t3.MERCHANT_ORG_CODE
                 LEFT JOIN zh_exms.tbemp t4 on t3.CUST_MANAGER_CODE = t4.empcode
                 LEFT JOIN zh_iips.tbepartner  t5 on t5.companycode=t4.companycode
        WHERE t1.INST_TYPE_FLAG = '1'

    </select>

    <select id="queryThreeMangerCodeByOrgCode" resultType="map">
        SELECT
            t4.INST_CODE as threeMangerCode,
            t4.INST_NAME as threeMangerName
        FROM (
                 SELECT MANAGEORGCODE
                 FROM zh_iips.u_base_inst
                 WHERE INST_CODE = #{orgCode}
                     LIMIT 1
             ) t1
                 JOIN zh_iips.u_base_inst t2
                      ON t1.MANAGEORGCODE = t2.INST_CODE
                 JOIN zh_iips.u_base_inst t3
                      ON t2.PARENT_INST_CODE = t3.INST_CODE
                 JOIN zh_iips.u_base_inst t4
                      ON t3.PARENT_INST_CODE = t4.INST_CODE
    </select>

</mapper>
