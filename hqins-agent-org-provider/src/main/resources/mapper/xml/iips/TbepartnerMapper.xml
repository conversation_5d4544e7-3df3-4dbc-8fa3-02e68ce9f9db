<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.iips.TbepartnerMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.iips.Tbepartner">
        <result property="companyid" column="companyid"/>
        <result property="partnercode" column="partnercode"/>
        <result property="companycode" column="companycode"/>
        <result property="companyname" column="companyname"/>
        <result property="companychannelcode" column="companychannelcode"/>
        <result property="companyshortname" column="companyshortname"/>
        <result property="companyenname" column="companyenname"/>
        <result property="companyenshortname" column="companyenshortname"/>
        <result property="companytype" column="companytype"/>
        <result property="mainaddress" column="mainaddress"/>
        <result property="mainzipcode" column="mainzipcode"/>
        <result property="websitejson" column="websitejson"/>
        <result property="emailjson" column="emailjson"/>
        <result property="faxjson" column="faxjson"/>
        <result property="telephonejson" column="telephonejson"/>
        <result property="countrycode" column="countrycode"/>
        <result property="citycode" column="citycode"/>
        <result property="provincecode" column="provincecode"/>
        <result property="areacode" column="areacode"/>
        <result property="crop" column="crop"/>
        <result property="logo" column="logo"/>
        <result property="proxyband" column="proxyband"/>
        <result property="activestatus" column="activestatus"/>
        <result property="comanystatus" column="comanystatus"/>
        <result property="bankaccountno" column="bankaccountno"/>
        <result property="bankaccountname" column="bankaccountname"/>
        <result property="bankname" column="bankname"/>
        <result property="cnapscode" column="CNAPSCODE"/>
        <result property="taxpayeridno" column="TAXPAYERIDNO"/>
        <result property="invoicetitle" column="INVOICETITLE"/>
        <result property="invoiceaddress" column="INVOICEADDRESS"/>
        <result property="invoicephone" column="INVOICEPHONE"/>
        <result property="invoicebankname" column="INVOICEBANKNAME"/>
        <result property="invoicebankaccountno" column="INVOICEBANKACCOUNTNO"/>
        <result property="imagelistjons" column="IMAGELISTJONS"/>
        <result property="applystate" column="APPLYSTATE"/>
        <result property="vertifymessage" column="VERTIFYMESSAGE"/>
        <result property="createtime" column="createtime"/>
        <result property="createuser" column="CREATEUSER"/>
        <result property="modifytime" column="MODIFYTIME"/>
        <result property="modifyuser" column="MODIFYUSER"/>
        <result property="createname" column="CREATENAME"/>
        <result property="modifyname" column="MODIFYNAME"/>
        <result property="cityname" column="cityname"/>
        <result property="provincename" column="provincename"/>
        <result property="areaname" column="areaname"/>
        <result property="cropname" column="cropname"/>
        <result property="legalPerson" column="LEGAL_PERSON"/>
        <result property="legalPersonPhone" column="LEGAL_PERSON_PHONE"/>
        <result property="businessScope" column="BUSINESS_SCOPE"/>
        <result property="businessLicense" column="BUSINESS_LICENSE"/>
        <result property="contactName" column="CONTACT_NAME"/>
        <result property="contactPhone" column="CONTACT_PHONE"/>
        <result property="invoiceType" column="INVOICE_TYPE"/>
        <result property="taxRate" column="TAX_RATE"/>
        <result property="channelRela" column="CHANNEL_RELA"/>
        <result property="channelType" column="CHANNEL_TYPE"/>
        <result property="parentCompanyId" column="PARENT_COMPANY_ID"/>
        <result property="parentCompanyName" column="PARENT_COMPANY_NAME"/>
        <result property="stateChangeReason" column="STATE_CHANGE_REASON"/>
        <result property="disabledDate" column="DISABLED_DATE"/>
        <result property="reenabledDate" column="REENABLED_DATE"/>
        <result property="companypath" column="companypath"/>
        <result property="managerinst" column="managerinst"/>
        <result property="managerinstcode" column="managerinstcode"/>
        <result property="cptype" column="CPTYPE"/>
        <result property="channelRank" column="CHANNEL_RANK"/>
        <result property="referrerName" column="REFERRER_NAME"/>
        <result property="raiseCompanycode" column="RAISE_COMPANYCODE"/>
        <result property="raiseCompanyname" column="RAISE_COMPANYNAME"/>
        <result property="partyGrade" column="PARTY_GRADE"/>
        <result property="cooperationStarttime" column="COOPERATION_STARTTIME"/>
        <result property="cooperationEndtime" column="COOPERATION_ENDTIME"/>
        <result property="initialCapital" column="INITIAL_CAPITAL"/>
        <result property="intermediaryLicenceNo" column="INTERMEDIARY_LICENCE_NO"/>
        <result property="intermediaryLicenceStartdate" column="INTERMEDIARY_LICENCE_STARTDATE"/>
        <result property="canSold" column="CAN_SOLD"/>
        <result property="intermediaryLicenceEnddate" column="INTERMEDIARY_LICENCE_ENDDATE"/>
        <result property="twostageBaseline" column="TWOSTAGE_BASELINE"/>
        <result property="canSoldIsinternet" column="CAN_SOLD_ISINTERNET"/>
        <result property="businesstype" column="BUSINESSTYPE"/>
        <result property="bucode" column="BUCODE"/>
        <result property="buname" column="BUNAME"/>
    </resultMap>

    <sql id="Base_Column_List">
        `companyid`,`partnercode`,`companycode`,`companyname`,`companychannelcode`,`companyshortname`,`companyenname`,`companyenshortname`,`companytype`,`mainaddress`,`mainzipcode`,`websitejson`,`emailjson`,`faxjson`,`telephonejson`,`countrycode`,`citycode`,`provincecode`,`areacode`,`crop`,`logo`,`proxyband`,`activestatus`,`comanystatus`,`bankaccountno`,`bankaccountname`,`bankname`,`CNAPSCODE`,`TAXPAYERIDNO`,`INVOICETITLE`,`INVOICEADDRESS`,`INVOICEPHONE`,`INVOICEBANKNAME`,`INVOICEBANKACCOUNTNO`,`IMAGELISTJONS`,`APPLYSTATE`,`VERTIFYMESSAGE`,`createtime`,`CREATEUSER`,`MODIFYTIME`,`MODIFYUSER`,`CREATENAME`,`MODIFYNAME`,`cityname`,`provincename`,`areaname`,`cropname`,`LEGAL_PERSON`,`LEGAL_PERSON_PHONE`,`BUSINESS_SCOPE`,`BUSINESS_LICENSE`,`CONTACT_NAME`,`CONTACT_PHONE`,`INVOICE_TYPE`,`TAX_RATE`,`CHANNEL_RELA`,`CHANNEL_TYPE`,`PARENT_COMPANY_ID`,`PARENT_COMPANY_NAME`,`STATE_CHANGE_REASON`,`DISABLED_DATE`,`REENABLED_DATE`,`companypath`,`managerinst`,`managerinstcode`,`CPTYPE`,`CHANNEL_RANK`,`REFERRER_NAME`,`RAISE_COMPANYCODE`,`RAISE_COMPANYNAME`,`PARTY_GRADE`,`COOPERATION_STARTTIME`,`COOPERATION_ENDTIME`,`INITIAL_CAPITAL`,`INTERMEDIARY_LICENCE_NO`,`INTERMEDIARY_LICENCE_STARTDATE`,`CAN_SOLD`,`INTERMEDIARY_LICENCE_ENDDATE`,`TWOSTAGE_BASELINE`,`CAN_SOLD_ISINTERNET`,`BUSINESSTYPE`,`BUCODE`,`BUNAME`
    </sql>

    <select id="selectInstListByCompanyCode" parameterType = "map" resultType="String">
        select
            DISTINCT t.INST_CODE
        from zh_iips.tbepartner e
        inner JOIN zh_iips.u_base_inst t ON e.companyid = t.COMPANYID and t.INST_STATUS = 1
        where e.companycode in
        <foreach collection="topCodeList" item="topCode" open="(" separator="," close=")">
            #{topCode}
        </foreach>
    </select>


</mapper>
