package com.hqins.agent.org;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import com.hqins.agent.org.model.vo.TeamVO;
import com.hqins.agent.org.utils.PasswordUtil;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14
 * @Description
 */
public class PartnerSalesmanServiceTest {
    @Test
    public void test2(){

        System.out.println(PhoneUtil.hideBetween("13333333333333").toString());
        System.out.println(PhoneUtil.hideBetween("13333333333").toString());
        System.out.println(PhoneUtil.hideBetween("1333333").toString());
        System.out.println(PhoneUtil.hideBetween("133333").toString());
        System.out.println(PhoneUtil.hideBetween("13").toString());
        System.out.println(PhoneUtil.hideBetween("").toString());
    }


    @Test
    public void test(){
//        //CPU核数
//        System.out.println(Runtime.getRuntime().availableProcessors());


//        if (!((CollectionUtils.isNotEmpty(employeeList) && employeeList.size() == 1) && (CollectionUtils.isNotEmpty(tbempList) && tbempList.size() == 1))) {
//            checkVo.setSuccess(false);
//            checkVo.setDescription("同一职业证号不能同时开多个账号，请确认;");
//            return JSONUtil.toJsonPrettyStr(checkVo);
//        }

    }

    @Test
    public void test3(){
        System.out.println(PasswordUtil.generatePwd("111111"));
    }



}