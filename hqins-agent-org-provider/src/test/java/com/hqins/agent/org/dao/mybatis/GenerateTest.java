package com.hqins.agent.org.dao.mybatis;

import com.google.common.collect.Sets;
import com.hqins.common.generator.CodeGenerator;
import com.hqins.common.generator.config.GenerateConfig;
import org.junit.Test;

import java.io.File;

import static org.junit.Assert.assertNull;


/**
 * <AUTHOR>
 * @date 2021/4/6
 * @Description
 */
public class GenerateTest {


    @Test
    public void testCodeGenerate() {
        //工作空间

        GenerateConfig generateConfig = new GenerateConfig();
        //作者
        generateConfig.setAuthor(System.getProperty("user.name"));
        //最顶层包名
        generateConfig.setBasePackage("com.hqins.agent.org");
        //工程名-默认取artifactId
        generateConfig.setProjectName("hqins-agent-org");
        //当前服务注册在nacos中的服务名-默认取artifactId
        generateConfig.setServiceNameRegisterInNacos("hqins-agent-org");
        String thisPath = new File(new File(this.getClass().getClassLoader().getResource("").getPath()).getParent()).getParent();
        //如果项目结构不是-provider和-api，请自行修改路径
        generateConfig.setProviderRootPath(thisPath);
        generateConfig.setApiRootPath(thisPath.replace("provider","api"));

//        //provider模块的根路径
//        generateConfig.setProviderRootPath(WORKSPACE+"\\"+generateConfig.getProjectName()+"\\"+generateConfig.getProjectName()+"-provider");
//        //api模块的根路径
//        generateConfig.setApiRootPath(WORKSPACE+"\\"+generateConfig.getProjectName()+"\\"+generateConfig.getProjectName()+"-api");

        /**
         * jdbc连接参数,每次生成前，请根据各个项目实际情况，自己更新jdbc连接参数之后再生成
         */
        generateConfig.setJdbcDriverClassName("com.mysql.jdbc.Driver");
//        generateConfig.setJdbcUrl("******************************************************************************************************************************************************************");
//        generateConfig.setJdbcUsername("root");
//        generateConfig.setJdbcPassword("123456");
        generateConfig.setJdbcUrl("****************************************************************************************************************************************************************************************************");
        generateConfig.setJdbcUsername("u_newsale_w");
        generateConfig.setJdbcPassword("mw1EjNw4LDzO");
        generateConfig.setContextPath("/zh_settle");

        generateConfig.setFilterTableNames(Sets.newHashSet());

        //表名前缀，比如t_user，那么该参数设置为t_，这样生成的实体类就是User，而不是TUser了。
        generateConfig.setTableNamePrefix("");

        /**
         * 哪些表需要生成对应的CRUD代码，包含api,request,vo,controller, service,serviceImpl
         * 如果不指定的话，默认只会生成entity, mapper, mapper.xml
         * 生成策略：只有entity重新生成时，才会根据最新的表结构替换掉原来的entity类
         * 其它的代码，如mapper, mapper.xml, service等，如果之前已经存在已经生成的代码文件，就不会重复覆盖生成。
         */
        generateConfig.setCrudRequiredTableNames(Sets.newHashSet());

        CodeGenerator generator = new CodeGenerator();
        generateConfig.setTableNames(Sets.newHashSet("policy_expire"));
        generator.generateAll(generateConfig);
        //解决sonar扫描问题
        assertNull(null);
//        generator.generate(generateConfig, Lists.newArrayList("channel_dealer"));

    }

    @Test
    public void testCodeGenerateForNewSale() {
        //工作空间

        GenerateConfig generateConfig = new GenerateConfig();
        //作者
        generateConfig.setAuthor(System.getProperty("user.name"));
        //最顶层包名
        generateConfig.setBasePackage("com.hqins.agent.org");
        //工程名-默认取artifactId
        generateConfig.setProjectName("hqins-agent-org");
        //当前服务注册在nacos中的服务名-默认取artifactId
        generateConfig.setServiceNameRegisterInNacos("hqins-agent-org");
        String thisPath = new File(new File(this.getClass().getClassLoader().getResource("").getPath()).getParent()).getParent();
        //如果项目结构不是-provider和-api，请自行修改路径
        generateConfig.setProviderRootPath(thisPath);
        generateConfig.setApiRootPath(thisPath.replace("provider","api"));

//        //provider模块的根路径
//        generateConfig.setProviderRootPath(WORKSPACE+"\\"+generateConfig.getProjectName()+"\\"+generateConfig.getProjectName()+"-provider");
//        //api模块的根路径
//        generateConfig.setApiRootPath(WORKSPACE+"\\"+generateConfig.getProjectName()+"\\"+generateConfig.getProjectName()+"-api");

        /**
         * jdbc连接参数,每次生成前，请根据各个项目实际情况，自己更新jdbc连接参数之后再生成
         */
        generateConfig.setJdbcDriverClassName("com.mysql.jdbc.Driver");
        generateConfig.setJdbcUrl("**********************************************************************************");
        generateConfig.setJdbcUsername("u_newsale_r");
        generateConfig.setJdbcPassword("ydwf12356aA!");
//        generateConfig.setContextPath("/insurance-goods");
//        generateConfig.setJdbcUrl("**********************************************************************************************");
//        generateConfig.setJdbcUsername("org_uat");
//        generateConfig.setJdbcPassword("Hqins@66");
        generateConfig.setFilterTableNames(Sets.newHashSet());

        //表名前缀，比如t_user，那么该参数设置为t_，这样生成的实体类就是User，而不是TUser了。
        generateConfig.setTableNamePrefix("");

        /**
         * 哪些表需要生成对应的CRUD代码，包含api,request,vo,controller, service,serviceImpl
         * 如果不指定的话，默认只会生成entity, mapper, mapper.xml
         * 生成策略：只有entity重新生成时，才会根据最新的表结构替换掉原来的entity类
         * 其它的代码，如mapper, mapper.xml, service等，如果之前已经存在已经生成的代码文件，就不会重复覆盖生成。
         */
        generateConfig.setCrudRequiredTableNames(Sets.newHashSet());

        CodeGenerator generator = new CodeGenerator();
        generateConfig.setTableNames(Sets.newHashSet("channel_employee_detail"));
        generator.generateAll(generateConfig);
        //解决sonar扫描问题
        assertNull(null);
//        generator.generate(generateConfig, Lists.newArrayList("channel_dealer"));

    }
}
