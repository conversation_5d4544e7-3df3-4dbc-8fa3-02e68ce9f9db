package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EmployeeLogServiceImpl 测试类
 * 主要测试根据团队等级取最高团队等级的功能
 */
@SpringBootTest
class EmployeeLogServiceImplTest {

    private EmployeeLogServiceImpl employeeLogService;

    @Test
    void testGetHighestLevelTeam_正常情况() {
        // 准备测试数据
        List<Tbsaleteam> teamList = new ArrayList<>();
        
        // 创建不同等级的团队
        Tbsaleteam team1 = new Tbsaleteam();
        team1.setSaleteamcode("T001");
        team1.setSaleteamname("营业组A");
        team1.setTeamlevel("01"); // 营业组
        
        Tbsaleteam team2 = new Tbsaleteam();
        team2.setSaleteamcode("T002");
        team2.setSaleteamname("营业部B");
        team2.setTeamlevel("02"); // 营业部
        
        Tbsaleteam team3 = new Tbsaleteam();
        team3.setSaleteamcode("T003");
        team3.setSaleteamname("营业区C");
        team3.setTeamlevel("03"); // 营业区
        
        teamList.add(team1);
        teamList.add(team2);
        teamList.add(team3);
        
        // 调用私有方法进行测试
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", teamList);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("03", result.getTeamlevel()); // 应该返回等级最高的团队（营业区）
        assertEquals("T003", result.getSaleteamcode());
        assertEquals("营业区C", result.getSaleteamname());
    }

    @Test
    void testGetHighestLevelTeam_空列表() {
        List<Tbsaleteam> teamList = new ArrayList<>();
        
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", teamList);
        
        assertNull(result);
    }

    @Test
    void testGetHighestLevelTeam_null列表() {
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", (List<Tbsaleteam>) null);
        
        assertNull(result);
    }

    @Test
    void testGetHighestLevelTeam_包含null等级() {
        List<Tbsaleteam> teamList = new ArrayList<>();
        
        Tbsaleteam team1 = new Tbsaleteam();
        team1.setSaleteamcode("T001");
        team1.setSaleteamname("团队A");
        team1.setTeamlevel(null); // null等级
        
        Tbsaleteam team2 = new Tbsaleteam();
        team2.setSaleteamcode("T002");
        team2.setSaleteamname("营业部B");
        team2.setTeamlevel("02"); // 营业部
        
        teamList.add(team1);
        teamList.add(team2);
        
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", teamList);
        
        assertNotNull(result);
        assertEquals("02", result.getTeamlevel());
        assertEquals("T002", result.getSaleteamcode());
    }

    @Test
    void testGetHighestLevelTeam_包含空字符串等级() {
        List<Tbsaleteam> teamList = new ArrayList<>();
        
        Tbsaleteam team1 = new Tbsaleteam();
        team1.setSaleteamcode("T001");
        team1.setSaleteamname("团队A");
        team1.setTeamlevel(""); // 空字符串等级
        
        Tbsaleteam team2 = new Tbsaleteam();
        team2.setSaleteamcode("T002");
        team2.setSaleteamname("营业组B");
        team2.setTeamlevel("01"); // 营业组
        
        teamList.add(team1);
        teamList.add(team2);
        
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", teamList);
        
        assertNotNull(result);
        assertEquals("01", result.getTeamlevel());
        assertEquals("T002", result.getSaleteamcode());
    }

    @Test
    void testGetHighestLevelTeam_相同等级() {
        List<Tbsaleteam> teamList = new ArrayList<>();
        
        Tbsaleteam team1 = new Tbsaleteam();
        team1.setSaleteamcode("T001");
        team1.setSaleteamname("营业部A");
        team1.setTeamlevel("02");
        
        Tbsaleteam team2 = new Tbsaleteam();
        team2.setSaleteamcode("T002");
        team2.setSaleteamname("营业部B");
        team2.setTeamlevel("02");
        
        teamList.add(team1);
        teamList.add(team2);
        
        Tbsaleteam result = (Tbsaleteam) ReflectionTestUtils.invokeMethod(
                employeeLogService, "getHighestLevelTeam", teamList);
        
        assertNotNull(result);
        assertEquals("02", result.getTeamlevel());
        // 相同等级时，应该返回其中一个（具体哪个取决于Stream的实现）
        assertTrue(result.getSaleteamcode().equals("T001") || result.getSaleteamcode().equals("T002"));
    }
}
